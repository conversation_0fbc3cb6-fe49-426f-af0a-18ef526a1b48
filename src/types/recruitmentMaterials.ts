import { PublishDetails } from './contentStack';

export type CategoryType = 'recruitment' | 'gyb' | 'agent_to_agent';

export type SortType = 'newest' | 'oldest';

export interface RecruitmentMaterialItem {
  uid: string;
  _version: number;
  locale: string;
  ACL: object;
  _in_progress: boolean;
  category: CategoryType;
  channels: string[];
  created_at: string;
  created_by: string;
  description: string;
  display_title: string;
  pdf: File | null;
  video: File | null;
  tags: string[];
  thumbnail_image: File;
  title: string;
  updated_at: string;
  updated_by: string;
  publish_details: PublishDetails;
}

interface File {
  uid: string;
  _version: number;
  created_by: string;
  updated_by: string;
  created_at: string;
  updated_at: string;
  content_type: string;
  file_size: string;
  filename: string;
  title: string;
  ACL: object;
  parent_uid: string | null;
  is_dir: boolean;
  tags: string[];
  publish_details: PublishDetails;
  url: string;
}

export interface AllRecruitmentMaterialsResponse {
  entries: RecruitmentMaterialItem[];
}

export interface SingleRecruitmentMaterialResponse {
  entry: RecruitmentMaterialItem;
}
