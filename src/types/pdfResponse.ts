export type RPQPdfResponse = {
  rpqDocument: {
    base64: string;
    docType: string;
    template: string;
  };
};

export type ApplicationPdfResponse = {
  report: {
    status: string;
    statusCode: string;
    message: string;
    pdfFiles: {
      UNPROTECTED_PDF: string;
    };
    outputFile: string;
  };
  // for MY
  status: string;
  statusCode: string;
  message: string;
  pdfFiles: {
    UNPROTECTED_PDF: string;
  };
  outputFile: string;
};

export type CFFPdfResponse = {
  report: string;
};
