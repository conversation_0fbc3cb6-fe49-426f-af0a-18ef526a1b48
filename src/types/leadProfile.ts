import { Fna as CaseFna, SelectedProduct } from './case';

export type LeadProfileFNAStatus =
  | 'Start'
  | 'Expired'
  | 'Completed'
  | 'Not Completed';
export type LeadProfileRPQStatus = 'Start' | 'Expired' | 'Completed';

export interface Rpq {
  questions: Questions;
  productRisk: PolicyStatement;
  policyStatement: PolicyStatement;
  riskLevel: string;
  rpqScore: number;
  riskLevelLabel: PolicyStatement;
  rpqAcceptedDate: string;
}

export interface PolicyStatement {
  en: string;
}

export interface Questions {
  financialGoal: string;
  investmentTime: string;
  importanceOfInvestedFund: string;
  riskAttitude: string;
  investmentKnowledge: string;
}

export type Fna = CaseFna &
  Partial<{
    id: string;
    selectedNeed: string;
    rejectRecommendedReason: number;
    selectedProduct: SelectedProduct;
  }>;

export interface FnaGoalData {
  educationGoal?: FnaGoalDataItem;
  retirementGoal?: FnaGoalDataItem;
  investmentGoal?: FnaGoalDataItem;
  savingsGoal?: FnaGoalDataItem;
  incomeProtectionGoal?: FnaGoalDataItem;
  healthProtectionGoal?: FnaGoalDataItem;
  legacyPlanningGoal?: FnaGoalDataItem;
  loanCoverageGoal?: FnaGoalDataItem;
}

interface FnaGoalDataItem {
  targetAmount: number;
  coverageAmount: number;
  gapAmount: number;
  yearsToAchieve?: number;
  priority?: number;
}
