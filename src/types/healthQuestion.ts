export type QuestionOptionsListParams = {
  branch: string;
  tag: string;
  questionName: string;
  locale?: string;
};

export type LookupQuestionOptionsListParams = {
  branch: string;
  tag: string;
  questionName: string;
  searchKeyword: string;
  locale?: string;
};

export type QuestionType =
  | 'STRING'
  | 'INTEGER'
  | 'NUMBER'
  | 'PAST_DATE'
  | 'FUTURE_DATE'
  | 'DATE'
  | 'OPTION_GROUP'
  | 'OPTION_LIST'
  | 'OPTION_BACKED';

export type QuestionOption = {
  name?: string;
  text: string;
  tags: string[];
};

export type QuestionOptionsList = {
  name: string;
  options: QuestionOption[];
};

type OptionOnly = {
  definition: {
    isStrict: boolean;
    minSearchChars: number;
    optionListName: string;
    options?: string[];
    optionTags?: {
      [option: string]: string[];
    };
  };
};

type DateAndNumberOnly = {
  definition: {
    lowerBound: number;
    upperBound: number;
  };
};

type StringOnly = {
  definition: {
    maxLength: number;
  };
};

type EnquiryLineQuestion = {
  name: string;
  path: string;
  locale: string;
  hasAnswer: boolean;
  isSatisfied: boolean;
  answers: string[];
  validationErrors: {
    [answer: string]: string[];
  };
};

export type BaseHealthQuestion = EnquiryLineQuestion & {
  definition: {
    name: string;
    type: string;
    text: string;
    helpText: string;
    isMultiValued: boolean;
    tags: string[];
    includedCategories: string[];
    excludedCategories: string[];
  };
};

export type StringHealthQuestion = BaseHealthQuestion &
  StringOnly & {
    definition: {
      type: Extract<QuestionType, 'STRING'>;
    };
  };

export type NumberHealthQuestion = BaseHealthQuestion &
  DateAndNumberOnly & {
    definition: {
      type: Extract<QuestionType, 'INTEGER' | 'NUMBER'>;
    };
  };

export type DateHealthQuestion = BaseHealthQuestion &
  DateAndNumberOnly & {
    definition: {
      type: Extract<QuestionType, 'PAST_DATE' | 'FUTURE_DATE' | 'DATE'>;
    };
  };

export type OptionGroupHealthQuestion = BaseHealthQuestion &
  Omit<OptionOnly, 'minSearchChars' | 'isStrict'> & {
    definition: {
      type: Extract<QuestionType, 'OPTION_GROUP'>;
    };
  };

export type OptionListHealthQuestion = BaseHealthQuestion &
  Omit<OptionOnly, 'isStrict'> & {
    definition: {
      type: Extract<QuestionType, 'OPTION_LIST'>;
    };
  };

export type OptionBackedHealthQuestion = BaseHealthQuestion &
  OptionOnly & {
    definition: {
      type: Extract<QuestionType, 'OPTION_BACKED'>;
    };
  };

export type HealthQuestion =
  | StringHealthQuestion
  | NumberHealthQuestion
  | DateHealthQuestion
  | OptionGroupHealthQuestion
  | OptionListHealthQuestion
  | OptionBackedHealthQuestion;

export type Contribution = {
  value: string;
  lowValue: string;
  duration: string;
  sources: string[];
};

export type Bucket = {
  type: 'NUMBER' | 'OPTION' | 'DATE';
  name: string;
  contributions: Contribution[];
  min: any;
  max: any;
  sum: any;
};

export type EnquiryLine = {
  name: string;
  path: string;
  alias: string;
  section: string;
  preamble: string;
  rawSection: string;
  rawPreamble: string;
  isSatisfied: boolean;
  hasQuestions: boolean;
  isWrapUpLine: boolean;
  isRoot: boolean;
  isGlobal: boolean;
  isForced: boolean;
  tags?: string[];
  triggers?: string[];
  triggerQuestions: {
    [questions: string]: string[];
  };
  questions: HealthQuestion[];
  buckets: Bucket[];
};

export type Section = {
  name: string;
  isSatisfied: boolean;
  hasQuestions: boolean;
  enquiryLines: EnquiryLine[];
};

export type Enquiry = {
  enquiryId: string;
  branch: string;
  tag: string;
  locale: string;
  availableLocales: string[];
  effectiveDate: number;
  isSatisfied: boolean;
  isCloseable: boolean;
  isOpen: boolean;
  sections: Section[];
  buckets: Bucket[];
  allAnswers: {
    [question: string]: string[];
  };
};

export type HealthQuestion2 = {
  enquiryId: string;
  branch: string;
  tag: string;
  locale: string;
  availableLocales: string[];
  effectiveDate: number;
  isSatisfied: boolean;
  isCloseable: boolean;
  isOpen: boolean;
  sections: Section[];
  buckets: Bucket[];
  allAnswers: {
    [question: string]: string[];
  };
};

export type QuestionToolTipParams = {
  branch: string;
  tag: string;
  optionListName: string;
  optionTag: string;
  locale?: string;
};

export type QuestionToolTipContent = {
  name: string;
  text: string;
  tags: string[];
  aliases: string[];
};

export type PreUWMEAnswers = {
  tsarFinancialLife: number;
};

export type Party = {
  name: string;
  role: string;
  priority: number;
  id: string;
  enquiryId: string;
  compareSeq: number;
  preUWMEAnswers: PreUWMEAnswers;
};

export type HealthQuestion3 = {
  policy: {
    parties: Array<Party>;
  };
  message: string;
  status: string;
  lang: string;
};
