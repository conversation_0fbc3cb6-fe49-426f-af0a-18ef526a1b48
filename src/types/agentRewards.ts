import { ContentStackBase, ContentStackResonse } from './contentStack';

export type AgentReward = ContentStackBase & {
  title: string;
  banner: Banner;
  mobile_banner: Banner;
  button_label: string;
  button_url: string;
  description: string;
  display_settings: {
    // not sure how to use it
    modal_design: string;
    show_pop_up_dismiss: boolean;
    channels: string[];
    // ISO-8601 format
    start_date: string;
    end_date: string;
    agents_exclusion_options?: string[];
  };
  display_title: string;
  pop_up_dismiss_text: string;
};

export type Banner = ContentStackBase & {
  parent_uid: string;
  content_type: string;
  file_size: string;
  filename: string;
  is_dir: boolean;
  url: string;
};

export type AgentRewardResponse = ContentStackResonse<AgentReward>;
