export type fetchDocumentResult = {
  _version: number;
  locale: string;
  uid: string;
  ACL: Record<string | number | symbol, unknown>;
  _in_progress: boolean;
  created_at: string;
  created_by: string;
  display_title: string;
  modular_blocks: MODULAR_BLOCKS_LIST;
};

export type MODULAR_BLOCKS_LIST = { category: categoryItem }[];

export type categoryItem = {
  category_title: string;
  _metadata: Metadata;
  title: string;
  file: DocumentListItem[];
};

export type DocumentListItem = {
  label: string;
  _metadata: Metadata;
  file?: fileItemDetail | null;
  tag: string;
  external_url?: string;
  tag2?: string;
  blacklist_designation?: string[];
};

export type fileItemDetail = {
  parent_uid: string;
  uid: string;
  created_by: string;
  updated_by: string;
  created_at: string;
  updated_at: string;
  content_type: string;
  file_size: string;
  filename: string;
  title: string;
  ACL: Record<string | number | symbol, unknown>;
  _version: number;
  is_dir: boolean;
  tags: Array<unknown>;
  publish_details: {
    environment: string;
    locale: string;
    time: string;
    user: string;
  };
  url: string;
};

type Metadata = {
  uid: string;
};

export type categoryTitle =
  | 'UW Forms'
  | 'UW Guidelines'
  | 'POS Forms'
  | 'Claim Forms'
  | 'Certificate Servicing Forms'
  | 'Underwriting Forms';

export type switchTabTitleObject = {
  name: string;
  label: string;
};
