export type RenewalPayStoreResponse = {
  success: boolean;
  resultData: {
    integrationCode: string;
    integrationSessionId: string;
    regionCode: string;
    responseData: {
      success: boolean;
    };
    companyCode: string;
    forwardRequestTime: string;
  };
};

export type RenewalPayStoreRequest = {
  application: {
    agent: {
      agentCode: string;
    };
    applicationNum: string;
    policyNum: string;
    directCredit: {
      paymentMethod: string;
      bankNameForRenewal?: string;
      accountHolderForRenewal?: string;
      accountNumberForRenewal?: string;
      accountHolder?: string;
      accountNumber?: string;
      bankName?: string;
      cardExpiryDate?: string;
      cardHolder?: string;
      cardNumber?: string;
    };
  };
};

export type WithdrawalPayStoreResponse = {
  success: boolean;
  resultData: {
    integrationCode: string;
    integrationSessionId: string;
    regionCode: string;
    responseData: {
      success: boolean;
    };
    companyCode: string;
    forwardRequestTime: string;
  };
};

export type WithdrawalPayStoreRequest = {
  application: {
    agent: {
      agentCode: string;
    };
    applicationNum: string;
    policyNum: string;
    directCredit: {
      bankNameForWithdrawal: string;
      accountNumberForWithdrawal: string;
      accountHolderForWithdrawal: string;
    };
  };
};
