export type ContentStackBase = {
  uid: string;
  _version: number;
  locale: string;
  ACL: object;
  _in_progress: boolean;
  created_by: string;
  updated_by: string;
  created_at: string;
  updated_at: string;
  publish_details: PublishDetails;
  tags: string[];
};

export type PublishDetails = {
  time: string;
  user: string;
  environment: string;
  locale: string;
};

export type ContentStackResonse<T> = {
  entries: T[];
};
