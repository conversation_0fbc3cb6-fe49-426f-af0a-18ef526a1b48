export type NumList = {
  date: string;
  num: number;
};

export type Overview = {
  allLeadNum: number;
  directLeadNum: number;
  directAddNum: number;
  indirectLeadNum: number;
  indirectAddNum: number;
  yourLeadNum: number;
  otherLeadNum: number;
  leadTotal: number;
  leadNumList: NumList[];
  shareTotal: number;
  shareNumList: NumList[];
  mostViewMedia: string;
  mostViewMediaNum: number;
  viewNumList: NumList[];
  mostLeadMedia: string;
  mostLeadMediaNum: number;
  mostLeadMediaNumList: NumList[];
};

export type LeadInterestedTopics = {
  topicName: string;
  leadNum: number;
  viewTimes: number;
  categoryId: number;
};

export type TopicInterestedByLead = {
  thisYear: LeadInterestedTopics[];
  past30Days: LeadInterestedTopics[];
};

export type MediaLeadClickNum = {
  leadNum: number;
  viewTimes: number;
  media: string;
};

export type PeriodOfLeadGeneratedByChannels = {
  leadTotal: number;
  MediaLeadClickNum: MediaLeadClickNum[];
};

export type StatsOfLeadGeneratedByChannels = {
  thisYear: PeriodOfLeadGeneratedByChannels;
  past30Days: PeriodOfLeadGeneratedByChannels;
};

export type AffiliateDashboard = {
  overview: Overview;
  topicInterestedByLeads: TopicInterestedByLead;
  statsOfLeadGeneratedByChannels: StatsOfLeadGeneratedByChannels;
};

export type AffiliateDashboardRecentPost = {
  id: number;
  recommend: string;
  postType: string;
  categoryId1: string | null;
  categoryId2: string | null;
  commission: string | null;
  bodyTitle: string;
  bodyContent: string;
  postFiles: {
    fileType: string;
    fileName: string;
    id: number;
    fileUrl: string;
  }[];
  imageCount: number;
  videoCount: number;
  viewIn90Days: number;
  publishDate: string;
  relatedPosts?: AffiliateDashboardRecentPost[];
  articleUrl?: string;
};

export type AffiliateAgentProfileRequest = {
  agentCode: string | undefined;
  agentName: string | undefined;
  expertiseFields: string | undefined | null;
  accolade: string | undefined | null;
  expertiseFieldsList: string[] | undefined;
  accoladeList: string[] | undefined;
  briefDescription: string | undefined;
  expertiseYear: number | undefined;
  expertiseOthersFlag: string;
  accoladeOthersFlag: string;
};

export type AffiliateAgentOption = {
  agentExpertise: { code: string; codeValue: string }[];
  agentAccolade: { code: string; codeValue: string }[];
};

export type AffiliateDashboardRecentPosts = AffiliateDashboardRecentPost[];

export type AffiliateShareLink = {
  shareLink: string;
};
