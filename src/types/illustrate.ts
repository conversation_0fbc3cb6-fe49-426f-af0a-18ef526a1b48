import { Quotation, Warning } from './quotation';

export type Illustrate = Quotation & {
  illustration?: Illustration;
};
export type IllustrateRequest = {
  quotation: Quotation;
  proposeDT: Date;
};

export type IllustrateResponse = {
  quotation: Illustrate;
  warnings?: Warning[];
};
export interface Illustration {
  baseIllust: BaseIllustration;
  VBT?: VibrantIllustration;
  EP?: EpIllustration;
  ICP?: IcpIllustration;
  STC?: StcIllustration;
  FWDM?: FwdmIllustration;
  US02?: Uc02or09Illustration;
  US09?: Uc02or09Illustration;
  US11?: Uc11Illustration;
  US13?: Uc13Illustration;
  RPVLR?: RPVLRIllustration;
  RPVLSO?: RPVLSOIllustration;
  USP?: USPIllustration;
  U2P?: U2PIllustration;
  UPW?: UPWIllustration;
  UUW?: UUWIllustration;
  TM1?: TM1Illustration;
  OFB?: OFBIllustration;
  EN01?: EN01Illustration;
  ILB?: ILBIllustration;
  ILBP?: ILBPIllustration;
  FAMILYCI?: FAMILYCIIllustration;
  ILM?: ILMIllustration;
  ILMI?: ILMIIllustration;
  NGC?: NGCIllustration;
  EN3?: EN3Illustration;
  LVSGIO?: LVSGIOIllustration;
  LVS?: LVSIllustration;
  EN4?: EN4Illustration;
  EN5?: EN5Illustration;
  EN6?: EN6Illustration;
  summary?: SummaryIllustration;
  IL8?: IL8Illustration;
  IL4?: IL4Illustration;
  IP1?: IP1Illustration;
  GS1?: GS1Illustration;
  GS2?: GS2Illustration;
  HA1?: HA1Illustration;
  GL2?: GL2Illustration;
  GL1?: GL1Illustration;
  QS1?: QS1Illustration;
  QCA1?: { benefit: number[] };
  QHI1?: { benefit: number[] };
  QWC1?: { benefit: number[] };
  QSB1?: { benefit: number[] };
  QPB1?: { benefit: number[] };
  EN8?: EN8Illustration;
  FA1?: FA1Illustration;
  EN7?: EN7Illustration;
  IJ7?: IJ7Illustration;
  HA2?: HA2Illustration;
  LPVL?: LPVLIllustration;
  EN9?: EN9Illustration;
  EN9M?: EN9MIllustration;
  FWDLPR?: FWDLPRIllustration;
  FWDLPJ?: FWDLPRIllustration; // same table  as FWDLPR
  PA1?: PA1Illustration;
  FLP?: FLPIllustration;
  FCA?: FCAIllustration;
  FTP?: FTPIllustration;
  HYT?: HYTIllustration;
  FA2?: FA2Illustration;
  FA3?: FA3Illustration;
  // summary
  summaries: Summaries;
}

export interface BaseIllustration {
  policyYear: number[];
  policyAge: number[];
  basePrem: number[];
  basePrem_A: number[];
  lumpSum_A: number[];
  withdrawal_A: number[];
  topUps: number[];
  fundAllocation: number[];
  low: BaseBaseIllustrationValue;
  med: BaseBaseIllustrationValue;
  high: BaseBaseIllustrationValue;
  totalPrem: number[];
  guaranteedCI: number[];
  guaranteed: number[];
  endOfYear: number[];
  baseCont: number[];
  topUpCont: number[];
  basePremPerc: number[];
  topUp: number[];
  topUpPerc: number[];
  totalContPaid: number[];
  wakalahCharge: number[];
  scenario1: {
    tc: number[];
    fundCharge: number[];
    av: number[];
    sv: number[];
    db: number[];
  };
  scenario2: {
    tc: number[];
    fundCharge: number[];
    av: number[];
    sv: number[];
    db: number[];
  };
  totalAllocatedCont?: number[];
  totalAllocatedContPerc?: number[];
  wakalahFee?: number[];
  sumCovered?: number[];
  fundBooster?: number[];
}

export type VibrantIllustration = BaseIllustration & {
  annualizedPremium: number[];
  cvWm: number[];
  cvNom: number[];
  totalRider: number[];
  cvWmRide: number[];
  cvNomRide: number[];
};

export type IcpIllustration = BaseIllustration & {
  annualizedPremium: number[];
  db: number[];
};

export type EpIllustration = BaseIllustration & {
  annualizedPremium: number[];
  db: number[];
};

export type StcIllustration = BaseIllustration & {
  annualizedPremium: number[];
  db: number[];
};

export type FwdmIllustration = BaseIllustration & {
  premiums: number[];
  topUp: number[];
  welcomeBonus: number[];
  extendPremiums: number[];
  bonus: number[];
  topUpLumpSums: number[];
  withdrawals: number[];
};

export type Uc02or09Illustration = BaseIllustration & {
  singlePrem: number[];
  topUpLumpSums: number[];
  withdrawals: number[];
};
export type Uc11Illustration = BaseIllustration & {
  singlePrem: number[];
  topUpLumpSums: number[];
  withdrawals: number[];
};
export type Uc13Illustration = BaseIllustration & {
  singlePrem: number[];
  topUpLumpSums: number[];
  withdrawals: number[];
};
export interface Summaries {
  initialInsur: number;
  endPolicyYear: number[];
  totalAnnualPrem: number[];
  totalCommissionValue: number[];
  totalCommissionRate: number[];
  totalCashPayment: number[];
  totalCashValue: number[];
  totalDeathBenefit: number[];
  attainAge: number[];
  policyYear?: number[];
  policyAge?: number[];
  totalRiderCashValues?: number[];
  totalRiderYearlyTabarruCharges?: number[];
  annualizedReturn?: number;
  gccSumAssuredPerYear?: number;
  gcdSumAssuredPerYear?: number;
  totalPremiumPaid?: number;
  totalLegacyCashPayment?: number;
  totalSumPremiumPaid?: number;
  totalAdditionalAccidentalDeathBenefit?: number;
  totalCompassionateBenefit?: number;
  endPolicyAge?: number;
  totalAmountGLCP_GCP_FYCB?: number;
}

export interface BaseBaseIllustrationValue {
  totFundValue: number[];
  totFundValue_A: number[];
  db: number[];
  db_A: number[];
  totLivBenefits: number[];
  med: number[];
  payoutAmount: number[];
  // for Fast lane (U2P)
  payoutAmountCwa: number[];
  totLivBenefitsCwa: number[];
  dbCwa: number[];
  annualizedPayout: number[];
  totalLivingBenefit: number[];

  // Below fields are presented when product is mannifest (FWDM)
  fundValueEOMA: number[];
  dbEOMA: number[];
  fundValueEOM: number[];
  dbEOM: number[];

  // for Golden 7 (USP)
  projectedAccountValue: number[];
  surrenderBenefit: number[];
  deathBenefit: number[];
}

export type RPVLRIllustration = BaseIllustration & {
  premiums: number[];
  topUp: number[];
  roc: number[];
  topUpLumpSums: number[];
  withdrawals: number[];
};

export type RPVLSOIllustration = BaseIllustration & {
  premiums: number[];
  topUp: number[];
  roc: number[];
  topUpLumpSums: number[];
  withdrawals: number[];
};

export type USPIllustration = BaseIllustration & {
  singlePrem: number[];
  annualPayouts: number[];
  topUpLumpSums: number[];
  withdrawals: number[];
};

export type U2PIllustration = BaseIllustration & {
  newFunds: U2PBaseIllustration;
  nitroFund: U2PBaseIllustration;
  velocityFund: U2PBaseIllustration;
  specialSI: U2PBaseIllustration;
};

export type U2PBaseIllustration = BaseIllustration & {
  basicPrem: number[];
  basicPremCwa: number[];
  startUpBonus: number[];
  fundAmountAllocated: number[];
  topUpLumpSums: number[];
  withdrawals: number[];
};

export type UPWIllustration = BaseIllustration & {
  newFunds: UPWBaseIllustration;
  specialSI: UPWBaseIllustration;
};

export type UPWBaseIllustration = BaseIllustration & {
  singlePrem: number[];
  basicPrem: number[];
  topUps: number[];
  withdrawals: number[];
  singlePremCwa: number[];
  boosterBonus: number[];
};

// The UUWIllustration is the same as UPWIllustration
export type UUWIllustration = UPWIllustration;

export type TM1Illustration = BaseIllustration & {
  eoy: number[];
  attainedAge: number[];
  contributionpaideachyear: number[];
  totalcontributionpaid: number[];
  managementexpenses: number[];
  cumulcommission: number[];
  guaranteeddb: number[];
  nonguaranteeddb: number[];
  guaranteedtpd: number[];
  nonguaranteedtpd: number[];
  maturitybenefit: number[];
};

export type NGCIllustration = BaseIllustration & {
  attainedAge: number[];
  temp1013: number[];
  temp1014: number[];
  temp1017: number[];
  temp1016: number[];
  deathbenefit: number[];
  manageExpenseRate: number[];
  directCommRate: number[];
};

export type OFBIllustration = BaseIllustration & {
  endOfYear: number[];
  policyAge: number[];
  annualPrem: number[];
  totalContPaid: number[];
  managementExpenses: number[];
  totalDirectCommission: number[];
  serviceWakalahCharge: number[];
  guaranteedNaturalCause: number[];
  guaranteedMosquitoBorneDisease: number[];
  guaranteedMultipleIndemnity: number[];
  guaranteedMultipleIndemnityPublic: number[];
  guaranteedMultipleIndemnityTravelling: number[];
  scenario1: {
    surrenderValue: number[];
    noneGuaranteedNaturalCause: number[];
    noneGuaranteedMosquitoBorneDisease: number[];
    noneGuaranteedMultipleIndemnity: number[];
    noneGuaranteedMultipleIndemnityPublic: number[];
    noneGuaranteedMultipleIndemnityTravelling: [];
    maturityBenefit: [];
  };
  scenario2: {
    surrenderValue: number[];
    noneGuaranteedNaturalCause: number[];
    noneGuaranteedMosquitoBorneDisease: number[];
    noneGuaranteedMultipleIndemnity: number[];
    noneGuaranteedMultipleIndemnityPublic: number[];
    noneGuaranteedMultipleIndemnityTravelling: [];
    maturityBenefit: [];
  };
};

export type GS1Illustration = BaseIllustration & {
  endPolicyYear: number[];
  annualPrem: number[];
  commissionValue: number[];
  commissionRate: number[];
  cashValue: number[];
  deathBenefit: number[];
  attainAge: number[];
};

export type FA2Illustration = BaseIllustration & {
  endPolicyYear: number[];
  annualPrem: number[];
  commissionValue: number[];
  commissionRate: number[];
  cashValue: number[];
  deathBenefit: number[];
  attainAge: number[];
};

export type FA3Illustration = FA2Illustration;

export type GL1Illustration = BaseIllustration & {
  endPolicyYear: number[];
  premiumPaid: number[];
  alllocatedPrem: number[];
  alllocatedPerc: number[];
  coiBasic: number[];
  coiRider: number[];
  policyFee: number[];
  scenario1: {
    av: number[];
    db: number[];
  };
  scenario2: {
    av: number[];
    db: number[];
  };
  sumAssured: number[];
  commission: number[];
  commissionPerc: number[];
  policyAge: number[];
};

export type GS2Illustration = GS1Illustration;

export type QS1Illustration = {
  endPolicyYear: number[];
  cashValue: number[];
  deathBenefit: number[];
  additionalAccidentalDeathBenefit: number[];
  tpdBenefit: number[];
  lifeAssuredAgeAtYearEnd: number[];
};

export type HA1Illustration = BaseIllustration & {
  policyYear: number[];
  premiumPaid: number[];
  commissionValue: number[];
  commissionRate: number[];
  cashPayment: number[];
  cashValue: number[];
  deathBenefit: number[];
  policyAge: number[];
};

export type EN01Illustration = BaseIllustration & {
  summaryTable: {
    certYear: number[];
    age: number[];
    yearlyContribution: number[];
    contributionPaidToDate: number[];
    managementExpense: number[];
    directCommission: number[];
    tpdNaturalCauses: number[];
    tpdAccident: number[];
    funeralExpense: number[];

    scenario1: {
      surrenderValue: number[];
      sumCoveredDeathNaturalCauses: number[];
      sumCoveredDeathAccidentCauses: number[];
    };

    scenario2: {
      surrenderValue: number[];
      sumCoveredDeathNaturalCauses: number[];
      sumCoveredDeathAccidentCauses: number[];
    };
  };
};

export type ILBIllustration = BaseIllustration & {
  endOfYear: number[];
  baseCont: number[];
  topUpCont: number[];
  basePremPerc: number[];
  topUp: number[];
  topUpPerc: number[];
  totalContPaid: number[];
  wakalahCharge: number[];
  tabarruCharge: number[];
  sumCovered: number[];
  guaranteedTPD: number[];
  scenario1: {
    fundCharge: number[];
    cv: number[];
    db: number[];
  };
  scenario2: {
    fundCharge: number[];
    cv: number[];
    db: number[];
  };
};

export type ILBPIllustration = ILBIllustration;

export type FAMILYCIIllustration = {
  policyYear: number[];
  attainedAgePrimary: number[];
  attainedAgeSpouse: number[];
  attainedAgeChild1: number[];
  attainedAgeChild2: number[];
  attainedAgeChild3: number[];
  attainedAgeChild4: number[];
  contributionpaideachyear: number[];
  totalcontributionpaid: number[];
  managementexpenses: number[];
  cumulcommission: number[];
  minorcibenefit: number[];
  majorcibenefit: number[];
  deathbenefit: number[];
  fccwholefamily: number[];
  manageExpenseRate: number[];
  directCommRate: number[];
};

export type ILMIllustration = BaseIllustration & {
  endOfYear: number[];
  baseCont: number[];
  topUpCont: number[];
  basePremPerc: number[];
  topUp: number[];
  topUpPerc: number[];
  totalContPaid: number[];
  wakalahCharge: number[];
  tabarruCharge: number[];
  sumCovered: number[];
  guaranteedTPD: number[];
  scenario1: {
    fundCharge: number[];
    cv: number[];
    db: number[];
  };
  scenario2: {
    fundCharge: number[];
    cv: number[];
    db: number[];
  };
  totalCommission: number[];
  totalCommissionPerc: number[];
};

export type ILMIIllustration = BaseIllustration & {
  tabarruCharges: number[];
};

export type EN3Illustration = BaseIllustration & {
  endOfYear: number[];
  totContPaid: number[];
  totManageExpense: number[];
  totCommissionFee: number[];
  totServFee: number[];
  sumCovered: number[];
  scenario1: {
    db: number[];
    av: number[];
    sv: number[];
    cv: number[];
  };
  scenario2: {
    db: number[];
    av: number[];
    sv: number[];
    cv: number[];
  };
  annContPaid: number[];
};

export type LVSGIOIllustration = BaseIllustration & {
  endOfYear: number[];
  annContPaid: number[];
  totContPaid: number[];
  totManageExpense: number[];
  totCommissionFee: number[];
  sumCoveredAfterLien: number[];
  scenario1: {
    cv: number[];
    db: number[];
  };
  scenario2: {
    cv: number[];
    db: number[];
  };
};

export type EN4Illustration = BaseIllustration & {
  endOfYear: number[];
  annContPaid: number[];
  totContPaid: number[];
  annManageExpense: number[];
  annCommissionFee: number[];
  totServFee: number[];
  sumCovered: number[];
  maturityBenefit: number[];
  scenario1: {
    cv: number[];
    db: number[];
  };
  scenario2: {
    cv: number[];
    db: number[];
  };
};

export type EN5Illustration = EN4Illustration;

export type EN6Illustration = BaseIllustration & {
  endOfYear: number[];
  deathSumCovered: number[];
  tpdSumCovered: number[];
  accidentDeathSumCovered: number[];
  accidentTpdSumCovered: number[];
  funeralExp: number[];
  annContPaid: number[];
  totContPaid: number[];
  annManageExpense: number[];
  annCommissionFee: number[];
  adminCharge: number[];
  scenario1: {
    sv: number[];
    av: number[];
    maturityBenefit: number[];
  };
  scenario2: {
    sv: number[];
    av: number[];
    maturityBenefit: number[];
  };
};

export type LVSIllustration = LVSGIOIllustration;

export type SummaryIllustration = {
  allocatedCont?: {
    endOfAge: number[];
    policyAge: number[];
    contribution: number[];
    wakalahFee: number[];
    allocated: {
      allocatedPrem: number[];
      allocatesPremPerc: number[];
    };
    certificate: {
      baseSceA: number[];
      baseSceB: number[];
    };
    serviceFee: number[];
    fund: {
      fundSceA: number[];
      fundSceB: number[];
    };
    sumCovered: number[];
    accumulated: {
      accumSceA: number[];
      accumSceB: number[];
    };
    benefits: {
      benefitScceA: number[];
      benefitScceB: number[];
    };
    cost: number[];
  };
};

export type IL8Illustration = BaseIllustration & {
  endOfYear: number[];
  policyAge: number[];
  baseCont: number[];
  topUpCont: number[];
  basePrem: number[];
  basePremPerc: number[];
  topUp: number[];
  topUpPerc: number[];
  totalContPaid: number[];
  wakalahCharge: number[];
  tabarruCharge: number[];
  sumCovered: number[];
  tpdBenefitNatural: number[];
  tpdBenefitAccidental: number[];
  maturityBenefit: number[];
  scenario1: {
    fundCharge: number[];
    cv: number[];
    db: number[];
    dbac: number[];
    mb: number[];
  };
  scenario2: {
    fundCharge: number[];
    cv: number[];
    db: number[];
    dbac: number[];
    mb: number[];
  };
};

export type IL4Illustration = BaseIllustration & {
  endOfYear: number[];
  baseCont: number[];
  topUpCont: number[];
  basePrem: number[];
  basePremPerc: number[];
  topUp: number[];
  topUpPerc: number[];
  totalContPaid: number[];
  wakalahCharge: number[];
  tabarruCharge: number[];
  sumCovered: number[];
  guaranteedTPD: number[];
  scenario1: {
    fundCharge: number[];
    cv: number[];
    db: number[];
  };
  scenario2: {
    fundCharge: number[];
    cv: number[];
    db: number[];
  };
};
export type IP1Illustration = BaseIllustration & {
  sumCovered: number[];
  tpdBenefitNatural: number[];
  tpdBenefitAccidental: number[];
  tpdAcipc: number[];
  tpdAcho: number[];
  cancer: number[];

  fundBooster: number[];
  maturityReward: number[];
  scenario1: {
    cv: number[];
    db: number[];
    dbac: number[];
    dbAcwipc: number[];
    dbAcho: number[];
  };
  scenario2: {
    cv: number[];
    db: number[];
    dbac: number[];
    dbAcwipc: number[];
    dbAcho: number[];
  };
};

export type GL2Illustration = BaseIllustration & {
  policyYear: [];
  premiumPaid: number[];
  alllocatedPrem: number[];
  alllocatedPerc: number[];
  policyFee: number[];
  coiRider: number[];
  sumAssured: number[];
  commission: number[];
  commissionPerc: number[];
  policyAge: number[];
  scenario1: {
    coiBasic: number[];
    coiRider: number[];
    fundCharge: number[];
    av: number[];
    db: number[];
    fundBOY: number[];
  };
  scenario2: {
    coiBasic: number[];
    coiRider: number[];
    fundCharge: number[];
    av: number[];
    db: number[];
    fundBOY: number[];
  };
};

export type IJ7Illustration = GL2Illustration;

export type EN8Illustration = BaseIllustration & {
  endOfYear: number[];
  policyAge: number[];
  annContPaid: number[];
  scenario1: {
    sv: number[];
    av: number[];
    cv: number[];
    db: number[];
  };
  scenario2: {
    sv: number[];
    av: number[];
    cv: number[];
    db: number[];
  };
  totContPaid: number[];
  totManageExpense: number[];
  totCommissionFee: number[];
  totServFee: number[];
  sumCovered: number[];
};

export type FA1Illustration = {
  policyYear: number[];
  premiumPaid: number[];
  commissionValue: number[];
  commissionRate: number[];
  cashValue: number[];
  nonAccidentalCauses: number[];
  accidentalCauses: number[];
  publicConveyance: number[];
  outsideMalaysia: number[];
  ciBenefit: number[];
  policyAge: number[];
  totalPremiumPaid: number[];
};

export type EN7Illustration = BaseIllustration & {
  endOfYear: number[];
  policyAge: number[];
  annualPrem: number[];
  totalContPaid: number[];
  managementExpenses: number[];
  totalDirectCommission: number[];
  serviceWakalahCharge: number[];
  guaranteedNaturalCause: number[];
  guaranteedAccidentalCause: number[];
  guaranteedHospitalCashBenefit: number;
  guaranteedCompassionateBenefit: number[];
  scenario1: {
    surrenderValue: number[];
    nonGuaranteedNaturalCause: number[];
    nonGuaranteedAccidentalCause: number[];
    maturityBenefit: number[];
  };
  scenario2: {
    surrenderValue: number[];
    nonGuaranteedNaturalCause: number[];
    nonGuaranteedAccidentalCause: number[];
    maturityBenefit: number[];
  };
};

export type HA2Illustration = {
  policyYear: number[];
  premiumPaid: number[];
  commissionValue: number[];
  commissionRate: number[];
  cashValue: number[];
  deathBenefit: number[];
  policyAge: number[];
};

export type LPVLIllustration = {
  policyYear: number[];
  policyAge: number[];
  premiums: number[];
  extendPremiums: number[];
  topUp: number[];
  welcomeBonus: number[];
  fundAllocation: number[];
  bonus: number[];
  topUpLumpSums: number[];
  withdrawals: number[];
  low: {
    fundValueEOMA: number[];
    dbEOMA: number[];
  };
  med: {
    fundValueEOMA: number[];
    dbEOMA: number[];
  };
  high: {
    fundValueEOMA: number[];
    dbEOMA: number[];
  };
};

export type EN9Illustration = {
  endOfYear: number[];
  policyAge: number[];
  annualPrem: number[];
  totalContPaid: number[];
  baseCont: number[];
  adminCharge: number[];
  managementExpenses: number[];
  totalDirectCommission: number[];
  scenario1: {
    tabarruCharge: number[];
    acp: number[];
    totalNaturalDeath: number[];
    totalAccidentalDeath: number[];
    surrenderValue: number[];
  };
  scenario2: {
    tabarruCharge: number[];
    acp: number[];
    totalNaturalDeath: number[];
    totalAccidentalDeath: number[];
    surrenderValue: number[];
  };
  naturalDeath: number[];
  accidentalDeath: number[];
  compassionateBenefit: number[];
  charityBenefit: number[];
};

export type EN9MIllustration = {
  cont: number[];
  managementExpenses: number[];
  totalDirectCommission: number[];
  scenario1: {
    fundCharge: number[];
    surrenderValue: number[];
  };
  scenario2: {
    fundCharge: number[];
    surrenderValue: number[];
  };
};

export type FWDLPRIllustration = {
  policyYear: number[];
  insuredAge: number[];
  annualizedPremium: number[];
  cashValue: number[];
  livingBenefit: number[];
  maturityBenefit: number[];
};

export type PA1Illustration = HA1Illustration;

export type FLPIllustration = {
  policyYear: number[];
  policyAge: number[];
  annualizedPremium: number[];
  cvPrem: number[];
};

export type FCAIllustration = {
  policyYear: number[];
  age: number[];
  annualizedPrem: number[];
  withoutMaCI: number[];
  withMaCI: number[];
  mab: number[];
};

export type FTPIllustration = {
  policyYear: number[];
  insuredAge: number[];
  totalAnnualizedPremium: number[];
  surrenderBenefit: number[];
  maturityBenefit: number[];
  accumulatedMaturityBenefit: number[];
  deathBenefit: number[];
  totalAccidentalDeathBenefit: number[];
};

export type HYTIllustration = BaseIllustration & {
  managementExpenses: number[];
  managementExpensesPerc: number[];
  totalDirectCommission: number[];
  totalDirectCommissionPerc: number[];
  deathBenefit: number[];
  tpdBenefit: number[];
  compassionateCare: number[];
  annualPrem: number[];
};
