import { AuthenticationType } from 'expo-local-authentication';

export type BioMetricInfo = {
  enrolled: boolean;
  supported: AuthenticationType[];
};

// type AwardsConfig = {
//   showAchievement: boolean;
// };
//
export type LicenseInfo = {
  number?: string;
  type: string;
  desc?: string;
  effectiveFrom?: string;
  effectiveTo?: string;
};

export type AgentAddress = {
  type: string | null;
  address1: string[] | null;
  address2: string[] | null;
  countryCode: string | null;
};

export type AgentProfile = {
  agentId: string;
  branch: {
    name: string;
    code: string | null;
  };
  companyTitle: string | null; // Allow null to match JSON
  contact: {
    email: string;
    mobilePhone: string;
    workAddress: string[];
    workAddressCountryCode: string;
  };
  channel: string;
  joiningDate: string;
  agentPhotoUrl: string;
  designation: string;
  designationCode?: string;
  isAgentActive: boolean;
  isAgentLicenseActive: boolean;
  isELITE: boolean;
  isMDRT: boolean;
  isLeader: boolean | null; // for Team Management Tab
  isApprover: boolean | null; // for Policy Tracking Review Application Tab
  licenses: string[] | Array<LicenseInfo>;
  person: {
    title: string;
    firstName: string;
    middleName: string;
    lastName: string;
    fullName: string;
    birthPlace: string;
    dateOfBirth: string;
    idNumber?: string;
  };
  trainingCourses: string[];
  clientScope: {
    enabled: boolean;
    permissions: Array<ModulePermissionKeys>;
  } | null;
  agentType?: string;
  monitoringStaff?: MonitoringStaff[];
  leaderList?: LeaderList[];
  alcGroups?: Array<{
    id: string;
    name: string;
  }> | null;
  address: AgentAddress[] | null;
  hasVoiceRecorded?: boolean;
};

export type IBAgentProfile = AgentProfile & {
  address: Address[];
  bancaPositionName: string | null; // for IB Banca (BSN name card)
  contact: {
    email: string;
    mobilePhone: string;
    residentialPhone: string;
    workPhone: string;
    web: string;
  };
  residenceAddress: {
    addrTypeCd: string;
    addrAddress1: string;
    addrAddress2: string;
    addrCountry: string;
    addrPostCode: string;
    addrProvince: string | null;
    addrCity: string | null;
  };
  supervisorCode: string;
  supervisorName: string;
  supervisorEmail: string;
};

type Address = {
  type: 'RESIDENTIAL' | 'NAME_CARD' | 'WORK' | string;
  address1: (string | null)[] | null;
  address2: (string | null)[] | null;
  countryCode: string | null;
};

type MonitoringStaff = {
  staffCode: string;
  staffDesignation: string;
  staffEmail: string;
  staffMobileNumber: string;
  staffName: string;
  staffTier: string;
};

type LeaderList = {
  leaderCode: string;
  leaderDesignation: string;
  leaderEmail: string;
  leaderMobileNumber: string;
  leaderName: string;
  leaderTier: string;
};

export type ModulePermissionKeys =
  | 'lead'
  | 'si'
  | 'savedProposal'
  | 'performance'
  | 'eRecruit'
  | 'eRecruit:approval'
  | 'policy'
  | 'team'
  | 'affiliate'
  | 'agentAssist'
  | 'myLMS';

export type CubeAuthToken = {
  sub: string;
  exp: number;
  iat: number;
};

export type CubeIdToken = {
  guru_enabled: boolean;
  ignite_enabled: boolean;
  trainer_guru_enabled: boolean;
  channel?: string;
  // sample data
  // "aud": [
  //   "u7lJFgL5FgzUllVxCTz97A"
  // ],
  // "iat": 1712563268,
  // "exp": 1712599268,
  // "auth_time": 1712563268,
  // "at_hash": "qckZ3hJA8OK5rGM97_LRUg",
  // "sub": "50000979",
  // "guru_enabled": true
  // "agent_guru_enabled": false
};
