import { Quotation, Template } from './quotation';

export type ProposalInitResponse = {
  quotation: Pick<
    Quotation,
    | 'pid'
    | 'quoteType'
    | 'proposers'
    | 'insureds'
    | 'channel'
    | 'plans'
    | 'basicInfo'
    | 'notices'
    | 'alterations'
    | 'summary'
    | 'proposalOptions'
    | 'eVersion'
    | 'templates'
  >;
  template: Template;
};

export type ProposalInitRequest = {
  quotation: Pick<
    Quotation,
    'pid' | 'quoteType' | 'proposers' | 'insureds' | 'channel' | 'plans'
  >;
};
