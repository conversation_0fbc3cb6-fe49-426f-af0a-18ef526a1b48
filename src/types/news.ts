const PH_NEWS_TYPES = ['promotion', 'information', 'company'] as const;
const PH_CONTENT_STACK_NEWS_TYPES = [
  'promotion',
  'general_information',
  'company_activity',
] as const;
export const MY_CONTENT_STACK_NEWS_TYPES = [
  'regulation',
  'campaign',
  'contest',
  'notice',
  'news',
  'info',
] as const;

type PhNewsTypes = (typeof PH_NEWS_TYPES)[number];
type PhContentStackNewsTypes = (typeof PH_CONTENT_STACK_NEWS_TYPES)[number];
export type MyContentStackNewsTypes =
  (typeof MY_CONTENT_STACK_NEWS_TYPES)[number];

export type AllNewsTagTypes = PhNewsTypes;
export type AllContentStackNewsTagTypes =
  | PhContentStackNewsTypes
  | MyContentStackNewsTypes;

export type NewsItem = {
  parentEventId: number;
  title: string;
  //   type: string;
  type: AllNewsTagTypes;
  publishDate: string;
  expiryDate: string;
  share: boolean;
  createDate: string;
  imageFileName: string;
  imageType: string;
  imageDecodeBase64: string;
  registerStartDate: string;
  registerEndDate: string;
  attend: number;
  startDate: string;
  endDate: string;
  noOfInterested: number;
  fwdEventDetailList: FwdEventDetailList[];
  isCampaign: boolean;
  imageUrl: string;
  modifiedDate: string;
  // TODO_Alex: check if the following are correct
  videoUrl: any;
  fwdEventResponseList: any[];
  fwdEventMediaList: any[];
  fwdEventAgentResponse: any;
};

type FwdEventDetailList = {
  parentEventId: number;
  eventId: number;
  title: string;
  detail: string;
  language: string;
  venue: string;
  address: string;
  remarks: string;
  createDate: string;
  urlList: UrlList[];
  eventDate: string;
  modifiedDate: string;
};

type UrlList = {
  url: string;
  title: string;
  text: string;
  isExternal: boolean;
};

// ContentStack News
export interface ContentStackNewsItem {
  uid: string;
  _version: number;
  locale: string;
  ACL: object;
  _in_progress: boolean;
  channels: string[];
  created_at: string;
  created_by: string;
  display_share_button: boolean;
  display_title: string;
  expiry_date: string;
  image: Image[];
  news_details: string;
  notification_language: string;
  publish_date: string;
  send_push_notification: boolean;
  tags: string[];
  thumbnail: Image;
  title: string;
  type_of_news_post: AllContentStackNewsTagTypes;
  updated_at: string;
  updated_by: string;
  video_url: string;
  publish_details: PublishDetails;
  pdf_upload_field?: pdfAdditionalInfo;
  show_news_on_top?: boolean;
  news_content_v2?: string;
}

interface pdfAdditionalInfo {
  url: string;
  title: string;
  filename: string;
  file_size: string;
  content_type: string;
  ACL?: object;
  created_at?: string;
  created_by?: string;
  is_dir?: boolean;
  parent_uid?: string;
  publish_details?: object;
  tags?: string[];
  uid?: string;
  updated_at?: string;
  updated_by?: string;
}

interface Image {
  uid: string;
  _version: number;
  parent_uid: string;
  created_by: string;
  updated_by: string;
  created_at: string;
  updated_at: string;
  content_type: string;
  file_size: string;
  filename: string;
  title: string;
  ACL: object;
  is_dir: boolean;
  tags: string[];
  publish_details: PublishDetails;
  url: string;
}

interface PublishDetails {
  time: string;
  user: string;
  environment: string;
  locale: string;
}

export interface ContentStackAllNewsResponse {
  entries: ContentStackNewsItem[];
}
export interface ContentStackNewsDetailResponse {
  entry: ContentStackNewsItem;
}
