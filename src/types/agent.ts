import { ImageSource } from 'expo-image';
import { LicenseInfo } from './auth';

export interface AgentInfo {
  agentId: string;
  branch: {
    code: null;
    name: string;
  };
  companyTitle: string;
  contact: {
    email: string;
    mobilePhone: string;
  };
  designation: string;
  isAgentActive: boolean;
  isAgentLicenseActive: boolean;
  isELITE: boolean;
  isMDRT: boolean;
  licenses: string[] | Array<LicenseInfo>;
  person: {
    birthPlace: string;
    dateOfBirth: string;
    firstName: string;
    fullName: string;
    lastName: string;
    middleName: string;
    title: string;
    idNumber?: string;
  };
  trainingCourses: string[];
}

const agentProfileKeys = [
  'Profile',
  'Settings',
  'Logout',
  'MyDocuments',
] as const;
export type AgentProfileKeys = (typeof agentProfileKeys)[number];

export type AgentProfileTab =
  | {
      key: Exclude<AgentProfileKeys, 'Logout'>;
      label:
        | 'agentProfile.tablet.tabs.profile'
        | 'agentProfile.tablet.tabs.setting'
        | 'agentProfile.tablet.tabs.myDocuments'
        | 'agentProfile.tablet.tabs.logout';

      Icon: React.FC;
      isScreen: boolean;
      isPdf?: boolean;
      Component: () => JSX.Element;
    }
  | {
      key: Extract<AgentProfileKeys, 'Logout'>;
      label:
        | 'agentProfile.tablet.tabs.profile'
        | 'agentProfile.tablet.tabs.setting'
        | 'agentProfile.tablet.tabs.myDocuments'
        | 'agentProfile.tablet.tabs.logout';

      Icon: React.FC;
      isScreen: boolean;
      isPdf?: boolean;
    };

const agentALCGroupKeys = [
  'AlIsraGroup',
  'ImanLegacy',
  'MillionaireSisters',
  'NadiInsan',
  'NajahGroup',
  'QaizerGroup',
] as const;
export type agentALCGroupKeys = (typeof agentALCGroupKeys)[number];

export type AgentALCGroupListTab =
  | {
      key: Extract<agentALCGroupKeys, 'AlIsraGroup'>;
      label: 'agentProfile.editNameCard.alIsraGroup';
      Icon: ImageSource;
      code: string;
    }
  | {
      key: Extract<agentALCGroupKeys, 'ImanLegacy'>;
      label: 'agentProfile.editNameCard.imanLegacy';
      Icon: ImageSource;
      code: string;
    }
  | {
      key: Extract<agentALCGroupKeys, 'MillionaireSisters'>;
      label: 'agentProfile.editNameCard.millionaireSisters';
      Icon: ImageSource;
      code: string;
    }
  | {
      key: Extract<agentALCGroupKeys, 'NadiInsan'>;
      label: 'agentProfile.editNameCard.nadiInsan';
      Icon: ImageSource;
      code: string;
    }
  | {
      key: Extract<agentALCGroupKeys, 'NajahGroup'>;
      label: 'agentProfile.editNameCard.najahGroup';
      Icon: ImageSource;
      code: string;
    }
  | {
      key: Extract<agentALCGroupKeys, 'QaizerGroup'>;
      label: 'agentProfile.editNameCard.qaizerGroup';
      Icon: ImageSource;
      code: string;
    };

export type ProfileLanguage = 'english' | 'melayu' | 'tagalog' | 'taglish';

export type ProfileToneOfVoice =
  | 'casual'
  | 'inspirational'
  | 'professional'
  | 'comical'
  | 'personal';
