import { UWMEDecision } from './decision';
import { Entity, Person, Registration } from './person';

export type ReferralInfo = {
  referralAffiliateCode?: string;
  referralLeadSource?: string;
  referralBankCustomerId?: string;
  referralCampaignCode?: string;
  referralReferrerCode?: string;
  referralRefNumber?: string;
  referralServicingBranch?: string | null;
  referralAffiliateShareId?: string;
};

export type Party = {
  id: string;
  leadId?: string;
  clientType: PartyType;
  roles: PartyRole[];
  person?: Person;
  entity?: Entity;
  contacts?: Contacts;
  addresses?: Address[];
  customerId?: string;
  relationship?: string;
  isMainInsured?: boolean;
  isIdentityVerified?: boolean;
  ekycRefNum?: string;
  metaData?: object;
  spouse?: Spouse;
  payorSetting?: PayorSetting;
  beneficiarySetting?: BeneficiarySetting;
  trusteeSetting?: TrusteeSetting;
  dependent?: {
    yearToSupport: number;
  };
  sourceLeadId?: string;
  isRemoteSelling?: boolean;
  uw?: {
    enquiryId?: string;
    result?: UWResult;
    tsarFinancialLife?: number;
    decisionResponse?: UWMEDecision;
    uwType?: string;
  };
  referralInfo?: ReferralInfo;
  preferredCertificateCopy?: string;
  mainAddressType?: string | null;
  survey?: {
    passion?: string;
    otherPassion?: string;
  };
  voiceFilename?: string;
};

export type UWResult = {
  evidences: any[];
  exclusions: any[];
  loadings: any[];
  products: any[];
  premiums: any[];
  scenario?: string;
};

export enum PartyType {
  INDIVIDUAL = 'INDIVIDUAL',
  ENTITY = 'ENTITY',
}

export enum PartyRole {
  INSURED = 'INSURED',
  PROPOSER = 'PROPOSER',
  BENEFICIARY = 'BENEFICIARY',
  PAYER = 'PAYER',
  RENEWAL_PAYER = 'RENEWAL_PAYER',
  BENEFICIAL_OWNER = 'BENEFICIAL_OWNER',
  DEPENDENT = 'DEPENDENT',
  WITNESS = 'WITNESS',
  TRUSTEE = 'TRUSTEE',
  PARENT = 'PARENT',
}

export type Contacts = {
  email: string;
  preferredContactMode?: string;
  preferredLanguage?: string;
  preferredCopy?: string;
  shippingType?: string;
  otherContactMode?: string;
  phones: {
    type: 'MOBILE' | 'HOME' | 'WORK' | 'FAX';
    number: string;
    countryCode: string;
  }[];
  sameAsPO?: boolean;
};

export type Address = {
  addressType: 'MAIN' | 'HOME' | 'WORK' | 'REGISTERED';
  street?: string;
  subDistrict?: string;
  district?: string;
  city?: string;
  province?: string;
  zipCode?: string;
  addressNo?: string;
  currentAddressSameAsPo?: string;
  businessAddressOpt?: string;
  countryCode?: string;
  additionalAddress?: string;
};

export type Spouse = {
  person: Person;
  registration: Registration;
};

export type PayorSetting = {
  reasonForPayment: string;
};

export type BeneficiarySetting = {
  designation?: string;
  benefitPercentage?: number;
  beneficiaryType?: string;
  trusteeName?: string;
  contactDetailSameAsPo?: boolean;
  organizationName?: string;
};

export enum TrusteeType {
  INDIVIDUAL = 'Individual',
  ENTITY = 'Entity',
}

export type TrusteeSetting = {
  trusteeType?: TrusteeType;
  countryOfIncorporation?: string;
};

export function getPartyName(party: Party) {
  if (!party.person) {
    return '';
  }

  return `${party.person.name.firstName || ''} ${
    party.person.name.lastName || ''
  }`.trim();
}
