import { TransactionsInLead } from './lead';

export type Customer = {
  nationality?: string;
  addressField1?: string;
  addressField2?: string;
  addressField3?: string;
  addressField4?: string;
  addressField5?: string;
  title?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  extensionName?: string;
  birthDate?: string;
  birthPlace?: string;
  agentId?: string;
  gender?: string;
  smsFlag?: string;
  customerId?: string;
  surname?: string;
  zipCode?: string;
  usTaxDec?: string;
  clientType?: string;
  email?: string;
  mobileNo?: string;
  mobilePhoneCountryCode?: string;
  mobilePhoneNumber?: string;
  fatca?: string;
  fatcaEffDate?: string;
  countryCode?: string;
  occupationCode?: string;
  securityNo?: string;
  phone1?: string;
  phone2?: string;
  fax?: string;
  vip?: string;
  amla?: string;
};

export type GetCustomersResponse = {
  data: Customer[];
  offset: number;
  limit: number;
  totalCount: number;
};

export type LACustomer = {
  nationality: string | null;
  identityType: string | null;
  addressType: string | null;
  maidenLastName: string | null;
  identityNo: string | null;
  ustinNo: string | null;
  firstName: string | null;
  birthDate: string | null;
  email: string | null;
  extensionName: string | null;
  status: string | null;
  middleName: string | null;
  lastName: string | null;
  maritalStatus: string | null;
  addressSmsFlag: string | null;
  altClientInd: string | null;
  gender: string | null;
  smsFlag: string | null;
  address1: string | null;
  gsisNumber: string | null;
  address3: string | null;
  customerId: string | null;
  address2: string | null;
  address5: string | null;
  address4: string | null;
  zipCode: string | null;
  mib: string | null;
  clientType: string | null;
  mobileNo: string | null;
  fatca: string | null;
  birthPlace: string | null;
  maidenFirstName: string | null;
  fatcaEffDate: string | null;
  altClientCode: string | null;
  countryCode: string | null;
  occupationCode: string | null;
  title: string | null;
  securityNo: string | null;
  phone2: string | null;
  phone1: string | null;
  fax: string | null;
  vip: string | null;
  tinNumber: string | null;
  mailingStatus: string | null;
  sssNumber: string | null;
  amla: string | null;
};

export type ExactCustomerResponse = LACustomer;
