import { BuildCountry } from 'types';
import { country } from 'utils/context';

export type Channels = {
  AGENCY: string;
  BANCA: string;
  AFFINITY?: string;
};

export const CHANNELS_BY_COUNTRY = {
  ph: {
    AGENCY: 'AGENCY',
    BANCA: 'BANCA',
    AFFINITY: 'AFFINITY',
  },
  my: {
    AGENCY: 'TA',
    BANCA: 'BCA',
  },
  ib: {
    AGENCY: 'GBSN_TA',
    BANCA: 'GBSN_BCA',
  },
  id: {
    // TODO: TBD
    AGENCY: 'AGENCY',
    BANCA: 'BANCA',
  },
} as const satisfies Record<BuildCountry, Channels>;

export type CHANNELS_OUTCOMES =
  (typeof CHANNELS_BY_COUNTRY)[BuildCountry][keyof (typeof CHANNELS_BY_COUNTRY)[BuildCountry]];

const DEFAULT_CHANNELS = {
  AGENCY: 'AGENCY',
  BANCA: 'BANCA',
};

console.log('------------ country from channel', country);

export const CHANNELS: Channels =
  CHANNELS_BY_COUNTRY[country] ?? DEFAULT_CHANNELS;

export const AGENT_BRANCHES = {
  CODE_XL: 'CodeXL-Broker',
};
export const CHATBOT_ID_BY_CHANNELS = {
  [`ph-${CHANNELS_BY_COUNTRY.ph.AGENCY}`]: 'ph-eg', // TODO: update to ph-eg-agency
  [`ph-${CHANNELS_BY_COUNTRY.ph.BANCA}`]: 'ph-eg-banca',
  [`fib-${CHANNELS_BY_COUNTRY.ib.AGENCY}`]: 'fib-eg', // TODO update to my-eg-agency-fib
  [`fib-${CHANNELS_BY_COUNTRY.ib.BANCA}`]: 'my-eg-banca-fib',
  [`my-${CHANNELS_BY_COUNTRY.my.AGENCY}`]: 'my-eg-agency-takaful',
  [`my-${CHANNELS_BY_COUNTRY.my.BANCA}`]: 'my-eg-banca-takaful',
  [`id-${CHANNELS_BY_COUNTRY.id.AGENCY}`]: 'id-eg-agency',
  [`id-${CHANNELS_BY_COUNTRY.id.BANCA}`]: 'id-eg-banca-bri',
};