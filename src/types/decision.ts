export type UWMEDecision = {
  status: string;
  lang: string;
  policy: {
    parties: Array<{
      question_answers: Array<{
        [question: string]: Array<{
          name: string;
          questions: Array<{
            answers: Array<string>;
            helpText?: string;
            isMultiValued: boolean;
            name: string;
            options: Array<string>;
            text: string;
          }>;
          description: string;
        }>;
      }>;
      uid: string;
      priority: number;
      compareSeq: number;
      tag: string;
      branch: string;
      products: Array<{
        result: {
          postpone_duration: Array<{
            code: string;
            value: string;
            reason: Array<string>;
          }>;
          loading: Array<{
            code: string;
            value: number;
            reason: Array<string>;
            furtherChecks: {
              result_source: Array<string>;
              notification_parameters: Array<string>;
              reason_group: Array<string>;
            };
          }>;
          decision: Array<{
            code: string;
            value: string;
            reason: Array<string>;
            furtherChecks: object;
          }>;
          exclusion: Array<{
            code: string;
            value: string;
            reason: Array<string>;
            furtherChecks: {
              benefit: Array<string>;
            };
          }>;
        };
        benefits: Array<{
          compare_seq: number;
          type: string;
          priority: number;
        }>;
        id: string;
        priority: number;
        compare_seq: number;
      }>;
      role: string;
      enquiryId: string;
      result: {
        loading: Array<{
          code: string;
          value: number;
          reason: Array<string>;
          furtherChecks: {
            result_source: Array<string>;
            notification_parameters: Array<string>;
            reason_group: Array<string>;
          };
        }>;
        evidence: Array<{
          code: string;
          value: string;
          reason: Array<string>;
        }>;
        decision_benefit: Array<{
          code: string;
          value: string;
          reason: Array<string>;
          furtherChecks: {
            result_source: Array<string>;
            notification_parameters: Array<string>;
            reason_group: Array<string>;
          };
        }>;
        decision_la_ow: Array<{
          code: string;
          value: string;
          reason: Array<string>;
          furtherChecks: {
            incomplete_pre_uw_check: string;
          };
        }>;
        decision: Array<{
          code: string;
          value: string;
          reason: Array<string>;
          furtherChecks: {
            incomplete_pre_uw_check: string;
          };
        }>;
      };
      jsonString: string;
      name: string;
      status: string;
      zip: {
        [key: string]: string;
      };
    }>;
    result: {
      decision_uw: Array<{
        code: string;
        value: string;
        reason: Array<string>;
        furtherChecks: object;
      }>;
      decision_la_ow: Array<{
        code: string;
        value: string;
        reason: Array<string>;
        furtherChecks: {
          incomplete_pre_uw_check: string;
        };
      }>;
      decision: Array<{
        code: string;
        value: string;
        reason: Array<string>;
        furtherChecks: {
          message_code: string;
          non_standard_reason: Array<string>;
          evidence_reason: Array<string>;
          incomplete_pre_uw_check: string;
        };
      }>;
    };
  };
};
