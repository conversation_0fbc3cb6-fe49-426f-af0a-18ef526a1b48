export type CustomerAddressList = {
  addressType: string;
  countryCode: string;
  addressLine1: string;
  addressLine2?: string;
  addressLine3?: string;
  addressLine4?: string;
  addressLine5?: string;
  clientRelation?: string;
  alternateContact?: string;
  alternateContactType?: string;
  fulladdress?: string;
  zipCode?: string;
  email: string;
  mobileNumber: string;
  customerAddrId: string;
};

export type CustomerDetails = {
  customerLaId: string;
  title: string;
  fullName: string;
  mobileNumber: string;
  emailAddress: string;
  dateOfBirth: string;
  gender: string;
  maritalStatus: string;
  smoker: string;
  religion: string;
  nationality: string;
  occupationDesc: string;
  identityNo?: string;
  identityType?: string;
  otherIdNumber?: string;
  otherIdType?: string;
  incomeRange?: string;
  height?: number;
  weight?: number;
  race?: string;
  status?: string;
  address?: CustomerAddressList;
};
