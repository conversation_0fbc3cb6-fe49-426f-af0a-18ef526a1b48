import { TeamCompareByOptionKey } from 'features/teamManagement/types';
import { ProcPerformanceResponse } from 'hooks/useGetPerformance';

export interface Item {
  key: TeamCompareByOptionKey;
  type?: string;
  label?: string;
  labelKey?: string;
  filterLabel?: string;
}
export type Achievement = {
  saleAPE: number;
  aprTarget: number;
  caseTarget: number;
};

export type TeamMemberInfo = {
  id: string;
  name: string;
  avatarUrl?: string;
  lastYearAchieved?: Achievement;
  thisYearTarget?: Achievement;
};

export enum TeamPerformanceViewType {
  Team = 'Team',
  Individual = 'Individual',
}

export type TeamBranch = {
  branchAgentCount: number;
  asOfDate: string;
  downlineAgents: TeamDownlineAgent[];
};

export type TeamDownlineAgentTarget = {
  week: number;
  contact: number;
  appointment: number;
  illustrate: number;
  submit: number;
  startDate: string;
  endDate: string;
};

export type TeamDownlineAgent = {
  agentCode: string;
  displayName: {
    en: string;
    th: string;
  };
  imageUrl: string;
  designationCode: string;
  ape: number;
  rank: number;
  mobilePhone?: string;
  targetUpdatedAt?: string;
  targetYear?: string;
  targetMonth?: string;
  targets?: TeamDownlineAgentTarget[];
};

export const LEAD_STATUS = [
  { key: 'leads', label: 'Leads', labelKey: 'lead.status.leads' },
  { key: 'contacted', label: 'Contacted', labelKey: 'lead.status.contacted' },
  {
    key: 'appointment',
    label: 'Appointment',
    labelKey: 'lead.status.appointment',
  },
  {
    key: 'illustration',
    label: 'Illustration',
    labelKey: 'lead.status.illustration',
  },
  { key: 'submitted', label: 'Submitted', labelKey: 'lead.status.submitted' },
];

export type AgentAchievements = {
  agentCode: string;
  agentId: string; // agentId in PH
  agentName?: string;
  ytdFypCompletion?: number;
  ytdApeCompletion?: number;
  ytdCaseCompletion?: number;
  agentCount?: number;
};

export type TeamMainPage = {
  totalPage: number;
  totalElements: number;
  agentCode: string;
  tm?: AgentAchievements[];
  fwd?: AgentAchievements[];
  afwd?: AgentAchievements[];
  fwm?: AgentAchievements[];
  afwm?: AgentAchievements[];
  fwo?: AgentAchievements[];
  afwo?: AgentAchievements[];
  fwp?: AgentAchievements[];
  fwa?: AgentAchievements[];
  fwbm?: AgentAchievements[];
  afwbm?: AgentAchievements[];
};

export type AgentTarget = {
  // agentCode?: string;
  agentId?: string;
  targetAPE?: number | null;
  targetCase?: number | null;
  lastYearAPE?: number;
  avatarUrl?: string;
  displayName?: {
    en?: string;
    th?: string;
  };
};

export type AgentContribution = {
  agentCode: string;
  agentId: string; // agentId in PH
  ape: number;
  displayName?: {
    en?: string;
    th?: string;
  };
};

export type TeamTarget = {
  id?: string;
  agentCode?: string;
  currency?: string;
  lastYearAPE?: number;
  lastYearFYP?: number;
  mm?: number;
  yyyy?: number;
  fypAsOfYYYY?: number;
  targetAPE?: number;
  targetCase?: number;
  downlineAgents: AgentTarget[];
};

export type TeamContribution = {
  id?: string;
  agentCode?: string;
  branchTotalAPE?: number;
  displayName?: {
    en?: string;
    th?: string;
  };
  asOfDate?: string;
  downlineAgents: AgentContribution[];
};

export type AgentRanking = {
  agentCode: string;
  agentId: string; // agentId in PH
  caseRanking: number;
  apeRanking: number;
  caseCompletion?: number;
  apeCompletion?: number;
  designationCode?: string;
  displayName?: {
    en?: string;
    th?: string;
  };
};

export type TeamRanking = {
  agentCode: string;
  totalAgentCount?: number;
  asOfDate?: string;
  branchAgentCount?: number;
  currency?: string;
  displayName?: {
    en?: string;
  };
  downlineAgents: AgentRanking[];
};

export type AgentAchievement = {
  agentCode: string;
  displayName?: {
    en?: string;
    th?: string;
  };
  phoneMobile?: string;
  shortfallToNextTier?: string;
  fyp?: number;
  total?: number;
  shortfallToMDRTPercent: number;
  shortfallToCOTPercent: number;
  shortfallToTOTPercent: number;
  shortfallToMDRT: number;
  shortfallToCOT: number;
  shortfallToTOT: number;
  cot: number;
  fyc: number;
  mdrt: number;
  tot: number;
  ytdCurrentTierPercent?: number;
};

export type TeamAchievement = {
  agentCode?: string;
  asOfDate?: string;
  downlineAgents: AgentAchievement[];
};

export type TeamLeadTrackingConversion = {
  leads: {
    value: number;
    ratio: number;
  };
  contact: {
    value: number;
    ratio: number;
  };
  appointment: {
    value: number;
    ratio: number;
  };
  illustrate: {
    value: number;
    ratio: number;
  };
  submit: {
    value: number;
    ratio: number;
  };
};

export type TeamLeadTrackingTarget = {
  week: number;
  contact: number;
  appointment: number;
  illustrate: number;
  submit: number;
  startDate: string;
  endDate: string;
};

export type TeamLeadTracking = {
  agentCode?: string;
  year?: string;
  month?: number;
  updatedAt?: string;
  conversions: TeamLeadTrackingConversion[];
  targets: TeamLeadTrackingTarget[];
  summary: {
    target: {
      contact: number;
      appointment: number;
      illustrate: number;
      submit: number;
    };
  };
  members?: TeamLeadTracking[];
};

export type TeamOverview = {
  id: string;
  agentCode: string;
  asOfDate: string;
  currency: string;
  branchYTDAPECompletion: number;
  branchYTDCASECompletion: number;
  branchMTDAPECompletion: number;
  branchMTDCASECompletion: number;
  yyyy: string;
  displayName: {
    en?: string;
    th?: string;
  };
  target: number;
  shortFall: number;
};

export type TeamPerformance = ProcPerformanceResponse;

type MapaActivityRatio = {
  submittedActivityRatio?: number;
  issuedActivityRatio?: number;
  numberOfIssuedActiveAgent?: number;
  numberOfSubmittedActiveAgent?: number;
  submittedActivityPercentage?: number;
  issuedActivityPercentage?: number;
};

type MapaProductivity = {
  issuedProductivity?: number;
  submittedCase?: number;
  issuedCase?: number;
  submittedProductivity?: number;
};

type MapaAverageCaseSize = {
  submittedAvgCaseSize?: number;
  issuedAvgCaseSize?: number;
};

type MapaStats = {
  activityRatio?: MapaActivityRatio;
  productivity?: MapaProductivity;
  averageCaseSize?: MapaAverageCaseSize;
};

export type Mapa = {
  agentCode: string;
  agentName: string;
  designation: string;
  totalAgent: number;
  mtd: MapaStats;
  ytd: MapaStats;
  members: Mapa[] | null;
};
// ib
type IBTimePerformance = {
  issuedApe: number;
  issuedCase: number;
  submittedApe: number;
  submittedCase: number;
};
type IBPersistency = {
  firstYear: number;
  secondYear: number;
  rolling: {
    month12: number | null;
    month13: number | null;
    month24: number | null;
    month25: number | null;
  };
  fixed: {
    month12: number | null;
    month13: number | null;
    month24: number | null;
    month25: number | null;
  };
};
type BSC = {
  mandatoryCpdHours: number | null;
  complaintsReceived: number | null;
  cff1Or2Option: number | null;
  totalCpdHours: number | null;
  bscScore: number | null;
};
export type MembersSummary = {
  mtd: IBTimePerformance;
  ytd: IBTimePerformance;
  persistency: IBPersistency;
  bsc?: BSC | null;
  target?: null | {
    agentCode: string;
    targetYear: string;
    year: {
      yyyy: string;
      cases: number;
      ace?: number;
      ape?: number;
    };
    months: [
      {
        mm: string;
        cases: number;
        ace?: number;
        ape?: number;
      },
    ];
    updateAt: number;
  };
};

type ConversionRate = {
  submittedLead: number;
  submittedLeadPercentage: number;
}

type LeadConversion = {
  conversions: TeamLeadTrackingConversion;
  numberOfLeadGenerated: number;
  conversionRate: ConversionRate;
}

export type TeamLeadConversion = {
  agentCode: string;
  agentName: string;
  designation: string;
  totalAgent: number | null;
  mtd: LeadConversion | null;
  ytd: LeadConversion | null;
  members: TeamLeadConversion[];
}

export enum BlockType {
  ROLLING = 'ROLLING',
  FIXED = 'FIXED',
}
export enum PersistencyMonth {
  MONTH_12 = 'MONTH12',
  MONTH_13 = 'MONTH13',
  MONTH_24 = 'MONTH24',
  MONTH_25 = 'MONTH25',
}

export enum PersistencyPolicyStatus {
  ALL = 'all',
  PAID = 'paid',
  MISSING_PREMIUM = 'missingPremium',
}

export type Policy = {
  policyNum: string;
  lifeAssured: string;
  policyOwner: string;
  policyStatus: string;
  premiumStatus: string;
  payMode: string;
  modalPremium: number;
  nextDueDate: string;
  lastDueDate: string;
  balanceModalPremium: number;
};

export type TeamPolicy = {
  agentCode: string;
  lastUpdatedDate: string;
  policies: Policy[];
};

export type SalesBalancePerformance = {
  designation: 'UM' | 'AGT';
  agentCode: string;
  agentName: string;
  individualPerformance: MembersSummary;
  teamPerformance: MembersSummary;
  members: SalesBalancePerformance[];
  membersSummary?: MembersSummary[];
};

export type TeamMember = {
  agentCode: string;
  agentName: string;
  designation: string;
  subteams: TeamMember[];
  members: TeamMember[];
};

export type Hierarchy = {
  agentCode: string;
  agentName: string;
  designation: string;
  subteams: [
    {
      agentCode: string;
      agentName: string;
      designation: string;
      subteams: TeamMember[];
      members: TeamMember[];
    },
  ];
  members: TeamMember[];
};

type TeamPerformanceData = {
  individualPerformance?: MembersSummary;
  teamPerformance?: MembersSummary;
  mapa?: Omit<Mapa, 'members'>;
};

export type TeamHierarchyPerformance = TeamMember &
  TeamPerformanceData & {
    subteams?: TeamHierarchyPerformance[];
    members?: (TeamMember &
      TeamPerformanceData & {
        members?: (TeamMember & TeamPerformanceData)[];
      })[];
  };

export interface ReminderNotificationMessage {
  title: string;
  body: string;
}

export interface ReminderNotificationMessageData {
  agentId: string;
  message: ReminderNotificationMessage;
}
export interface SendReminderNotificationRequest {
  messages: Array<ReminderNotificationMessageData>;
}

export interface ReminderNotificationMessageResult {
  id: string;
  status: string;
}
export interface ReminderNotificationMessageResponseData {
  agentId: string;
  results: Array<ReminderNotificationMessageResult>;
}

export interface SendReminderNotificationResponse {
  responses: Array<ReminderNotificationMessageResponseData>;
}
