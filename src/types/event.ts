export enum AppEvent {
  ProductSelectionPitch = 'ProductSelection:GuruFAB:ProductSelectionPitch',
  ProductSelectionSummary = 'ProductSelection:GuruFAB:ProductSummary',
  OcrRendered = 'Ocr:Rendered',
  StartOcr = 'Eapp:StartOcr',
  OcrCaptured = 'Ocr:OcrCaptured',
  OcrCancelled = 'Ocr:OcrCancelled',
}

export enum OcrRole {
  PI = 'PI',
  PO = 'PO',
  BeneficialOwner = 'BeneficialOwner',
  Payor = 'Payor',
  Beneficiary = 'Beneficiary',
}

export type AppEventArgsList = {
  [AppEvent.ProductSelectionPitch]: {
    pid: string;
  };
  [AppEvent.ProductSelectionSummary]: {
    pid: string;
  };
  [AppEvent.OcrRendered]: null;
  [AppEvent.StartOcr]: null;
  [AppEvent.OcrCaptured]: { role: OcrRole };
  [AppEvent.OcrCancelled]: { role: OcrRole };
};
