export type PolicyOwnerValidationRequest = {
  requestType: 'all';
  agentId: string;
  mobileNo: string;
  email: string;
  refNo: string;
  idNo: string;
};

export type PolicyOwnerValidationResponse = {
  refNo: string;
  validPhoneNo?: boolean;
  validEmail?: boolean;
  allowToProceed: boolean;
  message?: string;
};

export type PolicyOwnerValidationPayload = {
  agentId: string;
  mobileNo: string;
  email: string;
  refNo: string;
  idNo: string;
};

export type BlacklistInfoValidationRequest = {
  placeOfBirth: string;
  postalCode: string;
  province: string;
  refNo: string; // policy number
  idNo: string; // passport number/ KTP number
};
