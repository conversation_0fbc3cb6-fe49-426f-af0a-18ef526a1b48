export type CustomerProfile = {
  address1: string;
  address2: string;
  address3: string;
  address4: string;
  address5: string;
  addressSmsFlag: string;
  addressType: string;
  altClientCode: string;
  altClientInd: string;
  amla: string;
  birthDate: string;
  birthPlace: string;
  clientType: string;
  countryCode: string;
  customerId: string;
  email: string;
  extensionName: string;
  fatca: string;
  fatcaEffDate: null;
  fax: string;
  firstName: string;
  gender: string;
  gsisNumber: string;
  identityNo: string;
  identityType: string;
  lastName: string;
  maidenFirstName: string;
  maidenLastName: string;
  mailingStatus: string;
  maritalStatus: string;
  mib: string;
  middleName: string;
  mobileNo: string;
  nationality: string;
  occupationCode: string;
  phone1: string;
  phone2: string;
  securityNo: string;
  smsFlag: string;
  sssNumber: string;
  status: string;
  tinNumber: string;
  title: string;
  ustinNo: string;
  vip: string;
  zipCode: string;
  mobilePhoneCountryCode: string;
  mobilePhoneNumber: string;
  //
  servicingBranchName: string;
  servicingBranchId: string;
  bltsReferenceNumber: string;
  referrerSalesId: string;
};

// a new api is applicable to FIB project because of extra policy info are added in customer profile

type NewCustomerProfileData = {
  customerID: string;
  displayNameEN: string;
  gender: string;
  phoneMobile: string;
  birthdayDate: string;
};

export type ApiResponse = {
  apiTraceId: null | string;
  success: null | boolean;
  status: null | string;
  responseData: NewCustomerProfile[];
  messageList: null | string[];
};

export type NewCustomerProfile = {
  customerID?: string;
  displayNameEN?: string;
  gender?: string;
  phoneMobile?: string;
  birthdayDate?: string;
};

export type MockNewCustomerProfile = {
  apiTraceId: string;
  success: boolean;
  status: string;
  responseData: NewCustomerProfileData[];
  messageList: string;
};

export type CustomerPolicies = {
  customerID: string;
  policyNumber: string;
  productNameEN: string;
  policyStatusCode: string;
  policyStatusDesc: string;
  submissionDate: string;
  basePlanCode: string;
};
