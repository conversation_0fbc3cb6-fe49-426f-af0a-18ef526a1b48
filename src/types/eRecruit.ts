import {
  DocumentFileKeyType,
  MYSQualificationTypes,
} from 'features/eRecruit/my/type';
import { NewApplicationFormParmaList } from './navigation';
import { MutateOptions } from '@tanstack/react-query';
import { BuildCountry, CubeResponse } from 'types';
import { country } from 'utils/context';
import { Option } from 'utils/helper/getLabelFromValue';
import { AxiosError } from 'axios';
import { RecruitKey } from 'utils/translation/i18next';

export type RecruitmentHomeStat = {
  status: Status;
  performance: {
    week: PerformanceData;
    month: PerformanceData;
  };
};

type PerformanceData = {
  conversion: Tiers;
  target: Tiers;
  asOfDate: string;
};

type Tiers = {
  candidate: number;
  submitted: number;
  approve: number;
  paid?: number;
};

export type PersonalDetailsTabERecruitApp = {
  isRerouted?: boolean;
  setIsRerouted?: (value: boolean) => void;
};

type Status = {
  pendingRemoteSignature: number;
  remoteCheckingRequired: number;
  pendingPayment: number;
  pendingLeaderApproval: number;
  resumeApplication: number;
  potentialCandidate: number;
  requireYourReview: number;
  rejected: number;
  approved: number;
};

export interface ERecruitApplicationStatusQueryParams {
  status: string;
}

export type RecruitStatusStatusKeys = keyof Status;

export type ERecruitTierKeys = keyof Omit<Tiers, 'paid'>;

export type TimeSectionKeys = 'week' | 'month';

export type TableConfigItem = {
  content: string;
};

export type ApplicationStatusQueryParams = {
  statusType?: string;
};
export type ApplicationListQueryParams = {
  cubeStatusList?: Array<CubeStatusKeys>;
  limit?: number;
  offset?: number;
  query?: string;
  direction?: SortByKeys;
  followUpList?: boolean;
};

export type ApplicationListResponds = {
  cubeStatus: CubeStatusKeys;
  name: string;
  email: string;
  mobilePhone: string;
  registrationId?: number;
  candidatePosition?: string;
  candidatePositionCode?: string;
  status?: string;
  submissionDate?: string;
  approvedDate?: string;
  rejectedDate?: string;
  signatureDate?: string;
  registrationStagingId?: number;
  stage: ApplicationStageKeys;
  lstUpdDate?: string;
};

export type ApplicationListDataResponds = {
  data: ApplicationListResponds[];
};
export const applicationStageList = [
  'NEW_APPLICATION',
  'PERSONAL_DETAILS',
  'OCCUPATION_DETAILS',
  'OTHER_DETAILS',
  'DOCUMENT',
  'CONSENT',
  'REMOTE_SIGNATURE',
  'REMOTE_CHECKING ',
] as const;

export type ApplicationStageKeys = (typeof applicationStageList)[number];
export type IDNApplicationStageKeys = Exclude<
  ApplicationStageKeys,
  'OCCUPATION_DETAILS'
>;
// -------VVV----- To be merged
export type ERTableInProgressContent = {
  displayName: string;
  position?: string;
  status: CubeStatusKeys;
  stage: ApplicationStageKeys;
  recruitId?: number;
  lastUpdated: string;
};

export type ERTableApprovedContent = {
  displayName: string;
  position: string;
  status: CubeStatusKeys;
  stage: ApplicationStageKeys;
  recruitId?: number;
  approvalDate: string;
};

export type ERTableRejectedContent = {
  displayName: string;
  position?: string;
  rejectDate: string;
  recruitId?: number;
  status: CubeStatusKeys;
  stage: ApplicationStageKeys;
};

// -------^^^^^----- To be merged

const defaultCubeStatusList = [
  'REMOTE_SIGNATURE',
  'REMOTE_CHECKING',
  'PENDING_PAYMENT',
  'PENDING_LEADER_APPROVAL',
  'RESUME_APPLICATION',
  'POTENTIAL_CANDIDATE',
  'APPROVED',
  'REJECTED',
] as const;

const nonPaidCubeStatusList = [
  'REMOTE_SIGNATURE',
  'PENDING_LEADER_APPROVAL',
  'RESUME_APPLICATION',
  'POTENTIAL_CANDIDATE',
  'APPROVED',
  'REJECTED',
] as const;

export type DefaultCubeStatus = (typeof defaultCubeStatusList)[number];

const cubeStatusListMap: Record<
  BuildCountry,
  typeof defaultCubeStatusList | typeof nonPaidCubeStatusList
> = {
  my: defaultCubeStatusList,
  ph: defaultCubeStatusList,
  ib: defaultCubeStatusList,
  id: nonPaidCubeStatusList,
};

export const cubeStatusList = cubeStatusListMap[country];

export type CubeStatusKeys = (typeof cubeStatusList)[number];

export type CandidateProfileParams = {
  registrationId?: number | null;
  registrationStagingId?: number | null;
};

export type ApplicationStatusParams = {
  cubeStatusList: 'APPROVED' | 'REJECTED';
};

export type ReviewAgentApplicationListResponds = {
  applicationId: number;
  name: string;
  email: string;
  mobilePhone: string;
  introducerName: string;
  introducerCode: string;
  candidatePositionCode: string;
  candidatePosition: string;
  agencyAgreementType: string;
  agencyAgreementTypeDesc: string;
  status: string;
  submissionDate: string;
  lastPaidDate: string;
  signatureDate: string;
  lastUpdDate: string;
  salesOfficeDesc?: string;
};

export type CandidateProfileResponds = {
  registrationStagingId?: number | null;
  registrationId?: number | null;
  candidateRefId: string;
  name: string;
  gender: string;
  email: string;
  mobilePhone: string;
  introducerCode: string;
  introducer: IntroducerDetails | null;
  agencyAgreementType: string;
  source: string;
  status: string | null;
  rejectedReason: string | null;
  rejectedAgent: RejectedAgentDetails | null;
  createdDate: string | null;
  submissionDate: string | null;
  lastPaidDate: string | null;
  approvedDate: string | null;
  rejectedDate: string | null;
  signatureDate: string | null;
  stage: string | null;
  cubeStatus: string | null;
  link?: string;
  shareLink?: string;
  paymentUrl?: string;
  sharePaymentUrl?: string;
  approveReason: string | null;
  approvedAgent: ApprovedAgentDetails | null;
  agentCodeIssueDate: string | null;
  licenseActivatedDate: string | null;
  agentCode: string | null;
  comments?: Array<{
    approverAgentCode: string;
    positionCode: string;
    comment: string | null;
    actionDate: string | null;
    interviewDate: string | null;
    action: string;
  }>;
};

export type RejectedAgentDetails = {
  agentCode: string | null;
  agentName: string | null;
  designate: string | null;
  agencyManagerCode: string | null;
  agencyManagerName: string | null;
};
export type IntroducerDetails = RejectedAgentDetails;

export type ApprovedAgentDetails = RejectedAgentDetails;

export const mysFilterStatus = [
  'pendingPayment',
  'pendingLeaderApproval',
  'approved',
  'rejected',
  'remoteChecking',
  'remoteSignature',
] as const;

export type FilterStatusKeys = (typeof mysFilterStatus)[number];
export type SortByKeys = 'ASC' | 'DESC';
export type SortDirectionKeys = 'newest' | 'oldest';

export type RemoveCandidateParams = {
  registrationId?: number | null;
  registrationStagingId?: number | null;
};

export type GetRemoteLinkResponse = {
  message: string;
  link: string;
};

export type GenderKeys = 'M' | 'F';

export type NewCandidateRequestBody = {
  applicationId: null; //null will create a new application, input id will update it
  stage: 'NEW_APPLICATION'; //NEW_APPLICATION,PERSONAL_DETAILS,OCCUPATION_DETAILS,OTHER_DETAILS,DOCUMENT,CONSENT,SUBMITTED
  identity:
    | {
        firstName: string;
        lastName: string;
        gender: GenderKeys;
      }
    | {
        fullname: string;
        gender: GenderKeys;
      };
  contact: {
    email: string;
    phones: Array<{
      countryCode: string;
      number: string;
      type: 'MOBILE' | 'WORK';
    }>;
  };
};

export type NewCandidateResponseBody = {
  registrationStagingId: number;
};

export type GetERecruitConfigResponse = MYSGetERecruitConfigResponse &
  IDNGetERecruitConfigResponse;

type IDNGetERecruitConfigResponse = {
  idTypeList: ListItem[];
  salesOfficeList: ListItem[];
  domicileList: ListItem[];
  superiorAgentCodeList: ListItem[];
  agentPositionList: ListItem[];
  examCityList: ListItem[];
  identityList: ListItem[];
  refList: ListItem[];
  areaManagerList: ListItem[];
  dmsStatusList: ListItem[];
  regulatoryList: {
    section: string;
    longDesc: LongDesc;
    regulatoryList: {
      key: string;
      longDesc: LongDesc;
    }[];
  }[];
  industryList: ListItem[];
};

type MYSGetERecruitConfigResponse = {
  positionList: ListItem[];
  genderList: ListItem[];
  maritalList: ListItem[];
  agentAgreementList: ListItem[];
  familyGenernalTakafulCompaniesList: ListItem[];
  liftGenernalTakafulCompaniesList: ListItem[];
  titleList: ListItem[];
  candidateTypeList: ListItem[];
  religionList: ListItem[];
  educationList: ListItem[];
  nationalityList: ListItem[];
  raceList: ListItem[];
  languageList: ListItem[];
  postStateCityList: PostStateCityList[];
  stateList: ListItem[];
  cityList: ListItem[];
  provinceList: ListItem[];
  districtList: ListItem[];
  postalCodeList: ListItem[];
  bankList: ListItem[];
  gstStatusList: ListItem[];
  intermediaryTypeList: ListItem[];
  spouseInsuranceRankList: ListItem[];
  spouseInsuranceRepresentList: ListItem[];
  ceilliList: ListItem[];
  generalInsuranceList: ListItem[];
  pceList: ListItem[];
  tbeFamilyTakafulList: ListItem[];
  tbeGeneralTakafulList: ListItem[];
  occupationList: ListItem[];
  branchList: ListItem[];
  alcBranchList: ListItem[];
  branchListAgentType: BranchListItem[];
};

export type ConflictsOfInterestSection = {
  conflictOfInterest: {
    ownershipInterest: boolean;
    externalEmployment: boolean;
    businessAffiliationInterest: boolean;
    relationshipGovernmentOfficial: boolean;
    otherInterest: boolean;
    ownershipInterests: Array<{
      nameOfBusiness: string;
      natureOfBusiness: string;
      nameOfOwner: string;
      relationship: string;
      percentageOfOwnership: number;
      dateAcquired: string;
    }>;
    externalEmployments: Array<{
      nameOfBusiness: string;
      natureOfBusiness: string;
      position: string;
      details: string;
      compensationReceived: boolean;
    }>;
    businessAffiliationInterests: Array<{
      nameOfBusiness: string;
      natureOfBusiness: string;
      nameOfFamilyMember: string;
      relationship: string;
      positionDepartment: string;
      dateCommencementEmployment: string;
    }>;
    relationshipGovernmentOfficials: Array<{
      nameOfGovernment: string;
      positionDepartment: string;
      relationship: string;
    }>;
    otherInterests: Array<{
      details: string;
    }>;
  };
};

export type ListItem = {
  itemCode: string;
  longDesc: LongDesc;
};

type AgencyType = 'ALC' | 'TA' | 'FA';

export type BranchListItem = {
  agentTypes: AgencyType[];
  branchList: ListItem[];
};

export type BranchListOption = {
  agentTypes: AgencyType[];
  branchList: Option[];
};

type LongDesc = {
  en: string;
  my: string;
  id: string;
};

type PostStateCityList = {
  postCode: string;
  stateCode: string;
  cityCode: string;
  cityName: string;
  stateName: string;
};

export type DocumentByFileIdParams = {
  registrationStagingId: number;
  fileId: number;
};

export type ApplicationTabProps = {
  showFooter?: () => void;
};

export type SideBarConfigType =
  | 'agreement'
  | 'declaration'
  | 'codeOfEthics'
  | 'consent';

export type SideBarConfig = {
  type: SideBarConfigType;
  label: RecruitKey;
};

export type ConsentSectionProps = {
  registrationStagingId: string | undefined;
  showFooter: () => void;
  pressedTab: SideBarConfigType;
};

export type UploadDocument = {
  file: string;
  fileKey: string;
};

export type DocumentList = DocumentListDetail[];

export type DocumentListPrarams = {
  agentType: string;
  registrationStagingId: string;
};

export type DocumentListDetail = {
  cacheAt: number;
  enDesc: string;
  fileKey: DocumentFileKeyType;
  imageUploadLimit: number;
  mandatory: boolean;
};

export type UploadSignature = {
  registrationStagingId: string;
  candidateSignBase64: string;
  witnessSignBase64: string;
};
export type ApplicationSubmissionModel = {
  registrationStagingId: number;
  candidateSignBase64: string;
  witnessSignBase64: string;
};
export const StepStatus = [
  'noStatus',
  'currentStatus',
  'filledStatus',
] as const;

export type StepStatusKeys = (typeof StepStatus)[number];

export type StepConfigProps = {
  label: string;
  // status: StepStatusKeys;
  routeName: keyof NewApplicationFormParmaList;
};

export type AgentInfoResponse = {
  agentCode: string;
  agentName: string;
  designate: string;
  agencyManagerCode: string | null;
  agencyManagerName: string | null;
};

export type IDNPersonalIdentity = {
  fullname: string;
  birthPlace: string;
};

export type IDNFraudCheck = {
  requireOCR?: boolean;
  infoMatchOCR?: boolean;
  livenessCheckPass?: boolean;

  // For Dukcapil check
  idCardValid?: boolean;
};
export type RecruitmentFlow = {
  // If F2F, require OCR = true; otherwise, require OCR = false
  requireOCR?: boolean;
};
export type IDNWorkingExperience = {
  type: 'PRESENT';
  presentOccupation: string;
  industry: string;
  yearOfExperience: number;
  isHaveExpLifeInsurance: boolean;
  isHaveExpGeneInsurance: boolean;
};

type IDNPosition = {
  salesOffice: string;
  domicile: string;
  superiorAgentCode: string;
  superiorAgentName: string | null;
  financingProgram: boolean;
  ref: string;
  osAreaManager: string;
};

type IDNAddress = {
  district: string;
  subDistrict: string;
  neighborhoodAssociation: string;
  communityAssociation: string;
};

export type IDNEmergencyContact = {
  emergencyContact: {
    address: string;
    city: string;
    fullName: string;
    mobile: {
      countryCode: string;
      number: string;
      type: 'MOBILE';
      // type: 'MOBILE' | 'HOME' | 'WORK';
    };
    residentNumber: string;
    postCode: string;
    state: string;
  } | null;
};

export type IDNApproverComments = {
  approvalComments: Array<
    Pick<ApprovalComments, 'comment'> &
      Partial<Omit<ApprovalComments, 'comment'>>
  >;
};

export type IDNFinancingProgramComment = {
  financingProgramComment: string;
};

export type IDNRegulatorysQuestionsOnly = {
  'S-1-1': { checked: boolean; detail: string | null };
  'S-1-2': { checked: boolean; detail: string | null };
  'S-2-1': { checked: boolean; detail: string | null };
  'S-2-2': { checked: boolean; detail: string | null };
  'S-2-3': { checked: boolean; detail: string | null };
  'S-4-1': { checked: boolean; detail: string | null };
  'S-5-1': { checked: boolean; detail: string | null };
  'S-5-2': { checked: boolean; detail: string | null };
  'S-5-3': { checked: boolean; detail: string | null };
  'S-5-4': { checked: boolean; detail: string | null };
  'S-5-5': { checked: boolean; detail: string | null };
  'S-5-6': { checked: boolean; detail: string | null };
};

export type IDNCOI = {
  'S-3-1': {
    checked: boolean;
    answer?: {
      s31DateAcquired: string;
      s31PercentageOfOwnership: string;
      s31NameOfBusiness: string;
      s31NameOfOwner: string;
      s31NatureOfBusiness: string;
    };
  };
  'S-3-2': {
    checked: boolean;
    answer?: {
      s32Details: string;
      s32CompensationReceived: 'true' | 'false';
      s32NameOfBusiness: string;
      s32Position: string;
      s32NatureOfBusiness: string;
    };
  };
  'S-3-3': {
    checked: boolean;
    answer?: {
      s33DateCommencement: string;
      s33NameOfFamily: string;
      s33NatureOfBusiness: string;
      s33Position: string;
      s33NameOfBusiness: string;
    };
  };
  'S-3-4': {
    checked: boolean;
    answer?: {
      s34Position: string;
      s34NameOfGovernment: string;
      s34Relationship: string;
    };
  };
  'S-3-5': {
    checked: boolean;
    detail?: string;
  };
};

export type IDNRegulatorysFull = {
  regulatorys: IDNRegulatorysQuestionsOnly & IDNCOI;
};

type IDNBankInfo = {
  branchName: string;
};

type IDNphoneItem = { verified?: boolean };

export type ApplicationFormResponds = {
  applicationId: number | null;
  stage: ApplicationStageKeys;
  identity: {
    title: string;
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    ethnicity: string;
    gender: string;
    maritalStatus: string;
    religion: string;
    citizen: string | null;
    registration: Array<{
      expiryDate: string | null;
      issueDate: string | null;
      number: string | null;
      type: RegistrationTypeKeys;
      rawType?: string | null;
    }>;
  } & Partial<IDNPersonalIdentity>;
  position: {
    position: string;
    jobType: string;
    agencyType: string;
    branchCode: string;
    branchName: string;
  } & Partial<IDNPosition>;
  contact: {
    email?: string;
    phones?: Array<
      {
        countryCode: string;
        number: string;
        type: PhoneInfoTypeKeys;
      } & IDNphoneItem
    >;
    address: {
      line1: string;
      line2: string;
      city: string | null;
      cityDesc: string | null;
      state: string | null;
      stateDesc: string | null;
      postCode: string | null;
    } & IDNAddress;
    businessAddress: {
      line1: string | null;
      line2: string | null;
      city: string | null;
      cityDesc: string | null;
      state: string | null;
      stateDesc: string | null;
      postCode: string | null;
    } & IDNAddress;
    bankInformation: {
      accountNumber: string | null;
      icNumber: string | null;
      bankName: string | null;
    } & Partial<IDNBankInfo>;
  } & Partial<IDNEmergencyContact>;
  qualifications: {
    academic: {
      date: null;
      name: string;
    };
    certifications: Array<{
      issueDate: string | null;
      otherDesc: string | null;
      type: keyof typeof MYSQualificationTypes;
      yearOfPassing: number | null;
    }>;
  };
  workingExperiences: Array<
    {
      type: string;
      basicSalary: number;
      companyAddress: string | null;
      companyEmail: string | null;
      companyName: string;
      companyPhoneCountryCode: string | null;
      companyPhone: string | null;
      dateApplied: string | null;
      dateTermination: string | null;
      intermediaryType: string | null;
      position?: string | null;
      rank: string | null;
    } & Partial<IDNWorkingExperience>
  >;
  spouseInformation: {
    firstName: string;
    lastName: string;
    numberOfDependence: number;
    occupation: string;
    icNumber: string;
    oldIcNumber: string | null;
    passportNo: string | null;
    type: string | null;
    companyName: string | null;
    rank: string | null;
    salary: number | null;
    dateApplied: string | null;
    dateTermination: string | null;
    spouseInsuranceRepresent?: boolean;
  };
  leaderInformation: {
    agentCode: string;
    name: string;
    alcFwdName: string | null;
  };
  introducerCode: string;
  introducerName: string;
  documentFiles: DocumentFile;
} & Partial<ConflictsOfInterestSection> &
  Partial<IDNRegulatorysFull> &
  Partial<IDNApproverComments> &
  Partial<IDNFinancingProgramComment> &
  Partial<RecruitmentFlow> &
  Partial<IDNFraudCheck>;

export type DocumentFileValue = {
  fileId: number;
  fileSize: number;
  fileType: string;
  fileName?: string;
};

export type DocumentFile =
  | Record<DocumentFileKeyType, Array<DocumentFileValue>>
  | undefined;

export type ApplicationFormRequest = ApplicationFormResponds;

export type ApprovalComments = {
  agentCode: string | null;
  comment: string;
  approverAgentCode: string | null;
  actionDate: string;
  interviewDate: string | null;
  action: string;
  approverAgentName?: string;
  approverAgentRole?: string;
};

export type IDApprovalComments = {
  comment: string;
  approverAgentCode: string;
  actionDate: string;
  interviewDate: string | null;
  action: string;
  positionCode: string;
};

export type ReviewAgentExtraOnApplicationResponds = {
  approvalComments?: ApprovalComments[];
};

export type SaveApplicationFormResponse = {
  registrationStagingId: number;
};

type RegistrationTypeKeys = MYSRegistrationTypeKeys;
type MYSRegistrationTypeKeys = 'NRIC' | 'PASSPORT' | 'OLD_IC' | 'INCOME_TAX';
export type PhoneInfoTypeKeys = 'MOBILE' | 'WORK' | 'HOME' | 'OTHER';
export type ParsingAppFormDataActionKeys = 'next' | 'save';

export type SavedActionProps<AppForm> = {
  data: AppForm;
  onSuccess?: MutateOptions<
    SaveApplicationFormResponse,
    unknown,
    ApplicationFormResponds,
    unknown
  >['onSuccess'];
  pressAction?: ParsingAppFormDataActionKeys;
  onError?: MutateOptions<
    SaveApplicationFormResponse,
    AxiosError<CubeResponse<unknown>>,
    ApplicationFormResponds,
    unknown
  >['onError'];
};

export enum WorkingExperienceType {
  PREVIOUS = 'PREVIOUS',
  PRESENT = 'PRESENT',
  INSURANCE = 'INSURANCE',
  TAKAFUL = 'TAKAFUL',
}

export enum CertificationType {
  TAKAFUL_TBE_FAMILY = 'TAKAFUL_TBE_FAMILY',
  TAKAFUL_TBE_GENERAL = 'TAKAFUL_TBE_GENERAL',
  INSURANCE_PCE = 'INSURANCE_PCE',
  INSURANCE_GENERAL = 'INSURANCE_GENERAL',
  ISLAMIC_RFP_CFP_CHFC = 'ISLAMIC_RFP_CFP_CHFC',
  INSURANCE_CEILLI = 'INSURANCE_CEILLI',
  MFPC = 'MFPC',
  FPAM = 'FPAM',
  OTHER = 'OTHER',
}

const positionListMap: Record<BuildCountry, string[]> = {
  my: ['TAAM', 'TAAGT', 'TAUM', 'TACA'],
  ph: [],
  ib: ['AGT', 'UM', 'AM'],
  id: ['FWD', 'FWM', 'FWO', 'FWP'],
};

export const positionList = positionListMap[country];

const nonPaidFilterStatusList: Array<{
  status: CubeStatusKeys;
  label: string;
}> = [
  { status: 'REMOTE_SIGNATURE', label: 'pendingRemoteSignature' },
  { status: 'PENDING_LEADER_APPROVAL', label: 'pendingLeaderApproval' },
  { status: 'RESUME_APPLICATION', label: 'resumeApplication' },
  { status: 'POTENTIAL_CANDIDATE', label: 'created' },
];

const defaultFilterStatusList: Array<{
  status: CubeStatusKeys;
  label: string;
}> = [
  { status: 'REMOTE_SIGNATURE', label: 'pendingRemoteSignature' },
  { status: 'REMOTE_CHECKING', label: 'remoteCheckingRequired' },
  { status: 'PENDING_PAYMENT', label: 'pendingPayment' },
  { status: 'PENDING_LEADER_APPROVAL', label: 'pendingLeaderApproval' },
  { status: 'RESUME_APPLICATION', label: 'resumeApplication' },
  { status: 'POTENTIAL_CANDIDATE', label: 'created' },
];

const filterStatusListMap: Record<
  BuildCountry,
  Array<{ status: CubeStatusKeys; label: string }>
> = {
  my: defaultFilterStatusList,
  ph: defaultFilterStatusList,
  ib: defaultFilterStatusList,
  id: nonPaidFilterStatusList,
};

export const filterStatusList = filterStatusListMap[country];

const religionCode = ['BUDDHIST', 'CHRISTIAN', 'HINDU', 'ISLAM', 'OTHERS'];
export type ReligionList = (typeof religionCode)[number];

export type NewApplicationFormValues = {
  identity: {
    firstName: string;
    lastName: string;
    gender: string;
    title: string;
    dateOfBirth: Date | null | undefined;
    icNumber: string;
    citizen: string;
    ethnicity: string;
    religion: ReligionList;
    maritalStatus: string;
    passportNo: string;
    oldIcNumber: string;
    taxCode: string;
  };
  contact: {
    countryCode: string;
    phoneNumber: string;
    officeNumberCountryCode: string;
    officePhoneNumber: string;
    email: string;
    isSameAsResidentialAddress: boolean;
    address: {
      line1: string;
      line2: string;
      city: string;
      cityDesc: string | null;
      state: string;
      postCode: string;
    };
    businessAddress: {
      line1: string;
      line2: string;
      city: string;
      cityDesc: string | null;
      state: string;
      postCode: string;
    };
    bankInformation: {
      accountNumber: string;
      icNumber: string;
      bankName: string;
    };
  };

  position: {
    position: string;
    jobType: string;
    agencyType: string;
    branchCode: string;
  };

  leaderInformation: {
    agentCode: string;
    name: null | string;
    alcFwdName: string;
  };
  introducerCode: string;
  introducerName: string;

  personalInformation: {
    educationType: string;
    islamicCert: boolean;
    insuranceCertCeilli: boolean;
    insuranceCertGeneral: boolean;
    insuranceCertPce: boolean;
    otherQualifications: boolean;
    takafulCertFamily: boolean;
    takafulCertGeneral: boolean;

    MFPCQualification: boolean;
    MFCPYearOfPassing: string;
    FPAMQualification: boolean;
    FPAMYearOfPassing: string;
    otherQualificationsDesc: string;
  };
  spouseInformation: {
    firstName?: string;
    icNumber?: string;
    lastName?: string;
    numberOfDependence?: string | null;
    occupation?: string;
    oldIcNumber?: string | null;
    passportNo?: string | null;
  };
  currentOccupation: CurrentWorkingExperience;
  previousOccupationList: PreviousWorkingExperiences[];
  familyTakafulExperienceList: FamilyTakafulExperience[];
  lifeInsuranceExperienceList: LifeInsuranceExperience[];
  spouseInsuranceExpToggleCheck: boolean;
  spouseInsuranceExperience: SpouseInsuranceExperience;
} & {
  conflictOfInterest: ConflictsOfInterestSection['conflictOfInterest'] & {
    isCOIAffirmed: boolean;
  };
};

type CurrentWorkingExperience = {
  basicSalary: string;
  companyAddress: string;
  companyEmail: string;
  companyName: string;
  companyPhoneCountryCode: string;
  companyPhone: string;
  dateApplied: Date | undefined;
  dateTermination: Date | undefined;
  position: string;
  rank: string;
};

type PreviousWorkingExperiences = {
  basicSalary: string;
  companyAddress: string;
  companyEmail: string;
  companyName: string;
  companyPhoneCountryCode: string;
  companyPhone: string;
  dateApplied: Date | undefined;
  dateTermination: Date | undefined;
  position: string;
  rank: string;
};

type FamilyTakafulExperience = {
  intermediaryType: string;
  companyName: string;
  rank: string;
  basicSalary: string;
  dateApplied: Date;
  dateTermination: Date;
};

type LifeInsuranceExperience = {
  intermediaryType: string;
  companyName: string;
  rank: string;
  basicSalary: string;
  dateApplied: Date;
  dateTermination: Date;
};

type SpouseInsuranceExperience = {
  intermediaryType: string;
  companyName: string;
  rank: string;
  salary?: string;
  dateApplied: Date | undefined;
  dateTermination: Date | undefined;
  type: string;
};
