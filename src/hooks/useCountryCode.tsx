import { useCallback, useMemo } from 'react';
import { useGetOptionList } from './useGetOptionList';

const COUNTRY_CODE_LIST = [
  { countryCode: 'RP', countryName: 'Philippines' },
  { countryCode: 'SG', countryName: 'Singapore' },
  { countryCode: 'MY', countryName: 'Malaysia' },
  { countryCode: 'IB', countryName: 'Malaysia' },
  { countryCode: 'HK', countryName: 'Hong Kong' },
  { countryCode: 'ID', countryName: 'Indonesia' },
  { countryCode: 'TH', countryName: 'Thailand' },
  { countryCode: 'VN', countryName: 'Vietnam' },
  { countryCode: 'TW', countryName: 'Taiwan' },
  { countryCode: 'CN', countryName: 'China' },
  { countryCode: 'JP', countryName: 'Japan' },
  { countryCode: 'KR', countryName: 'Korea' },
  { countryCode: 'AU', countryName: 'Australia' },
  { countryCode: 'NZ', countryName: 'New Zealand' },
  { countryCode: 'IN', countryName: 'India' },
  { countryCode: 'US', countryName: 'United States' },
  { countryCode: 'CA', countryName: 'Canada' },
  { countryCode: 'UK', countryName: 'United Kingdom' },
  { countryCode: 'FR', countryName: 'France' },
  { countryCode: 'DE', countryName: 'Germany' },
  { countryCode: 'IT', countryName: 'Italy' },
  { countryCode: 'ES', countryName: 'Spain' },
  { countryCode: 'NL', countryName: 'Netherlands' },
  { countryCode: 'BE', countryName: 'Belgium' },
  { countryCode: 'AT', countryName: 'Austria' },
];

export function useCountryCode() {
  return {
    toCountryName: (countryCode?: string) =>
      COUNTRY_CODE_LIST.find(list => {
        return list.countryCode === countryCode;
      })?.countryName || '',
  };
}

export function useReverseCallingCode() {
  const { data: optionList } = useGetOptionList();
  const callingCodeOptionList = useMemo(
    () =>
      optionList?.COUNTRY_CODE.options.map(({ value }) => {
        const [callingCode, _] = value.split(' - ');
        return {
          value,
          callingCode,
        };
      }) ?? [],
    [optionList?.COUNTRY_CODE.options],
  );

  return useCallback(
    (code?: string) =>
      callingCodeOptionList.find(({ callingCode }) => callingCode === code)
        ?.value,
    [callingCodeOptionList],
  );
}
