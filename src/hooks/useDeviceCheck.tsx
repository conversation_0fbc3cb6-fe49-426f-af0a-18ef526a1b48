import useBoundStore from 'hooks/useBoundStore';
import { isPhoneSupported, isTabletSupported } from 'utils/config/module';

export default function useLayoutAdoptionCheck() {
  const isDeviceTablet = useBoundStore(store => store.isTablet);
  const isTabletMode = isPhoneSupported
    ? isDeviceTablet && isTabletSupported
    : isTabletSupported;
  return {
    isTabletMode,
    isMobileMode: !isTabletMode,
  };
}
