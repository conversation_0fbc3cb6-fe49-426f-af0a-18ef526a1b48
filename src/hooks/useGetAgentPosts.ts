import { useInfiniteQuery } from '@tanstack/react-query';
import socialMarketingApi from 'api/socialMarketingApi';

export const AGENT_POSTS_QUERY_KEY = ['agent-posts'];

export function useGetAgentPosts() {
  return useInfiniteQuery({
    queryKey: AGENT_POSTS_QUERY_KEY,
    queryFn: ({ pageParam = 1 }) =>
      socialMarketingApi.getAgentPosts({ page: pageParam }),
    getNextPageParam: lastPage => {
      return lastPage.hasNext ? lastPage.page + 1 : undefined;
    },
  });
}
