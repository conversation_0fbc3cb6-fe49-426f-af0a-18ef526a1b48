import { useCallback, useMemo } from 'react';
import { useGetOptionList } from './useGetOptionList';

export function useExtensionName() {
  const { data: optionList } = useGetOptionList();
  const extensionNameList = useMemo(
    () =>
      optionList?.EXTENSION?.options?.map(({ value }) => {
        return {
          value,
        };
      }) ?? [],
    [optionList?.EXTENSION?.options],
  );

  return useCallback(
    (code?: string) =>
      extensionNameList.find(({ value }) => value === code)?.value,
    [extensionNameList],
  );
}
