import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createCase } from 'api/caseApi';
import { Case } from 'types/case';
import { getCaseByIdKey } from './useGetCase';

export function useCreateCase() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: Pick<Case, 'agent'>) => createCase(data),
    onSuccess: async caseId => {
      await queryClient.invalidateQueries(getCaseByIdKey(caseId));
    },
  });
}
