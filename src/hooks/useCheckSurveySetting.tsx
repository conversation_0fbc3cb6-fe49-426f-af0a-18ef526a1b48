import { contentStackSettingEntryId, country } from 'utils/context';
import Stack, {
  CONTENT_TYPES,
  CubeSettingsContentStackItem,
  SurveySettings,
} from './useContentStack';

export async function checkSurveySetting() {
  const ContentTypeKey = CONTENT_TYPES.VERSION_CHECK;
  const Query = Stack.ContentType(ContentTypeKey).Query();
  const result = await Query.toJSON()
    .find()
    .catch(error => {
      console.error(
        '🚀 ~ file: useDoVersionCheck.tsx:8 ~ doVersionCheck ~ error:',
        error,
      );
    });
  const nullCase = { day_of_month: undefined } satisfies SurveySettings;

  // TODO : should be mapping to entity ID from eas.json
  if (country == 'my') {
    const takafulEntityID = contentStackSettingEntryId;
    const dataArray: Array<CubeSettingsContentStackItem> = JSON.parse(
      JSON.stringify(result[0]),
    );
    console.log(
      '------ dataArray: ',
      dataArray?.find(d => d.uid === takafulEntityID)?.survey_settings,
    );
    return (
      dataArray?.find(d => d.uid === takafulEntityID)?.survey_settings ??
      nullCase
    );
  }

  if (result && result[0] && result[0][0]) {
    const data: CubeSettingsContentStackItem = JSON.parse(
      JSON.stringify(result[0][0]),
    );

    return { ...data?.survey_settings };
  }

  return nullCase;
}
