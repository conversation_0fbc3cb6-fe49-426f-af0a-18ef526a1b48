import { StoreSlice } from 'types';
import { create } from 'zustand';
import {
  createAppSlice,
  createAuthSlice,
  createHomeSlice,
  createLeadSlice,
} from 'utils/zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { initialState as homeInitialState } from 'utils/zustand/homeSlice';
import { immer } from 'zustand/middleware/immer';

import { ObjectUtil } from 'utils';
import { merge, unionBy } from 'lodash';
import { createQuotationSlice } from 'features/proposal/store/quotationSlice';
import { createNewsSlice } from 'utils/zustand/newsSlice';
import { createTeamSlice } from 'features/teamManagement/store/teamSlice';
import { createPerformanceSlice } from 'features/performance/store/performanceSlice';
import { createCaseSlice } from 'utils/zustand/caseSlice';
import { cloneDeep } from 'utils/helper/objectUtil';
import { createCandidateSlice } from 'utils/zustand/candidateSlice';
import { createEcoachSlice } from 'features/ecoach/store/ecouchSlice';
import { createPolicySlice } from 'utils/zustand/policySlice';

const useBoundStore = create<
  StoreSlice,
  [['zustand/immer', never], ['zustand/persist', unknown]]
>(
  immer(
    persist(
      (...a) => ({
        ...createAppSlice(...a),
        ...createAuthSlice(...a),
        ...createHomeSlice(...a),
        ...createLeadSlice(...a),
        ...createCandidateSlice(...a),
        ...createNewsSlice(...a),
        ...createQuotationSlice(...a),
        ...createTeamSlice(...a),
        ...createPerformanceSlice(...a),
        ...createEcoachSlice(...a),
        ...createCaseSlice(...a),
        ...createPolicySlice(...a),
      }),
      {
        name: 'CubeStore',
        version: 1, // equals 0 by default
        migrate: (
          persistedState: unknown,
          version: number,
        ): StoreSlice | Promise<StoreSlice> => {
          const newState = cloneDeep(persistedState as StoreSlice);
          if (version === 0) {
            newState.home.myTasks.sectionsOrder =
              homeInitialState.myTasks.sectionsOrder;
          }
          return newState;
        },
        storage: createJSONStorage(() => AsyncStorage),
        partialize: state => {
          const persistedState = Object.fromEntries(
            Object.entries(state).filter(([key]) => {
              return !key.includes('Actions');
            }),
          );

          return ObjectUtil.extractByKeys(persistedState, persistWhiteList);
        },
        merge: (persistedState, currentState) => {
          const hasCards = !!(persistedState as StoreSlice)?.home?.overView
            ?.cardsOrder;
          if (!hasCards) {
            return merge(currentState, persistedState);
          }
          // TODO: refactor this logic to have a robust merging function
          // Temp fix:
          // Check if two arrays have the same elements
          // regardless of their precedences
          const savedCards = cloneDeep(
            (persistedState as StoreSlice).home.overView.cardsOrder,
          );
          const definedCards = cloneDeep(currentState.home.overView.cardsOrder);
          const isSame = definedCards.every(item => {
            return savedCards.some(currentItem => {
              return currentItem.key === item.key;
            });
          });
          if (isSame) {
            currentState.home.overView.cardsOrder = savedCards;
            return merge(currentState, persistedState);
          }
          // Handle cards array merging
          // otherwise _.merge will break the elements
          const newCards = unionBy(definedCards, savedCards, 'key');
          (persistedState as StoreSlice).home.overView.cardsOrder = newCards;
          currentState.home.overView.cardsOrder = newCards;
          return merge(currentState, persistedState);
        },
      },
    ),
  ),
);

export const getBoundStoreState = useBoundStore.getState();

const persistWhiteList = [
  'auth.authInfo',
  'auth.agentCode',
  'auth.previousAgentCode',
  'auth.bioMetricEnabled',
  'home.showReorderTooltip',
  'home.addLeadPrivacyDialogConfirmed',
  'home.showTermsAndConditions',
  'home.overView',
  'home.myTasks',
  'lead.today.showToolTip',
  'leadSearch.recentSearchLeadItem',
  'leadSearch.recentSearchCustomerItem',
  'news.bookmarkedNewsIdMap',
  'pushNotificationEnabled',
  'firstLoginTime',
  'lastSurveyTriggerTime',
  'lead.expiredLeadSkippedTime',
  'deviceId',
  'performance.tipYourPerformance',
  'candidateSearch.recentSearchCandidateItem',
  'language',
];

export default useBoundStore;
