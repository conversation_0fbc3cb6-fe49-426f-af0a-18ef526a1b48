import { usePromptContext } from 'components/prompt/PromptContext';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { OcrResult } from 'types/ocr';
import { getAlertActions } from 'components/prompt/PromptDialog';
import { moduleConfigs } from 'utils/config/module';
import { country } from 'utils/context';

const defaultValidationOrder = ['blurry', 'blank'];

export const useOcrDataHandler = (
  { silent }: { silent: boolean } = { silent: false },
) => {
  const { prompt } = usePromptContext();
  const { t } = useTranslation(['eApp']);
  const handler = useCallback(
    async (
      data: OcrResult['extract'],
      onRetake?: () => void,
      onSkip?: () => void,
      idType?: OcrResult['type'],
    ) => {
      const validationOrder =
        moduleConfigs[country].ocrConfig?.validationOrder ??
        defaultValidationOrder;

      for (const type of validationOrder) {
        // handle image quality
        if (type === 'blurry' && data.imageQuality === 'blurry') {
          if (silent) return false;
          const res = await prompt<{ accept: string }>({
            title: t('eApp:ocr.error.blurryPhoto.title'),
            description: t('eApp:ocr.error.blurryPhoto.desc'),
            actions: getAlertActions,
            config: {
              accept: t('eApp:ocr.error.retake'),
            },
            onClose: () => {
              onSkip?.();
            },
            closable: true,
          });

          if (res) {
            onRetake?.();
          }
          return false;
        }

        // handle isBlankPhoto
        if (type === 'blank' && data.isBlankPhoto) {
          if (silent) return false;
          const res = await prompt<{ accept: string }>({
            title: t('eApp:ocr.error.blankPhoto.title'),
            description: t('eApp:ocr.error.blankPhoto.desc'),
            actions: getAlertActions,
            config: {
              accept: t('eApp:ocr.error.retake'),
            },
            onClose: () => {
              onSkip?.();
            },
            closable: true,
          });

          if (res) {
            onRetake?.();
          }
          return false;
        }
      }

      // handle id type validation
      if (
        country === 'id' &&
        ['OTHER', 'passport', 'unknown'].includes(idType ?? '')
      ) {
        if (silent) return false;
        const res = await prompt<{ accept: string }>({
          title: t('eApp:ocr.error.invalidPhoto.title'),
          description: t('eApp:ocr.error.invalidPhoto.desc'),
          actions: getAlertActions,
          config: {
            accept: t('eApp:ocr.error.retake'),
          },
          onClose: () => {
            onSkip?.();
          },
          closable: true,
        });

        if (res) {
          onRetake?.();
        }
        return false;
      }
      return true;
    },
    [prompt, silent, t],
  );
  return handler;
};
