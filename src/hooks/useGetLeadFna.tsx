import { useQuery } from '@tanstack/react-query';
import { getFnaByLeadId, getRpqByLeadId } from 'api/leadProfileApi';
import { Fna, Rpq } from 'types/leadProfile';

export const QUERY_KEY_FNA = '/lead/fna';
export const QUERY_KEY_RPQ = '/lead/rpq';

export function useGetFnaByLeadId(leadId: string | undefined) {
  return useQuery<Fna>({
    queryKey: [QUERY_KEY_FNA, leadId],
    queryFn: () => getFnaByLeadId(String(leadId)),
    enabled: Boolean(leadId),
  });
}
export function useGetRpqByLeadId(leadId: string | undefined) {
  return useQuery<Rpq>({
    queryKey: [QUERY_KEY_RPQ, leadId],
    queryFn: () => getRpqByLeadId(String(leadId)),
    enabled: <PERSON><PERSON><PERSON>(leadId),
  });
}
