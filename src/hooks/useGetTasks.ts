import { useQuery } from '@tanstack/react-query';
import useBoundStore from './useBoundStore';
import { getTasks, getTasksCount } from 'api/taskApi';

const QUERY_KEY = 'task';

export function useGetTasks() {
  return useQuery({
    queryKey: [QUERY_KEY],
    queryFn: () => getTasks(),
    refetchInterval: 1000 * 60,
    refetchIntervalInBackground: true,
  });
}

export function useGetTasksCount() {
  return useQuery({
    queryKey: ['task/count'],
    queryFn: () => getTasksCount(),
    refetchInterval: 1000 * 60,
    refetchIntervalInBackground: true,
  });
}
