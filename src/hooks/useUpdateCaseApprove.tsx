import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Approve, updateCaseApprove } from 'api/caseApi';
import { SAVED_PROPOSAL_URL } from './useGetSavedProposals';

export function useUpdateCaseApprove() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ approveData }: { approveData: Approve[] }) =>
      updateCaseApprove({ approveData }),
    onSuccess: async data => {
      let failedArr = data.filter(item => {
        return item.success === false;
      });
      console.log('mutate', data);
      if (failedArr.length <= data.length) {
        await queryClient.invalidateQueries({
          queryKey: ['/case', data[0].caseId],
          refetchType: 'active',
        });
        await queryClient.invalidateQueries({
          queryKey: [SAVED_PROPOSAL_URL],
        });
      }
    },
  });
}
