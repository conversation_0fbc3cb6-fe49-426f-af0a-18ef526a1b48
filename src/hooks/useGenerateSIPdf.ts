import { useMutation } from '@tanstack/react-query';
import { generateSIPdf, SIPdfResponse, SIPdfWarnings } from 'api/proposalApi';
import { AxiosError } from 'axios';
import useQuotationInsureds from 'features/proposal/hooks/useQuotationInsureds';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import {
  PayloadForAgentFromCase,
  PayloadForRegeneratePdf,
  QuotationRequest,
} from 'types/quotation';

export function useGenerateSIPdfWithCase() {
  const { data: agentProfile } = useGetAgentProfile();

  const { insureds, proposers } = useQuotationInsureds();
  const defaultAgent = {
    agentCode: agentProfile?.agentId || '',
    fullName: agentProfile?.person?.fullName || '',
    virtualBranch: agentProfile?.branch?.name,
    branchName: agentProfile?.branch?.name || '',
    licenses: agentProfile?.licenses || [],
    trainingCodes: agentProfile?.trainingCourses || [],
  };
  return useMutation<
    SIPdfResponse,
    SIPdfWarnings[] | AxiosError,
    QuotationRequest,
    unknown
  >({
    mutationFn: (
      body: QuotationRequest &
        PayloadForRegeneratePdf &
        PayloadForAgentFromCase,
    ) => {
      const activeAgent = body.targetAgent ? body.targetAgent : defaultAgent;
      return generateSIPdf({
        quotation: {
          ...body.quotation,
          proposers: body.proposerPayload ? body.proposerPayload : proposers,
          insureds: body.insuredPayload ? body.insuredPayload : insureds,
          agents: [{ licenseChecking: 'Y', ...activeAgent }],
        },
        appVersion: body.appVersion,
        pdfPassword: body.pdfPassword,
        party: body.party,
        application: body.application,
      });
    },
  });
}

export const useGenerateSIPdf = () => {
  return useMutation<
    SIPdfResponse,
    SIPdfWarnings[] | AxiosError,
    QuotationRequest,
    unknown
  >({
    mutationFn: (body: QuotationRequest) => generateSIPdf(body),
  });
};
