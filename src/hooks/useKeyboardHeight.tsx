import { useState, useEffect } from 'react';
import {
  KeyboardEventData,
  KeyboardEvents,
} from 'react-native-keyboard-controller';

const useKeyboardHeight = () => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const handleKeyboardDidShow = (event: KeyboardEventData) => {
      setKeyboardHeight(event.height);
    };

    const handleKeyboardDidHide = () => {
      setKeyboardHeight(0);
    };

    const kbdShowListener = KeyboardEvents.addListener(
      'keyboardDidShow',
      handleKeyboardDidShow,
    );

    const kbdHideListener = KeyboardEvents.addListener(
      'keyboardDidHide',
      handleKeyboardDidHide,
    );

    return () => {
      kbdShowListener.remove();
      kbdHideListener.remove();
    };
  }, []);

  return keyboardHeight;
};

export default useKeyboardHeight;
