import { useMutation, useQueryClient } from '@tanstack/react-query';
import { completeTaskById } from 'api/taskApi';

export function useCompleteTaskById() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (taskId: string) => {
      return completeTaskById(taskId);
    },
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: ['task'],
        });
        queryClient.invalidateQueries({
          queryKey: ['task/count'],
        });
      }, 1000);
    },
  });
}
