import { useMutation } from '@tanstack/react-query';
import { getPaymentUrl } from 'api/dokuApi';
import { useAlert } from './useAlert';

export const useDokuPaymentUrl = () => {
  const { alertError } = useAlert();
  return useMutation({
    mutationFn: (payload: Parameters<typeof getPaymentUrl>[0]) =>
      getPaymentUrl(payload),
    onError: (error, variables, context) => {
      alertError(
        `Doku payment error: ${error}, variables: ${variables}, context: ${context}`,
      );
    },
  });
};
