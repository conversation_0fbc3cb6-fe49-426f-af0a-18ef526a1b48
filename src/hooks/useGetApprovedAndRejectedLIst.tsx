import { useQuery } from '@tanstack/react-query';
import {
  getApplicationApprovedAndRejectedList,
  RECRUIT_LEADER_APPROVAL_ENDPOINT,
} from 'api/eRecruitApi';

import { ERecruitApplicationStatusQueryParams } from 'types/eRecruit';

export function useGetApprovedList(
  params: ERecruitApplicationStatusQueryParams,
) {
  return useQuery({
    queryKey: [
      RECRUIT_LEADER_APPROVAL_ENDPOINT,
      { ...params, status: 'APPROVED' },
    ],
    queryFn: () => getApplicationApprovedAndRejectedList(params, 'APPROVED'),
    enabled: true,
  });
}

export function useGetRejectedList(
  params: ERecruitApplicationStatusQueryParams,
) {
  return useQuery({
    queryKey: [
      RECRUIT_LEADER_APPROVAL_ENDPOINT,
      { ...params, status: 'REJECTED' },
    ],
    queryFn: () => getApplicationApprovedAndRejectedList(params, 'REJECTED'),
    enabled: true,
  });
}

export function useGetReviewApplicationList(
  params: ERecruitApplicationStatusQueryParams,
) {
  return useQuery({
    queryKey: [
      RECRUIT_LEADER_APPROVAL_ENDPOINT,
      { ...params, status: params.status },
    ],
    queryFn: () => getApplicationApprovedAndRejectedList(params, params.status),
    enabled: true,
  });
}
