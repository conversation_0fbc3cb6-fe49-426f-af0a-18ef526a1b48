// ------*****Jessica: Possible alter from this function for result = Documents lists ****----------

import { addErrorToast } from 'cube-ui-components';
import { fetchDocumentResult } from 'types/documents';
import Stack, { CONTENT_TYPES } from './useContentStack';
import { baseUrl } from 'utils/context';

export async function fetchDocument() {
  const Query = Stack.ContentType(CONTENT_TYPES.DOCUMENT).Query();
  const result = await Query.toJSON()
    .find()
    .then(
      function success(result) {
        return result;
      },
      function error(err) {
        console.error(
          '🚀 ~ file: useDocuments.tsx ~ fetchDocument ~ error:',
          err,
        );
        addErrorToast([{ message: `Error: ${err}` }]);
        return null;
      },
    );

  if (result && result[0] && result[0][0]) {
    const data: fetchDocumentResult = JSON.parse(JSON.stringify(result[0][0]));
    const documentList = data.modular_blocks;

    return documentList;
  }

  return null;
}

export const getAgreementDocumentUrl = (type?: AgreementDocumentType) => {
  return `${baseUrl}/api-gateway/proc/recruitment/document/signed-document/form?type=${type}`;
};

export enum AgreementDocumentType {
  AGENT_CONTRACT = 'AGENT_CONTRACT',
  LEADER_CONTRACT = 'LEADER_CONTRACT',
}
