import { countryModuleSellerConfig } from 'utils/config/module';
import useBoundStore from './useBoundStore';
import { useUnRegisterPushToken } from 'features/notification/hooks/useUnRegisterPushToken';
import { registerForPushNotificationsAsync } from 'utils';
import {
  useAppStore,
  useSessionsStore,
  useSuggestionsStore,
} from 'features/aiBot/store';
import { useAiBotTooltipStore } from 'features/aiBot/store/tooltipStore';
import { updateContext as updateAiBotContext, usePrompt } from 'agent-guru';
import { usePromptsStore } from 'features/aiBot/store/prompsStore';

export const useForceLogout = () => {
  const isLoggedInUntilAppKilled = useBoundStore(
    state => state.auth.isLoggedInUntilAppKilled,
  );
  const shouldTriggerLogout =
    countryModuleSellerConfig.isLoggedInUntilAppKilled &&
    isLoggedInUntilAppKilled;
  const logout = useBoundStore(store => store.authActions.logout);
  const pushNotiEnabled = useBoundStore(store => store.pushNotificationEnabled);
  const agentId = useBoundStore(store => store.auth.agentCode);
  const clearSessions = useSessionsStore(state => state.clearSessions);
  const updateHasFetchedSessions = useSessionsStore(
    state => state.updateHasFetchedSessions,
  );
  const clearProps = useAiBotTooltipStore(state => state.clearProps);
  const setCurrentSessionId = useAppStore(state => state.setCurrentSessionId);
  const clearSuggestions = useSuggestionsStore(state => state.clearSuggestions);
  const { resetAllPrompts, resetLandingPagePrompts } = usePrompt(usePromptsStore)

  const { mutateAsync: unregisterAsync } = useUnRegisterPushToken();

  const unregisterPushTokenAsync = async () => {
    const token = await registerForPushNotificationsAsync();
    if (!token || !agentId) return;
    pushNotiEnabled && (await unregisterAsync({ agentId, pushToken: token }));
  };
  const forceLogout = async () => {
    await unregisterPushTokenAsync()
      .catch(e => console.log('unregisterPushTokenAsync error', e))
      .finally(() => {
        clearSessions();
        updateHasFetchedSessions(false);
        setCurrentSessionId(null);
        clearSuggestions();
        resetAllPrompts();
        resetLandingPagePrompts();
        clearProps();
        updateAiBotContext({
          aiBotChatBotId: undefined
        })
        logout();
      });
  };

  return { forceLogout, shouldTriggerLogout };
};
