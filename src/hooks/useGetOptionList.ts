import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getOptionList } from 'api/optionListApi';
import { useMemo } from 'react';
import { BuildCountry } from 'types';
import { LocaleHeader } from 'types/localeHeader';
import { CubeTitle, OptionList } from 'types/optionList';
import { useGetCubeChannel } from './useGetCubeChannel';
import { useLocaleHeader } from './useLocaleHeader';

const getOptionListKey = ({
  channel,
  ...localeHeader
}: { channel: string } & LocaleHeader) => [
  'exp/api/query/optionList',
  channel,
  localeHeader,
];

export const useGetOptionList = <T extends BuildCountry>() => {
  const localeHeader = useLocaleHeader();
  const channel = useGetCubeChannel();

  return useQuery({
    queryKey: getOptionListKey({ channel, ...localeHeader }),
    queryFn: () =>
      getOptionList({ channel, ...localeHeader }) as Promise<
        OptionList<T, string>
      >,
    cacheTime: 60 * 60 * 1000, // 1 hour
    staleTime: Infinity,
  });
};

export const useGetOptionListManually = <T extends BuildCountry>() => {
  const localeHeader = useLocaleHeader();
  const channel = useGetCubeChannel();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () =>
      (queryClient.getQueryData(
        getOptionListKey({ channel, ...localeHeader }),
      ) as OptionList<T, string>) ||
      ((await getOptionList({ channel, ...localeHeader })) as OptionList<
        T,
        string
      >),
  });
};

export const getTitleList = (CUBE_TITLE: CubeTitle[]) => {
  const maleTitles: CubeTitle[] = [];
  const femaleTitles: CubeTitle[] = [];
  CUBE_TITLE.forEach(title => {
    if (title?.gender?.male === 'True') {
      maleTitles.push(title);
    } else {
      femaleTitles.push(title);
    }
  });

  return {
    maleTitles,
    femaleTitles,
  };
};

export const useGetTitleList = (CUBE_TITLE: CubeTitle[]) => {
  return useMemo(() => {
    return getTitleList(CUBE_TITLE);
  }, [CUBE_TITLE]);
};
