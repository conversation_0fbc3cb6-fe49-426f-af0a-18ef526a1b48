import { getProductName } from 'features/eApp/utils/eAppFormat';
import { useGetProductList } from 'features/productSelection/hooks/useProducts';
import { RelationshipValue } from 'features/proposal/types';
import { useCallback, useMemo } from 'react';
import { Case, CaseStatus, DependentRelationship } from 'types/case';
import { PartyRole } from 'types/party';
import { Gender } from 'types/person';
import { AgentPolicy, OwbPolicyListItem } from 'types/policy';
import { Product } from 'types/products';
import { i18n } from 'utils';
import { useGetCasesByIdentity } from './useGetCase';
import { useGetCubeChannel } from './useGetCubeChannel';
import { useQueryPolicyListByParty } from './useGetPolicyList';

type CustomerInfo = {
  firstName: string;
  lastName?: string;
  dob?: string;
  gender?: Gender;
  idNumber: string;
};
export type GetUnionPolicyListParams = { customerId?: string } | CustomerInfo;

export type UnionPolicy = AgentPolicy & {
  subPolicies: AgentPolicy[];
  quota: {
    spouse: number;
    spouseMax: number;
    child: number;
    childMax: number;
    total: number;
    totalMax: number;
  } | null;
};

export function useGetUnionPolicyList(params?: GetUnionPolicyListParams) {
  const channel = useGetCubeChannel();
  const { data: productList, isFetching: isFetchingProductList } =
    useGetProductList({ channel });
  let requestPayload = {};

  // query the policy list from OWB by customerId
  if (params && 'customerId' in params && params?.customerId) {
    requestPayload = {
      customerId: params?.customerId,
    };
  } else {
    const { firstName, lastName, dob, gender, idNumber } =
      (params as CustomerInfo) ?? {};

    requestPayload = {
      customerFirstName: firstName,
      customerSurname: lastName,
      dob,
      gender,
      customerIdNo: idNumber,
      customerIdType: 'ID',
      customerType: 'P',
    };
  }

  const {
    data: owbPolicyList,
    isFetching: isFetchingPolicyList,
    refetch: refetchPolicyList,
    error,
    isError,
  } = useQueryPolicyListByParty({
    customerInfo: requestPayload,
  });

  const { firstName, lastName, dob, gender, idNumber } =
    (params as CustomerInfo) ?? {};
  const {
    data: cubePolicyList,
    isFetching: isFetchingCubePolicyList,
    refetch: refreshCubePolicyList,
  } = useGetCasesByIdentity({
    firstName: firstName,
    lastName: lastName,
    dob: dob,
    gender: gender,
    id: idNumber,
    status: CaseStatus.APP_SUBMITTED,
  });

  const ret = {
    data: useMemo(
      () => toUnionPolicyList(owbPolicyList, cubePolicyList, productList),
      [owbPolicyList, cubePolicyList, productList],
    ),
    isFetching:
      isFetchingProductList || isFetchingPolicyList || isFetchingCubePolicyList,
    refetch: useCallback(
      () =>
        Promise.all([refetchPolicyList(), refreshCubePolicyList()]).then(
          ([{ data: owbPolicyList }, { data: cubePolicyList }]) =>
            toUnionPolicyList(owbPolicyList, cubePolicyList, productList),
        ),
      [productList, refetchPolicyList, refreshCubePolicyList],
    ),
    error,
    isError,
  };

  return ret;
}

function toUnionPolicyList(
  owbPolicyList?: OwbPolicyListItem[],
  cubePolicyList?: Case[],
  productList?: Product[],
): UnionPolicy[] | undefined {
  const owbPrimaryPolicies = owbPolicyList
    ?.filter(d => !d.crossReferenceNumber)
    .map(d => toAgentPolicy(d, productList));

  const cubePrimaryPolicies = (cubePolicyList ?? [])
    .filter(c => c.application?.policyNum)
    .filter(c => {
      // filter out policies that exist in owb
      const existInOwb = owbPrimaryPolicies?.find(
        o => o.policyNumber === c.application?.policyNum,
      );
      return !existInOwb;
    })
    .filter(c => {
      // filter out policies in which selected quotation has family plan info
      if (!Array.isArray(c.quotations)) return false;
      const selectedQuotation = c.quotations.find(
        q => q.id === c.selectedQuotationId,
      );
      const selectedQuotationHasFamilyPlanInfo =
        selectedQuotation?.familyPlanInfo?.primaryPolicyNum;
      if (selectedQuotationHasFamilyPlanInfo) return false;
      return true;
    })
    .map(caseToAgentPolicy);

  const primaryPolicies = owbPrimaryPolicies?.concat(cubePrimaryPolicies);

  return primaryPolicies?.map(d => {
    const owbSubPolicies = (owbPolicyList ?? [])
      .filter(sp => sp.crossReferenceNumber === d.policyNumber)
      .map(sp => toAgentPolicy(sp, productList));

    const cubeSubPolicies = (cubePolicyList ?? [])
      .filter(c => {
        // filter out policies that exist in owb
        const existInOwb = owbSubPolicies?.find(
          o => o.policyNumber === c.application?.policyNum,
        );
        return !existInOwb;
      })
      .filter(c => {
        // only pick policy that has family plan's policy num == primary policy num
        const selectedQuotation = c.quotations?.find(
          q => q.id === c.selectedQuotationId,
        );
        return (
          selectedQuotation?.familyPlanInfo?.primaryPolicyNum === d.policyNumber
        );
      })
      .map(caseToAgentPolicy);

    const subPolicies = owbSubPolicies.concat(cubeSubPolicies);

    const maxQuota =
      productList?.find(p => p.pid === d.planCode)?.quota?.max ?? null;

    const is3rdPartyPO = d.relationOfProposer !== RelationshipValue.OWNER;

    return {
      ...d,
      subPolicies,
      quota: !maxQuota
        ? null
        : {
            spouse:
              (d.relationOfProposer &&
              [RelationshipValue.SPOUSE, 'SPOU'].includes(d.relationOfProposer)
                ? 1
                : 0) +
              subPolicies.reduce(
                (ret, sp) =>
                  sp.relationOfProposer &&
                  [RelationshipValue.SPOUSE, 'SPOU'].includes(
                    sp.relationOfProposer,
                  )
                    ? ret + 1
                    : ret,
                0,
              ),
            spouseMax: maxQuota?.spouse ?? 0,
            child:
              (d.relationOfProposer &&
              [RelationshipValue.CHILD, 'CHLD'].includes(d.relationOfProposer)
                ? 1
                : 0) +
              subPolicies.reduce(
                (ret, sp) =>
                  // todo: remove 'CHLD'
                  sp.relationOfProposer &&
                  [RelationshipValue.CHILD, 'CHLD'].includes(
                    sp.relationOfProposer,
                  )
                    ? ret + 1
                    : ret,
                0,
              ),
            childMax: maxQuota?.child ?? 0,
            total: subPolicies.length + 1,
            totalMax:
              (maxQuota?.self ?? 0) +
              (maxQuota?.spouse ?? 0) +
              (maxQuota?.child ?? 0) -
              (is3rdPartyPO ? 1 : 0),
          },
    };
  });
}

function toAgentPolicy(
  data: OwbPolicyListItem,
  productList?: Product[],
): AgentPolicy {
  return {
    status: data.riskStatus ?? '',
    issueDate: data.policyIssueDate?.toString(),
    statusCode: data.riskStatus,
    policyNumber: data.policyNo,
    planCode: data.contractType ?? '',
    firstIssueDate: data.policyIssueDate?.toString(),
    clientCode: data.clientId,
    policyOwner: data.fullName || '',
    insuredName: data.insuredName || '',
    paidToDate: data.nextPremDueDate,
    proposalDate: data.proposalDate,
    relationOfProposer: data.relationOfProposer,
    submitDate: data.submissionDate || '',
    planName: getProductName(
      productList?.find(p => p.pid === data.contractType)?.productName,
      i18n.language,
    ),
    // unused fields, default as empty string
    agentId: '',
    planCategory: '',
    premiumStatusCode: '',
  };
}

function caseToAgentPolicy(data: Case): AgentPolicy {
  const proposer = data.parties?.find(p =>
    p.roles.includes(PartyRole.PROPOSER),
  );
  const insured = data.parties?.find(p => p.roles.includes(PartyRole.INSURED));

  const selectedQutation = (data.quotations ?? [])?.find(
    q => q.id === data.selectedQuotationId,
  );

  let relationship = 'SELF';
  switch (insured?.relationship) {
    case RelationshipValue.CHILD:
    case DependentRelationship.kid:
      relationship = 'CHLD';
      break;
    case DependentRelationship.spouse:
      relationship = 'SPOUSE';
      break;
  }

  return {
    status: 'PS',
    issueDate: null,
    statusCode: 'PS',
    policyNumber: data.application?.policyNum ?? '',
    planCode: selectedQutation?.pid ?? '',
    firstIssueDate: null,
    clientCode: undefined,
    policyOwner: proposer?.person?.name?.fullName ?? '',
    insuredName: insured?.person?.name.fullName ?? '',
    paidToDate: null,
    proposalDate: data.updatedAt?.toString() || '',
    relationOfProposer: relationship,
    // unused fields, default as empty string
    submitDate: '',
    planName: '',
    agentId: '',
    planCategory: '',
    premiumStatusCode: '',
  };
}
