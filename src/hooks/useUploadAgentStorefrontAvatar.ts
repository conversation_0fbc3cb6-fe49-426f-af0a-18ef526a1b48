import { useMutation } from '@tanstack/react-query';
import { queryClient } from 'api/RootQueryClient';
import { getAgentStorefrontProfileKey } from './useGetAgentStorefrontProfile';
import useBoundStore from './useBoundStore';
import { uploadAgentAvatar } from 'api/storefrontApi';

export const useUploadAgentStorefrontAvatar = () => {
  const accessToken = useBoundStore(state => state.auth.authInfo?.accessToken);
  return useMutation({
    mutationFn: (path: string) => uploadAgentAvatar(path, accessToken),
    onSuccess: async () => {
      await queryClient.invalidateQueries(getAgentStorefrontProfileKey());
    },
  });
};
