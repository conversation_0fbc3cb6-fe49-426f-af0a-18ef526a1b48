import { useQuery } from '@tanstack/react-query';
import { getProductExtraConfig } from 'api/proposalApi';
import { CHANNELS } from 'types/channel';
import { ProductExtraConfig } from 'types/products';

// this hook is for checking if product is allowed to
// proceed to IN_APP stage of the sales flow
export const useGetProductExtraConfig = () => {
  return useQuery<ProductExtraConfig>({
    queryKey: ['productExtraConfig'],
    queryFn: getProductExtraConfig,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
};

/**
 * Checks if allowApplication is true for a given product code and channel.
 * Returns true if product code is not found.
 */
export const useCheckProductApplicationAllowed = () => {
  const { data: productExtraConfig } = useGetProductExtraConfig();

  return (
    isQuickSi: boolean | undefined,
    productCode: string,
    channel: string = CHANNELS.AGENCY,
  ) => {
    if (isQuickSi) return true;
    if (!productExtraConfig || !productExtraConfig[productCode]) return true;

    const config = productExtraConfig[productCode];
    if (!config || !config.allowApplication) return true;
    if (!(channel in config.allowApplication)) return true;
    return !!config.allowApplication[channel];
  };
};

export default useGetProductExtraConfig;
