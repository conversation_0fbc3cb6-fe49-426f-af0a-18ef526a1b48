import useBoundStore from 'hooks/useBoundStore';
import { useForceLogout } from 'hooks/useForceLogout';

const useCheckIsLoggedIn = () => {
  const accessToken = useBoundStore(state => state.auth.authInfo.accessToken);

  const { shouldTriggerLogout } = useForceLogout();

  const isLoggedIn = (Boolean(accessToken) && shouldTriggerLogout) ?? false;

  return { isLoggedIn };
};

export default useCheckIsLoggedIn;
