import { useQuery } from '@tanstack/react-query';
import { getPolicyNewBusinessByAgentIdPH } from 'api/policyListApi';
import {
  QUERY_KEY_TEAM,
  getPeristencyByAgentId,
  getPOSPolicyByAgentId,
  getPolicyNewBusinessByAgentId,
  getTeamAchievementByAgentId,
  getTeamBranchDownlineAgent,
  getTeamContributionByAgentId,
  getTeamHierarchy,
  getTeamIndividualPerformanceByAgentId,
  getTeamIndividualTargetByAgentId,
  getTeamLeadTrackingByAgentId,
  getTeamMainByIndividual,
  getTeamMainByTeam,
  getTeamMapa,
  getTeamOverviewByAgentId,
  getTeamPerformanceByAgentId,
  getTeamRankingByAgentId,
  getTeamSalesBalanacePerformance,
  getTeamTargetByAgentId,
  getTeamPolicyListByAgentId,
  getTeamLeadConversionByAgentId,
} from 'api/teamApi';
import {
  AgentNewBusinessPolicy,
  AgentNewBusinessPolicyPH,
  AgentPosPolicy,
  AgentPosPolicyPH,
} from 'types/policy';
import {
  TeamAchievement,
  TeamBranch,
  TeamContribution,
  TeamLeadTracking,
  TeamMainPage,
  TeamRanking,
  TeamTarget,
  TeamOverview,
  TeamPerformance,
  SalesBalancePerformance,
  BlockType,
  TeamLeadConversion,
} from 'types/team';

export const useGetTeamBranchByAgentId = (id: string) => {
  return useQuery<TeamBranch>([QUERY_KEY_TEAM.BRANCH, id], () =>
    getTeamBranchDownlineAgent(id),
  );
};

export const useGetTeamMainByTeam = (
  enabled: boolean,
  id: string,
  designation?: string,
) => {
  return useQuery<TeamMainPage>(
    [`${QUERY_KEY_TEAM.HOME}?category=TEAM`, id, designation],
    () => getTeamMainByTeam(id, designation),
    { enabled: enabled && id?.length > 0 },
  );
};

export const useGetTeamMainByIndividual = (
  enabled: boolean,
  id: string,
  designation?: string,
) => {
  return useQuery<TeamMainPage>(
    [`${QUERY_KEY_TEAM.HOME}?category=INDIVIDUAL`, id, designation],
    () => getTeamMainByIndividual(id, designation),
    { enabled: enabled && id?.length > 0 },
  );
};

export const useGetTeamTargetByAgentId = (id: string) => {
  return useQuery<TeamTarget>(
    [`${QUERY_KEY_TEAM.TARGET}`, id],
    () => getTeamTargetByAgentId(id),
    {
      enabled: id?.length > 0,
    },
  );
};

export const useGetTeamContributionByAgentId = (id: string) => {
  return useQuery<TeamContribution>(
    [`${QUERY_KEY_TEAM.CONTRIBUTION}/${id}`, id],
    () => getTeamContributionByAgentId(id),
    {
      enabled: id?.length > 0,
    },
  );
};

export const useGetTeamAchievementByAgentId = (id: string) => {
  return useQuery<TeamAchievement>(
    [`${QUERY_KEY_TEAM.ACHIEVEMENT}/${id}`, id],
    () => getTeamAchievementByAgentId(id),
    {
      enabled: id?.length > 0,
    },
  );
};

export const useGetTeamRankingByAgentId = (id: string) => {
  return useQuery<TeamRanking>(
    [`${QUERY_KEY_TEAM.RANKING}/${id}`, id],
    () => getTeamRankingByAgentId(id),
    {
      enabled: id?.length > 0,
    },
  );
};

export const useGetTeamLeadTrackingByAgentId = (id: string) => {
  return useQuery<TeamLeadTracking>(
    [`${QUERY_KEY_TEAM.TRACKING}/${id}`, id],
    () => getTeamLeadTrackingByAgentId(id),
    {
      enabled: id?.length > 0,
    },
  );
};

export const useGetTeamLeadConversionByAgentId = (id: string, isTeam = true) => {
  return useQuery<TeamLeadConversion>(
    [`${QUERY_KEY_TEAM.LEADS}/${id}`, id],
    () => getTeamLeadConversionByAgentId(id, isTeam),
    {
      enabled: id?.length > 0,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  );
};

export const useGetTeamOverviewByAgentId = (id: string) => {
  return useQuery<TeamOverview>(
    [`${QUERY_KEY_TEAM.OVERVIEW}/${id}`, id],
    () => getTeamOverviewByAgentId(id),
    {
      enabled: id?.length > 0,
    },
  );
};

export const useGetTeamPerformanceByAgentId = (id: string) => {
  return useQuery<TeamPerformance>(
    [`${QUERY_KEY_TEAM.PERFORMANCE}/${id}`, id],
    () => getTeamPerformanceByAgentId(id),
    {
      enabled: id?.length > 0,
    },
  );
};

export const useGetTeamNewBusinessPolicyByAgentId = (
  id: string | undefined,
) => {
  return useQuery<AgentNewBusinessPolicyPH[]>({
    queryFn: () => getPolicyNewBusinessByAgentId(id),
    queryKey: [QUERY_KEY_TEAM.AGENT_POS_NEW_BUSINESS, id],
    enabled: Boolean(id && id?.length > 0),
  });
};

export const useGetTeamPosPolicyByAgentId = (id: string) => {
  return useQuery<AgentPosPolicyPH[]>({
    queryKey: [QUERY_KEY_TEAM.AGENT_POS_POLICY, id],
    queryFn: () => getPOSPolicyByAgentId(id),
    enabled: Boolean(id?.length > 0),
  });
};

export const useGetTeamSalesBalancePerformance = (
  key?: string,
  params?: {
    month?: string;
    year?: string;
  },
) => {
  const queryKey = [QUERY_KEY_TEAM.PERFORMANCE];

  if (key) {
    queryKey.push(key);
  }
  return useQuery({
    queryKey,
    queryFn: () => getTeamSalesBalanacePerformance(params),
  });
};

export const useGetIndividualPersistency = (
  agentId: string | null,
  params?: {
    month?: string;
    year?: string;
  },
  isEnabled: boolean = true,
) => {
  const queryKey = [QUERY_KEY_TEAM.INDIVIDUAL_PERSISTENCY, agentId];
  if (params) {
    queryKey.push(`${params?.month}-${params.year}`);
  }

  return useQuery({
    queryKey,
    queryFn: () => getPeristencyByAgentId(agentId, params),
    enabled: isEnabled,
  });
};

export const useGetTeamPolicyList = (
  agentId: string,
  params?: {
    period?: string;
    type?: BlockType;
    year?: string;
    month?: string;
  },
) => {
  const queryKey = [QUERY_KEY_TEAM.POLICY_LIST, agentId, params];
  if (params) {
    queryKey.push(params);
  }

  /* 
    CUBEFIB-6428: Replace "Contribution" with "Premium" 
      and "Cert" with "Policy" in Takaful premium status by FE 
    */
  return useQuery({
    queryKey,
    queryFn: () => getTeamPolicyListByAgentId(agentId, params),
    select: data => ({
      ...data,
      policies: data.policies.map(policy => ({
        ...policy,
        premiumStatus: policy.premiumStatus
          .replace(/Contribution/g, 'Premium')
          .replace(/Cert/g, 'Policy'),
      })),
    }),
  });
};

export const useGetTeamMapa = () => {
  return useQuery({
    queryKey: [QUERY_KEY_TEAM.MAPA],
    queryFn: () => getTeamMapa(),
  });
};

export const useGetTeamHierarchy = () => {
  return useQuery({
    queryKey: [QUERY_KEY_TEAM.HIERARCHY],
    queryFn: () => getTeamHierarchy(),
  });
};

export const useGetTeamIndividualPerformanceByAgentId = (agentId: string) =>
  useQuery({
    queryKey: ['teamPerformanceIndividual', agentId],
    queryFn: () => getTeamIndividualPerformanceByAgentId(agentId),
  });

export const useGetTeamIndividualTargetByAgentId = (agentId: string) =>
  useQuery({
    queryKey: ['teamPerformanceIndividualTarget', agentId],
    queryFn: () => getTeamIndividualTargetByAgentId(agentId),
  });
