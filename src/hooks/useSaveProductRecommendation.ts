import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FnaProductSelection } from 'types/case';
import { getCaseKey } from './useGetCase';
import { saveProductRecommendation } from 'api/caseApi';

export function useSaveProductRecommendation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      caseId,
      data,
    }: {
      caseId: string;
      data: FnaProductSelection;
    }) => saveProductRecommendation({ caseId, data }),
    onSuccess: async () => {
      await queryClient.invalidateQueries(getCaseKey());
    },
  });
}
