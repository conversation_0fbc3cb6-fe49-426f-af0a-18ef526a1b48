import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { removeCandidate, RECRUIT_ENDPOINT } from 'api/eRecruitApi';
import { RemoveCandidateParams } from 'types/eRecruit';

const getRemoveERCandidateKey = (params: RemoveCandidateParams) => {
  return [RECRUIT_ENDPOINT, 'application', 'candidate', 'remove', params];
};

export function useRemoveERCandidate(params: RemoveCandidateParams) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: getRemoveERCandidateKey(params),
    mutationFn: () => removeCandidate(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [RECRUIT_ENDPOINT],
      });
    },
    onError: error => {
      console.log('error!!!', error);
    },
  });
}
