import { useEffect, useState } from 'react';
import useLayoutAdoptionCheck from './useDeviceCheck';
import { DeviceMotion, DeviceMotionOrientation } from 'expo-sensors';

const orientationCalculation = (
  gamma: number,
  beta: number,
  deviceOrientation: DeviceMotionOrientation,
): DeviceMotionOrientation => {
  if (beta > 0.45) {
    return DeviceMotionOrientation.Portrait;
  } else if (beta < -0.45) {
    return DeviceMotionOrientation.UpsideDown;
  } else if (gamma > 0.45) {
    return DeviceMotionOrientation.LeftLandscape;
  } else if (gamma < -0.45) {
    return DeviceMotionOrientation.RightLandscape;
  } else {
    // Fallback to base orientation
    return deviceOrientation;
  }
};

export function useRotateByMotion() {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const [orientation, setOrientation] = useState<DeviceMotionOrientation>(
    isTabletMode
      ? DeviceMotionOrientation.RightLandscape
      : DeviceMotionOrientation.Portrait,
  );
  useEffect(() => {
    const subscription = DeviceMotion.addListener(
      ({ rotation: rotationMetrics, orientation: deviceOrientation }) => {
        if (rotationMetrics) {
          const newOrientation = orientationCalculation(
            Math.round(rotationMetrics.gamma * 1e2) / 1e2,
            Math.round(rotationMetrics.beta * 1e2) / 1e2,
            deviceOrientation,
          );
          setOrientation(newOrientation);
        }
      },
    );
    return subscription.remove;
  }, []);

  return { orientation };
}
