import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { ModulePermissionKeys } from 'types';
import { useCallback, useMemo } from 'react';

export default function useCheckClientScope(permissions: ModulePermissionKeys) {
  const { data: agentProfile } = useGetAgentProfile();
  const isCheckedEnabled = agentProfile?.clientScope?.enabled;
  const permissionsWhiteList = agentProfile?.clientScope?.permissions;
  const isWhiteListed = useMemo(
    () => permissionsWhiteList?.includes(permissions),
    [permissionsWhiteList, permissions],
  );

  return {
    isCheckedEnabled,
    isWhiteListed,
    permissionsWhiteList,
    isBothEnabledAndWhiteListed: isCheckedEnabled && isWhiteListed,
  };
}

export function useHasPermission() {
  const { data: agentProfile } = useGetAgentProfile();
  const isCheckedEnabled = agentProfile?.clientScope?.enabled;
  const permissionsWhiteList = agentProfile?.clientScope?.permissions;

  const hasPermission = useCallback(
    (feature: ModulePermissionKeys) => {
      if (!isCheckedEnabled) {
        return true;
      }
      return !!permissionsWhiteList?.includes(feature);
    },
    [isCheckedEnabled, permissionsWhiteList],
  );

  return hasPermission;
}
