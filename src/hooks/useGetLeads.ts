import {
  useInfiniteQuery,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import {
  getLeadTabFilters,
  getLeadByCustomerId,
  getLeadByLeadId,
  getLeads,
  LeadApiFilter,
  queryLead,
} from 'api/leadApi';
import useBoundStore from './useBoundStore';
import { useMutation } from '@tanstack/react-query';
import { roundToNearestMinutes, sub } from 'date-fns';
import {
  GetLeadsResponse,
  Lead,
  LeadsFilters,
  MARKETING_LEAD_SOURCES,
  NOT_CONTACTED_LEAD_STATUS,
  SELF_GEN_LEAD_SOURCES,
} from 'types';
import { country } from 'utils/context';
import { useGetAgentProfile } from './useGetAgentProfile';
import { CHANNELS } from 'types/channel';

export const QUERY_KEY = 'lead';

const getSub24Time = () => {
  return roundToNearestMinutes(sub(new Date(), { days: 1 }), {
    roundingMethod: 'floor',
  });
};

export function getQueryKey(filters?: LeadApiFilter, listType?: ListType) {
  const queryKeyOptions: LeadApiFilter & {
    listType?: ListType;
  } = {
    ...filters,
  };

  if (listType) {
    queryKeyOptions.listType = listType;
  }

  if (listType && queryKeyOptions.from) {
    delete queryKeyOptions['from'];
  }

  if (listType && queryKeyOptions.to) {
    delete queryKeyOptions['to'];
  }

  return [QUERY_KEY, queryKeyOptions];
}

export function useSearchLead({
  queryText,
  enabled,
  isToday,
  isOther,
  params,
}: {
  queryText: string;
  enabled: boolean;
  isToday?: boolean;
  isOther?: boolean;
  params?: LeadApiFilter;
}) {
  const filters: {
    q: string;
    opportunityOnly?: boolean;
    from?: Date;
    to?: Date;
  } = {
    q: queryText,
    opportunityOnly: true,
    ...params,
  };

  if (isToday && !isOther) {
    filters.from = getSub24Time();
  }
  if (isOther && !isToday) {
    filters.to = getSub24Time();
  }

  return useQuery({
    queryKey: getQueryKey(
      filters,
      isToday && !isOther ? 'today' : !isToday && isOther ? 'other' : undefined,
    ),
    queryFn: () => getLeads(filters),
    enabled,
  });
}

export function useGetLeadsToday() {
  const { data: agentProfile } = useGetAgentProfile();
  const { sortByNewest, filters: todayFilters } = useBoundStore(
    store => store.lead.today,
  );
  const sortByOppUpdatedAtOperator = sortByNewest ? '-' : '';

  let filters = {
    sortBy: sortByOppUpdatedAtOperator + 'opportunityUpdatedAt',
    from: getSub24Time(),
    ...getLeadFilters(todayFilters),
    opportunityOnly: true,
  };

  if (country === 'my' && agentProfile?.channel === CHANNELS.BANCA) {
    filters = { ...filters, ...{ isIndividual: true } };
  }

  return useInfiniteQuery({
    queryKey: getQueryKey(filters, 'today'),
    queryFn: ({ pageParam = 1 }) => getLeads(filters, { page: pageParam }),
    getNextPageParam: getNexPageLeads,
    staleTime: 60 * 1000,
    keepPreviousData: true,
  });
}

export function useGetLeadsAllFilters(enabled: boolean) {
  const filters = {
    sortBy: 'opportunityUpdatedAt',
    opportunityOnly: true,
  };

  return useQuery({
    queryKey: [QUERY_KEY, 'allFilters'],
    queryFn: () => getLeadTabFilters(filters),
    enabled,
  });
}

export function useGetLeadsAllSearchFilters(q: string, enabled: boolean) {
  const searchfilters = {
    sortBy: 'opportunityUpdatedAt',
    opportunityOnly: true,
    q,
  };

  return useQuery({
    queryKey: [QUERY_KEY, 'allSearchFilters'],
    queryFn: () => getLeadTabFilters(searchfilters),
    enabled,
  });
}

export function useGetLeadsTodayFilters(enabled: boolean) {
  const filters = {
    sortBy: 'opportunityUpdatedAt',
    from: getSub24Time(),
    opportunityOnly: true,
  };

  return useQuery({
    queryKey: [QUERY_KEY, 'todayFilters'],
    queryFn: () => getLeadTabFilters(filters),
    enabled,
  });
}

export function useGetLeadsOtherFilters(enabled: boolean) {
  const filters = {
    to: getSub24Time(),
    opportunityOnly: true,
  };

  return useQuery({
    queryKey: [QUERY_KEY, 'otherFilters'],
    queryFn: () => getLeadTabFilters(filters),
    enabled,
  });
}

export function useGetExpiredLeads() {
  const status = NOT_CONTACTED_LEAD_STATUS.reduce((acc, cur) => {
    return { ...acc, [cur]: true };
  }, {});

  const filters = {
    sortBy: '-createdAt',
    to: getSub24Time(),
    ...getLeadFilters({ type: {}, status, campaignCode: {}, source: {} }),
    opportunityOnly: true,
  };

  const query = useQuery({
    queryKey: getQueryKey(filters, 'expiredLead'),
    queryFn: () => getLeads(filters),
    staleTime: 60 * 1000,
  });
  return query;
}

export function useGetLeadsOther() {
  const { data: agentProfile } = useGetAgentProfile();
  const { sortByNewest, filters: othersFilters } = useBoundStore(
    store => store.lead.others,
  );

  const sortByUpdatedAtOperator = sortByNewest ? '-' : '';

  let filters = {
    // sortBy: sortByUpdatedAtOperator + 'updatedAt',
    sortBy:
      country !== 'ph'
        ? sortByUpdatedAtOperator + 'updatedAt'
        : sortByUpdatedAtOperator + 'createdAt',
    to: getSub24Time(),
    ...getLeadFilters(othersFilters),
    opportunityOnly: true,
  };

  if (country === 'my' && agentProfile?.channel === CHANNELS.BANCA) {
    filters = { ...filters, ...{ isIndividual: true } };
  }

  return useInfiniteQuery({
    queryKey: getQueryKey(filters, 'other'),
    queryFn: ({ pageParam = 1 }) => {
      return getLeads(filters, { page: pageParam });
    },
    getNextPageParam: getNexPageLeads,
    staleTime: 60 * 1000,
    keepPreviousData: true,
  });
}

const getNexPageLeads = (lastPage: GetLeadsResponse) => {
  const { offset, limit, totalCount } = lastPage;

  const lastPageNum = (offset + limit) / limit;

  const nextPageNum = lastPageNum + 1;

  if (offset + limit > totalCount || !limit) {
    return undefined;
  }

  return nextPageNum;
};

export function getLeadFilters(leadApiFilters: LeadsFilters) {
  let leadFilters: Record<string, string | boolean> = {};

  for (const filterCat in leadApiFilters) {
    if (filterCat === 'type') {
      const leadCatFilter = leadApiFilters['type'];
      leadFilters = parseLeadCatFilter(leadCatFilter) as never;
    } else {
      const filterArr = [];

      const leadCatFilters =
        leadApiFilters[filterCat as keyof typeof leadApiFilters];

      for (const [key, value] of Object.entries(leadCatFilters)) {
        if (value) {
          if (key === 'SELF' && filterCat === 'source') {
            SELF_GEN_LEAD_SOURCES.forEach(sourceId => {
              filterArr.push(sourceId);
            });
          } else if (key === 'Marketing' && filterCat === 'source') {
            MARKETING_LEAD_SOURCES.forEach(sourceId => {
              filterArr.push(sourceId);
            });
          } else if (key === 'not_contacted' && filterCat === 'status') {
            NOT_CONTACTED_LEAD_STATUS.forEach(status => {
              filterArr.push(status);
            });
          } else {
            filterArr.push(key);
          }
        }
      }

      /**
       *  If the filter category is 'campaignCode', we use ';' to join the values,
       *  otherwise we use ',' for other categories.
       */
      const joinSymbol = filterCat === 'campaignCode' ? ';' : ',';

      leadFilters[filterCat] = filterArr.join(joinSymbol);
    }
    if (!leadFilters[filterCat]) {
      delete leadFilters[filterCat];
    }
  }
  return leadFilters;
}

const parseLeadCatFilter = (filter: Record<string, boolean>) => {
  // Only for lead type, "entity" | "individual"
  const { entity, individual } = filter;
  if (!entity && individual) return { isIndividual: true };
  if (entity && !individual) return { isIndividual: false };
  return {};
};

type ListType = 'today' | 'other' | 'expiredLead';

export function useGetLeadByLeadId(leadId: string | undefined) {
  return useQuery<Lead>({
    queryKey: [QUERY_KEY, leadId],
    queryFn: () => getLeadByLeadId(String(leadId)),
    keepPreviousData: true,
    enabled: Boolean(leadId),
  });
}

export function useGetLeadByLeadIdManually() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (leadId: string) => getLeadByLeadId(leadId),
    onSuccess: (lead: Lead) => {
      queryClient.setQueryData([QUERY_KEY, lead.id], lead);
      queryClient.invalidateQueries({ queryKey: [QUERY_KEY, lead.id] });
    },
  });
}

export function useGetLeadByCustomerId(customerId: string | undefined) {
  return useQuery({
    queryKey: [QUERY_KEY, 'customerId', customerId],
    queryFn: () => getLeadByCustomerId(String(customerId)),
    keepPreviousData: true,
    enabled: Boolean(customerId),
  });
}

export const useQueryLead = () => {
  return useMutation({
    mutationFn: (query: string) => queryLead(query),
  });
};
