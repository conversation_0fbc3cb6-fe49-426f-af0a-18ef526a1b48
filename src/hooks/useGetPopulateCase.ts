import useBoundStore from 'hooks/useBoundStore';
import {
  useGetApplicationsByIdentity,
  usePopulateCase,
} from 'hooks/useGetCase';
import { useCallback, useEffect, useState } from 'react';
import { Party } from 'types/party';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';

export const useGetPopulateCase = ({
  ownerParty,
  formTrigger,
}: {
  ownerParty?: Party;
  formTrigger?: () => void;
}) => {
  const { caseId } = useBoundStore(state => state.case);

  const ownerMobile = ownerParty?.contacts?.phones?.find(
    ({ type }) => 'MOBILE' === type,
  );
  const {
    mutateAsync: getApplications,
    isLoading: isGettingPopulatableApplications,
  } = useGetApplicationsByIdentity();

  const { mutateAsync: populateApplication, isLoading: isPopulatingCase } =
    usePopulateCase();

  const refreshEApp = useEAppStore(state => state.refreshEApp);
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  useEffect(() => {
    if (isPopulatingCase) {
      setAppLoading();
    } else {
      setAppIdle();
    }
  }, [isPopulatingCase, setAppIdle, setAppLoading]);
  const [populated, setPopulated] = useState(false);

  useEffect(() => {
    if (populated) formTrigger?.();
  }, [populated]);

  return {
    isGettingPopulatableApplications,
    getPopulatableApplications: useCallback(async () => {
      const allApplicationsOfLead = await getApplications({
        firstName: ownerParty?.person?.name?.firstName ?? '',
        code: ownerMobile?.countryCode ?? '',
        phone: ownerMobile?.number ?? '',
      });
      return allApplicationsOfLead?.filter(({ id }) => id !== caseId);
    }, [
      caseId,
      getApplications,
      ownerMobile?.countryCode,
      ownerMobile?.number,
      ownerParty?.person?.name?.firstName,
    ]),
    setPopulateCaseId: useCallback(
      async (populateCaseId: string) => {
        if (caseId) {
          await populateApplication({
            target: caseId,
            source: populateCaseId,
          });
          refreshEApp();
          setPopulated(true);
        }
      },
      [refreshEApp, caseId, populateApplication],
    ),
  };
};
