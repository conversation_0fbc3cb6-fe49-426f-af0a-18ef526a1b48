import { useMemo } from 'react';
import { isTokenValid } from 'features/aiBot/utils/misc/tokenUtils';
import useBoundStore from './useBoundStore';
import { useForceLogout } from './useForceLogout';

/**
 * Check if the token is still valid, expired or not
 * @returns
 */
export default function useCheckLogin() {
  const { shouldTriggerLogout } = useForceLogout();

  const accessToken = useBoundStore(state => state.auth.authInfo.accessToken);

  const isLoggedIn = useMemo(
    () => accessToken && isTokenValid(accessToken) && shouldTriggerLogout,
    [accessToken, shouldTriggerLogout],
  );

  return isLoggedIn;
}
