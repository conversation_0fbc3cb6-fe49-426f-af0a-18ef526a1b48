import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createCFF } from 'api/caseApi';
import { CFF } from 'types/case';
import { getCaseKey } from './useGetCase';

export function useCreateCFF() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: { caseId: string; cff: Partial<CFF> }) =>
      createCFF(data.caseId, data.cff),
    onSuccess: async () => {
      await queryClient.invalidateQueries(getCaseKey());
    },
  });
}
