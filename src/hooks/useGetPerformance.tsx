import { useQuery } from '@tanstack/react-query';
import {
  HOME_PERFORMANCE_ENDPOINT,
  ALL_PERFORMANCE_ENDPOINT,
  getHomePerformance,
  // getPerformance,
  getAllPerformance,
  TEAM_PERFORMANCE_INDIVIDUAL_ENDPOINT,
  getTeamIndividualPerformance,
  getTeamIndividualMDRT,
  TEAM_PERFORMANCE_INDIVIDUAL_MDRT_ENDPOINT,
  KPI_ENDPOINT,
  getPerformanceKpi,
} from 'api/performanceApi';
import { FEMappedIDNHomePerfFields, HomePerformance } from 'types/performance';
import { useGetPerformanceTarget } from './usePerformanceTarget';
import { parsingHomePerformanceData } from 'features/performance/utlils/parsedPerformanceFunction';
import { country } from 'utils/context';

// const QUERY_KEY_PERFORMANCE = ['/exp/perf'];
export const QUERY_KEY_HOME_PERFORMANCE = [
  ALL_PERFORMANCE_ENDPOINT,
  HOME_PERFORMANCE_ENDPOINT,
];

//TODO: felix: get the performance after Quil has merged the two exp backend endpoints into 1
// export const useGetFullPerformance = () => {
//   return useQuery<FullPerformance>(QUERY_KEY_PERFORMANCE, getPerformance);
// };

export const useGetHomePerformance = () =>
  useQuery<HomePerformance>({
    queryKey: QUERY_KEY_HOME_PERFORMANCE,
    queryFn: getHomePerformance,
  });

export const useGetAllPerformance = (props?: { enabled?: boolean }) =>
  useQuery({
    queryKey: [ALL_PERFORMANCE_ENDPOINT],
    queryFn: getAllPerformance,
    enabled: props?.enabled ?? true,
  });

export const useGetTeamIndividualPerformance = (
  key?: string,
  params?: {
    month?: string;
    year?: string;
  },
  isEnabled = true,
) => {
  const queryKey = [TEAM_PERFORMANCE_INDIVIDUAL_ENDPOINT, 'perf'];
  if (key) {
    queryKey.push(key);
  }
  return useQuery({
    queryKey,
    queryFn: () => getTeamIndividualPerformance(params),
    enabled: isEnabled,
  });
};

export const useGetTeamIndividualMDRT = ({
  params,
  agentCode,
  isEnabled = true,
}: {
  params?: { isGetTeam: boolean };
  agentCode?: string | null;
  isEnabled?: boolean;
}) => {
  const queryKey = [TEAM_PERFORMANCE_INDIVIDUAL_MDRT_ENDPOINT];
  if (agentCode) {
    queryKey.push(agentCode);
  }
  return useQuery({
    queryKey,
    queryFn: () => getTeamIndividualMDRT(params, agentCode),
    enabled: isEnabled,
  });
};
export const useGetHomePerformanceV2 = () => {
  const {
    data: teamIndividualData,
    isLoading: isIndividualPerformanceLoading,
  } = useGetTeamIndividualPerformance();

  const { data: targetData, isLoading: isPerformanceTargetLoading } =
    useGetPerformanceTarget();

  const { data: homeData, isLoading: isHomeDataLoading } =
    useGetHomePerformance();

  const { data: allData, isLoading: isAllDataLoading } = useGetAllPerformance();

  if (country === 'ib' || country === 'my') {
    const mergedData = parsingHomePerformanceData(
      targetData,
      teamIndividualData,
    );
    return {
      data: mergedData,
      isLoading: isIndividualPerformanceLoading || isPerformanceTargetLoading,
    };
  }

  if (country === 'id') {
    const currentMonth = String(new Date().getMonth() + 1);

    const getMDRTData = () => {
      switch (allData?.mdrt?.nextTier) {
        case 'MDRT':
          return {
            nextTier: allData?.mdrt?.nextTier,
            percent: 100 - (allData?.mdrt?.shortfallToMDRTPercent ?? 0),
          };
        case 'COT':
          return {
            nextTier: allData?.mdrt?.nextTier,
            percent: 100 - (allData?.mdrt?.shortfallToCOTPercent ?? 0),
          };
        case 'TOT':
          return {
            nextTier: allData?.mdrt?.nextTier,
            percent: 100 - (allData?.mdrt?.shortfallToTOTPercent ?? 0),
          };
        default:
          return {
            nextTier: '--',
            percent: 0,
          };
      }
    };

    const mdrtData = getMDRTData();

    const currentData = new Date();

    return {
      data: {
        agentCode: homeData?.agentCode ?? '',
        mdrt: homeData?.mdrt ?? 0,
        mdrtLevel: homeData?.mdrtLevel ?? 0,
        ytdAPECompletion: homeData?.ytdAPECompletion ?? 0, //APE for IB
        ytdAPETarget: homeData?.ytdAPETarget ?? 0, //APE for IB
        ytdFYPCompletion: homeData?.ytdFYPCompletion ?? 0,
        mtdAPECompletion: homeData?.mtdAPECompletion ?? 0, //APE for IB
        mtdAPETarget: homeData?.mtdAPETarget,

        ytdTargetCASE: (homeData?.ytdTargetCASE || targetData?.targetCASE) ?? 0,
        mtdTargetCASE:
          (homeData?.mtdTargetCASE ||
            targetData?.monthTarget?.find(
              mon => mon.month == String(currentData.getMonth() + 1),
            )?.targetCASE) ??
          0,

        persistency: homeData?.persistency ?? 0,
        mtdCASESubmissionCM: homeData?.mtdCASESubmissionCM ?? 0,
        mtdCASECompletionCM: homeData?.mtdCASECompletionCM ?? 0,
        ytdCASESubmissionCY: homeData?.ytdCASESubmissionCY ?? 0,
        ytdCASECompletionCY: homeData?.ytdCASECompletionCY ?? 0,
        mtdACECompletionCM: homeData?.mtdACECompletionCM ?? 0, //ACE for MY
        mtdTargetACE: homeData?.mtdTargetACE ?? 0, //ACE for MY
        ytdACECompletionCY: homeData?.ytdACECompletionCY ?? 0, //ACE for MY
        ytdTargetACE: homeData?.ytdTargetACE ?? 0, //ACE for MY

        ytdAFYPCompletionCY: homeData?.ytdAFYPCompletionCY || 0,
        ytdAFYPTarget: targetData?.targetAFYP || 0,
        mtdAFYPCompletionCM: allData?.mtd?.afypCompletion,
        mtdAFYPTarget:
          targetData?.monthTarget?.find(m => m.month === currentMonth)
            ?.targetAFYP ?? 0,
        mdrtData,
      } satisfies HomePerformance & FEMappedIDNHomePerfFields,
      isLoading:
        isHomeDataLoading || isAllDataLoading || isPerformanceTargetLoading,
    };
  }

  return {
    data: homeData,
    isLoading: isHomeDataLoading,
  };
};

export const useGetPerformanceKpi = (props?: { isDisabled?: boolean }) =>
  useQuery({
    queryKey: [KPI_ENDPOINT],
    queryFn: getPerformanceKpi,
    enabled: !props?.isDisabled,
  });

export type ProcPerformanceResponse = {
  agentCode: string;
  kpi: PersistencyAndEfficiency;
  ytd: GeneralPerformanceItem & YtdPerformanceList & { afypAsOfDate?: string };
  mtd: GeneralPerformanceItem & MtdPerformanceItem;
  nbc: NbcItem;
  mdrt: MDRT;
  // !-------------eliteAgent Not included in Release 1---------
  eliteAgent?: EliteAgent;
  // !-------------^^^---------
  currency: string | null;
  //!-------------todo Date?String?
  cachedAt: Date;
};

export type PersistencyAndEfficiency = {
  asOfDate: string | null;
  persistency: number | null;
  secondYearPersistency: number | null;
  collectionEfficiency: number | null;
  currentYearPersistency: number | null;
};

null;
export type GeneralPerformanceItem = {
  fypAsOfDate?: string;
  fypSubmission?: number;
  fypCompletion?: number;

  apeAsOfDate?: string;
  apeSubmission?: number;
  apeCompletion?: number;
  apeTarget?: number;

  caseAsOfDate: string;
  caseSubmission: number;
  caseCompletion: number;
  caseTarget: number;

  fycAsOfDate?: string;
  fycSubmission?: number;
  fycCompletion?: number;
  // TODO_Alex: check if this is correct, ytd response is missing fycTarget
  fycTarget?: number;

  //MY_new
  aceAsOfDate?: string;
  aceCompletion?: number;
  aceSubmission?: number;
  currency?: string;
  targetACE?: number;

  ranking?: Ranking;
} & IDNPerfMetric;

export type IDNPerfMetric = {
  // IDN
  afypCompletion?: number;
  afypSubmission?: number;
  afypTarget?: number;
  afypAsOfDate?: string;
};

export type FypCompletionList = ListItem & ListItemDataFyp;
export type ApeCompletionList = ListItem & ListItemDataApe;
export type CaseCompletionList = ListItem & ListItemDataCase;
export type FypSubmissionList = ListItem & ListItemDataFyp;
export type ApeSubmissionList = ListItem & ListItemDataApeSub;
export type CaseSubmissionList = ListItem & ListItemDataCaseSub;
export type AceCompletionList = ListItem & ListItemDataAce;
export type AceSubmissionList = ListItem & ListItemDataAceSub;
export type AfypCompletionList = ListItem & ListItemDataAfyp;

export type YtdPerformanceList = {
  fypCompletionList?: FypCompletionList[];
  apeCompletionList?: ApeCompletionList[];
  caseCompletionList?: CaseCompletionList[];
  fypSubmissionList?: FypSubmissionList[];
  apeSubmissionList?: ApeSubmissionList[];
  caseSubmissionList?: CaseSubmissionList[];
  aceCompletionList?: AceCompletionList[];
  aceSubmissionList?: AceSubmissionList[];
  afypCompletionList?: AfypCompletionList[];
  persistency?: number;
};

export type YtdPerformanceListItemSub =
  | ListItemDataFyp
  | ListItemDataApeSub
  | ListItemDataAceSub;
export type YtdPerformanceListItemData =
  | ListItemDataFyp
  | ListItemDataApe
  | ListItemDataAce;
type ListItem = {
  agentCode: string;
  mm: number;
  yyyy: number;
};

type ListItemDataFyp = {
  fyp: number;
};
type ListItemDataApe = {
  ape: number;
};
type ListItemDataApeSub = {
  apeSub: number;
  ape?: number;
};
type ListItemDataAce = {
  ace: number;
};
type ListItemDataAceSub = {
  aceSub: number;
};
type ListItemDataAfyp = {
  afyp: number;
};
type ListItemDataCase = {
  caseComp: number;
};
type ListItemDataCaseSub = {
  caseSub: number;
};

type MtdPerformanceItem = {
  fycTarget?: number;
  persistency?: number;
};

type NbcItem = {
  agentCode?: string;
  ytd?: number;
  daily?: number;
  monthly?: number;
  currency?: string;
  asOfDate?: string;
};

type Ranking = {
  company: {
    apeRank: number;
    caseCountRank: number;
    teamTopApe: number;
    ape: number;
    caseCount: number;
    asOfDate: string | null;
    branchAgentRanks: any | null;
  };
  team: {
    apeRank: number;
    caseCountRank: number;
    teamTopApe: number;
    ape: number;
    caseCount: number;
    asOfDate: string | null;
    branchAgentRanks: any | null;
  };
  teamSize: number;
  companySize: number;
};

type RankingItem = {
  // TODO_Alex: check if this is correct, rank response is missing items in the following arrays
  apeRankingList?: ApeItem[];
  // TODO_Alex: to be updated into CamelCase
  myAPE?: ApeItem[];
  no1APE?: ApeItem[];
  // !-----------------^^---------
  agentCode?: string;
  companyAsOfDate: string;
  // TODO_Alex: to be updated into CamelCase
  ytdAPE?: number;
  ytdAPERanking?: number;
  ytdCase: number;
  ytdCaseRanking: number;
  mtdFYP?: number;
  mtdFYPRanking?: number;
  mtdCase?: number;
  mtdCaseRanking?: number;
  teamAsOfDate: string;
  teamRanking?: number;
  teamSize?: number;
  toBeTop?: number;
  totalAgentCount?: number;
};

type ApeItem = {
  agentCode: string;
  ape: number;
  apeRanking: number;
  mm: string;
  yyyy: string;
};

type HomeMDRT = {
  mdrt?: number;
  mdrtLevel?: number;
  cotLevel?: number;
  totLevel?: number;
};

export type MDRT = HomeMDRT & {
  total: number;
  cot: number;
  tot: number;

  // TODO_Alex: to be updated into CamelCase
  shortfallToCOT: number;
  shortfallToCOTPercent: number;
  shortfallToMDRT: number;
  shortfallToMDRTPercent: number;
  shortfallToTOT: number;
  shortfallToTOTPercent: number;
  // !-----------------^^---------
  nextTier: string;
  shortfallToNextTier: number;
  shortfallToNextTierPercent: number;
  tierLevelAchievement: string;
  mdrtPercent: number;
  cotPercent: number;
  totPercent: number;
  // TODO_Alex: to be updated into CamelCase
  mdrtLevelFYC?: number;
  cotLevelFYC?: number;
  totLevelFYC?: number;
  mdrtLevelFYP?: number;
  cotLevelFYP?: number;
  totLevelFYP?: number;
  // !-----------------^^---------
  fyc?: number;
  fyp?: number;
};

type EliteAgent = {
  // ? To be comfirmed
  agentCode?: string;
  total?: number;
  level?: number;
  shortfallToNextTier?: number;
  shortfallToNextTierPercent?: number;
};
