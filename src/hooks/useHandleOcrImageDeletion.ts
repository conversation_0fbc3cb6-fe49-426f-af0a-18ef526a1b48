import { useCallback } from 'react';
import useBoundStore from './useBoundStore';
import { useGetActiveCase } from './useGetActiveCase';
import { useDeleteDocument } from './useParty';
import { DocumentType } from 'types/document';

export const useHandleOcrImageDeletion = (partyId = '') => {
  const caseId = useBoundStore(state => state.case.caseId);
  const { caseObj } = useGetActiveCase();

  const { mutateAsync: deleteDocument, isLoading: isDeletingDocument } =
    useDeleteDocument();

  const handleOcrImageDeletion = useCallback(
    async (newFrontImage?: { name?: string; fromOcr?: boolean }) => {
      const caseFiles = !partyId
        ? []
        : caseObj?.files?.filter(f => f.partyId === partyId);
      const frontImage = caseFiles?.find(file =>
        file.fileName.includes(DocumentType.FrontID),
      );

      if (
        newFrontImage?.fromOcr &&
        frontImage &&
        frontImage.fileName !== newFrontImage?.name &&
        caseId
      ) {
        await deleteDocument({
          caseId,
          fileName: frontImage.fileName,
        });
      }
    },
    [caseObj?.files, caseId, partyId, deleteDocument],
  );

  return {
    caseId,
    handleOcrImageDeletion,
    deleteDocument,
    isDeletingDocument,
  };
};
