import { useMutation } from '@tanstack/react-query';
import { queryClient } from 'api/RootQueryClient';
import {
  showYearOfJoiningAgentStorefront,
  ShowYearOfJoiningAgentStorefrontProfileRequest,
} from 'api/storefrontApi';
import { getAgentStorefrontProfileKey } from './useGetAgentStorefrontProfile';

export const useShowYearOfJoiningAgentStorefrontProfile = () => {
  return useMutation({
    mutationFn: (body: ShowYearOfJoiningAgentStorefrontProfileRequest) =>
      showYearOfJoiningAgentStorefront(body),
    onSuccess: async () => {
      await queryClient.invalidateQueries(getAgentStorefrontProfileKey());
    },
  });
};
