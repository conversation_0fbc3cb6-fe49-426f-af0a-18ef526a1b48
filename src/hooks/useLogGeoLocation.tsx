import { updateCase, UpdateCaseBody } from 'api/caseApi';
import { usePromptContext } from 'components/prompt/PromptContext';
import { getAlertActions } from 'components/prompt/PromptDialog';
import dayjs from 'dayjs';
import * as Location from 'expo-location';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { AgentAction, LogLocation } from 'types';
import { countryModuleEAppConfig } from 'utils/config/module';
import useBoundStore from './useBoundStore';
import useCheckLogin from './useCheckLogin';
import { useGetCaseManually } from './useGetCase';

export function useGetGeoLocation(action: AgentAction) {
  const { t } = useTranslation(['eApp']);
  const { mutateAsync: getCase } = useGetCaseManually();
  const activeCaseId = useBoundStore(state => state.case.caseId);
  const isLoggedIn = useCheckLogin();
  const { shouldTrackGeoLocation } = countryModuleEAppConfig;
  const { prompt } = usePromptContext();

  const handleGeoLocationTracking = useCallback(async () => {
    async function getCurrentLocation() {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();

        if (status !== 'granted') {
          const accept = await prompt({
            description: t('eApp:geoLocationPermission.permissionNotGranted'),
            actions: getAlertActions,
            config: {
              accept: t('eApp:ok'),
            },
          });
          return false;
        }

        if (!activeCaseId) {
          throw new Error('Case id not found while logging geo location');
        }

        const caseObj = await getCase(activeCaseId);
        const existingTransaction = caseObj.geographicTransactions?.filter(
          info => info.action !== action,
        );
        const location = await Location.getCurrentPositionAsync({});
        const locationInfo: LogLocation = {
          action,
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          createdDT: dayjs(location.timestamp).toISOString(),
        };

        let payload: UpdateCaseBody;
        if (existingTransaction) {
          payload = {
            geoTransactions: [...existingTransaction, locationInfo],
          };
        } else {
          payload = {
            geoTransactions: [locationInfo],
          };
        }

        await updateCase({
          caseId: activeCaseId,
          ...payload,
        });

        return true;
      } catch (error) {
        console.error(error);
        return false;
      }
    }

    if (shouldTrackGeoLocation && isLoggedIn) {
      return await getCurrentLocation();
    }
    return false;
  }, [action, activeCaseId, getCase, isLoggedIn, prompt, t]);

  return { shouldTrackGeoLocation, handleGeoLocationTracking };
}
