import { useState, useCallback } from 'react';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const useLoadingAsync = <ReturnValue, Args extends any[]>(
  asyncFunction: (...args: Args) => Promise<ReturnValue> | ReturnValue,
): [(...args: Args) => Promise<ReturnValue> | ReturnValue, boolean] => {
  const [loading, setLoading] = useState(false);

  const wrappedFunction = useCallback(
    async (...args: Args) => {
      setLoading(true);
      try {
        return await asyncFunction(...args);
      } finally {
        setLoading(false);
      }
    },
    [asyncFunction],
  );

  return [wrappedFunction, loading];
};
