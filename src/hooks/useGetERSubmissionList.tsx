import { useQuery } from '@tanstack/react-query';
import { getSubmissionList } from 'api/eRecruitApi';
import { ApplicationStatusQueryParams } from 'types/eRecruit';

export const ER_SUBMISSION_URL = '/proc/recruitment/registration/submission';

const getERSubmissionListKey = (params: ApplicationStatusQueryParams) => [
  ER_SUBMISSION_URL,
  params,
];

export function useGetERSubmissionList(params: ApplicationStatusQueryParams) {
  return useQuery({
    queryKey: getERSubmissionListKey(params),
    queryFn: () => getSubmissionList(params),
    enabled: true,
  });
}
