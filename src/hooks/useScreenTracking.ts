import { createNavigationContainerRef } from '@react-navigation/native';
import { useRef } from 'react';
import GATracking from 'utils/helper/gaTracking';

export const navigationRef = createNavigationContainerRef();

const useScreenTracking = () => {
  const routeNameRef = useRef<string>();

  const onNavigationStateChange = async () => {
    try {
      const previousRouteName = routeNameRef.current;
      const currentRouteName = navigationRef.getCurrentRoute()?.name;
      const { index, routes } = navigationRef.getRootState();
      const parentName = routes?.[index]?.name;

      if (previousRouteName !== currentRouteName && !!currentRouteName) {
        // Save the current route name for later comparison
        routeNameRef.current = currentRouteName;
        const gaTrackingScreenName =
          GATracking.MAIN_SCREENS_REMAP[currentRouteName];
        const screenName = gaTrackingScreenName || currentRouteName;

        // Replace the line below to add the tracker from a. mobile analytics SDK
        GATracking.trackScreen(
          screenName,
          gaTrackingScreenName ? '' : parentName,
        ).then();
      }
    } catch (error) {
      __DEV__ && console.error(error);
    }
  };

  const onNavigationReady = () => {
    try {
      routeNameRef.current = navigationRef.getCurrentRoute()?.name;
    } catch (error) {
      __DEV__ && console.error(error);
    }
  };

  return {
    navigationRef,
    routeNameRef,
    onNavigationStateChange,
    onNavigationReady,
  };
};

export default useScreenTracking;
