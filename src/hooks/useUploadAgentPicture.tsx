import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteAgentProfile, uploadAgentPhoto } from 'api/authApi';
import { getQuery<PERSON>ey as getAgentProfileQueryKey } from 'hooks/useGetAgentProfile';
import useBoundStore from './useBoundStore';

export function useUploadAgentPicture() {
  const agentId = useBoundStore(state => state.auth.agentCode);
  const accessToken = useBoundStore(state => state.auth.authInfo.accessToken);
  return useMutation({
    mutationFn: (imgPath: string) =>
      uploadAgentPhoto({ imgPath, agentId, accessToken }),
  });
}

export function useDeleteAgentPicture() {
  const queryClient = useQueryClient();
  const agentId = useBoundStore(state => state.auth.agentCode);

  return useMutation({
    mutationFn: deleteAgentProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getAgentProfileQueryKey(agentId),
      });
    },
  });
}
