
import { useMutation } from '@tanstack/react-query';
import { PaymentGatewayRequest, PaymentGatewayStatusRequest, queryPaymentStatus, requestPaymentGateway } from 'api/dragonPayApi';

export const usePaymentGateway = () => {
    return useMutation({
        mutationFn: (
            body: PaymentGatewayRequest
        ) => requestPaymentGateway(body),
    });
};


export const useQueryPaymentGatewayStatus = () => {
    return useMutation({
        mutationFn: (
            body: PaymentGatewayStatusRequest
        ) => queryPaymentStatus(body),
    });
};
