import { useQuery } from '@tanstack/react-query';
import { getAgentInfo } from 'api/eRecruitApi';

export const QUERY_KEY = 'agent-info';

const getAgentInfoQueryKey = (agentCode: string | null) => [
  QUERY_KEY,
  agentCode,
];

export function useGetAgentInfo(agentCode: string | null) {
  return useQuery({
    queryKey: getAgentInfoQueryKey(agentCode),
    queryFn: () => getAgentInfo(agentCode),
    enabled: Bo<PERSON><PERSON>(agentCode),
  });
}
