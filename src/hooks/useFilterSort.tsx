import { useEffect, useMemo } from 'react';

export default function useFilterSort<T>({
  data,
  sortOptions,
  filterOptions = [],
  onUpdate = () => null,
  onEnd = () => null,
}: {
  data: T[];
  sortOptions?: {
    sortBy: boolean;
    compareFn: ((a: T, b: T) => number) | undefined;
  };
  filterOptions?: FilterOptions<T> | FilterOptions<T>[];
  onUpdate?: (result: T[]) => void;
  onEnd?: (result: T[]) => void;
}) {
  const result = useMemo(() => {
    const output = { data: data.slice() };

    if (sortOptions) {
      const { compareFn } = sortOptions;
      output.data.sort(compareFn);
    }

    const filterOptionsArr = Array.isArray(filterOptions)
      ? filterOptions
      : [filterOptions];

    if (filterOptionsArr.length > 0) {
      for (const f of filterOptionsArr) {
        const filtered = output.data.filter(f.predicate);
        output.data = filtered;
      }
    }

    onUpdate(output.data);
    return output.data;
  }, [data, sortOptions, filterOptions]);

  useEffect(() => {
    return () => {
      // Clean up when it is unmount
      onEnd(result);
    };
  }, []);

  return {
    result,
  };
}

type FilterOptions<T> = {
  predicate: (value: T, index: number, array: T[]) => unknown;
};
