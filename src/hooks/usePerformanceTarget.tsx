import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  ALL_PERFORMANCE_ENDPOINT,
  PERFORMANCE_TARGET_ENDPOINT,
  getPerformanceTarget,
  updatePerformanceTarget,
} from 'api/performanceApi';

const QUERY_KEY_PERF_TARGET = [
  ALL_PERFORMANCE_ENDPOINT,
  PERFORMANCE_TARGET_ENDPOINT,
];
export function useGetPerformanceTarget() {
  return useQuery({
    queryKey: QUERY_KEY_PERF_TARGET,
    queryFn: getPerformanceTarget,
  });
}

export function useUpdatePerformanceTarget() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updatePerformanceTarget,
    onSuccess: () => queryClient.invalidateQueries([QUERY_KEY_PERF_TARGET[0]]),
  });
}
