import {
  usePermissions,
  getAssetsAsync,
  Asset,
  MediaTypeValue,
  SortByValue,
} from 'expo-media-library';
import { useCallback, useEffect, useRef, useState } from 'react';

type Config = {
  mediaType?: MediaTypeValue | MediaTypeValue[];
  sortBy?: SortByValue | SortByValue[] | undefined;
};

export const useMediaLibraryAssets = ({ mediaType, sortBy }: Config) => {
  const [permissionResponse, requestPermission] = usePermissions();
  const [assets, setAssets] = useState<Asset[]>([]);
  const isFetchingStarted = useRef(false);

  const getAllMedias = useCallback(async () => {
    isFetchingStarted.current = true;
    let page = await getAssetsAsync({
      mediaType,
      sortBy: sortBy ?? [['creationTime', false]],
    });

    do {
      setAssets(p => [...p, ...page.assets]);
      page = await getAssetsAsync({
        mediaType,
        sortBy: sortBy ?? [['creationTime', false]],
        after: page.endCursor,
      });
    } while (page.hasNextPage);
  }, [mediaType, sortBy]);

  useEffect(() => {
    if (permissionResponse?.granted && !isFetchingStarted.current) {
      getAllMedias();
    }
  }, [getAllMedias, permissionResponse?.granted]);

  return {
    assets,
    permissionResponse,
    requestPermission,
  };
};
