import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getAgentInfoById } from 'api/agentInfoApi';
import {
  getAccount,
  getApplicationTrackerInfo,
  getDocumentsList,
  getFund,
  getHistory,
  getPendingDocuments,
  getPolicy,
  getPolicyDetail,
  getProduct,
  getRole,
  uploadPendingDocument,
  submitUploadedV2PendingDocument,
  deletePendingDocument,
  uploadV2PendingDocumentByFileSystems,
} from 'api/policyDetailApi';
import { AgentInfo } from 'types/agent';
import {
  Account,
  ApplicationTrackerInfo,
  Fund,
  PolicyDetails,
  PolicyDetailsPolicy,
  Product,
  Role,
  History,
  PendingDocuments,
  UploadPendingDocumentBody,
  GetDocumentsListParams,
  UploadV2PendingDocumentBody,
} from 'types/policy';
import { QUERY_KEY_PO_NEW_BUSINESS } from './useGetPolicyList';
import { PolicyDomains } from 'types';
import useBoundStore from './useBoundStore';

const QUERY_KEY_PO_NB_DETAIL = '/proc/policy/details/:id';
const QUERY_KEY_APPLICATION_TRACKER = 'policyApplicationTracker';
const QUERY_KEY_POLICY = '/proc/policy/details/:id/policy';
const QUERY_KEY_PRODUCT = '/proc/policy/details/:id/product';
const QUERY_KEY_FUND = '/proc/policy/details/:id/fund';
const QUERY_KEY_HISTORY = '/proc/policy/details/:id/history';
const QUERY_KEY_BENE = '/proc/policy/details/:id/role';
const QUERY_KEY_SUPERVISOR_AGENT = '/proc/agent/:agentId';
const QUERY_KEY__AGENT = '/proc/agent/:agentId';
const QUERY_KEY_ACCOUNT = '/proc/policy/details/:id/account';
const QUERY_KEY_PENDING_DOCUMENTS =
  '/proc/policy/details/:id/pending-documents';
export const QUERY_KEY_DOCUMENTS_LIST = '/proc/upload/memo'; //add by Hardy

export const useGetPolicyDetail = ({
  policyNo,
  policyDomain,
}: {
  policyNo: string;
  policyDomain: PolicyDomains;
}) => {
  return useQuery<PolicyDetails>({
    queryKey: [QUERY_KEY_PO_NB_DETAIL, policyNo, policyDomain],
    queryFn: () =>
      getPolicyDetail({
        policyNo,
        policyDomain,
      }),
    enabled: Boolean(policyNo),
  });
};
export const useGetApplicationTrackerInfo = (policyNo: string) => {
  return useQuery<ApplicationTrackerInfo>({
    queryKey: [QUERY_KEY_APPLICATION_TRACKER, policyNo],
    queryFn: async () => {
      const res = await getApplicationTrackerInfo(policyNo);
      const isSubmission = Boolean(res?.proposalReceived);
      const isScreening = Boolean(res?.screening);
      const isUW = Boolean(res?.underWriting);
      const isDecision = Boolean(res?.decisionDate);
      const isIssue = Boolean(res?.issueDate);
      const isDispatch = Boolean(res?.dispatchDate);
      const status = {
        isSubmission,
        isScreening,
        isUW,
        isDecision,
        isIssue,
        isDispatch,
      };
      return { ...res, status };
    },
  });
};
export const useGetPolicy = (policyNo: string) => {
  return useQuery<PolicyDetailsPolicy>({
    queryKey: [QUERY_KEY_POLICY, policyNo],
    queryFn: () => getPolicy(policyNo),
  });
};
export const useGetProduct = (policyNo: string) => {
  return useQuery<Product[]>({
    queryKey: [QUERY_KEY_PRODUCT, policyNo],
    queryFn: () => getProduct(policyNo),
  });
};
export const useGetFund = (policyNo: string) => {
  return useQuery<Fund[]>({
    queryKey: [QUERY_KEY_FUND, policyNo],
    queryFn: () => getFund(policyNo),
  });
};
export const useGetHistory = (policyNo: string) => {
  return useQuery<History>({
    queryKey: [QUERY_KEY_HISTORY, policyNo],
    queryFn: () => getHistory(policyNo),
  });
};
export const useGetBene = (policyNo: string) => {
  return useQuery<Role[]>({
    queryKey: [QUERY_KEY_BENE, policyNo],
    queryFn: () => getRole(policyNo),
  });
};
export const useGetSupervisor = (agentId: string) => {
  return useQuery<AgentInfo>({
    queryKey: [QUERY_KEY_SUPERVISOR_AGENT, agentId],
    queryFn: () => getAgentInfoById(agentId),
  });
};
export const useGetAgent = (agentId: string) => {
  return useQuery<AgentInfo>({
    queryKey: [QUERY_KEY__AGENT, agentId],
    queryFn: () => getAgentInfoById(agentId),
  });
};
export const useGetAccount = (policyNo: string) => {
  return useQuery<Account>({
    queryKey: [QUERY_KEY_ACCOUNT, policyNo],
    queryFn: () => getAccount(policyNo),
  });
};
export const useGetPendingDocuments = (policyNo: string | undefined) => {
  return useQuery<PendingDocuments[]>({
    queryKey: [QUERY_KEY_PENDING_DOCUMENTS, policyNo],
    queryFn: () => getPendingDocuments(policyNo ?? ''),
    enabled: Boolean(policyNo),
  });
};
export const useDeletePendingDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deletePendingDocument,
    onSuccess: async () =>
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEY_PENDING_DOCUMENTS],
      }),
  });
};

export const useUploadPendingDocument = () => {
  // PH using old upload document flow, which update documents directly to next processing system
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      body,
      onProgress,
    }: {
      body: UploadPendingDocumentBody;
      onProgress?: (progress: number) => void;
    }) => uploadPendingDocument(body, onProgress),
    onSuccess: async () =>
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEY_PENDING_DOCUMENTS],
      }),
  });
};

export const useBatchUploadPendingDocument = () => {
  // MYS using different upload document flow (saved on cube 1st, then submit to next processing system)
  const queryClient = useQueryClient();
  const accessToken = useBoundStore(state => state.auth.authInfo.accessToken);

  return useMutation({
    mutationFn: ({
      body,
      onProgress,
    }: {
      body: UploadV2PendingDocumentBody;
      onProgress?: (progress: number) => void;
    }) => {
      return uploadV2PendingDocumentByFileSystems({
        requestData: body,
        accessToken: accessToken,
      });

      // return uploadV2PendingDocument(body, onProgress);
    },

    onSuccess: async () =>
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEY_PENDING_DOCUMENTS],
      }),
  });
};

export const useSubmitV2Document = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: submitUploadedV2PendingDocument,
    onSuccess: () => {
      setTimeout(async () => {
        console.log('useSubmitV2Document on Success in setTimeout');
        await queryClient.invalidateQueries({
          queryKey: [QUERY_KEY_PENDING_DOCUMENTS],
        });
        await queryClient.invalidateQueries({
          queryKey: [QUERY_KEY_PO_NEW_BUSINESS],
        });
      }, 300);
    },
    onError: error => {
      console.log(
        '------ useSubmitV2Document onError:',
        error?.response?.request?._response,
      );
    },
  });
};

export const useDeleteDocument = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deletePendingDocument,
    onSuccess: async () =>
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEY_DOCUMENTS_LIST],
      }),
  });
};

export const useGetDocumentList = (params: GetDocumentsListParams) => {
  return useQuery({
    queryKey: [QUERY_KEY_DOCUMENTS_LIST, params],
    queryFn: () => getDocumentsList(params),
  });
}; //add by Hardy
