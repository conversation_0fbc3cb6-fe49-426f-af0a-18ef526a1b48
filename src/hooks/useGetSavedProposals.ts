import { useInfiniteQuery } from '@tanstack/react-query';
import { SavedProposalsQueryParams, getSavedProposals } from 'api/caseApi';
import { SavedProposalPaginationResponse } from 'types/proposal';

export const SAVED_PROPOSAL_URL = '/exp/case/saved';

const getSavedProposalsKey = (params: SavedProposalsQueryParams) => [
  SAVED_PROPOSAL_URL,
  params,
];

export function useGetSavedProposals(
  params: SavedProposalsQueryParams,
  isEnabled?: boolean,
) {
  return useInfiniteQuery({
    queryKey: getSavedProposalsKey(params),
    queryFn: ({ pageParam = {} as SavedProposalsQueryParams }) => {
      return getSavedProposals({ ...params, ...pageParam });
    },
    getNextPageParam: getNextPageSavedProposals,
    enabled: isEnabled ?? true,
  });
}

export function getNextPageSavedProposals(
  lastPage: SavedProposalPaginationResponse,
): SavedProposalsQueryParams | undefined {
  const { limit, offset, next } = lastPage;
  if (!next) {
    return undefined;
  }

  return {
    offset: offset + limit,
    limit,
  };
}
