import { useCallback, useRef } from 'react';
import useLatest from './useLatest';

export const useThrottle = (fn: any) => { // eslint-disable-line @typescript-eslint/no-explicit-any
  const throttlingPress = useRef(false);
  const functionRef = useLatest(fn);
  return useCallback(async () => {
    if (throttlingPress.current) return;
    throttlingPress.current = true;
    functionRef.current &&
      typeof functionRef.current === 'function' &&
      (await functionRef.current());
    throttlingPress.current = false;
  }, [functionRef]);
};
