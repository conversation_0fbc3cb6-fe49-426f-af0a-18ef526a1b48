import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  GET_NOTIFICATION_API,
  deleteNotificationMessage,
  getListNotification,
  readNotificationMessage
} from 'api/notificationApi';
import { sendNotification } from 'api/teamApi';
import { NotificationMessage } from 'types/home';
import { ReminderNotificationMessageData } from 'types/team';

export const useGetNotificationMessage = () => {
  return useQuery<NotificationMessage[]>({
    queryKey: [GET_NOTIFICATION_API],
    queryFn: () => getListNotification(),
  });
};

export const useDeleteNotificationMessage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => {
      return deleteNotificationMessage(id);
    },
    onSuccess: async () =>
      await queryClient.invalidateQueries({ queryKey: [GET_NOTIFICATION_API] }),
  });
};

export const useSendNotificationMessage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ data }: { data: Array<ReminderNotificationMessageData> }) => {
      return sendNotification({ data });
    },
    onSuccess: async () => {
      return await queryClient.invalidateQueries({
        queryKey: [GET_NOTIFICATION_API],
      });
    },
  });
};

export const useReadNotificationMessage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => {
      return readNotificationMessage(id);
    },
    onSuccess: async () =>
      await queryClient.invalidateQueries({ queryKey: [GET_NOTIFICATION_API] }),
  });
};
