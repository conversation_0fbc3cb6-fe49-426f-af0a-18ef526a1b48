import { useMutation, useQuery } from '@tanstack/react-query';
import {
  uploadAndScanDocument,
  updateOcrResultWithRefId,
  UpdateOcrResultWithRefIdParams,
} from 'api/ocrApi';
import { OcrRequestBody } from 'types/ocr';

export const useDocumentOcr = () => {
  return useMutation({
    mutationFn: ({
      body,
      onProgress,
    }: {
      body: OcrRequestBody;
      onProgress?: (progress: number) => void;
    }) => uploadAndScanDocument(body, onProgress),
  });
};

export const useUpdateOcrResultWithRefId = () => {
  return useMutation({
    mutationFn: (params: UpdateOcrResultWithRefIdParams) =>
      updateOcrResultWithRefId(params),
  });
};
