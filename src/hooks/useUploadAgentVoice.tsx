import { useMutation, useQueryClient } from '@tanstack/react-query';
import { uploadAgentVoice } from 'api/authApi';
import useBoundStore from './useBoundStore';
import { QUERY_KEY as AGENT_PROFILE_QUERY_KEY } from 'hooks/useGetAgentProfile';

export function useUploadAgentVoice() {
  const queryClient = useQueryClient();

  const agentId = useBoundStore(state => state.auth.agentCode);
  const accessToken = useBoundStore(state => state.auth.authInfo.accessToken);
  return useMutation({
    mutationFn: (voicePath: string) =>
      uploadAgentVoice({ voicePath, agentId, accessToken }),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [AGENT_PROFILE_QUERY_KEY],
      });
    },
  });
}
