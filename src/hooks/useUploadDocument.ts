import { useMutation } from '@tanstack/react-query';
import { uploadDocument } from 'api/documentUploadApi';
import { UploadDocumentBody } from 'types/document';
import { useLocaleHeader } from './useLocaleHeader';

export const useUploadDocument = () => {
  const localeHeader = useLocaleHeader();
  return useMutation({
    mutationFn: ({
      body,
      onProgress,
    }: {
      body: UploadDocumentBody;
      onProgress?: (progress: number) => void;
    }) => uploadDocument(body, localeHeader, onProgress),
  });
};
