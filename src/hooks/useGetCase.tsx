import {
  QueryKey,
  UseQueryOptions,
  useMutation,
  useQueries,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import {
  getCaseById,
  getCaseCount,
  getCaseDocuments,
  getCasesByIdentity,
  getOwbModel,
  IdentityQueryParams,
  populateApplication,
  PopulateApplicationBody,
  QUERY_CASE_BY_IDENTITY,
} from 'api/caseApi';
import { Case, CaseStatus } from 'types/case';

export const getCaseKey = () => ['/case'];
export const getCaseByIdKey = (caseId: string) => ['/case', caseId];
export const getCaseDocumentsByIdKey = (caseId: string) => [
  '/case',
  caseId,
  'documents',
];

export function useGetCase(
  caseId = '',
  options?: Omit<
    UseQueryOptions<Case, unknown, Case, QueryKey>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) {
  return useQuery<Case>(getCaseByIdKey(caseId), () => getCaseById(caseId), {
    enabled: Boolean(caseId),
    ...options,
  });
}

export function useGetCases(
  caseIds: string[],
  options?: Omit<
    UseQueryOptions<Case, unknown, Case, QueryKey>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) {
  return useQueries({
    queries: caseIds?.map(caseId => ({
      queryKey: getCaseByIdKey(caseId),
      queryFn: () => getCaseById(caseId),
      options,
      enabled: options?.enabled ?? true,
    })),
  });
}

export function useGetCaseManually() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (caseId: string) => getCaseById(caseId),
    onSuccess: (caseObj: Case) => {
      queryClient.setQueryData(getCaseByIdKey(caseObj.id), caseObj);
    },
  });
}

export function useConvertCaseToOwb() {
  return useMutation({
    mutationFn: async (caseId: string) => getOwbModel(caseId),
  });
}

export function useGetCaseCount() {
  return useQuery({
    queryFn: getCaseCount,
    queryKey: ['exp/case/count'],
  });
}

export function useGetApplicationsByIdentity() {
  return useMutation({
    mutationFn: async (params: IdentityQueryParams) =>
      getCasesByIdentity({
        ...params,
        status: CaseStatus.IN_APP,
      }),
  });
}

export function useGetCasesByIdentity(params: IdentityQueryParams) {
  return useQuery({
    queryKey: [QUERY_CASE_BY_IDENTITY, params],
    queryFn: () => getCasesByIdentity(params),
    enabled: Boolean(
      params.firstName && params.dob && params.gender && params.id,
    ),
  });
}

export function usePopulateCase() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (body: PopulateApplicationBody) => populateApplication(body),
    onSuccess: (caseObj: Case) => {
      queryClient.setQueryData(getCaseByIdKey(caseObj.id), caseObj);
    },
  });
}

export function useGetCaseDocuments(caseId = '') {
  return useQuery({
    queryKey: getCaseDocumentsByIdKey(caseId),
    queryFn: () => getCaseDocuments(caseId),
  });
}
