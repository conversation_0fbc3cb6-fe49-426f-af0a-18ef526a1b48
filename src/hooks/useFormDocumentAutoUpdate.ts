import { useEffect, useMemo } from 'react';
import { useGetActiveCase } from './useGetActiveCase';
import { DocumentType } from 'types/document';
import { UseFormReturn } from 'react-hook-form';

export type FormDocumentAutoUpdateType = UseFormReturn<{
  document?: {
    frontImage?: {
      base64?: string;
      name?: string;
      thumbnail?: string;
      fromOcr?: boolean;
    };
  };
}>;

/*
Automatically sync fileName and filePath from activeCase to the local form document state
to prevent unnecessary API calls for file deletion and re-uploading of ocrImage.
*/
export const useFormDocumentAutoUpdate = (
  partyId = '',
  form: FormDocumentAutoUpdateType,
) => {
  const { caseObj } = useGetActiveCase();

  const frontImage = useMemo(() => {
    if (!partyId) return;
    const caseFiles = caseObj?.files?.filter(
      f => f.partyId === partyId && !!f.fromOcr,
    );
    const frontImage = caseFiles?.find(file =>
      file.fileName.includes(DocumentType.FrontID),
    );
    return frontImage;
  }, [caseObj?.files, partyId]);

  const { setValue, getValues } = form;

  useEffect(() => {
    if (frontImage) {
      const localImage = getValues('document.frontImage');
      if (
        localImage?.name !== frontImage.fileName ||
        localImage?.thumbnail !== frontImage.filePath
      ) {
        const document = getValues('document');
        setValue('document', {
          ...document,
          frontImage: {
            ...localImage,
            fromOcr: frontImage.fromOcr,
            name: frontImage.fileName,
            thumbnail: frontImage.filePath,
          },
        });
      }
    }
  }, [frontImage]);
};
