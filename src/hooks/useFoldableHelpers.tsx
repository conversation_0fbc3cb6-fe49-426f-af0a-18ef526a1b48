import { useWindowDimensions } from 'react-native';

export default function useFoldableHelpers() {
  const { width, height } = useWindowDimensions();
  const isWideFoldScreen = width > 580;

  const isNarrowFlipScreen = width <= 340;

  const scaleInWideFoldable = (input: number, factor: number) => {
    return isWideFoldScreen ? input * factor : input;
  };
  const scaleInNarrowFoldable = (input: number, factor: number) => {
    return isNarrowFlipScreen ? input * factor : input;
  };

  const scaleInWideAndNarrowFoldable = (
    input: number,
    wideFactor: number,
    narrowFactor: number,
  ) => {
    return isWideFoldScreen
      ? input * wideFactor
      : isNarrowFlipScreen
      ? input * narrowFactor
      : input;
  };

  return {
    isWideFoldScreen,
    isNarrowFlipScreen,
    scaleInWideFoldable,
    scaleInNarrowFoldable,
    scaleInWideAndNarrowFoldable,
  };
}
