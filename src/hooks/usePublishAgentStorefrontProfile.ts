import { useMutation } from '@tanstack/react-query';
import { queryClient } from 'api/RootQueryClient';
import {
  publishAgentStorefront,
  PublishAgentStorefrontProfileRequest,
} from 'api/storefrontApi';
import { getAgentStorefrontProfileKey } from './useGetAgentStorefrontProfile';

export const usePublishAgentStorefrontProfile = () => {
  return useMutation({
    mutationFn: (body: PublishAgentStorefrontProfileRequest) =>
      publishAgentStorefront(body),
    onSuccess: async () => {
      await queryClient.invalidateQueries(getAgentStorefrontProfileKey());
    },
  });
};
