import { useWindowDimensions as originalUseWindowDimensions } from 'react-native';
import useLayoutAdoptionCheck from './useDeviceCheck';

export function useCustomWindowDimensions() {
  const { width, height, ...remain } = originalUseWindowDimensions();

  const { isTabletMode } = useLayoutAdoptionCheck();

  if (isTabletMode) {
    // to fix library issue where useWindowDimensions() returns the wrong width/height in certain devices
    // here always assume tablet is always in landscape mode
    const trueWidth = width > height ? width : height;
    const trueHeight = height < width ? height : width;

    return { width: trueWidth, height: trueHeight, ...remain };
  }

  return { width, height, ...remain };
}
