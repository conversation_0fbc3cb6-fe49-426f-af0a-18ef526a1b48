import { useMutation } from '@tanstack/react-query';
import { updateLead } from 'api/leadApi';
import { Party } from 'types/party';

export function useUpdateLead() {
  return useMutation({
    mutationFn: (owner: Party) => {
      if (!owner.leadId) {
        return Promise.reject('policy owner has no lead attached');
      }
      const { person, addresses, contacts } = owner;
      const mobile = contacts.phones.find(p => p.type === 'MOBILE');
      const work = contacts.phones.find(p => p.type === 'WORK');
      const home = contacts.phones.find(p => p.type === 'HOME');
      const addr = addresses.find(a => a.addressType === 'MAIN');
      return updateLead(owner.leadId, {
        salutation: person.name.title,
        firstName: person.name.firstName,
        middleName: person.name.middleName,
        lastName: person.name.lastName,
        nameExtension: person.name.extensionName,
        email: contacts.email,
        mobilePhoneCountryCode: mobile?.countryCode,
        mobilePhoneNumber: mobile?.number,
        workPhoneCountryCode: work?.countryCode,
        workPhoneNumber: work?.number,
        homePhoneCountryCode: home?.countryCode,
        homePhoneNumber: home?.number,
        addressStreet: `${addr?.addressNo} ${addr?.street}`,
        addressCity: addr?.city,
        addressProvinceCode: addr?.province,
        addressPostalCode: addr?.zipCode,
        addressCountryCode: addr?.countryCode,
        genderCode: person.gender,
        birthDate: person.dateOfBirth.date,
        maritalStatusCode: person.maritalStatus,
        annualIncomeRangeLower: person.occupation?.income?.toString(),
        annualIncomeRangeUpper: person.occupation?.income?.toString(),
        companyName: person.occupation?.nameOfEmployer,
        occupationIndustryCode: person.occupation?.natureOfBusiness,
        occupationGroupCode: person.occupation?.natureOfWork,
        occupationClassCode: person.occupation?.natureOfSubWork,
      });
    },
  });
}
