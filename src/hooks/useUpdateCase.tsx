import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UpdateCaseBody, updateCase } from 'api/caseApi';
import { getCaseByIdKey } from './useGetCase';

export function useUpdateCase() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: { caseId: string } & UpdateCaseBody) => updateCase(data),
    onSuccess: async (_, { caseId }) => {
      await queryClient.invalidateQueries(getCaseByIdKey(caseId));
    },
  });
}
