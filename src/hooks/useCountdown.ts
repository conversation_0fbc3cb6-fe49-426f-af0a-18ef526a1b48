import { useCallback, useState } from 'react';
import { useCounter } from 'hooks/useCounter';
import { useInterval } from 'hooks/useInterval';

type CountdownOptions = {
  countStart: number;
  countStop?: number;
  intervalMs?: number;
  isIncrement?: boolean;
};

export function useCountdown({
  countStart,
  countStop = 0,
  intervalMs = 1000,
  isIncrement = false,
}: CountdownOptions) {
  const {
    count,
    increment,
    decrement,
    reset: resetCounter,
  } = useCounter(countStart);

  /*
   * Note: used to control the useInterval
   * running: If true, the interval is running
   * start: Should set running true to trigger interval
   * stop: Should set running false to remove interval.
   */
  const [isCountdownRunning, setIsCountdownRunning] = useState(false);
  const startCountdown = useCallback(() => setIsCountdownRunning(true), []);
  const stopCountdown = useCallback(() => setIsCountdownRunning(false), []);

  // Will set running false and reset the seconds to initial value.
  const resetCountdown = useCallback(() => {
    stopCountdown();
    resetCounter();
  }, [stopCountdown, resetCounter]);

  const countdownCallback = useCallback(() => {
    if (count === countStop) {
      stopCountdown();
      return;
    }

    if (isIncrement) {
      increment();
    } else {
      decrement();
    }
  }, [count, countStop, decrement, increment, isIncrement, stopCountdown]);

  useInterval(countdownCallback, isCountdownRunning ? intervalMs : null);

  return { count, startCountdown, stopCountdown, resetCountdown };
}
