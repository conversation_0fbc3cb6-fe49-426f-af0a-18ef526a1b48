import {
  NavigationProp,
  StackActions,
  useNavigation,
} from '@react-navigation/native';
import { useCallback } from 'react';
import { RootStackParamList } from 'types';

type RouteName = keyof RootStackParamList;

const useSkipScreen = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const backToBefore = useCallback(
    (targetScreen: RouteName, backOnceIfNotFound = true) => {
      const { routes: existingRoutes, index } = navigation.getState();
      const targetIdx = existingRoutes.findLastIndex(
        ({ name }) => name === targetScreen,
      );
      if (targetIdx !== -1) {
        navigation.dispatch(StackActions.pop(index - targetIdx + 1));
      } else if (backOnceIfNotFound) {
        navigation.goBack();
      }
    },
    [navigation],
  );

  return {
    backToBefore,
  };
};

export default useSkipScreen;
