import { useQuery } from '@tanstack/react-query';
import { useGetCubeChannel } from './useGetCubeChannel';
import {
  getRecruitmentMaterials,
  getSingleRecruitmentMaterial,
} from 'api/recruitmentMaterialsApi';
import { SortType, RecruitmentMaterialItem } from 'types/recruitmentMaterials';

export const QUERY_KEY_ALL_RECRUITMENT_MATERIALS =
  '/contentStack/cube_recruitment_materials';

export const QUERY_KEY_SINGLE_RECRUITMENT_MATERIAL =
  '/contentStack/cube_recruitment_materials/materialUid';

export const useGetRecruitmentMaterials = ({
  sortType,
}: {
  sortType: SortType;
}) => {
  const channel = useGetCubeChannel();
  return useQuery<Array<RecruitmentMaterialItem>>({
    queryKey: [QUERY_KEY_ALL_RECRUITMENT_MATERIALS, channel, sortType],
    queryFn: () => getRecruitmentMaterials({ channel, sortType }),
  });
};

export const useGetSingleRecruitmentMaterial = ({
  materialUid,
}: {
  materialUid: string;
}) => {
  return useQuery<RecruitmentMaterialItem | null>({
    queryKey: [QUERY_KEY_SINGLE_RECRUITMENT_MATERIAL, materialUid],
    queryFn: () => getSingleRecruitmentMaterial({ materialUid }),
  });
};
