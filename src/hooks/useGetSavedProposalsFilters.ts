import { useQuery } from '@tanstack/react-query';
import {
  getSavedProposalsFilters,
  SavedProposalsQueryParams,
} from 'api/caseApi';
import { SAVED_PROPOSAL_URL } from 'hooks/useGetSavedProposals';
import { CaseStatus } from 'types/case';
import { SavedProposalFiltersResponse } from 'types/proposal';
import { FilterTagConfig } from 'features/savedProposals/types';
import { filterTagsConfig } from 'features/savedProposals/config';
const getSavedProposalsFiltersKey = (params: SavedProposalsQueryParams) => [
  SAVED_PROPOSAL_URL,
  params,
];

type Props = {
  isDownlineOnly?: boolean;
  isEnabled?: boolean;
  // * TBC what other SavedProposalsQueryParams are also usable here
} & Pick<SavedProposalsQueryParams, 'q' | 'clientTypes' | 'start' | 'end'>;
export function useGetSavedProposalsFilters(props?: Props) {
  const { isDownlineOnly, isEnabled, ...rest } = props ?? {};
  const params = {
    downlineOnly: isDownlineOnly,
    ...rest,
  };

  return useQuery({
    queryKey: getSavedProposalsFiltersKey(params),
    queryFn: () => getSavedProposalsFilters(params),
    enabled: isEnabled ?? true,
  });
}

export function useGetSavedProposalCountMap(
  data: SavedProposalFiltersResponse | undefined,
) {
  const defaultCountMap = {
    total: 0,
    FULL_SI: 0,
    QUICK_SI: 0,
    IN_APP: 0,
    CFF: 0,
    FNA: 0,
  } satisfies Partial<Record<keyof typeof CaseStatus | 'total', number>>;
  const totalProposalCount =
    data?.latestStatusStats?.reduce((acc, curr) => {
      if (
        filterTagsConfig.some(tag => tag.type == curr.latestStatus) == false
      ) {
        // counting only the statuses that are in the filterTagsConfig
        return acc;
      }
      switch (curr.latestStatus) {
        case 'FULL_SI':
          acc.FULL_SI = acc.FULL_SI + curr.count;
          acc.total = acc.total + curr.count;
          return acc;
        case 'FNA':
          acc.FNA = acc.FNA + curr.count;
          acc.total = acc.total + curr.count;
          return acc;
        case 'CFF':
          acc.CFF = acc.CFF + curr.count;
          acc.total = acc.total + curr.count;
          return acc;
        case 'QUICK_SI':
          acc.QUICK_SI = acc.QUICK_SI + curr.count;
          acc.total = acc.total + curr.count;
          return acc;
        case 'IN_APP':
          acc.IN_APP = acc.IN_APP + curr.count;
          acc.total = acc.total + curr.count;
          return acc;
        default:
          return acc;
      }
    }, defaultCountMap) ?? defaultCountMap;

  return totalProposalCount;
}
