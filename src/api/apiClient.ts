import Axios, { GenericAbortSignal } from 'axios';
const axios = Axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000',
});

export type ApiClientConfig = {
  signal?: GenericAbortSignal;
  params?: any;
  headers?: any;
};

const apiClient = {
  get: <ResponseType = any>(route: string, config: ApiClientConfig = {}) =>
    axios.get<ResponseType>(route, {
      signal: config?.signal,
      params: config?.params,
      headers: config?.headers,
    }),
  post: <ResponseType = any>(
    route: string,
    data: any,
    config?: ApiClientConfig,
  ) => axios.post<ResponseType>(route, data, { signal: config?.signal }),
  put: <ResponseType = any>(
    route: string,
    data: any,
    config?: ApiClientConfig,
  ) => axios.put<ResponseType>(route, data, { signal: config?.signal }),
  patch: <ResponseType = any>(
    route: string,
    data: any,
    config?: ApiClientConfig,
  ) => axios.patch<ResponseType>(route, data, { signal: config?.signal }),
};

export default apiClient;
