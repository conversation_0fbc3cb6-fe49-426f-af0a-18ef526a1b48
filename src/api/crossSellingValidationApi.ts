import { cubeClient } from './cubeClient';

const endpoints = {
  crossSellingValidation: '/exp/api/application-validation/cross-selling',
};

export type CrossSellingValidationRequest = {
  caseId: string;
};

export type CrossSellingValidationResponse = {
  allowToProceed: boolean;
  message: string;
  flag: 'Y' | 'N';
  originalCrossSellingFlag: 'Y' | 'N' | 'O';
};

export const crossSellingValidationApi = async (
  payload: CrossSellingValidationRequest,
) => {
  const data = await cubeClient.post<
    CrossSellingValidationRequest,
    CrossSellingValidationResponse
  >(endpoints.crossSellingValidation, payload);
  return data;
};
