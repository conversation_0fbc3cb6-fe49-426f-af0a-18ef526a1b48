import { LeadTrackingDetails, RequestDataType } from 'types/leadTracking';
import { cubeClient } from './cubeClient';

const SALES_ACTIVITIES_END_POINT = '/proc/lead/lead-tracking';
const SALES_ACTIVITIES_FILTER_END_POINT = '/proc/lead/lead-tracking/filter';

export async function getLeadTracking() {
  // agentId: string | null,
  // isRestful: boolean,
  // auth: string | null,
  return await cubeClient.get<LeadTrackingDetails>(
    `${SALES_ACTIVITIES_END_POINT}`,
    // {
    //   headers: {
    //     'x-agent-id': agentId,
    //     Authorization: 'Bearer' + auth,
    //     'is-restful': isRestful,
    //   },
    // },
  );
}

// yyyy-MM-dd
export async function getLeadTrackingFilter(fromDate: string, toDate: string) {
  return await cubeClient.get<LeadTrackingDetails>(
    `${SALES_ACTIVITIES_FILTER_END_POINT}`,
    {
      params: {
        fromDate,
        toDate,
      },
    },
  );
}

export async function postLeadTracking(
  agentId: string | null,
  requestData: RequestDataType,
) {
  return await cubeClient.post<RequestDataType, string>(
    `${SALES_ACTIVITIES_END_POINT}`,
    requestData,
    {
      headers: {
        'x-agent-id': agentId,
        // Authorization: 'Bearer' + auth,
        // 'Content-Type': 'application/json',
        // 'is-restful': isRestful,
      },
    },
  );
}
