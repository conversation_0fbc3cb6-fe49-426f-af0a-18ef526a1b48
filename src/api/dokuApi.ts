import { cubeClient } from './cubeClient';
import { CASE_API } from './caseApi';
import useBoundStore from 'hooks/useBoundStore';

const endpoints = {
  getPaymentUrl: `${CASE_API}/payment-gateway`,
};

export interface PaymentLinkResponse {
  url: string;
  tokenId: string;
  expiredDate: string;
}

export interface PaymentResultResponse {
  acquirer: {
    id: string;
  };
  order: {
    invoiceNumber: string;
    amount: number;
  };
  customer: {
    name: string;
    email: string;
  };
  transaction: {
    type: string;
    status: string;
    date: string;
    originalRequestId: string;
  };
  service: {
    id: string;
  };
  cardPayment: {
    maskedCardNumber: string;
    approvalCode: string;
    responseCode: string;
    responseMessage: string;
    issuer: string;
    identifier: {
      name: string;
      value: string;
    }[];
    brand: string;
    authenticationId: string;
    threeDSecureStatus: string;
  };
  channel: {
    id: string;
    name: string;
  };
  verification: {
    status: string;
    reason: string;
  };
}

export const getPaymentUrl = async (payload: {
  caseId: string;
  callbackRoute: string;
  callbackUrl?: string; // This will override the callbackRoute if provided
}) => {
  const data = await cubeClient.post<
    {
      caseId: string;
      callbackRoute: string;
      callbackUrl?: string;
    },
    PaymentLinkResponse
  >(
    endpoints.getPaymentUrl,
    {
      caseId: payload.caseId,
      callbackRoute: payload.callbackRoute,
      callbackUrl: payload.callbackUrl,
    },
    {
      headers: {
        Authorization:
          'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken,
      },
    },
  );
  return data;
};
