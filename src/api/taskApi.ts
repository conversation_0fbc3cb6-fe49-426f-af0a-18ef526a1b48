import {
  CompleteTaskRequest,
  GetTasksResponse,
  Task,
  GetTasksCount,
} from 'types';
import { cubeClient } from './cubeClient';

const ENDPOINT = '/proc/task';

export async function getTasks() {
  return await cubeClient.get<GetTasksResponse>(ENDPOINT, {});
}

export async function getTasksCount() {
  return await cubeClient.get<GetTasksCount>(`${ENDPOINT}/count`, {});
}

export async function completeTaskById(taskId: string | number) {
  return await cubeClient.patch<CompleteTaskRequest, Task>(
    `${ENDPOINT}/${taskId}`,
    {
      isCompleted: true,
    },
  );
}
