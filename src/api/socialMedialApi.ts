import {
  FacebookPage,
  SocialMarketingPost,
} from 'features/socialMarketing/types';
import { cubeClient } from './cubeClient';

export type VerifySocialMediaTokenResponse = {
  agentId: string;
  expiredAt: number;
  facebookPageId: string;
};

type PollStatusResponse =
  | 'pending'
  | {
      agentId: string;
      success: boolean;
      accessToken: string;
      error?: null | string;
    };

export type GetStatusPostVideoToFacebookResponse = {
  agentPost: SocialMarketingPost;
  status: string;
  pageId: string;
  postId: string;
};

export const getFacebookPage = () => {
  return cubeClient.get<FacebookPage[]>(
    'proc/agent/social-media/facebook/pages',
  );
};

export const connectFacebookPage = (pageId: string) => {
  return cubeClient.post<{ pageId: string }, unknown>(
    'proc/agent/social-media/facebook/page/connect',
    {
      pageId,
    },
  );
};

export const verifyFacebookToken = () => {
  return cubeClient.get<VerifySocialMediaTokenResponse>(
    '/proc/agent/social-media/facebook',
  );
};

export const initiateFacebookPoll = () => {
  return cubeClient.postDirectPayLoad<unknown, string>(
    '/proc/agent/social-media/auth/facebook/initiate',
    {},
  );
};

export const checkFacebookPollStatus = (sessionId: string) => {
  return cubeClient.get<PollStatusResponse>(
    `/proc/agent/social-media/auth/facebook/check/${sessionId}`,
  );
};

export const postToFacebook = (postId: string) => {
  return cubeClient.postDirectPayLoad<unknown, SocialMarketingPost>(
    `/proc/agent/post/${postId}/share/facebook`,
    {},
  );
};

export const initiatePostVideoToFacebook = (postId: string) => {
  return cubeClient.postDirectPayLoad(
    `/proc/agent/post/${postId}/share/facebook/video/initiate`,
    {},
  );
};

export const getStatusPostVideoToFacebook = (postId: string) => {
  return cubeClient.get<GetStatusPostVideoToFacebookResponse>(
    `/proc/agent/post/${postId}/share/facebook/video/status`,
  );
};

export const verifyInstagramToken = () => {
  return cubeClient.get<VerifySocialMediaTokenResponse>(
    '/proc/agent/social-media/instagram',
  );
};

export const initiateInstagramPoll = () => {
  return cubeClient.post<unknown, string>(
    '/proc/agent/social-media/auth/instagram/initiate',
    {},
  );
};

export const checkInstagramPollStatus = (sessionId: string) => {
  return cubeClient.get<PollStatusResponse>(
    `/proc/agent/social-media/auth/instagram/check/${sessionId}`,
  );
};

export const initiatePostToInstagram = (postId: string) => {
  return cubeClient.postDirectPayLoad(
    `/proc/agent/post/${postId}/share/instagram/initiate`,
    {},
  );
};

export const getStatusPostToInstagram = (postId: string) => {
  return cubeClient.get<GetStatusPostVideoToFacebookResponse>(
    `/proc/agent/post/${postId}/share/instagram/status`,
  );
};

export const initiateLinkedInPoll = () => {
  return cubeClient.postDirectPayLoad<unknown, string>(
    '/proc/agent/social-media/auth/linkedin/initiate',
    {},
  );
};

/**
 * Long polling waits up to 60 seconds for a result - Much more efficient than short polling!
 * @param sessionId {string}
 * @returns {Promise<PollStatusResponse>}
 */
export const checkLinkedInLongPollingStatus = (sessionId: string) => {
  return cubeClient.get<PollStatusResponse>(
    `/proc/agent/social-media/auth/linkedin/status/${sessionId}`,
  );
};

/**
 * **Poll every 10 seconds** until you get a completed result
 * @param sessionId {string}
 * @returns {Promise<PollStatusResponse>}
 */
export const checkLinkedInShortPollingStatus = (sessionId: string) => {
  return cubeClient.get<PollStatusResponse>(
    `/proc/agent/social-media/auth/linkedin/check/${sessionId}`,
  );
};

export const verifyLinkedInToken = () => {
  return cubeClient.get<VerifySocialMediaTokenResponse>(
    '/proc/agent/social-media/linkedin',
  );
};
