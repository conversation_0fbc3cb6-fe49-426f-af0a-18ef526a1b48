import { LocaleHeader } from 'types/localeHeader';
import { CFFPdfResponse } from 'types/pdfResponse';
import { cubeClient } from './cubeClient';

export const generateCFFPdf = async (
  body: unknown,
  localeHeader: LocaleHeader,
) => {
  const response = await cubeClient.post<unknown, CFFPdfResponse>(
    'exp/api/pdf/cff',
    {
      case: body,
    },
    {
      headers: {
        ...localeHeader,
      },
    },
  );
  return response?.report;
};
