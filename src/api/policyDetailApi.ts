import * as FileSystem from 'expo-file-system';
import { PolicyDomains } from 'types';
import {
  Account,
  ApplicationTrackerInfo,
  Fund,
  GetDocumentsListParams,
  History,
  PendingDocuments,
  PolicyDetails,
  PolicyDetailsPolicy,
  Product,
  Role,
  UploadPendingDocumentBody,
  UploadPendingDocumentResponse,
  UploadV2PendingDocumentBody,
} from 'types/policy';
import { baseUrl, country } from 'utils/context';
import { cubeClient } from './cubeClient';

const POLICY_NEW_BUSINESS_DETAILS_ENDPOINT = '/proc/policy/details/';
const APPLICATION_TRACKER_INFO_ENDPOINT =
  '/proc/policy/details/:policyNo/tracking';
const POLICY_ENDPOINT = '/proc/policy/details/:id/policy';
const PRODUCT_ENDPOINT = '/proc/policy/details/:id/product';
const FUND_ENDPOINT = '/proc/policy/details/:id/fund';
const HISTORY_ENDPOINT = '/proc/policy/details/:id/history';
const ROLE_ENDPOINT = '/proc/policy/details/:id/role';
const ACCOUNT_ENDPOINT = '/proc/policy/details/:id/account';
const PENDING_DOCUMENTS_ENDPOINT = '/proc/policy/details/:id/pending-documents';
const UPLOAD_PENDING_DOCUMENTS_ENDPOINT = '/proc/policy/upload-document';
const UPLOAD_PENDING_DOCUMENTS_ENDPOINT_V2 =
  '/proc/policy/batch-upload-document';
const V2_UPLOAD_PENDING_DOCUMENTS_ENDPOINT = '/proc/upload/memo/upload'; //to CUBE DB only add by Hardy
const GET_DOCUMENTS_LIST_ENDPOINT = '/proc/upload/memo';
const DELETE_DOCUMENT_FOR_PENDING_ENDPOINT = '/proc/upload/memo/:id';
export const POLICY_PACK_ENDPOINT = '/proc/policy/documents/:id/download';

export async function getPolicyDetail({
  policyNo,
  policyDomain,
}: {
  policyNo: string;
  policyDomain: PolicyDomains;
}) {
  return await cubeClient.get<PolicyDetails>(
    country === 'ph'
      ? POLICY_ENDPOINT.replace(':id', policyNo)
      : `${POLICY_NEW_BUSINESS_DETAILS_ENDPOINT}${policyNo}`,
    {
      params: {
        domain: policyDomain,
      },
    },
  );
}

export async function getApplicationTrackerInfo(policyNo: string) {
  return await cubeClient.get<ApplicationTrackerInfo>(
    APPLICATION_TRACKER_INFO_ENDPOINT.replace(':policyNo', policyNo),
  );
}

export async function getPolicy(policyNo: string) {
  return await cubeClient.get<PolicyDetailsPolicy>(
    POLICY_ENDPOINT.replace(':id', policyNo),
  );
}

export async function getProduct(policyNo: string) {
  return await cubeClient.get<Product[]>(
    PRODUCT_ENDPOINT.replace(':id', policyNo),
  );
}

export async function getFund(policyNo: string) {
  return await cubeClient.get<Fund[]>(FUND_ENDPOINT.replace(':id', policyNo));
}

export async function getHistory(policyNo: string) {
  return await cubeClient.get<History>(
    HISTORY_ENDPOINT.replace(':id', policyNo),
  );
}

export async function getRole(policyNo: string) {
  return await cubeClient.get<Role[]>(ROLE_ENDPOINT.replace(':id', policyNo));
}

export async function getAccount(policyNo: string) {
  return await cubeClient.get<Account>(
    ACCOUNT_ENDPOINT.replace(':id', policyNo),
  );
}

export async function getPendingDocuments(policyNo: string) {
  return await cubeClient.get<PendingDocuments[]>(
    PENDING_DOCUMENTS_ENDPOINT.replace(':id', policyNo),
  );
}

export async function getDocumentsList(params: GetDocumentsListParams) {
  return await cubeClient.get<GetDocumentsListResponse>(
    GET_DOCUMENTS_LIST_ENDPOINT,
    {
      params,
    },
  );
} //add by Hardy

export type UploadedDocument = {
  id: string;
  fileName: string;
  originalFilename: string;
  fileSize: number;
  fileType: null | string;
  pendMemo: string;
  documentClass: string;
  documentId: string;
  createdAt: number;
};

export type GetDocumentsListResponse = Array<UploadedDocument>;

export async function uploadPendingDocument(
  requestData: UploadPendingDocumentBody,
  onProgress?: (progress: number) => void,
): Promise<UploadPendingDocumentResponse> {
  const data = await cubeClient.post<
    UploadPendingDocumentBody,
    UploadPendingDocumentResponse
  >(UPLOAD_PENDING_DOCUMENTS_ENDPOINT, requestData, {
    onUploadProgress: progressEvent => {
      if (
        progressEvent.loaded !== undefined &&
        progressEvent.total !== undefined
      ) {
        onProgress &&
          onProgress(Math.min(progressEvent.loaded / progressEvent.total, 1));
      }
    },
  });
  return data;
}

export type SubmitUploadedV2PendingDocumentBody = GetDocumentsListParams;
type SubmitUploadedV2PendingDocumentResponse = unknown;

export async function submitUploadedV2PendingDocument(
  body: SubmitUploadedV2PendingDocumentBody,
) {
  return await cubeClient.post<
    SubmitUploadedV2PendingDocumentBody,
    SubmitUploadedV2PendingDocumentResponse
  >(UPLOAD_PENDING_DOCUMENTS_ENDPOINT_V2, body);
}

export function deletePendingDocument({ id }: { id: string }) {
  return cubeClient.delete(
    DELETE_DOCUMENT_FOR_PENDING_ENDPOINT.replace(':id', id),
  );
}

export async function uploadV2PendingDocument(
  requestData: UploadV2PendingDocumentBody,
  onProgress?: (progress: number) => void,
): Promise<UploadPendingDocumentResponse> {
  const formData = new FormData();
  formData.append('files', requestData.files);
  formData.append('pendMemo', requestData.pendMemo);
  formData.append('documentClass', requestData.documentClass);
  formData.append('documentId', requestData.documentId);
  formData.append('policyNumber', requestData.policyNumber);

  const data = await cubeClient.postDirectPayLoad<
    FormData,
    UploadPendingDocumentResponse
  >(V2_UPLOAD_PENDING_DOCUMENTS_ENDPOINT, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: progressEvent => {
      if (
        progressEvent.loaded !== undefined &&
        progressEvent.total !== undefined
      ) {
        onProgress &&
          onProgress(Math.min(progressEvent.loaded / progressEvent.total, 1));
      }
    },
  });
  return data;
}

export async function uploadV2PendingDocumentByFileSystems({
  requestData,
  accessToken,
}: {
  requestData: UploadV2PendingDocumentBody;
  accessToken: string | null;
}) {
  const destinationUrl = `${baseUrl}/api-gateway${V2_UPLOAD_PENDING_DOCUMENTS_ENDPOINT}`;

  const { files, ...parameters } = requestData;
  const options = {
    fieldName: 'files',
    uploadType: FileSystem.FileSystemUploadType.MULTIPART,
    headers: {
      Authorization: 'Bearer ' + accessToken,
      'Content-Type': 'multipart/form-data',
    },
    sessionType: FileSystem.FileSystemSessionType.FOREGROUND,
    parameters,
  };

  const imgPath = requestData.files?.uri;

  return await FileSystem.uploadAsync(destinationUrl, imgPath, options);
}
