import {
  BlacklistCheckingResponse,
  DukcapilRequestBody,
  DukcapilValidationResponse,
} from 'types/dukcapil';
import { LocaleHeader } from 'types/localeHeader';
import { ApplicationPdfResponse } from 'types/pdfResponse';
import {
  BlacklistInfoValidationRequest,
  PolicyOwnerValidationRequest,
  PolicyOwnerValidationResponse,
} from 'types/validation';
import { cubeClient } from './cubeClient';

type ExclusiveLetterType = 'HCB' | 'CI';

export const generateApplicationPdf = async (
  body: unknown,
  localeHeader: LocaleHeader,
) => {
  const response = await cubeClient.post<unknown, ApplicationPdfResponse>(
    '/exp/api/pdf/app',
    {
      case: body,
    },
    {
      headers: {
        ...localeHeader,
      },
    },
  );

  return (
    response?.report?.pdfFiles?.UNPROTECTED_PDF ??
    response?.pdfFiles?.UNPROTECTED_PDF
  );
};

export const sendRSEmailConfirmation = (
  caseId: string,
  exclusiveLetter?: ExclusiveLetterType,
) => {
  return cubeClient.post(
    `/exp/case/send-remote-selling-confirmation/${caseId}`,
    {
      exclusiveLetter,
    },
  );
};

export const getInitialPremium = (caseId: string) => {
  return cubeClient.get<{ currency: 'string'; initialPremium: number }>(
    `/exp/case/${caseId}/payment-info`,
  );
};

// IDN Eapp validation with local service
export const getDukcapilValidation = (body: DukcapilRequestBody) => {
  const { type, ...payload } = body;
  return cubeClient.post<unknown, DukcapilValidationResponse>(
    '/exp/api/nric/validate',
    payload,
  );
};

// IDN Eapp blacklist agent checking with local service
export const getBlacklistIdValidation = (body: { idNumber: string }) => {
  return cubeClient.post<unknown, BlacklistCheckingResponse>(
    '/exp/api/nric/blacklist-check',
    body,
  );
};

// validate with local IDN service to check if entered data has been used before
export const validatePolicyOwner = (body: PolicyOwnerValidationRequest) => {
  return cubeClient.post<unknown, PolicyOwnerValidationResponse>(
    '/exp/api/application-validation/policy-holder',
    body,
  );
};

export const getBlackListInfoValidation = (
  body: BlacklistInfoValidationRequest,
) => {
  return cubeClient.post<unknown, BlacklistCheckingResponse>(
    '/exp/api/nric/validate-blacklist-info',
    body,
  );
};
