import { EntityInsured, EntityInsuredParams } from 'types/entityInsured';
import { cubeClient } from './cubeClient';

const getInsuredApi = (leadId: string) => `/proc/lead/${leadId}/insured`;

export async function getInsuredList(leadId: string) {
  return await cubeClient.get<EntityInsured[]>(getInsuredApi(leadId));
}

export async function createInsured(
  leadId: string,
  requestData: EntityInsuredParams,
) {
  return await cubeClient.post<EntityInsuredParams, string>(
    getInsuredApi(leadId),
    normalizeData(requestData),
  );
}

export async function updateInsured(
  leadId: string,
  insuredId: string,
  requestData: EntityInsuredParams,
) {
  return await cubeClient.put<EntityInsuredParams, string>(
    `${getInsuredApi(leadId)}/${insuredId}`,
    requestData,
  );
}

// remove the field if it is empty
const normalizeData = (data: EntityInsuredParams): EntityInsuredParams => {
  const { nameExtension, middleName, ...rest } = data;
  const normalizedData = { ...rest } as EntityInsuredParams;
  if (nameExtension) {
    normalizedData.nameExtension = nameExtension;
  }
  if (middleName) {
    normalizedData.middleName = middleName;
  }
  return normalizedData;
};
