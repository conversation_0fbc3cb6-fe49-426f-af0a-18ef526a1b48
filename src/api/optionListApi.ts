import { LocaleHeader } from 'types/localeHeader';
import { OptionList } from 'types/optionList';
import { cubeClient } from './cubeClient';
import { LanguagesKeys } from 'utils/translation';

type OptionListLanguageKeys = Record<LanguagesKeys, string>

export async function getOptionList({
  channel,
  ...localeHeaders
}: {
  channel: string;
} & LocaleHeader): Promise<OptionList> {
  const data = await cubeClient.post<
    { channel: string },
    OptionList<OptionListLanguageKeys>
  >(
    '/exp/api/query/optionList',
    {
      channel,
    },
    {
      headers: {
        ...localeHeaders,
      },
    },
  );
  const result: any = {}; // eslint-disable-line @typescript-eslint/no-explicit-any
  Object.keys(data).forEach(key => {
    result[key as keyof OptionList] = {
      ...data[key as keyof OptionList],
      options: data[key as keyof OptionList].options.map(option => ({
        ...option,
        label:
          (option.label as OptionListLanguageKeys)[
            localeHeaders['Accept-Language'] as LanguagesKeys
          ] || option.label.en,
      })),
    };
  });
  return result as OptionList;
}
