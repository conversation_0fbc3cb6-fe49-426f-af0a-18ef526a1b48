import { cubeClient } from './cubeClient';
import { AgentProfile, CubeResponse } from 'types';
import * as FileSystem from 'expo-file-system';
import { baseUrl, country } from 'utils/context';
import { t } from 'i18next';

const AGENT_PROFILE_API = '/proc/agent';

export async function getAgentProfileById(agentId: string) {
  return await cubeClient.get<AgentProfile>(
    `${AGENT_PROFILE_API}/${agentId}?photo=1`,
    {
      headers:
        country === 'my' || country === 'ib'
          ? {
              'x-agent-id': agentId,
            }
          : {},
    },
  );
}

export async function uploadAgentPhoto({
  imgPath,
  accessToken,
  agentId,
}: {
  imgPath: string;
  agentId: string | null;
  accessToken: string | null;
}) {
  return await FileSystem.uploadAsync(
    `${baseUrl}/api-gateway${AGENT_PROFILE_API}/photo/${agentId}`,
    imgPath,
    {
      fieldName: 'photo',
      uploadType: FileSystem.FileSystemUploadType.MULTIPART,
      headers: {
        Authorization: 'Bearer ' + accessToken,
        'Content-Type': 'multipart/form-data',
      },
      sessionType: FileSystem.FileSystemSessionType.FOREGROUND,
    },
  );
}

let uploadTask: FileSystem.UploadTask;

export async function uploadAgentVoice({
  voicePath,
  accessToken,
  agentId,
}: {
  voicePath: string;
  agentId: string | null;
  accessToken: string | null;
}) {
  uploadTask = FileSystem.createUploadTask(
    `${baseUrl}/api-gateway${AGENT_PROFILE_API}/voice/${agentId}`,
    voicePath,
    {
      fieldName: 'voice',
      uploadType: FileSystem.FileSystemUploadType.MULTIPART,
      headers: {
        Authorization: 'Bearer ' + accessToken,
        'Is-Restful': 'true',
      },
      sessionType: FileSystem.FileSystemSessionType.FOREGROUND,
    },
  );

  const uploadResult = await uploadTask.uploadAsync();

  // Check if the status does not starts with '2'
  if (/^[2]/.test(uploadResult?.status?.toString() ?? '') == false) {
    throw new Error(t('backendError'));
  }

  const responseBody = JSON.parse(
    uploadResult?.body ?? '',
  ) as CubeResponse<unknown>;

  // Check if the status starts with '4' or '5'
  if (/^[45]/.test(responseBody?.status ?? '')) {
    const msg =
      responseBody.messageList?.find(e => Boolean(e.content))?.content ??
      'unknown issues';
    throw new Error(msg);
  }
  return responseBody;
}

export async function cancelUploadVoicePrint() {
  if (uploadTask) {
    try {
      await uploadTask.cancelAsync();
      console.log('Upload cancelled.');
    } catch (error) {
      console.error('Failed to cancel upload:', error);
    }
  } else {
    console.log('No active upload to cancel.');
  }
}

export async function deleteAgentProfile(agentId: string) {
  return await cubeClient.delete<AgentProfile>(
    `${AGENT_PROFILE_API}/photo/${agentId}`,
  );
}
