import { DocumentType } from 'types/document';
import { cubeClient } from './cubeClient';
import {
  Case,
  Fna,
  CaseStatus,
  Application,
  FnaProductSelection,
  CFF,
} from 'types/case';
import * as FileSystem from 'expo-file-system';
import { Party } from 'types/party';
import {
  SavedProposalFiltersResponse,
  SavedProposalPaginationResponse,
} from 'types/proposal';
import { Quotation } from 'types/quotation';
import { baseUrl } from 'utils/context';
import { CubeResponse } from 'types';
import { t } from 'i18next';

export const CASE_API = '/exp/case';
export const QUERY_CASE_BY_IDENTITY = `${CASE_API}/by-identity`;

export type SavedProposalsQueryParams = {
  offset?: number;
  limit?: number;
  status?: CaseStatus[];
  clientTypes?: string[];
  start?: string;
  end?: string;
  sort_by?: string;
  q?: string;
  leadId?: string;
  downlineOnly?: boolean;
};

export type IdentityQueryParams = CaseQueryParams & {
  firstName: string;
  lastName?: string;
  dob?: string;
  gender?: string;
  phone?: string;
  email?: string;
  id?: string;
};

export type CaseQueryParams = {
  status?: CaseStatus;
};

export type PopulateApplicationBody = { target: string; source: string };

export async function getCaseById(caseId: string) {
  return await cubeClient.get<Case>(`${CASE_API}/${caseId}`);
}

export async function createCase(requestData: Pick<Case, 'agent'>) {
  return await cubeClient.post<Pick<Case, 'agent'>, string>(
    `${CASE_API}/`,
    requestData,
  );
}

export async function createFNA(caseId: string, requestData: Fna) {
  return await cubeClient.post<Fna, string>(
    `${CASE_API}/${caseId}/fna`,
    requestData,
  );
}

export async function createParty(caseId: string, requestData: Party) {
  return await cubeClient.post<Party, string>(
    `${CASE_API}/${caseId}/party`,
    requestData,
  );
}

export async function updateParty(caseId: string, requestData: Party) {
  return await cubeClient.put<Party, string>(
    `${CASE_API}/${caseId}/party`,
    requestData,
  );
}

export async function getQuotationById(caseId: string, quotationId: string) {
  return await cubeClient.get<Quotation>(
    `${CASE_API}/${caseId}/quotation/${quotationId}`,
  );
}

export async function getQuotationsByCaseId(caseId: string) {
  return await cubeClient.get<Quotation>(`${CASE_API}/${caseId}/quotation`);
}

export async function getSavedProposals(
  queryParams?: SavedProposalsQueryParams,
) {
  return await cubeClient.get<SavedProposalPaginationResponse>(
    `${CASE_API}/saved`,
    {
      params: {
        ...queryParams,
        status: queryParams?.status?.join(','),
        ...(queryParams?.clientTypes &&
          queryParams?.clientTypes?.length > 0 && {
            clientTypes: queryParams?.clientTypes?.join(','),
          }),
      },
    },
  );
}

export async function getCasesByIdentity(params?: IdentityQueryParams) {
  return await cubeClient.get<Case[]>(QUERY_CASE_BY_IDENTITY, {
    params,
  });
}

export async function getSavedProposalsFilters(
  props: SavedProposalsQueryParams,
) {
  return await cubeClient.get<SavedProposalFiltersResponse>(
    `${CASE_API}/saved/filters`,
    {
      // params: { downlineOnly: Boolean(isDownlineOnly) },
      params: { ...props, clientTypes: props.clientTypes?.join(',') },
    },
  );
}

export async function getOwbModel(caseId: string) {
  return await cubeClient.get<object>(`${CASE_API}/${caseId}`, {
    params: { model: 'owb' },
  });
}

export async function createApplication(
  caseId: string,
  requestData: Application,
) {
  return await cubeClient.post<Application, string>(
    `${CASE_API}/${caseId}/application`,
    requestData,
  );
}

export async function populateApplication(body: PopulateApplicationBody) {
  return await cubeClient.post<PopulateApplicationBody, Case>(
    `${CASE_API}/prepopulate`,
    body,
  );
}

export async function deleteParty(caseId: string, partyId: string) {
  return cubeClient.delete<string>(`${CASE_API}/${caseId}/party/${partyId}`);
}

export type AddDocumentBody = {
  partyId: string;
  fileName: string;
  filePath: string;
  docType: string;
  fromOcr?: boolean;
};
export async function addDocument(caseId: string, data: AddDocumentBody) {
  return cubeClient.post<AddDocumentBody, unknown>(
    `${CASE_API}/${caseId}/file`,
    data,
  );
}

export async function deleteDocument(caseId: string, fileName: string) {
  return cubeClient.delete<string>(`${CASE_API}/${caseId}/file/${fileName}`);
}

export enum ApplicationProgress {
  STARTED = 'STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}
export async function getApplicationProgress(caseId: string) {
  return cubeClient
    .get<{ status: { [key: string]: ApplicationProgress } }>(
      `${CASE_API}/${caseId}/app/progress`,
    )
    .then(data => data.status);
}
export async function updateApplicationProgress(
  caseId: string,
  status: {
    [key: string]: ApplicationProgress;
  },
) {
  return cubeClient.post<
    { status: { [key: string]: ApplicationProgress } },
    string
  >(`${CASE_API}/${caseId}/app/progress`, { status });
}

export type SubmissionResponse = {
  appStatus?: string; // only for rookie submission
  success: boolean | string;
  type: string | null;
  warnData: string | null;
  resultData: {
    integrationCode: string;
    integrationSessionId: string | null;
    regionCode: string;
    responseData: {
      returnCode: string;
      returnMsg: string;
      caseNo: string;
      businessNo: string;
    }[];
    callBackEntry: string | null;
    companyCode: string | null;
    forwardRequestTime: string | null;
    skip: string | null;
    abort: string | null;
  };
  promptMessages: (string | null | { metaData?: { originalMsg?: string } })[];
};

export async function submitApplication(caseId: string) {
  return cubeClient.post<unknown, SubmissionResponse>(
    `${CASE_API}/${caseId}/submit`,
    null,
  );
}
export async function submitToOwb(owbModel: unknown) {
  return cubeClient.post<unknown, SubmissionResponse>(`/exp/owb/submit`, {
    case: owbModel,
    finalCall: true,
  });
}

export type UpdateCaseBody = {
  havePayer?: boolean;
  haveBeneficialOwner?: boolean;
  isRemoteSelling?: boolean;
  havePolicyControlAuthority?: boolean;
  havePayorRole?: boolean;
  geoTransactions?: {
    createdDT: string | Date;
    action: string;
    latitude: number;
    longitude: number;
  }[];
};
export async function updateCase({
  caseId,
  ...payload
}: {
  caseId: string;
} & UpdateCaseBody) {
  return cubeClient.patch<UpdateCaseBody, Case>(
    `${CASE_API}/${caseId}`,
    payload,
  );
}

export async function saveProductRecommendation({
  caseId,
  data,
}: {
  caseId: string;
  data: FnaProductSelection;
}) {
  return cubeClient.post<FnaProductSelection, Fna>(
    `${CASE_API}/${caseId}/fna/select-product`,
    data,
  );
}

export async function getCaseCount() {
  return cubeClient.get<Array<CaseCountByStatus>>(`${CASE_API}/count`);
}

type CaseCountByStatus = {
  status:
    | 'UNKNOWN'
    | 'FULL_SI'
    | 'FNA'
    | 'IN_APP'
    | 'QUICK_SI'
    | 'COVERAGE'
    | 'APP_SUBMITTED';
  count: number;
};

export async function createCFF(caseId: string, requestData: Partial<CFF>) {
  return await cubeClient.post<Partial<CFF>, string>(
    `${CASE_API}/${caseId}/cff`,
    requestData,
  );
}

export type Approve = {
  caseId: string;
  approve: boolean;
  rejectReason: string;
};

type ApprovalResponse = {
  success: boolean;
  message: string;
  caseId: string;
  action: string;
};

export async function updateCaseApprove({
  approveData,
}: {
  approveData: Approve[];
}) {
  return cubeClient.patch<Approve[], Array<ApprovalResponse>>(
    `${CASE_API}/approve`,
    approveData,
  );
}

export type CaseDocumentsResponse = {
  partyId: string;
  configName: string;
  docList: {
    type: DocumentType;
    labelKey: DocumentType;
    files: {
      fileName: string;
      filePath: string;
      partyId: string;
      docType: string;
      fromOcr: boolean;
    }[];
    required: {
      defaultValue: boolean;
    };
    hidden: {
      defaultValue: boolean;
    };
    multiple: {
      defaultValue: boolean;
    };
    limit: {
      min: number | null;
      max: number | null;
    } | null;
  }[];
  showF2FToggle: boolean;
  name: {
    title: string;
    firstName: string;
    lastName?: string;
    middleName?: string;
    extensionName?: string;
    fullName: string;
  };
}[];

export async function getCaseDocuments(caseId: string) {
  return cubeClient.get<CaseDocumentsResponse>(
    `${CASE_API}/${caseId}/document`,
  );
}

let uploadTask: FileSystem.UploadTask;

export async function uploadPartyVoicePrint(
  caseId: string,
  partyId: string,
  voicePath: string,
  accessToken: string | null,
) {
  uploadTask = FileSystem.createUploadTask(
    `${baseUrl}/api-gateway${CASE_API}/${caseId}/party/${partyId}/voice`,
    voicePath,
    {
      fieldName: 'voice',
      uploadType: FileSystem.FileSystemUploadType.MULTIPART,
      headers: {
        Authorization: 'Bearer ' + accessToken,
        'Content-Type': 'multipart/form-data',
        'Is-Restful': 'true',
      },
      sessionType: FileSystem.FileSystemSessionType.FOREGROUND,
    },
  );

  const uploadResult = await uploadTask.uploadAsync();

  // Check if the status does not starts with '2'
  if (/^[2]/.test(uploadResult?.status?.toString() ?? '') == false) {
    throw new Error(t('backendError'));
  }

  const responseBody = JSON.parse(
    uploadResult?.body ?? '',
  ) as CubeResponse<unknown>;

  // Check if the status starts with '4' or '5'
  if (/^[45]/.test(responseBody?.status ?? '')) {
    const msg =
      responseBody.messageList?.find(e => Boolean(e.content))?.content ??
      'unknown issues';
    throw new Error(msg);
  }
  return responseBody;
}

export async function uploadAgentVoicePrintForCase({
  caseId,
  voicePath,
  accessToken,
}: {
  caseId: string;
  voicePath: string;
  accessToken: string | null;
}) {
  uploadTask = FileSystem.createUploadTask(
    `${baseUrl}/api-gateway${CASE_API}/${caseId}/agent/voice`,
    voicePath,
    {
      fieldName: 'voice',
      uploadType: FileSystem.FileSystemUploadType.MULTIPART,
      headers: {
        Authorization: 'Bearer ' + accessToken,
        'Content-Type': 'multipart/form-data',
        'Is-Restful': 'true',
      },
      sessionType: FileSystem.FileSystemSessionType.FOREGROUND,
    },
  );

  const uploadResult = await uploadTask.uploadAsync();

  // Check if the status does not starts with '2'
  if (/^[2]/.test(uploadResult?.status?.toString() ?? '') == false) {
    throw new Error(t('backendError'));
  }

  const responseBody = JSON.parse(
    uploadResult?.body ?? '',
  ) as CubeResponse<unknown>;

  // Check if the status starts with '4' or '5'
  if (/^[45]/.test(responseBody?.status ?? '')) {
    const msg =
      responseBody.messageList?.find(e => Boolean(e.content))?.content ??
      'unknown issues';
    throw new Error(msg);
  }
  return responseBody;
}

export async function cancelUploadVoicePrint() {
  if (uploadTask) {
    try {
      await uploadTask.cancelAsync();
      console.log('Upload cancelled.');
    } catch (error) {
      console.error('Failed to cancel upload:', error);
    }
  } else {
    console.log('No active upload to cancel.');
  }
}
