import { UpdateCFF } from 'types/case';
import {
  CreateEntityRequest,
  CreateLeadRequest,
  GetLeadFiltersResponse,
  GetLeadsResponse,
  Lead,
  LeadStatus,
  LookupEntityRequest,
  LookupLeadRequest,
  UpdateLeadRequest,
} from 'types/lead';
import { cubeClient } from './cubeClient';
import useBoundStore from 'hooks/useBoundStore';
import { build, appVersion, country } from 'utils/context'; ///will remove if the real data response is ready
const ENDPOINT = '/proc/lead';

const CFFENDPOINT = '/exp/case/lead';

export type LeadApiFilter = Partial<Omit<Lead, 'id' | 'status'>> & {
  sortBy?: string;
  from?: Date;
  to?: Date;
  status?: Record<string, boolean>;
  source?: Record<string, boolean>;
  q?: string;
  query?: string;
  isIndividual?: boolean;
};

export const ITEMS_PER_PAGE = 9999;

export async function getLeads(
  filters?: LeadApiFilter,
  options?: {
    page?: number;
    headers?: Record<string, string>;
    signal?: any;
  },
) {
  const agentId = useBoundStore.getState().auth.agentCode;
  const curPage = options?.page ?? 1;

  const pageParams = options?.page
    ? {
        offset: (curPage - 1) * ITEMS_PER_PAGE,
        limit: ITEMS_PER_PAGE,
      }
    : {};

  const headers = options?.headers || {};

  return await cubeClient.get<GetLeadsResponse>(ENDPOINT, {
    params: {
      ...pageParams,
      ...filters,
    },
    signal: options?.signal,
    headers: {
      ...headers,
      'x-agent-id': agentId,
    },
  });
}

export async function getLeadTabFilters(
  filters?: { from?: Date; to?: Date; opportunityOnly?: boolean; q?: string },
  options?: {
    headers?: Record<string, string>;
  },
) {
  const agentId = useBoundStore.getState().auth.agentCode;
  const headers = options?.headers || {};

  return await cubeClient.get<GetLeadFiltersResponse>(ENDPOINT + '/filters', {
    params: {
      ...filters,
    },
    headers: {
      ...headers,
      'x-agent-id': agentId,
    },
  });
}

export async function createLead(
  data: CreateLeadRequest | CreateEntityRequest,
) {
  const agentId = useBoundStore.getState().auth.agentCode;

  const body = { ...data };

  return await cubeClient.post(ENDPOINT, body, {
    // TODO: remove it after api gateway ready
    headers: {
      'x-agent-id': agentId,
    },
  });
}

export async function updateLeadStatus(leadId: string, status: LeadStatus) {
  const agentId = useBoundStore.getState().auth.agentCode;
  const { data: id } = await cubeClient.post<
    { status: LeadStatus },
    { data: string }
  >(
    `ENDPOINT/${leadId}`,
    {
      status,
    },
    {
      // TODO: remove it after api gateway ready
      headers: {
        'x-agent-id': agentId,
      },
    },
  );
  return id;
}

export async function updateLeadCaseId(leadId: string, caseId: string) {
  const agentId = useBoundStore.getState().auth.agentCode;
  return await cubeClient.patch<string, null>(
    `${ENDPOINT}/${leadId}/case-id`,
    caseId,
    {
      // TODO: remove it after api gateway ready
      headers: {
        'x-agent-id': agentId,
      },
    },
  );
}

export async function getLeadByLeadId(leadId: string) {
  const agentId = useBoundStore.getState().auth.agentCode;

  const response = await cubeClient.get<Lead>(`${ENDPOINT}/${leadId}`, {
    headers: { 'x-agent-id': agentId },
  });

  return response;
}

export async function getLeadByCustomerId(customerId: string) {
  const agentId = useBoundStore.getState().auth.agentCode;

  const response = await cubeClient.get<GetLeadsResponse>(
    `${ENDPOINT}?customerId=${customerId}&sortBy=-createdAt`,
    {
      headers: { 'x-agent-id': agentId },
    },
  );

  return response;
}

export async function updateLead(
  leadId: string,
  updateLead: UpdateLeadRequest,
) {
  const agentId = useBoundStore.getState().auth.agentCode;

  const response = await cubeClient.put<UpdateLeadRequest, string>(
    `${ENDPOINT}/${leadId}`,
    updateLead,
    { headers: { 'x-agent-id': agentId } },
  );
  return response;
}

export async function lookupLead(
  data: LookupLeadRequest | LookupEntityRequest,
) {
  const agentId = useBoundStore.getState().auth.agentCode;
  const body = { ...data };

  const response = await cubeClient.post<
    LookupLeadRequest | LookupEntityRequest,
    Lead
  >(`${ENDPOINT}/lookup-leadprofile`, body, {
    headers: {
      'x-agent-id': agentId,
    },
  });
  return response;
}

export const queryLead = (query: string) => {
  return cubeClient.get<GetLeadsResponse>(`${ENDPOINT}?q=${query}`);
};

export const getCFFByLeadId = async (leadId: string) => {
  const response = await cubeClient.get<UpdateCFF>(
    `${CFFENDPOINT}/${leadId}/cff`,
  );
  return response;
};
