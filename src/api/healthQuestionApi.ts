import axios from 'axios';
import {
  HealthQuestion2,
  HealthQuestion3,
  LookupQuestionOptionsListParams,
  QuestionOptionsListParams,
  QuestionToolTipContent,
  QuestionToolTipParams,
} from 'types/healthQuestion';

import { cubeClient } from './cubeClient';
import { LocaleHeader } from 'types/localeHeader';
import useBoundStore from 'hooks/useBoundStore';
import { baseUrl } from 'utils/context';
import { escapeHookFormPath, rollbackHookFormPath } from 'utils';
import crashlytics from '@react-native-firebase/crashlytics';
import { countryModuleEAppConfig } from 'utils/config/module';
import { Case } from 'types/case';

interface HealthQuestionLog {
  response: unknown;
  functionName: string;
  functionParams: object;
  message?: string;
}

const endPoints = {
  generateHealthQuestion: '/exp/uwme/generateHealthQuestion',
  getHealthQuestion: (id: string) => `/exp/uwme/healthQuestion/${id}`,
  answerHealthQuestion: (id: string) => `/exp/uwme/answerHealthQuestion/${id}`,
  generateHealthQuestions: '/exp/uwme/generateHealthQuestions',
  helpText: '/exp/uwme/helptext',
  lookupQuestion: '/exp/uwme/lookupQuestionOption',
  closeHealthQuestion: '/exp/uwme/close',
};

export const generateHealthQuestion = async (
  requestData: object,
  localeHeader: LocaleHeader,
) => {
  try {
    return await cubeClient.post<object, HealthQuestion2>(
      endPoints.generateHealthQuestion,
      requestData,
      {
        headers: {
          ...localeHeader,
        },
      },
    );
  } catch (error) {
    logError(error, 'generateHealthQuestion', requestData);
    throw error;
  }
};

export const generateHealthQuestions = async (
  requestData: object,
  localeHeader: LocaleHeader,
) => {
  try {
    // https://fwdnextgen.atlassian.net/browse/CUBEMY-4337
    if (
      countryModuleEAppConfig.verifyReplacementInfoInHealthQuestion &&
      !(requestData as Case)?.application?.replacementInfo
    ) {
      throw new Error('Replacement info is required');
    }
    const data: unknown = await cubeClient.post<object, HealthQuestion3>(
      endPoints.generateHealthQuestions,
      { case: requestData },
      {
        headers: {
          ...localeHeader,
        },
      },
    );
    if (
      typeof data === 'object' &&
      data &&
      'code' in data &&
      data.code !== 200
    ) {
      const error = new Error(
        'msg' in data
          ? (data.msg as string)
          : 'Failed to generate health questions',
      );
      logError(error, 'generateHealthQuestions', { case: requestData });
      throw error;
    }
    return data as HealthQuestion3;
  } catch (error) {
    logError(error, 'generateHealthQuestions', { case: requestData });
    throw error;
  }
};

export const getHealthQuestion = async (
  id: string,
  localeHeader: LocaleHeader,
): Promise<HealthQuestion2> => {
  const res = await fetch(
    `${baseUrl}/api-gateway${endPoints.getHealthQuestion(id)}`,
    {
      headers: {
        Authorization:
          'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken,
        'Is-Restful': 'true',
        'Content-Type': 'application/json',
        ...localeHeader,
      },
    },
  );
  if (!res.ok) {
    const json = await res.json();
    logError(json, 'getHealthQuestion', { id });
    throw new Error('Failed to get health question');
  }

  const json = await res.json();
  const data: HealthQuestion2 = json.responseData;
  if (data) {
    data.sections = data?.sections?.map(s => ({
      ...s,
      enquiryLines: s?.enquiryLines?.map(l => ({
        ...l,
        questions: l?.questions?.map(q => ({
          ...q,
          path: escapeHookFormPath(q.path),
        })),
      })),
    }));
    data.allAnswers = Object.entries(data.allAnswers).reduce<
      Record<string, string[]>
    >((answers, [key, value]) => {
      answers[escapeHookFormPath(key)] = value;
      return answers;
    }, {});
  }
  return data;
  // return cubeClient
  //   .get<HealthQuestion2>(endPoints.getHealthQuestion(id), {
  //     headers: {
  //       ...localeHeader,
  //       'Content-Type': 'application/json',
  //     },
  //   })
  //   .then((data: HealthQuestion2) => {
  //     if (data) {
  //       data.sections = data?.sections?.map(s => ({
  //         ...s,
  //         enquiryLines: s?.enquiryLines?.map(l => ({
  //           ...l,
  //           questions: l?.questions?.map(q => ({
  //             ...q,
  //             path: escapeHookFormPath(q.path),
  //           })),
  //         })),
  //       }));
  //       data.allAnswers = Object.entries(data.allAnswers).reduce<
  //         Record<string, string[]>
  //       >((answers, [key, value]) => {
  //         answers[escapeHookFormPath(key)] = value;
  //         return answers;
  //       }, {});
  //     }
  //     return data;
  //   });
};

export const answerHealthQuestion = async (
  id: string,
  requestData: {
    answers: { [question: string]: (string | undefined)[] | undefined };
  },
  localeHeader: LocaleHeader,
) => {
  const body = {
    answers: requestData?.answers
      ? Object.entries(requestData.answers).reduce<
          Record<string, (string | undefined)[] | undefined>
        >((answers, [key, value]) => {
          answers[rollbackHookFormPath(key)] = value;
          return answers;
        }, {})
      : {},
  };
  try {
    const data: HealthQuestion2 = await cubeClient.post<
      { answers: { [question: string]: (string | undefined)[] | undefined } },
      HealthQuestion2
    >(endPoints.answerHealthQuestion(id), body, {
      headers: {
        ...localeHeader,
        'Content-Type': 'application/json',
      },
    });
    if (data) {
      data.sections = data?.sections?.map(s => ({
        ...s,
        enquiryLines: s?.enquiryLines?.map(l => ({
          ...l,
          questions: l?.questions?.map(q => ({
            ...q,
            path: escapeHookFormPath(q.path),
          })),
        })),
      }));
      data.allAnswers = Object.entries(data.allAnswers).reduce<
        Record<string, string[]>
      >((answers, [key, value]) => {
        answers[escapeHookFormPath(key)] = value;
        return answers;
      }, {});
    }
    return data;
  } catch (error) {
    logError(error, 'answerHealthQuestion', { ...body, id });
    throw error;
  }
};

export const lookupQuestionOptionsList = async (
  {
    questionName,
    searchKeyword,
    locale = 'English',
  }: LookupQuestionOptionsListParams,
  localeHeader: LocaleHeader,
): Promise<string[]> => {
  const res = await fetch(
    `${baseUrl}/api-gateway${endPoints.lookupQuestion}/${questionName}/${locale}/${searchKeyword}`,
    {
      headers: {
        Authorization:
          'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken,
        'Is-Restful': 'true',
        'Content-Type': 'application/json',
        ...localeHeader,
      },
    },
  );
  if (!res.ok) {
    const json = await res.json();
    logError(json, 'lookupQuestionOptionsList', {
      questionName,
      searchKeyword,
      locale,
    });
  }
  const json = await res.json();
  return json?.responseData?.options || [];
  // return cubeClient.get<string[]>(
  //   `${endPoints.lookupQuestion}/${questionName}/${locale}/${searchKeyword}`,
  //   {
  //     headers: {
  //       ...localeHeader,
  //       'Content-Type': 'application/json',
  //     },
  //   },
  // );
};

export const getQuestionToolTipContent = async (
  { optionListName, optionTag, locale = 'English' }: QuestionToolTipParams,
  localeHeader: LocaleHeader,
): Promise<QuestionToolTipContent> => {
  const res = await fetch(
    `${baseUrl}/api-gateway${endPoints.helpText}/${optionListName}/${optionTag}/${locale}`,
    {
      headers: {
        Authorization:
          'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken,
        'Is-Restful': 'true',
        'Content-Type': 'application/json',
        ...localeHeader,
      },
    },
  );
  if (!res.ok) {
    const json = await res.json();
    logError(json, 'getQuestionToolTipContent', {
      optionListName,
      optionTag,
      locale,
    });
  }
  const json = await res.json();
  return json?.responseData;
  // return cubeClient.get<QuestionToolTipContent>(
  //   `${endPoints.helpText}/${optionListName}/${optionTag}/${locale}`,
  //   {
  //     headers: {
  //       ...localeHeader,
  //       'Content-Type': 'application/json',
  //     },
  //   },
  // );
};

export const closeHealthQuestion = async (owbModel: object) => {
  try {
    return cubeClient.post<
      object,
      {
        lang: string;
        policy: {
          parties: {
            enquiry_id: string;
            enquiry_status: 'Closed' | 'Open';
          }[];
        };
      }
    >(endPoints.closeHealthQuestion, {
      case: owbModel,
    });
  } catch (error) {
    logError(error, 'closeHealthQuestion', { case: owbModel });
    throw error;
  }
};

const logError = (
  error: unknown,
  functionName: string,
  functionParams: object,
) => {
  try {
    const log: HealthQuestionLog = {
      response: error,
      functionName,
      functionParams,
      message: error instanceof Error ? error.message : undefined,
    };
    console.log('logError', functionName, log);
    crashlytics().recordError(new Error(JSON.stringify(log)));
  } catch (logError) {
    console.log('logError', logError);
  }
};
