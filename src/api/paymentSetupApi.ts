import { LocaleHeader } from 'types/localeHeader';
import {
  RenewalPayStoreRequest,
  RenewalPayStoreResponse,
  WithdrawalPayStoreRequest,
  WithdrawalPayStoreResponse,
} from 'types/paymentSetup';
import { cubeClient } from './cubeClient';

export const saveRenewalPayment = async (
  body: RenewalPayStoreRequest,
  localeHeader: LocaleHeader,
): Promise<RenewalPayStoreResponse> => {
  return cubeClient.post<
    { case: RenewalPayStoreRequest },
    RenewalPayStoreResponse
  >(
    '/exp/owb/renewalPayStore',
    {
      case: body,
    },
    {
      headers: {
        ...localeHeader,
      },
    },
  );
};

export const saveWithdrawalPayment = async (
  body: WithdrawalPayStoreRequest,
  localeHeader: LocaleHeader,
): Promise<RenewalPayStoreResponse> => {
  return cubeClient.post<
    { case: WithdrawalPayStoreRequest },
    WithdrawalPayStoreResponse
  >(
    '/exp/owb/withdrawalPayStore',
    {
      case: body,
    },
    {
      headers: {
        ...localeHeader,
      },
    },
  );
};
