import { AxiosRequestConfig, AxiosResponse } from 'axios';

import useBoundStore from 'hooks/useBoundStore';
import { CubeResponse } from 'types/response';
import { baseUrl, buildNumber, appVersion } from 'utils/context';
import { createAxiosClient } from './axiosClient';

export const axiosInstance = createAxiosClient({
  options: {
    baseURL: baseUrl + '/api-gateway',
    headers: {
      'x-agent-id': useBoundStore.getState().auth.agentCode,
      'Is-Restful': true,
      'app-version': `v${appVersion}`,
      'app-build-number': buildNumber,
    },
  },
  failedQueue: [],
});

const parseCubeData = <Data>(data: CubeResponse<Data>): Data => {
  if (
    data.success ||
    (typeof data.status === 'string' && data.status.match(/20[01]/))
  ) {
    return data.responseData;
  }

  // response data is no null, but success is false
  // The error messages may be in the responseData not the messageList
  if (data.responseData) {
    throw data.responseData;
  }

  // normal error handling
  throw new Error(
    data.messageList?.[0]?.content ||
      `Request failed with status code ${data.status}`,
    {
      cause: data.messageList?.[0],
    },
  );
};

export const cubeClient = {
  get: <Response>(url: string, config: AxiosRequestConfig = {}) =>
    axiosInstance
      .get<CubeResponse<Response>>(url, config)
      .then(response => parseCubeData(response.data)),
  delete: <Response>(url: string, config: AxiosRequestConfig = {}) =>
    axiosInstance
      .delete<CubeResponse<Response>>(url, config)
      .then(response => parseCubeData(response.data)),
  post: <Body, Response>(
    url: string,
    body: Body,
    config: AxiosRequestConfig = {},
  ) => {
    const payload = {
      requestData: body,
    };
    return axiosInstance
      .post<CubeResponse<Response>>(url, payload, config)
      .then(response => parseCubeData(response.data));
  },
  postBinary: <Body>(
    url: string,
    body: Body,
    config: AxiosRequestConfig = {},
  ): Promise<AxiosResponse<ArrayBuffer>> => {
    const payload = body;
    return axiosInstance.post<ArrayBuffer>(url, payload, {
      ...config,
      responseType: 'arraybuffer',
    });
  },
  postDirectPayLoad: <Body, Response>(
    url: string,
    body: Body,
    config: AxiosRequestConfig = {},
  ) => {
    const payload = body;
    return axiosInstance
      .post<CubeResponse<Response>>(url, payload, config)
      .then(response => parseCubeData(response.data));
  },
  patch: <Body, Response>(
    url: string,
    body: Body,
    config: AxiosRequestConfig = {},
  ) => {
    const payload = {
      requestData: body,
    };
    return axiosInstance
      .patch<CubeResponse<Response>>(url, payload, config)
      .then(response => parseCubeData(response.data));
  },
  put: <Body, Response>(
    url: string,
    body: Body,
    config: AxiosRequestConfig = {},
  ) => {
    const payload = {
      requestData: body,
    };
    return axiosInstance
      .put<CubeResponse<Response>>(url, payload, config)
      .then(response => parseCubeData(response.data));
  },
};
