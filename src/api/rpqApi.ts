import { RPQResult, RPQSubmitRequest } from 'types/quotation';
import { cubeClient } from './cubeClient';
import { LocaleHeader } from 'types/localeHeader';
import { RPQPdfResponse } from 'types/pdfResponse';

export async function submitRpqQuestions(requestData: RPQSubmitRequest) {
  const response = await cubeClient.post<RPQSubmitRequest, RPQResult>(
    '/exp/rpq/result',
    requestData,
  );
  return response;
}

export const generateRPQPdf = async (
  body: unknown,
  localeHeader: LocaleHeader,
) => {
  const response = await cubeClient.post<unknown, RPQPdfResponse>(
    '/exp/rpq/pdf',
    body,
    {
      headers: {
        ...localeHeader,
      },
    },
  );
  return response?.rpqDocument?.base64;
};
