import { cubeClient } from './cubeClient';

// OTP API Types
export interface SendOTPParam {
  channel: string;
  refNum: string;
  phone: string;
  email: string;
}

export interface VerifyOTPParam {
  channel: string;
  requestId: string;
  otp: string;
}

export interface OTPResponse {
  status: 'SUCCESS' | 'FAILED!';
  message: string;
  requestId: string;
  expiryPeriod: number | null;
}

const SEND_OTP_ENDPOINT = '/exp/api/otp/send';
const VERIFY_OTP_ENDPOINT = '/exp/api/otp/verify';

export const sendOTP = async (params: SendOTPParam): Promise<OTPResponse> => {
  const response = await cubeClient.post<SendOTPParam, OTPResponse>(
    SEND_OTP_ENDPOINT,
    params,
  );
  return response;
};

export const verifyOTP = async (
  params: VerifyOTPParam,
): Promise<OTPResponse> => {
  const response = await cubeClient.post<VerifyOTPParam, OTPResponse>(
    VERIFY_OTP_ENDPOINT,
    params,
  );
  return response;
};
