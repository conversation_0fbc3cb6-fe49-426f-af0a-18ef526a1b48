import {
  AgentPosPolicy,
  AgentNewBusinessPolicy,
  AgentPolicy,
  AgentNewBusinessPolicyPH,
  AgentPosPolicyPH,
  QueryPolicyListRequest,
  QueryPolicyListResponse,
  ExpressClaimPolicy,
  OwbPolicyListItem,
  OwbQueryPolicyListResponse, ClaimTrackingData, ExpressStartClaim
} from 'types/policy';
import { cubeClient } from './cubeClient';
import useBoundStore from 'hooks/useBoundStore';

export const POLICY_POS_ENDPOINT = '/proc/policy/pos';
export const POLICY_NEW_BUSINESS_ENDPOINT = '/proc/policy/newBusiness';
export const QUERY_KEY_POLICY_CLAIM = '/proc/policy/claim';
export const CLAIMS_START_ENDPOINT = '/proc/policy/eclaims/start';
export const POLICY_OF_ONE_CUSTOMER_ENDPOINT = '/proc/policy/all';
export const POLICY_LIST_QUERY_ENDPOINT = '/exp/owb/policy';
const policyStatus = 'ISSUED';

export async function getPosByAgentId() {
  return await cubeClient.get<AgentPosPolicy>(POLICY_POS_ENDPOINT);
}

export async function getPolicyNewBusinessByAgentId(id: string) {
  return await cubeClient.get<AgentNewBusinessPolicy>(
    POLICY_NEW_BUSINESS_ENDPOINT,
  );
}
export async function getPosByAgentIdPH(id: string) {
  return await cubeClient.get<AgentPosPolicyPH[]>(POLICY_POS_ENDPOINT);
}

export async function getPolicyNewBusinessByAgentIdPH() {
  return await cubeClient.get<AgentNewBusinessPolicyPH[]>(
    POLICY_NEW_BUSINESS_ENDPOINT,
  );
}

export async function getPolicyListByCustomerId(customerId: string) {
  const agentId = useBoundStore.getState().auth.agentCode;

  return await cubeClient.get<AgentPolicy[]>(POLICY_OF_ONE_CUSTOMER_ENDPOINT, {
    params: {
      agentId: agentId,
      customerId: customerId,
    },
  });
}

export async function getListStartClaim() {
  return await cubeClient.get<ExpressClaimPolicy[]>(
    POLICY_NEW_BUSINESS_ENDPOINT,
    {
      params: {
        deathBenefit: true,
        nbStatus: policyStatus,
      },
    },
  );
}

export async function getListOverView() {
  return await cubeClient.get<ClaimTrackingData>(
    QUERY_KEY_POLICY_CLAIM,
  );
}

export async function queryOwbPolicyList(
  request: QueryPolicyListRequest,
): Promise<OwbPolicyListItem[]> {
  const response = await cubeClient.post<
    QueryPolicyListRequest,
    OwbQueryPolicyListResponse
  >(POLICY_LIST_QUERY_ENDPOINT, request);
  return response.policies ?? [];
}

export async function getClaimsStart(claimId?: string) {
  return await cubeClient.post<undefined, ExpressStartClaim>(`${CLAIMS_START_ENDPOINT}/${claimId}`, undefined);
}
