import { AgentInfo } from 'types/agent';
import { cubeClient } from './cubeClient';
import { GenesisPurlResponse } from 'features/eRecruit/ph/types';

const endpoints = {
  agentById: (agentId: string) => `/proc/agent/${agentId}`,
};

export const GET_GENESIS_END_POINT = '/proc/agent/ecard/purl';

export const getAgentInfoById = (agentId: string) => {
  return cubeClient.get<AgentInfo>(endpoints.agentById(agentId));
};

export async function getGenesisPurl() {
  return await cubeClient.get<GenesisPurlResponse>(GET_GENESIS_END_POINT);
}
