import { build, country } from 'utils/context';
import { smartClient } from './smartClient';

const endpoints = {
  generateDeviceId:
    build === 'sit' && (country === 'my' || country === 'ib')
      ? '/smart2/users/getDeviceId'
      : build === 'uat' && country === 'ib'
      ? '/capricorn-integration/users/getDeviceId'
      : '/smart-integration/users/getDeviceId',
};

export const generateDeviceId = () => {
  return smartClient
    .get<{ deviceId: string }>(endpoints.generateDeviceId, {
      headers: {
        region: 'MYS',
        Accept: 'application/json',
        Cookie:
          'TS01c5c3ce=01684642c932c3d3cace63c0c04ae60e1e27fb27744c813b7fd9f88a9552bb77e6e4647aec1503733ce97a923013902dc7b3fa5835',
      },
    })
    .then(data => data.deviceId);
};
