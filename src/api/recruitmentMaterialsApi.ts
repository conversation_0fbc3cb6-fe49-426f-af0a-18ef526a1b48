import apiClient from './apiClient';
import { CONTENT_TYPES } from 'hooks/useContentStack';
import { contentStackEnvironment as currentEnv } from 'utils/context';
import {
  SortType,
  AllRecruitmentMaterialsResponse,
  SingleRecruitmentMaterialResponse,
} from 'types/recruitmentMaterials';
import { contentStackDeliveryToken, contentStackKey } from 'utils/context';

export async function getRecruitmentMaterials({
  channel,
  sortType,
}: {
  channel: string;
  sortType: SortType;
}) {
  const lowerCaseChannel = channel?.toLocaleLowerCase();

  const channelQueryParam = channel
    ? `"channels":{"$in":["${lowerCaseChannel}"]}`
    : ``;

  const sortQueryParam =
    sortType === 'newest' ? 'desc=created_at' : 'asc=created_at';

  const RECRUITMENT_MATERIALS_ENDPOINT = `https://cdn.contentstack.io/v3/content_types/${CONTENT_TYPES.RECRUITMENT_MATERIALS}/entries?environment=${currentEnv}&query={${channelQueryParam}}&${sortQueryParam}&include_fallback=true`;

  const res = await apiClient.get<AllRecruitmentMaterialsResponse>(
    RECRUITMENT_MATERIALS_ENDPOINT,
    {
      headers: {
        api_key: contentStackKey,
        access_token: contentStackDeliveryToken,
      },
    },
  );

  if (res?.data?.entries) return res?.data?.entries;

  return [];
}

export async function getSingleRecruitmentMaterial({
  materialUid,
}: {
  materialUid: string;
}) {
  const SINGLE_RECRUITMENT_MATERIAL_ENDPOINT = `https://cdn.contentstack.io/v3/content_types/cube_recruitment_materials/entries/${materialUid}?environment=${currentEnv}&include_fallback=true`;

  const res = await apiClient.get<SingleRecruitmentMaterialResponse>(
    SINGLE_RECRUITMENT_MATERIAL_ENDPOINT,
    {
      headers: {
        api_key: contentStackKey,
        access_token: contentStackDeliveryToken,
      },
    },
  );

  if (res?.data?.entry) return res?.data?.entry;

  return null;
}
