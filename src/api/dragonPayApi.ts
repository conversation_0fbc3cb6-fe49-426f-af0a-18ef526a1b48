import { build, country, dragonPayToken } from 'utils/context';
import { smartClient } from './smartClient';

interface BillingDetails {
  address1: string;
  address2: string;
  city: string;
  country: string;
  email: string;
  firstName: string;
  lastName: string;
  province: string;
  telNo: string;
  zipCode: string;
}

export interface PaymentGatewayRequest {
  applicationNumber: string;
  billingDetails?: BillingDetails;
  description: string;
  email: string;
  expiredInDays: string;
  paymentAmount: string;
  hash: string;
  paymentCurrencyCode: string;
  paymentGatewayCode: string;
  paymentMode: string;
  policyNumber: string;
}

export interface PaymentGatewayResponse {
  applicationNumber: string;
  sourceOfBusiness: string;
  expiryDate: string;
  paymentAmount: string;
  policyNumber: string;
  paymentUrl: string;
  paymentTransactionId: string;
}
export const paymentEndpoints = {
  paymentGatewayStatus:
    build === 'sit' && (country === 'my' || country === 'ib')
      ? '/smart2/payment/query-payment-gateway-for-payment-status'
      : build === 'uat' && country === 'ib'
      ? '/capricorn-integration/payment/query-payment-gateway-for-payment-status'
      : '/smart-integration/payment/query-payment-gateway-for-payment-status',
  paymentGateway:
    build === 'sit' && (country === 'my' || country === 'ib')
      ? '/smart2/payment/request-payment-link'
      : build === 'uat' && country === 'ib'
      ? '/capricorn-integration/payment/request-payment-link'
      : '/smart-integration/payment/request-payment-link',
  paymentGatewayLink: '/smart-payment/pay/',
};

const headers = {
  Accept: 'application/json',
  'User-Agent':
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36',
  token: dragonPayToken,
  userId: 'smart2_service',
  'Content-Type': 'application/json',
  region: 'PH',
  tenant: 'PH',
};

export const requestPaymentGateway = async (
  body: PaymentGatewayRequest,
): Promise<PaymentGatewayResponse> => {
  const data = await smartClient.post<
    PaymentGatewayRequest,
    PaymentGatewayResponse
  >(paymentEndpoints.paymentGateway, body, {
    headers: headers,
  });
  return data;
};

export interface PaymentGatewayStatusRequest {
  paymentTransactionId: string;
}

export interface PaymentGatewayStatusResponse {
  paymentTransactionId: string;
  paymentStatus: string;
  paymentGatewayReturnDetails: {
    payloadMap: {
      refNo: string;
      merchantId: string;
      txnId: string;
      refDate: string;
      mobileNo: string;
      procId: string;
      procMsg: string;
      settleDate: string;
      fee: string;
      currency: string;
      status: string;
      email: string;
      param1: string;
      param2: string;
      amount: string;
      description: string;
    };
  };
}

export const queryPaymentStatus = async (
  body: PaymentGatewayStatusRequest,
): Promise<PaymentGatewayStatusResponse> => {
  const data = await smartClient.post<
    PaymentGatewayStatusRequest,
    PaymentGatewayStatusResponse
  >(paymentEndpoints.paymentGatewayStatus, body, { headers: headers });
  return data;
};
