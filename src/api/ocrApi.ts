import { parse } from 'date-fns';
import {
  DocumentType<PERSON><PERSON><PERSON>,
  DocumentTypeIDN,
  DocumentTypeMY,
  ExtractedDataCommon,
  ExtractedDataIDN,
  OcrRequestBody,
  OcrResult,
} from 'types/ocr';
import { country } from 'utils/context';
import { cubeClient } from 'api/cubeClient';
import { newNRICtoDOB } from 'utils/helper/idNumberUtils';
import {
  capitalizeFirstLetterOfEachWord,
  replaceApostrophe,
} from 'components/NameField';

const OCR_PROCESS_ENDPOINT = '/exp/api/ocr/process';
const OCR_RESULT_ENDPOINT = '/exp/api/ocr/result';
const OCR_RESULT_INQUIRY_ENDPOINT = '/exp/api/ocr/result/inquiry';

interface PHNewResponse {
  success: boolean;
  message: string;
  data: {
    genAIEvaluation: {
      fields: {
        idNumber: string;
        fullName: string;
        firstName: string;
        lastName: string;
        middleName: string;
        address: string;
        dateOfBirth: string;
        placeOfBirth: string;
        nationality: string;
        sex: string;
        dateOfIssue: string;
        dateOfExpiry: string;
        postalCode?: string;
        imageQuality?: 'clear' | 'blurry';
        isBlankPhoto?: boolean;
      };
      idType:
        | 'passport'
        | 'umid'
        | 'sss'
        | 'bir'
        | 'drivers_license'
        | 'prc'
        | 'unknown';
    };
  } | null;
}

interface MYResponse {
  success: boolean;
  message: string;
  data: {
    genAIEvaluation: {
      fields: {
        idNumber: string;
        nameInEnglish: string;
        address: string;
        dateOfBirth: string;
        residency: string;
        sex: string;
        imageQuality?: 'clear' | 'blurry';
        isBlankPhoto?: boolean;
      };
      idType: DocumentTypeCommon | DocumentTypeMY;
    };
  } | null;
}

export type IDNResponse = {
  success: boolean;
  message: string;
  data: {
    genAIEvaluation: {
      idType: DocumentTypeCommon | DocumentTypeIDN;
      fields: ExtractedDataCommon & ExtractedDataIDN;
    };
  } | null;
};

const IGNORE_HYPHEN_TYPES: Array<
  'passport' | 'umid' | 'sss' | 'bir' | 'drivers_license' | 'prc' | 'unknown'
> = ['umid', 'drivers_license', 'sss', 'bir'];

const IGNORE_UMID_PREFIX = 'CRN';

const OCR_RESULT_POLLING_INTERVAL_IN_MS = 1000;

export const uploadAndScanDocument = async (
  body: OcrRequestBody,
  onProgress?: (progress: number) => void,
): Promise<OcrResult> => {
  switch (country) {
    case 'ph':
      return OcrExtractionPH(body, onProgress);
    case 'my':
    case 'ib':
      return OcrExtractionMY(body, onProgress);
    case 'id':
      return OcrExtractionIDN(body, onProgress);
    default:
      throw new Error('Not implemented');
  }
};

const OcrExtractionPH = (
  body: OcrRequestBody,
  onProgress?: (progress: number) => void,
): Promise<OcrResult> => {
  return new Promise<OcrResult>((resolve, reject) => {
    cubeClient
      .post<
        OcrRequestBody,
        {
          success: boolean;
          data: {
            uuid: string;
          } | null;
        }
      >(OCR_PROCESS_ENDPOINT, body, {
        onUploadProgress: progressEvent => {
          if (
            progressEvent.loaded !== undefined &&
            progressEvent.total !== undefined
          ) {
            onProgress &&
              onProgress(
                Math.min(progressEvent.loaded / progressEvent.total, 1),
              );
          }
        },
      })
      .then(res => {
        if (!res.success || !res.data?.uuid)
          throw new Error('Ocr upload failed');
        const interval = setInterval(async () => {
          console.log('start ocr check interval');
          try {
            const ocrResult = await cubeClient.get<PHNewResponse>(
              OCR_RESULT_ENDPOINT,
              {
                params: {
                  'case-id': body.caseId,
                  'party-id': body.partyId,
                  uuid: res.data?.uuid,
                  isSave: true,
                },
              },
            );
            if (!ocrResult.success) {
              clearInterval(interval);
              reject(new Error('Ocr process failed'));
            } else if (ocrResult.success && ocrResult.data !== null) {
              clearInterval(interval);
              const extraction = ocrResult.data.genAIEvaluation.fields;

              let idNumber = extraction.idNumber;

              if (
                IGNORE_HYPHEN_TYPES.includes(
                  ocrResult.data.genAIEvaluation.idType,
                )
              ) {
                idNumber = idNumber.replace(/-/g, '');
              }

              if (ocrResult.data.genAIEvaluation.idType === 'umid') {
                if (idNumber.toUpperCase().startsWith(IGNORE_UMID_PREFIX)) {
                  idNumber = idNumber.substring(IGNORE_UMID_PREFIX.length);
                }
              }

              return resolve({
                type: ocrResult.data.genAIEvaluation
                  .idType as OcrResult['type'],
                extract: {
                  fullName: replaceApostrophe(
                    capitalizeFirstLetterOfEachWord(extraction.fullName || ''),
                  ),
                  firstName: replaceApostrophe(
                    capitalizeFirstLetterOfEachWord(extraction.firstName || ''),
                  ),
                  lastName: replaceApostrophe(
                    capitalizeFirstLetterOfEachWord(extraction.lastName || ''),
                  ),
                  middleName: replaceApostrophe(
                    capitalizeFirstLetterOfEachWord(
                      extraction.middleName || '',
                    ),
                  ),
                  dateOfBirth:
                    extraction.dateOfBirth !== ''
                      ? parse(extraction.dateOfBirth, 'yyyy-MM-dd', new Date())
                      : null,
                  idNumber,
                  gender:
                    extraction.sex !== ''
                      ? extraction.sex.toLowerCase() === 'm'
                        ? 'MALE'
                        : 'FEMALE'
                      : null,
                  expiryDate:
                    extraction.dateOfExpiry !== ''
                      ? parse(extraction.dateOfExpiry, 'yyyy-MM-dd', new Date())
                      : null,
                  imageQuality: extraction.imageQuality,
                  isBlankPhoto: extraction.isBlankPhoto,
                },
              });
            }
          } catch (e) {
            clearInterval(interval);
            reject(e);
          }
        }, OCR_RESULT_POLLING_INTERVAL_IN_MS);
      })
      .catch(reject);
  });
};

const OcrExtractionMY = (
  body: OcrRequestBody,
  onProgress?: (progress: number) => void,
): Promise<OcrResult> => {
  return new Promise<OcrResult>((resolve, reject) => {
    cubeClient
      .post<
        OcrRequestBody & {
          documentType: string;
          consent: boolean;
        },
        {
          success: boolean;
          data: {
            uuid: string;
          } | null;
        }
      >(
        OCR_PROCESS_ENDPOINT,
        {
          ...body,
          documentType: 'my_mykad',
          consent: true,
        },
        {
          onUploadProgress: progressEvent => {
            if (
              progressEvent.loaded !== undefined &&
              progressEvent.total !== undefined
            ) {
              onProgress &&
                onProgress(
                  Math.min(progressEvent.loaded / progressEvent.total, 1),
                );
            }
          },
        },
      )
      .then(res => {
        if (!res.success || !res.data?.uuid)
          throw new Error('Ocr upload failed');
        let counter = 0;
        const interval = setInterval(async () => {
          console.log('start ocr check interval');
          counter++;
          if (counter > (30 * 1000) / OCR_RESULT_POLLING_INTERVAL_IN_MS) {
            // kill after 30 sec
            clearInterval(interval);
            return reject(new Error('Ocr process failed'));
          }
          try {
            const ocrResult = await cubeClient.get<MYResponse>(
              OCR_RESULT_ENDPOINT,
              {
                params: {
                  'case-id': body.caseId,
                  'party-id': body.partyId,
                  uuid: res.data?.uuid,
                  isSave: true,
                },
              },
            );
            if (!ocrResult.success) {
              clearInterval(interval);
              return reject(new Error('Ocr process failed'));
            }
            if (!ocrResult.data) return;
            clearInterval(interval);
            const extraction = ocrResult.data.genAIEvaluation.fields;
            if (
              ['mykid_id_card', 'mykad_id_card'].includes(
                ocrResult.data.genAIEvaluation.idType,
              ) &&
              (extraction.idNumber === '' ||
                extraction.nameInEnglish === '' ||
                extraction.sex === '' ||
                extraction.dateOfBirth === '')
            ) {
              // NRIC should not have any empty field
              return resolve({
                type: ocrResult.data.genAIEvaluation
                  .idType as OcrResult['type'],
                extract: {
                  imageQuality: extraction.imageQuality,
                  isBlankPhoto: true,
                },
              });
            }
            let dateOfBirth = null;

            if (
              ['mykid_id_card', 'mykad_id_card'].includes(
                ocrResult.data.genAIEvaluation.idType,
              )
            ) {
              dateOfBirth = newNRICtoDOB(
                extraction.dateOfBirth.replace(/-/g, ''),
              );
            }

            if (!dateOfBirth) {
              return resolve({
                type: ocrResult.data.genAIEvaluation
                  .idType as OcrResult['type'],
                extract: {
                  imageQuality: extraction.imageQuality,
                  isBlankPhoto: extraction.isBlankPhoto,
                },
              });
            }

            return resolve({
              type: ocrResult.data.genAIEvaluation.idType as OcrResult['type'],
              extract: {
                fullName: replaceApostrophe(
                  capitalizeFirstLetterOfEachWord(
                    extraction.nameInEnglish || '',
                  ),
                ),
                dateOfBirth,
                idNumber: extraction.idNumber.replace(/-/g, ''),
                gender:
                  extraction.sex.toLowerCase() === 'm' ? 'MALE' : 'FEMALE',
                imageQuality: extraction.imageQuality,
                isBlankPhoto: extraction.isBlankPhoto,
              },
            });
          } catch (e) {
            clearInterval(interval);
            reject(e);
          }
        }, OCR_RESULT_POLLING_INTERVAL_IN_MS);
      })
      .catch(reject);
  });
};

// TODO: Refine the logic of this function
const OcrExtractionIDN = async (
  body: OcrRequestBody,
  onProgress?: (progress: number) => void,
): Promise<OcrResult<'id'>> => {
  const res = await cubeClient.post<
    OcrRequestBody,
    { success: boolean; data: { uuid: string } | null }
  >(OCR_PROCESS_ENDPOINT, body, {
    onUploadProgress: progressEvent => {
      if (
        progressEvent.loaded !== undefined &&
        progressEvent.total !== undefined
      ) {
        onProgress?.(Math.min(progressEvent.loaded / progressEvent.total, 1));
      }
    },
  });

  if (!res.success || !res.data?.uuid) throw new Error('Ocr upload failed');

  let ocrResult: OcrResult<'id'> | undefined = undefined;

  while (!ocrResult) {
    console.log('start ocr check interval');
    let ocrResponse: IDNResponse | undefined = undefined;
    try {
      ocrResponse = await cubeClient.get<IDNResponse>(OCR_RESULT_ENDPOINT, {
        params: {
          'case-id': body.caseId,
          'party-id': body.partyId,
          uuid: res.data?.uuid,
          isSave: true,
        },
      });
    } catch (e) {
      console.error('Ocr process failed', e);
      throw new Error('Ocr process failed');
    }

    // If the OCR process is not done yet, sleep for 1 second then retry
    if (!ocrResponse?.success || ocrResponse.data === null) {
      await new Promise(resolve =>
        setTimeout(resolve, OCR_RESULT_POLLING_INTERVAL_IN_MS),
      );
      continue;
    }

    const extraction = ocrResponse.data.genAIEvaluation.fields;

    const idNumber = extraction.idNumber;
    const fullName = replaceApostrophe(
      capitalizeFirstLetterOfEachWord(extraction.fullName || ''),
    );
    const dateOfBirth = extraction.dateOfBirth
      ? parse(extraction.dateOfBirth, 'yyyy-MM-dd', new Date())
      : null;
    const gender = extraction.sex
      ? extraction.sex.toLowerCase() === 'm'
        ? 'MALE'
        : 'FEMALE'
      : null;
    const expiryDate = extraction.dateOfExpiry
      ? parse(extraction.dateOfExpiry, 'yyyy-MM-dd', new Date())
      : null;
    const maritalStatus = extraction.maritalStatus;
    const address = extraction.address;
    const nationality = extraction.nationality;
    const placeOfBirth = extraction.placeOfBirth;
    const religion = extraction.religion;

    ocrResult = {
      type: ocrResponse.data.genAIEvaluation.idType,
      extract: {
        fullName,
        dateOfBirth,
        idNumber,
        gender,
        expiryDate,
        imageQuality: extraction.imageQuality,
        isBlankPhoto: extraction.isBlankPhoto,
        maritalStatus,
        address,
        nationality,
        placeOfBirth,
        religion,
      },
      dataUUID: res.data.uuid,
    };
  }
  return ocrResult;
};

export const updateOcrResultWithRefId = async ({
  uuid,
  candidateRefId,
}: UpdateOcrResultWithRefIdParams) => {
  return await cubeClient.put(OCR_RESULT_ENDPOINT, {
    params: {
      uuid,
      'candidate-ref-id': candidateRefId,
    },
  });
};

export type UpdateOcrResultWithRefIdParams = {
  uuid: string;
  candidateRefId: string;
};

export type GetOcrResultByCandidateIdParams = Pick<
  UpdateOcrResultWithRefIdParams,
  'candidateRefId'
>;

export const getOcrResultByCandidateId = async (
  params: Pick<UpdateOcrResultWithRefIdParams, 'candidateRefId'>,
) => {
  return await cubeClient.get<IDNResponse>(OCR_RESULT_INQUIRY_ENDPOINT, {
    params: {
      'candidate-ref-id': params.candidateRefId,
    },
  });
};
