import * as FileSystem from 'expo-file-system';
import {
  GetAgentPostsListParams,
  GetMarketingActivityStatictics,
  PostPaginationResponse,
  SocialMarketingCreatePostPayload,
  SocialMarketingGeneratePostCaptionPayload,
  SocialMarketingGeneratePostResponse,
  SocialMarketingPost,
  SocialMarketingPostType,
  SocialMarketingRegeneratePostPayload,
  SocialMarketingUpdatePostCaptionPayload,
  SocialMarketingTemplatesParams,
  SocialMarketingStoriesTemplate,
  SocialMarketingCreateTemplatePostPayload,
  SocialMarketingPlatform,
  SocialMarketingStoryTemplate,
  SocialCrossPlatformAdaptPostPayload,
} from 'features/socialMarketing/types';
import useBoundStore from 'hooks/useBoundStore';
import { baseUrl } from 'utils/context';
import { ensureDirectoryExists } from 'utils/helper/fileUtils';
import { cubeClient } from './cubeClient';
import { withCompliance } from 'utils/helper/complianceCheckUtils';

const AGENT_PROFILE_API = '/proc/agent';

export async function fetchAndCacheImage(imagePath: string): Promise<string> {
  const localUri = FileSystem.cacheDirectory + imagePath;

  await ensureDirectoryExists(localUri);

  await FileSystem.downloadAsync(
    `${baseUrl}/api-gateway${imagePath}`,
    localUri,
    {
      headers: {
        Authorization: `Bearer ${
          useBoundStore.getState().auth.authInfo?.accessToken
        }`,
      },
    },
  );

  return localUri;
}

async function parsePostImageFromResponse(
  post: SocialMarketingPost,
): Promise<SocialMarketingPost> {
  try {
    const nextPost = {
      ...post,
    };

    if (
      post.mediaType === SocialMarketingPostType.Image &&
      post.mediaUrl &&
      !post.mediaUrl.startsWith('http')
    ) {
      // Fetch the image and cache it locally
      // This will return the local URI of the cached image
      // or the original URL if caching fails
      const localUrl = await fetchAndCacheImage(post.mediaUrl);

      nextPost.localUrl = localUrl;
    }

    return nextPost;
  } catch (error) {
    console.error('Error fetching image:', error);
  }

  return post;
}

async function createPost(payload: SocialMarketingCreatePostPayload) {
  const response = await cubeClient.post<
    SocialMarketingCreatePostPayload,
    SocialMarketingGeneratePostResponse
  >(`${AGENT_PROFILE_API}/post/generate`, payload);
  return withCompliance(async () => {
    const post = await parsePostImageFromResponse(response.post);

    return {
      ...response,
      post,
    };
  });
}

async function regeneratePost(
  postId: string,
  payload: SocialMarketingRegeneratePostPayload,
) {
  const response = await cubeClient.post<
    SocialMarketingRegeneratePostPayload,
    SocialMarketingGeneratePostResponse
  >(`${AGENT_PROFILE_API}/post/${postId}/regenerate`, payload);
  const post = await parsePostImageFromResponse(response.post);

  return {
    ...response,
    post,
  };
}

async function getPostSessionStatus(
  postId: string,
  sessionId: string,
): Promise<SocialMarketingGeneratePostResponse | null> {
  return withCompliance(async () => {
    const response = await cubeClient.get<SocialMarketingGeneratePostResponse>(
      `${AGENT_PROFILE_API}/post/${postId}/generate/status?sessionId=${sessionId}`,
    );
    const post = await parsePostImageFromResponse(response.post);

    return {
      ...response,
      post,
    };
  });
}

async function generatePromptFromTopic(
  mediaType: SocialMarketingPostType,
  topic: string,
): Promise<string> {
  return withCompliance(() =>
    cubeClient.post<
      {
        mediaType: SocialMarketingPostType;
        topic: string;
      },
      string
    >(`${AGENT_PROFILE_API}/post/generate-prompt`, {
      mediaType,
      topic,
    }),
  );
}

async function generatePostCaption(
  postId: string,
  payload: SocialMarketingGeneratePostCaptionPayload,
): Promise<SocialMarketingPost> {
  return cubeClient.postDirectPayLoad<Object, SocialMarketingPost>(
    `${AGENT_PROFILE_API}/post/${postId}/generate-caption`,
    payload,
  );
}

async function updatePostCaption(
  postId: string,
  payload: SocialMarketingUpdatePostCaptionPayload,
): Promise<SocialMarketingPost> {
  return withCompliance(() =>
    cubeClient.postDirectPayLoad<
      SocialMarketingUpdatePostCaptionPayload,
      SocialMarketingPost
    >(`${AGENT_PROFILE_API}/post/${postId}/update-caption`, payload),
  );
}

async function getMarketingActivityStatistics() {
  const response = await cubeClient.get<GetMarketingActivityStatictics>(
    `${AGENT_PROFILE_API}/marketing-activity/performance`,
  );

  return response;
}

async function getAgentPosts({
  page = 1,
}: GetAgentPostsListParams): Promise<PostPaginationResponse> {
  return cubeClient.get<PostPaginationResponse>(`${AGENT_PROFILE_API}/post`, {
    params: { page },
  });
}

async function getPostStatus(
  postId: string,
): Promise<SocialMarketingGeneratePostResponse> {
  return withCompliance(() =>
    cubeClient.get<SocialMarketingGeneratePostResponse>(
      `${AGENT_PROFILE_API}/post/${postId}/generate/status`,
    ),
  );
}

async function getPostById(postId: string): Promise<SocialMarketingPost> {
  return cubeClient.get<SocialMarketingPost>(
    `${AGENT_PROFILE_API}/post/${postId}`,
  );
}

async function deletePost(postId: string): Promise<string> {
  return cubeClient.delete<string>(`${AGENT_PROFILE_API}/post/${postId}`);
}

async function getTemplateCategories(): Promise<string[]> {
  return cubeClient.get<string[]>(
    `${AGENT_PROFILE_API}/post/templates/categories`,
  );
}

async function getTemplates({
  categories,
  excludeBatchIds,
}: SocialMarketingTemplatesParams): Promise<SocialMarketingStoriesTemplate> {
  return cubeClient.get<SocialMarketingStoriesTemplate>(
    `${AGENT_PROFILE_API}/post/templates`,
    {
      params: {
        ...(categories ? { categories } : undefined),
        ...(excludeBatchIds ? { excludeBatchIds } : undefined),
      },
    },
  );
}

async function createPostFromTemplate({
  storyId,
  platform = SocialMarketingPlatform.Facebook,
}: SocialMarketingCreateTemplatePostPayload): Promise<SocialMarketingPost> {
  return cubeClient.postDirectPayLoad<
    SocialMarketingCreateTemplatePostPayload,
    SocialMarketingPost
  >(`${AGENT_PROFILE_API}/post/create-from-template`, {
    storyId,
    platform,
  });
}

async function getTemplateByStory(
  storyId: string,
): Promise<SocialMarketingStoryTemplate> {
  return cubeClient.get<SocialMarketingStoryTemplate>(
    `${AGENT_PROFILE_API}/post/templates/${storyId}`,
  );
}

async function crossPlatformAdaptPost(
  postId: string,
  payload: SocialCrossPlatformAdaptPostPayload,
): Promise<SocialMarketingPost> {
  return cubeClient.post<
    SocialCrossPlatformAdaptPostPayload,
    SocialMarketingPost
  >(`${AGENT_PROFILE_API}/post/${postId}/cross-platform-adapt`, payload);
}

export default {
  createPost,
  regeneratePost,
  generatePostCaption,
  updatePostCaption,
  getPostSessionStatus,
  generatePromptFromTopic,
  getMarketingActivityStatistics,
  deletePost,
  getAgentPosts,
  getPostStatus,
  getPostById,
  getTemplateCategories,
  getTemplates,
  createPostFromTemplate,
  getTemplateByStory,
  crossPlatformAdaptPost,
};
