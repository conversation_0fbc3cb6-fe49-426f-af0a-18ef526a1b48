import React from 'react';
import { Platform, UIManager, View } from 'react-native';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import NotificationContent from 'features/notification/components/NotificationContent';

if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
export default function NotificationScreen() {
  return (
    <>
      <ScreenHeader
        route={'NotificationScreen'}
        customTitle={'Notifications'}
        leftChildren={<CustomHeaderBackButton />}
        showBottomSeparator={true}
      />
      <NotificationContent />
    </>
  );
}
