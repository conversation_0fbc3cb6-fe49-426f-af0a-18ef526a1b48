import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import EAppV2 from 'features/eAppV2/EApp';
import { memo, useEffect, useReducer } from 'react';

export const EAppScreen = memo(function EAppScreen() {
  const [key, increaseKey] = useReducer(key => key + 1, 0);
  const setRefreshEApp = useEAppStore(state => state.setRefreshEApp);
  useEffect(() => {
    setRefreshEApp(increaseKey);
  }, [setRefreshEApp]);

  return <EAppV2 key={key} />;
});

export default EAppScreen;
