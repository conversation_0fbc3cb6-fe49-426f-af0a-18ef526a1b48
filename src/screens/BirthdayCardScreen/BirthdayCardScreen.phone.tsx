import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { Icon } from 'cube-ui-components';
import { RootStackParamList } from 'types';
import { useTheme } from '@emotion/react';

import { HIT_SLOP_SPACE } from 'constants/hitSlop';
import BirthdayCardContent from 'features/home/<USER>/MyTasks/BirthdayCardContent';
import { useTranslation } from 'react-i18next';

const HIT_SLOP_SPACE_ONE = HIT_SLOP_SPACE(1);

export default function BirthdayCardScreenPhone() {
  // const {taskId} =
  const taskId =
    useRoute<RouteProp<RootStackParamList, 'BirthdayCardScreen'>>()?.params
      ?.taskId;

  const customerId =
    useRoute<RouteProp<RootStackParamList, 'BirthdayCardScreen'>>()?.params
      ?.customerId;

  const customerName =
    useRoute<RouteProp<RootStackParamList, 'BirthdayCardScreen'>>()?.params
      ?.customerName;

  const { t } = useTranslation('common');

  return (
    <>
      <ScreenHeader
        route={'BirthdayCardScreen'}
        customTitle={t('birthDay.sendCard')}
        leftChildren={<HeaderLeftButton />}
        showBottomSeparator={false}
      />
      <BirthdayCardContent
        taskId={taskId}
        customerId={customerId}
        customerName={customerName}
      />
    </>
  );
}
const HeaderLeftButton = () => {
  const { colors, space } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  return (
    <TouchableOpacity
      hitSlop={HIT_SLOP_SPACE_ONE}
      onPress={() => navigation.goBack()}>
      <Icon.Close size={space[6]} fill={colors.secondary} />
    </TouchableOpacity>
  );
};
