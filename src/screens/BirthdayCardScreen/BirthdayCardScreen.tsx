import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';

import { HIT_SLOP_SPACE } from 'constants/hitSlop';

import BirthdayCardScreenTablet from './BirthdayCardScreen.tablet';
import BirthdayCardScreenPhone from './BirthdayCardScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const HIT_SLOP_SPACE_ONE = HIT_SLOP_SPACE(1);

export default function BirthdayCardScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <BirthdayCardScreenTablet />
  ) : (
    <BirthdayCardScreenPhone />
  );
}
