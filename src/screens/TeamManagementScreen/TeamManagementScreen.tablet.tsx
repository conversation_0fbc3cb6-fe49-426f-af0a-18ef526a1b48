import React from 'react';
import { country } from 'utils/context';
import TeamManagementPH from 'features/teamManagement/ph/tablet/TeamManagementPH';
import TeamManagementIB from 'features/teamManagement/ib/tablet';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function TeamManagement() {
  switch (country) {
    case 'ph':
      return <TeamManagementPH />;
    case 'my':
      return <></>;
    case 'ib':
      return <TeamManagementIB />;
    default:
      return <NotFoundScreen />;
  }
}
