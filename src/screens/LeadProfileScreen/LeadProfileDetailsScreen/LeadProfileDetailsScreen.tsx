import React from 'react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import LeadProfileDetailsScreenTablet from './LeadProfileDetailsScreen.tablet';
import LeadProfileDetailsScreenPhone from './LeadProfileDetailsScreen.phone';

export default function ProfileDetailsScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <LeadProfileDetailsScreenTablet />
  ) : (
    <LeadProfileDetailsScreenPhone />
  );
}
