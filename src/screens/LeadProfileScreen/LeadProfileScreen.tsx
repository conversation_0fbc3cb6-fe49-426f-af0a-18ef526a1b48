import React from 'react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import LeadProfileScreenTablet from './LeadProfileScreen.tablet';
import LeadProfileScreenPhone from './LeadProfileScreen.phone';
import { NavigationProp, RouteProp } from '@react-navigation/native';
import { RootStackParamList } from 'types';

export default function LeadProfileScreen({
  navigation,
  route,
}: {
  navigation: NavigationProp<RootStackParamList, 'LeadProfile'>;
  route: RouteProp<RootStackParamList, 'LeadProfile'>;
}) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <LeadProfileScreenTablet />
  ) : (
    <LeadProfileScreenPhone navigation={navigation} route={route} />
  );
}
