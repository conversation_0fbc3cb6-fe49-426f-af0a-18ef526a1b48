import React from 'react';
import { country } from 'utils/context';
import LeadProfileLayoutPH from 'features/lead/ph/LeadProfile/phone/LeadProfilePhoneLayout';
import LeadProfileLayoutMY from 'features/lead/my/LeadProfile/phone/LeadProfilePhoneLayout';
import LeadProfileLayoutIB from 'features/lead/ib/LeadProfile/phone/LeadProfilePhoneLayout';
import { NavigationProp, RouteProp } from '@react-navigation/native';
import { RootStackParamList } from 'types';

export default function LeadProfileScreenPhone({
  navigation,
  route,
}: {
  navigation: NavigationProp<RootStackParamList, 'LeadProfile'>;
  route: RouteProp<RootStackParamList, 'LeadProfile'>;
}) {
  switch (country) {
    case 'ph':
      return <LeadProfileLayoutPH />;
    case 'my':
    case 'ib':
    case 'id':
      return <LeadProfileLayoutIB navigation={navigation} route={route} />;
    default:
      return <LeadProfileLayoutMY />;
  }
}
