import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { addErrorToast } from 'cube-ui-components';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { Ocr } from 'features/ocr/components';
import { useEffect } from 'react';
import { RootStackParamList } from 'types';
import { AppEvent } from 'types/event';
import { OcrFile } from 'types/ocr';
import { EventRegister } from 'utils/helper/eventRegister';

export function OcrScreen() {
  const setOcrFile = useEAppStore(state => state.setOcrFile);
  const { canGoBack, goBack } =
    useNavigation<NavigationProp<RootStackParamList>>();
  const { params } = useRoute<RouteProp<RootStackParamList, 'Ocr'>>();

  useEffect(() => {
    EventRegister.emit(AppEvent.OcrRendered, null);
    return () => {
      console.log('Unmounting OcrScreen ...');
    };
  }, []);

  const onShutterPressHandler = (file: OcrFile) => {
    if (!canGoBack()) {
      addErrorToast([{ message: 'Cannot go back' }]);
    }
    setOcrFile(file);
    console.log('OcrScreen: Emitting ', AppEvent.OcrCaptured);
    EventRegister.emit(AppEvent.OcrCaptured, { role: params.role });
    goBack();
  };

  const onBackPress = () => {
    if (!canGoBack()) {
      addErrorToast([{ message: 'Cannot go back' }]);
    }
    EventRegister.emit(AppEvent.OcrCancelled, { role: params.role });
    console.log('OcrScreen: Emitting ', AppEvent.OcrCancelled);
    goBack();
  };

  return (
    <Ocr onShutterPress={onShutterPressHandler} onBackPress={onBackPress} />
  );
}
