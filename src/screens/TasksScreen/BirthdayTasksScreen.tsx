import BirthdayTasksScreenTablet from './BirthdayTasksScreen.tablet';
import BirthdayTasksScreenPhone from './BirthdayTasksScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function HomeScreen() {
  // const isTablet = useBoundStore(store => store.isTablet);
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <BirthdayTasksScreenTablet />
  ) : (
    <BirthdayTasksScreenPhone />
  );
}
