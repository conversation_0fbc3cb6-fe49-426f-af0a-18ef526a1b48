import { TouchableOpacity, View } from 'react-native';
import React, { useMemo, useState } from 'react';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { FlashList } from '@shopify/flash-list';
import { Column, Icon, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import TopLabel from 'features/policy/components/TopLabel';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { useTabbarStyle as useTabBarStyle } from 'hooks/useTabbarStyle';
import {
  BirthdayListItem,
  BirthdayListTabsParamList,
  RootStackParamList,
} from 'types';

import { useTranslation } from 'react-i18next';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { useGetBirthday } from 'features/birthday/hooks/useGetBirthday';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { GeneralContactModalPhone } from 'components/ContactModal/GeneralContactModal.phone';
import { country } from 'utils/context';

const BirthdayListTabs =
  createMaterialTopTabNavigator<BirthdayListTabsParamList>();

export default function BirthdayTasksScreenPhone() {
  const { sizes, space, colors } = useTheme();
  const { data } = useGetBirthday();
  const birthdayListCount = data
    ? data?.todayList?.length + data?.next7daysList?.length
    : 0;
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const [sortByNewest, setSortByNewest] = useState(true);

  // const filterByTodayDob = (bdList: BirthdayListItem[]) => {
  //   const today = new Date();
  //   const todayMonth = today.getMonth();
  //   const todayDate = today.getDate();
  //   const filteredObjects = bdList?.filter(obj => {
  //     const objDob = new Date(obj.dateOfBirth);
  //     const objDobMonth = objDob.getMonth();
  //     const objDobDate = objDob.getDate();
  //     return objDobMonth === todayMonth && objDobDate === todayDate;
  //   });

  //   return filteredObjects;
  // };

  const birthdayTaskTodayList = data?.todayList ? data?.todayList : [];

  const filterByNextSevenDaysDob = (bdList: BirthdayListItem[]) => {
    const today = new Date();
    const nextSevenDays = new Date();
    nextSevenDays.setDate(today.getDate() + 7);
    const filteredObjects = bdList.filter(obj => {
      const objDob = new Date(obj.dateOfBirth);
      return obj === obj;
    });
    return filteredObjects;
  };

  const birthdayTaskNextSevenDaysList = useMemo(() => {
    if (data?.next7daysList) {
      const sortedObjects = data?.next7daysList.sort((a, b) => {
        const dateA = new Date(a.dateOfBirth);
        const dateB = new Date(b.dateOfBirth);

        const monthA = dateA.getMonth();
        const monthB = dateB.getMonth();
        if (sortByNewest) {
          if (monthA !== monthB) {
            return monthA - monthB;
          } else {
            const dayA = dateA.getDate();
            const dayB = dateB.getDate();
            return dayA - dayB;
          }
        } else {
          if (monthA !== monthB) {
            return monthB - monthA;
          } else {
            const dayA = dateA.getDate();
            const dayB = dateB.getDate();
            return dayB - dayA;
          }
        }
      });
      return filterByNextSevenDaysDob(sortedObjects);
    }
  }, [data?.next7daysList, sortByNewest]);

  const onPressSort = () => {
    setSortByNewest(prev => !prev);
  };
  const tabBarStyle = useTabBarStyle();

  return (
    <Column flex={1}>
      <ScreenHeader
        route={'BirthdayTasksScreen'}
        customTitle={`Birthday list (${birthdayListCount})`}
        isLeftArrowBackShown
        showBottomSeparator={false}
      />
      <BirthdayListTabs.Navigator screenOptions={{ ...tabBarStyle }}>
        <BirthdayListTabs.Screen name="Today">
          {() => (
            <FlashList
              data={birthdayTaskTodayList}
              contentContainerStyle={{
                paddingHorizontal: space[isNarrowScreen ? 3 : 4],
                paddingTop: space[4],
                paddingBottom: space[20],
              }}
              ItemSeparatorComponent={() => (
                <View
                  style={{
                    height: space[2],
                  }}
                />
              )}
              estimatedItemSize={sizes[39]}
              renderItem={({ item }) => <BirthdayList {...item} />}
              keyExtractor={({ customerId }) => String(customerId)}
            />
          )}
        </BirthdayListTabs.Screen>
        <BirthdayListTabs.Screen name="SevenDays">
          {() => (
            <>
              <View
                style={{
                  paddingHorizontal: space[isNarrowScreen ? 3 : 4],
                  paddingVertical: space[3],
                }}>
                <TopLabel
                  sortProps={sortByNewest}
                  onPressSort={onPressSort}
                  sortByWordings={
                    ['id', 'my'].includes(country) ? undefined : 'closest'
                  }
                />
              </View>
              <FlashList
                data={birthdayTaskNextSevenDaysList}
                contentContainerStyle={{
                  paddingHorizontal: space[isNarrowScreen ? 3 : 4],
                  paddingBottom: space[20],
                }}
                ItemSeparatorComponent={() => (
                  <View
                    style={{
                      height: space[2],
                    }}
                  />
                )}
                estimatedItemSize={sizes[39]}
                renderItem={({ item }) => <BirthdayList {...item} />}
                keyExtractor={({ customerId }) => String(customerId)}
              />
            </>
          )}
        </BirthdayListTabs.Screen>
      </BirthdayListTabs.Navigator>
    </Column>
  );
}

function BirthdayList({
  firstName,
  displayName,
  phoneNumber,
  recType,
  customerId,
  dateOfBirth,
  emailAddr,
}: BirthdayListItem) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('home');
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const [showModal, setShowModal] = useState(false);

  const getLeadRole = () => {
    switch (recType) {
      case 'C':
        return 'policyOwner';
      case 'I':
        return 'insured';
      case 'A':
        return 'teammate';
    }
  };

  const onPressBirthdayTask = () => {
    navigation.navigate('BirthdayCardScreen', {
      customerId: Number(customerId),
      customerName: displayName.en,
    });
  };

  return (
    <TouchableOpacity
      style={{
        flexDirection: 'row',
        backgroundColor: colors.background,
        borderRadius: borderRadius.large,
        padding: space[4],
        gap: space[4],
      }}
      onPress={() => {
        onPressBirthdayTask();
      }}>
      <Column gap={space[2]} flex={1}>
        <Typography.LargeBody
          fontWeight={'bold'}
          color={colors.primary}
          ellipsizeMode="tail"
          numberOfLines={1}>
          {displayName?.en || firstName}
        </Typography.LargeBody>
        <Row alignItems="center">
          <Icon.Birthday fill={colors.secondaryVariant} size={18} />
          <Typography.Body color={colors.secondary} style={{ marginLeft: 7 }}>
            {dateFormatUtil(dateOfBirth)}
          </Typography.Body>
        </Row>
        <Row alignItems="center">
          <Icon.Account fill={colors.secondaryVariant} size={18} />
          <Typography.Body color={colors.secondary} style={{ marginLeft: 7 }}>
            {t(getLeadRole())}
          </Typography.Body>
        </Row>
        <Row alignItems="center">
          <Icon.Call fill={colors.secondaryVariant} size={18} />
          <Typography.Body color={colors.secondary} style={{ marginLeft: 7 }}>
            {phoneNumber}
          </Typography.Body>
        </Row>
      </Column>
      <Column justifyContent="center">
        <GeneralContactModalPhone
          showModal={showModal}
          onClose={() => setShowModal(false)}
          phoneMobile={phoneNumber}
          emailAddress={emailAddr ?? ''}
        />
        <TouchableOpacity
          hitSlop={ICON_HIT_SLOP}
          onPress={() => {
            setShowModal(true);
          }}>
          <Icon.Call fill={colors.primary} size={24} />
        </TouchableOpacity>
      </Column>
    </TouchableOpacity>
  );
}
