import { ScrollView, TouchableOpacity, View } from 'react-native';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React from 'react';
import styled from '@emotion/native';
import { StatusDetail } from 'features/agentAssist/components/status/StatusDetail';
import { ClaimDetails } from 'features/agentAssist/components/details/ClaimDetails';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamList } from 'types';

export default function AgentAssistDetailScreenPhone() {
  const route =
    useRoute<RouteProp<RootStackParamList, 'agentAssistDetail'>>();
  return (
    <Root>
      <ScreenHeader
        route={'agentAssistDetail'}
        isLeftArrowBackShown
        customTitle={`Claim #${route.params.data?.caseNo}`}
      />
      <ScrollView>
        <StatusDetail
          businessDecision={route.params.data?.businessDecision}
          status={route.params.data?.caseStatus} />
        {/*<PendingIssue />*/}
        {/*<Decline />*/}
        {/*<Approved />*/}
        <ClaimDetails data={route.params.data} />
      </ScrollView>
      {/*<ViewFooter>*/}
      {/*  <BtnClaim>*/}
      {/*    <Typography.H7 fontWeight={'bold'} color={colors.palette.white}>*/}
      {/*      {t('agentAssist.detail.resubmit')}*/}
      {/*    </Typography.H7>*/}
      {/*  </BtnClaim>*/}
      {/*</ViewFooter>*/}
    </Root>
  );
}
const Root = styled(View)(() => ({
  flex: 1,
}));
const ViewFooter = styled(View)(({ theme: { space, colors } }) => ({
  paddingHorizontal: space[5],
  borderTopWidth: 1,
  borderColor: colors.palette.fwdGrey['100'],
  paddingTop: space[5],
  paddingBottom: space[10],
  backgroundColor: colors.background,
}));
const BtnClaim = styled(TouchableOpacity)(({ theme: { space, colors, borderRadius } }) => ({
  backgroundColor: colors.primary,
  paddingVertical: space[3],
  alignItems: 'center',
  borderRadius: borderRadius['x-small'],
}));

