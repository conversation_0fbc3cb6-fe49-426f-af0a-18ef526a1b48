import React from 'react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import AgentAssistDetailScreenPhone from 'screens/AgentAssitDetail/AgentAssistDetailScreen.phone';

export default function AgentAssistDetailScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <AgentAssistDetailScreenPhone /> //TODO: tablet screen
  ) : (
    <AgentAssistDetailScreenPhone />
  );
}
