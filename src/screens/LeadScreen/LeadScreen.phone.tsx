import React from 'react';

import { SharedValue } from 'react-native-reanimated';
import { build, country } from 'utils/context';
import LeadScreenContentPH from 'features/lead/ph/phone/LeadScreenContent';
import LeadScreenContentIB from 'features/lead/ib/phone/LeadScreenContent';
import LeadScreenContentID from 'features/lead/id/phone/LeadScreenContent';

export interface HeaderAnimatedProps {
  show: SharedValue<boolean>;
  scrollY: SharedValue<number>;
}
export interface HeaderShowProps {
  showHeaderSearch: () => void;
  hideHeaderSearch: () => void;
}
export default function LeadScreenPhone() {
  switch (country) {
    case 'ph':
      return <LeadScreenContentPH />;
    case 'ib':
      return build === 'dev' || build === 'sit' ? ( //TODO: To be removed
        <LeadScreenContentIB />
      ) : (
        <LeadScreenContentPH />
      );
    case 'id':
      return <LeadScreenContentID />;

    default:
      return <LeadScreenContentIB />;
  }
}
