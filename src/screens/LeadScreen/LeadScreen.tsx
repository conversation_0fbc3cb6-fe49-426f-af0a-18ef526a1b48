import React from 'react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import LeadScreenTablet from 'screens/LeadScreen/LeadScreen.tablet';
import LeadScreenPhone from './LeadScreen.phone';

export default function LeadScreen() {
  // const isTablet = useBoundStore(store => store.isTablet);
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? <LeadScreenTablet /> : <LeadScreenPhone />;
}
