import React, { useState, useRef } from 'react';
import { country } from 'utils/context';
import LeadsCustomersNavigator from 'features/lead/ph/tablet/LeadsCustomersNavigator';
import LeadScreenContent from 'features/lead/tablet/components/LeadScreenContent';
import LeadsCustomersContent from 'features/lead/ib/tablet/LeadsCustomersContent';

export default function LeadScreenTablet() {
  switch (country) {
    case 'ph':
      return <LeadsCustomersNavigator />;
    case 'my':
      return <LeadScreenContent />;
    // case 'ib':
    //   return <LeadScreenContent />;
    case 'ib':
      return <LeadsCustomersContent />;
    default:
      return <LeadScreenContent />;
  }
}
