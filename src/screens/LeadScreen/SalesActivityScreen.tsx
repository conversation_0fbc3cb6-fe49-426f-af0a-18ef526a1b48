import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import ResponsiveView from 'components/ResponsiveView';
import { Icon } from 'cube-ui-components';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import SalesActivityChart from 'features/lead/components/SalesActivityChart';
import { useGetLeadTracking } from 'features/lead/hooks/useGetSalesActivity';
import { userPrivacyDialog } from 'features/lead/hooks/usePrivacyDialog';
import LoadingLayer from 'features/policy/components/LoadingLayer';
import { useBottomBar } from 'hooks/useBottomBar';
import useBoundStore from 'hooks/useBoundStore';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useMemo } from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import Animated, {
  FadeIn,
  LinearTransition,
  useAnimatedStyle,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';
import GATracking from 'utils/helper/gaTracking';

export default function SalesActivityScreen() {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { name: screenName } = useRoute();
  const { colors, space } = useTheme();



  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();
  const { isShowBottomBar, safeBottomPadding } = useBottomBar();

  // * Set fixed header height when keyboard is open
  const { top } = useSafeAreaInsets();
  const searchBarHeight = useBoundStore(store => store.lead.searchBarHeight);
  const primaryBarHeight = useBoundStore(store => store.lead.primaryBarHeight);
  const secondaryBarHeight = useBoundStore(
    store => store.lead.secondaryBarHeight,
  );
  const FixedHeaderHeight =
    searchBarHeight + primaryBarHeight + secondaryBarHeight + top + sizes[5];

  const isAndroid = Platform.OS === 'android';

  const { data: leadTrackingDetails, isLoading } = useGetLeadTracking();

  const summary = useMemo(
    () => leadTrackingDetails?.summary,
    [leadTrackingDetails],
  );

  const conversions = useMemo(
    () => leadTrackingDetails?.conversions,
    [leadTrackingDetails],
  );

  const targets = useMemo(
    () => leadTrackingDetails?.targets,
    [leadTrackingDetails],
  );

  const addLeadBtnOnPress = () => {
    GATracking.logButtonPress({
      screenName,
      buttonName: 'Create new lead button',
      screenClass: 'Seller exp flow',
      actionType: 'cta_button',
    });

    navigation.navigate('AddNewLeadOrEntity');
  };

  const addLeadBtnOnPressFn = userPrivacyDialog(addLeadBtnOnPress);

  const addButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: -space[isWideScreen ? 5 : 4],
        },
      ],
      bottom: isShowBottomBar?.value ? 60 : 10,
    };
  }, [isShowBottomBar]);

  if (isLoading) {
    return <LoadingLayer isLoading={true} />;
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'position' : 'height'}
        keyboardVerticalOffset={
          Platform.OS === 'ios' ? 170 : FixedHeaderHeight
        }>
        <ScrollView
          contentContainerStyle={{
            paddingTop: sizes[4],
            paddingBottom: isAndroid ? sizes[42] : sizes[34],
          }}
          showsVerticalScrollIndicator={true}
          scrollIndicatorInsets={{ right: 1 }}>
          <Container
            narrowStyle={{
              paddingHorizontal: space[3],
            }}>
            <SalesActivityChart
              conversions={conversions}
              targets={targets}
              summary={summary}
            />
          </Container>
        </ScrollView>
      </KeyboardAvoidingView>

      <Animated.View
        entering={FadeIn.delay(500)}
        layout={LinearTransition}
        style={[addButtonAnimatedStyle]}>
        <AddLeadBtn
          style={{
            right: space[isWideScreen ? 6 : 4],
            width: space[isWideScreen ? 18 : 14],
            height: space[isWideScreen ? 18 : 14],
            bottom: isAndroid ? sizes[8] : 0,
            position: 'absolute',
          }}
          onPress={addLeadBtnOnPressFn}
          bottom={safeBottomPadding + space[isWideScreen ? 5 : 4]}>
          <Icon.Plus
            size={sizes[isWideScreen ? 8 : 6]}
            fill={colors.background}
          />
        </AddLeadBtn>
      </Animated.View>
    </SafeAreaView>
  );
}

const AddLeadBtn = styled.TouchableOpacity<{ bottom: number }>(
  ({ theme, bottom = 0 }) => ({
    height: theme.space[14],
    width: theme.space[14],
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.primary,
    position: 'absolute',
    // marginTop: theme.space[6],
    bottom,
    right: theme.space[4],
    ...theme.elevation[5],
  }),
);

const Container = styled(ResponsiveView)(({ theme }) => ({
  width: '100%',
}));