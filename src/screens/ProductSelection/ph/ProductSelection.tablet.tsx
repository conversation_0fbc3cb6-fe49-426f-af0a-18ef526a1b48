import { useTheme } from '@emotion/react';
import { Header } from '@react-navigation/elements';
import {
  NavigationProp,
  StackActions,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { Box, Icon, Row, Typography } from 'cube-ui-components';
import ProductList from 'features/productSelection/components/ProductList';
import { useProductListQuery } from 'features/productSelection/hooks/useProducts';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  LayoutChangeEvent,
  LayoutRectangle,
  NativeScrollEvent,
  NativeSyntheticEvent,
  TouchableOpacity,
} from 'react-native';
import { LicenseInfo, RootStackParamList } from 'types';

import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import useLatest from 'hooks/useLatest';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useTranslation } from 'react-i18next';
import { Platform, ScrollView } from 'react-native';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import {
  Route,
  TabBar,
  TabBarItem,
  TabBarItemProps,
  TabView,
} from 'react-native-tab-view';
import { Product } from 'types/products';
import GATracking from 'utils/helper/gaTracking';
import { mapProducts } from 'utils/helper/mapProducts';
import { hasLicense } from 'utils/helper/agentUtils';

const ProductSelection = () => {
  const { colors, space } = useTheme();

  const { t } = useTranslation(['product', 'navigation']);

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { name: screenName } = useRoute();

  const { mutate, data } = useProductListQuery();

  useEffect(() => {
    mutate({});
  }, []);

  const { data: agentInfo } = useGetAgentProfile();

  const validateLicense = useCallback(
    (product: Product) => {
      if (!agentInfo) return false;
      const { licenses, isAgentLicenseActive } = agentInfo;
      if (!isAgentLicenseActive) return false;
      // Check for TL or VL licenses
      if (hasLicense(licenses, 'TL') || hasLicense(licenses, 'VL')) {
        return true;
      }

      // Check for TL license and non-VUL product
      if (hasLicense(licenses, 'TL') && !product.isVUL) {
        return true;
      }

      // Check for VL license and VUL product
      if (hasLicense(licenses, 'VL') && product.isVUL) {
        return true;
      }
      return false;
    },
    [agentInfo],
  );

  const products = useMemo(
    () => mapProducts((data ?? []).filter(validateLicense)),
    [data, validateLicense],
  );

  const [tabIndex, setTabIndex] = useState(0);
  const [sectionLayouts, setSectionLayouts] = useState<Array<LayoutRectangle>>([
    { height: 0, width: 0, x: 0, y: 0 },
  ]);
  const latestSectionLayouts = useLatest(sectionLayouts);
  const scrollViewRef = useRef<ScrollView>(null);
  const scrollingRef = useRef<boolean>(false);
  const scrollOffsetRef = useRef(0);
  const latestTabIndex = useLatest(tabIndex);
  const { height } = useSafeAreaFrame();
  const { top } = useSafeAreaInsets();

  const headerHeight = Platform.OS === 'android' ? 56 : 44;

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const routes = useMemo(() => {
    return products.map(i => ({
      key: i.title,
      count: i.data.length,
    }));
  }, [products]);

  const navigationState = useMemo(() => {
    return {
      index: tabIndex,
      routes,
    };
  }, [tabIndex, routes]);

  const renderTabBarItem = useCallback(
    (props: TabBarItemProps<Route & { count: number }> & { key: string }) => {
      return <TabBarItem {...props} pressColor="transparent" />;
    },
    [],
  );

  const handleOnTabPress = useCallback(
    (index: number) => {
      scrollingRef.current = true;
      scrollViewRef?.current?.scrollTo({
        y: latestSectionLayouts.current[index].y,
        animated: true,
      });
      setTimeout(() => {
        scrollingRef.current = false;
      }, 500);
    },
    [latestSectionLayouts],
  );

  const onTabPress = useCallback(
    ({ route: { key } }: { route: { key: string } }) => {
      const index = routes.findIndex(i => i.key === key);
      if (index >= 0) {
        handleOnTabPress(index);
      }
    },
    [handleOnTabPress, routes],
  );

  const onSectionLayout = useCallback(
    (index: number) => (e: LayoutChangeEvent) => {
      setSectionLayouts(layouts => {
        layouts[index] = e.nativeEvent?.layout;
        return layouts;
      });
    },
    [setSectionLayouts],
  );

  const handleOnScroll = useCallback(
    (e: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { y } = e.nativeEvent.contentOffset;
      const direction = y > scrollOffsetRef.current ? 'up' : 'down';
      scrollOffsetRef.current = y;
      if (scrollingRef.current) {
        return;
      }
      let index = -1;

      if (direction === 'up') {
        for (let i = 0; i < latestSectionLayouts.current.length; i++) {
          const space =
            i > 0 ? latestSectionLayouts.current[i - 1].height * 0.4 : 0;
          if (y >= latestSectionLayouts.current[i].y - space) {
            index = i;
          }
        }
      } else {
        for (let i = 0; i < latestSectionLayouts.current.length; i++) {
          const space =
            i > 0 ? latestSectionLayouts.current[i - 1].height * 0.3 : 0;
          if (
            y >= latestSectionLayouts.current[i].y - space &&
            y <=
              latestSectionLayouts.current[i].y +
                latestSectionLayouts.current[i].height * 0.4
          ) {
            index = i;
          }
        }
      }
      if (index !== latestTabIndex.current && index >= 0) {
        setTabIndex(index);
      }
    },
    [],
  );

  const onPressItemSelect = useCallback(
    (p: Product) => {
      navigation.dispatch(
        StackActions.replace('SalesIllustrationForm', {
          pid: p?.pid,
          from: 'quick_quote_form',
        }),
      );
    },
    [navigation],
  );

  let appendMainBottomPadding = height - top - 44 - headerHeight;
  if (
    sectionLayouts.length > 0 &&
    sectionLayouts[sectionLayouts.length - 1]?.height
  ) {
    appendMainBottomPadding -= sectionLayouts[sectionLayouts.length - 1].height;
  }

  const onBackPress = () => {
    GATracking.logButtonPress({
      screenName,
      screenClass: 'Sales flow',
      actionType: 'non_cta_button',
      buttonName: 'Back',
    });
    navigation.goBack();
  };

  return (
    <Box flex={1} bgColor={colors.background}>
      <Header
        title=""
        headerRight={() => (
          <Row gap={space[6]}>
            <TouchableOpacity
              onPress={() =>
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Main' }],
                })
              }>
              <Row gap={space[1]} alignItems="center">
                <Icon.Home
                  fill={colors.palette.fwdDarkGreen[100]}
                  height={space[6]}
                  width={space[6]}
                />
                <Typography.Body
                  fontWeight="bold"
                  color={colors.palette.fwdDarkGreen[100]}>
                  {t('navigation:tabScreen.Home')}
                </Typography.Body>
              </Row>
            </TouchableOpacity>
          </Row>
        )}
        headerLeft={() => (
          <Row gap={space[4]} alignItems="center">
            <TouchableOpacity onPress={onBackPress}>
              <Icon.ArrowLeft size={22} fill={colors.palette.black} />
            </TouchableOpacity>

            <Typography.LargeLabel
              fontWeight="bold"
              color={colors.onBackground}>
              {t('product:selectOneProduct')}
            </Typography.LargeLabel>
          </Row>
        )}
        headerLeftContainerStyle={{
          paddingLeft: space[3],
          justifyContent: 'center',
        }}
        headerRightContainerStyle={{
          paddingRight: space[3],
          justifyContent: 'center',
        }}
        headerShadowVisible={false}
        headerStyle={{ backgroundColor: colors.background }}
      />

      {Boolean(products.length > 0) && (
        <TabView
          style={{ height: 44, flex: 0 }}
          sceneContainerStyle={{ flex: 0 }}
          swipeEnabled={false}
          navigationState={navigationState}
          onIndexChange={setTabIndex}
          renderScene={() => <></>}
          renderTabBar={props => (
            <TabBar
              {...props}
              activeColor={colors.primary}
              inactiveColor={colors.onBackground}
              indicatorStyle={{ backgroundColor: colors.primary }}
              scrollEnabled={routes.length >= (isNarrowScreen ? 3 : 4)}
              renderTabBarItem={renderTabBarItem}
              tabStyle={routes.length >= 4 ? { width: 'auto' } : undefined}
              style={{
                height: 44,
                backgroundColor: 'transparent',
                overflow: 'hidden',
                elevation: 0,
                borderBottomColor: colors.palette.fwdGrey[50],
                borderBottomWidth: 1,
              }}
              android_ripple={{ color: colors.palette.whiteTransparent }}
              onTabPress={onTabPress}
              renderLabel={({
                route,
                focused,
                color,
              }: {
                route: { key: string; count: number };
                focused: boolean;
                color: string;
              }) => (
                <Box justifyContent="center" alignItems="center">
                  <Typography.LargeLabel
                    numberOfLines={1}
                    style={{
                      position: 'absolute',
                      paddingHorizontal: 12,
                      textAlign: 'center',
                    }}
                    fontWeight={focused ? 'bold' : 'normal'}
                    color={color}>
                    {`${route.key} (${route.count})`}
                  </Typography.LargeLabel>
                </Box>
              )}
            />
          )}
        />
      )}

      <ProductList
        sections={products}
        onSectionLayout={onSectionLayout}
        scrollViewRef={scrollViewRef}
        onScroll={handleOnScroll}
        isNarrowScreen={isNarrowScreen}
        contentContainerStyle={{
          paddingBottom: appendMainBottomPadding,
        }}
        onPressItemSelect={onPressItemSelect}
      />
    </Box>
  );
};

export default ProductSelection;
