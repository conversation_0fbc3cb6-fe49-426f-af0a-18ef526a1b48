import React from 'react';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { useTranslation } from 'react-i18next';
import EliteAgencyRequirementsContent from 'features/recognition/EliteAgencyRequirementslContent';


export default function EliteAgencyRequirementsScreen() {
  const { t } = useTranslation('recognition');

  return (
    <>
      <ScreenHeader
        route={'EliteAgencyDetails'}
        customTitle={t('recognition.eliteAgent.requirements.title')}
        isLeftArrowBackShown
      />
      <EliteAgencyRequirementsContent />
    </>
  );
}





