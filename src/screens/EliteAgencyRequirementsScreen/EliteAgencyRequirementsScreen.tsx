import React from 'react';
import EliteAgencyRequirementsScreenTablet from 'screens/EliteAgencyRequirementsScreen/EliteAgencyRequirementsScreen.tablet';
import EliteAgencyRequirementsScreenPhone from 'screens/EliteAgencyRequirementsScreen/EliteAgencyRequirementsScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function EliteAgencyRequirementsScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <EliteAgencyRequirementsScreenTablet />
  ) : (
    <EliteAgencyRequirementsScreenPhone />
  );
}
