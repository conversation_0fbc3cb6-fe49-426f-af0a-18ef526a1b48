import React from 'react';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import { useTranslation } from 'react-i18next';
import HomeButton from 'navigation/components/ScreenHeader/tablet/HomeButton';
import EliteAgencyRequirementsContent from 'features/recognition/tablet/EliteAgencyRequirementsContent';

export default function EliteAgencyRequirementsScreen() {
  const { t } = useTranslation('recognition');

  return (
    <>
      <ScreenHeader
        route={'EliteAgencyDetails'}
        customTitle={t('recognition.eliteAgent.requirements.title')}
        isLeftArrowBackShown
        rightChildren={<HomeButton />}
      />
      <EliteAgencyRequirementsContent />
    </>
  );
}
