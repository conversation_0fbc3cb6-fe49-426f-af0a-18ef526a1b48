import { memo } from 'react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ACRTablet from 'features/eAppV2/ph/components/acr/ACR.tablet';
import ACRPhone from 'features/eAppV2/ph/components/acr/ACR.phone';

export const ACRScreen = memo(function ACRScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? <ACRTablet /> : <ACRPhone />;
});
export default ACRScreen;
