import { View, StyleSheet, ImageBackground, PixelRatio } from 'react-native';
import { modelName } from 'expo-device';
import React from 'react';
import LoginLogo from 'components/LoginLogo';
import { loginTablet, loginTabletPH, loginTabletID } from 'assets/images';
import LoginForm from 'features/login/LoginForm/tablet';
import ForgetPwForm from 'features/login/ForgetPwForm';
import { PageRouter } from 'components/PageRouter';
import { PageRoute } from 'components/PageRouter/PageRouter';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import { appVersion, build, country } from 'utils/context';
import { LoginRoutes } from 'types/login';
import VersionCheckModal from 'features/home/<USER>/VersionCheckModal';
import { getMaxSizeInMbErrorMessage } from 'utils/helper/translation';

const loginRoutes: PageRoute<LoginRoutes>[] = [
  {
    name: 'Login',
    page: ({ navigation }) => <LoginForm navigation={navigation} />,
  },
  {
    name: 'ForgetPw',
    page: ({ navigation }) => <ForgetPwForm navigation={navigation} />,
  },
];

export default function LoginScreenTablet() {
  const getImageBG = () => {
    switch (country) {
      case 'ph':
        return loginTabletPH;
      case 'id':
        return loginTabletID;
      case 'my':
      case 'ib':
      default:
        return loginTablet;
    }
  };

  return (
    <KeyboardAwareScrollView
      contentContainerStyle={styles.container}
      bounces={false}>
      <ImageBackground style={styles.imageBackground} source={getImageBG()}>
        <VersionCheckModal
          ComponentsToBeHidden={() => (
            <LoginFormContainer>
              <LoginLogoContainer>
                <LoginLogo />
              </LoginLogoContainer>
              <PageRouter pages={loginRoutes} />
            </LoginFormContainer>
          )}
        />

        <VersionNumberText>
          {'v' +
            appVersion +
            (build !== 'prd'
              ? ` (${build}), ${modelName}, L-${PixelRatio.get()}`
              : '')}
        </VersionNumberText>
      </ImageBackground>
    </KeyboardAwareScrollView>
  );
}

const LoginFormContainer = styled(View)(({ theme }) => ({
  width: '80%',
  maxWidth: 520,
  backgroundColor: theme?.colors.background,
  borderRadius: 16,
  padding: theme?.space[12],
}));

const LoginLogoContainer = styled(View)(({ theme }) => ({
  paddingBottom: theme?.space[6],
}));

const VersionNumberText = styled(Typography.LargeLabel)(({ theme }) => ({
  textAlign: 'center',
  color: theme?.colors.background,
  position: 'absolute',
  bottom: theme?.space[10],
}));

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  imageBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
