import { Image, useWindowDimensions, View, PixelRatio } from 'react-native';
import { modelName } from 'expo-device';
import React, { useMemo } from 'react';
import { Typography } from 'cube-ui-components';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import LoginLogo from 'components/LoginLogo';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import LoginForm from 'features/login/LoginForm/phone';
import ForgetPwForm from 'features/login/ForgetPwForm';
import Animated, { LinearTransition } from 'react-native-reanimated';
import { PageRouter } from 'components/PageRouter';
import { PageRoute } from 'components/PageRouter/PageRouter';
import { LoginRoutes } from 'types/login';
import { appVersion, build, country } from 'utils/context';
import VersionCheckModal from 'features/home/<USER>/VersionCheckModal';
import {
  loginBG as PhLoginBg,
  loginWideBG as PhLoginWideBg,
  // loginBG2 as IbLoginBg, - image size not suitable
  loginWideBG2 as IbLoginWideBg,
  loginPhoneID,
  loginPhoneMY,
} from 'assets/images';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import useKeyboardHeight from 'hooks/useKeyboardHeight';

enum LOGIN_BG_DIMENSION {
  HEIGHT = 1056,
  WIDTH = 750,
}

enum LOGIN_WIDE_BG_DIMENSION {
  HEIGHT = 671,
  WIDTH = 884,
}

const loginRoutes: PageRoute<LoginRoutes>[] = [
  {
    name: 'Login',
    page: ({ navigation }) => <LoginForm navigation={navigation} />,
  },
  {
    name: 'ForgetPw',
    page: ({ navigation }) => <ForgetPwForm navigation={navigation} />,
  },
];

export default function LoginScreenPhone() {
  const heightOfKeyboard = useKeyboardHeight();
  const { colors, sizes, space } = useTheme();
  const { width, height } = useWindowDimensions();
  const { top, bottom } = useSafeAreaInsets();
  const { t } = useTranslation();
  const { shouldAdaptWideLayout } = useWindowAdaptationHelpers();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const backgroundHeight =
    (width / LOGIN_BG_DIMENSION.WIDTH) * LOGIN_BG_DIMENSION.HEIGHT;

  const wideBackgroundHeight =
    (width / LOGIN_WIDE_BG_DIMENSION.WIDTH) * LOGIN_WIDE_BG_DIMENSION.HEIGHT;

  const defaultMinHeight = height - backgroundHeight + sizes[35];

  const wideDefaultMinHeight = height - wideBackgroundHeight + sizes[10];

  const getBackgroundImageSource = () => {
    if (country === 'ib')
      return shouldAdaptWideLayout ? IbLoginWideBg : IbLoginWideBg;
    if (country === 'ph')
      return shouldAdaptWideLayout ? PhLoginWideBg : PhLoginBg;
    if (country === 'id')
      return shouldAdaptWideLayout ? loginPhoneID : loginPhoneID;
    if (country === 'my')
      return shouldAdaptWideLayout ? loginPhoneMY : loginPhoneMY;
    return undefined;
  };
  const backgroundImageSource = getBackgroundImageSource();

  const maxHeightOfForm = useMemo(
    () => (heightOfKeyboard === 0 ? undefined : height - heightOfKeyboard - 88),
    [heightOfKeyboard, height],
  );
  const minHeightOfForm = useMemo(() => {
    const minHeight = shouldAdaptWideLayout
      ? wideDefaultMinHeight
      : defaultMinHeight;

    if (maxHeightOfForm && minHeight > maxHeightOfForm) {
      return maxHeightOfForm;
    }
    return minHeight;
  }, [
    shouldAdaptWideLayout,
    wideDefaultMinHeight,
    defaultMinHeight,
    maxHeightOfForm,
  ]);
  return (
    <KeyboardAvoidingView
      style={{
        flex: 1,
        paddingTop: top,
        paddingBottom: bottom,
        justifyContent: 'flex-end',
        backgroundColor: colors.background,
      }}
      behavior={'padding'}>
      <Image
        source={backgroundImageSource}
        style={{
          position: 'absolute',
          top: 0,
          width,
          height: shouldAdaptWideLayout
            ? wideBackgroundHeight
            : backgroundHeight,
        }}
        resizeMode="cover"
      />
      <Form maxHeight={maxHeightOfForm} minHeight={minHeightOfForm}>
        <View style={{ paddingBottom: space[6] }}>
          <LoginLogo />
        </View>
        <PageRouter pages={loginRoutes} />
        <View
          style={{
            paddingTop: country === 'id' && !isTabletMode ? space[2] : space[5],
            flex: 1,
          }}
        />
        <Typography.SmallBody>
          {t('login.footer', { appVersion })}
          {build &&
            build !== 'prd' &&
            ` (${build}), ${modelName}, L-${PixelRatio.get()}]`}
        </Typography.SmallBody>
      </Form>
      <VersionCheckModal />
    </KeyboardAvoidingView>
  );
}

const Form = styled(Animated.View)<{ minHeight: number; maxHeight?: number }>(
  ({ theme, minHeight, maxHeight }) => ({
    backgroundColor: theme.colors.background,
    width: '100%',
    borderTopLeftRadius: theme.sizes[8],
    borderTopRightRadius: theme.sizes[8],
    padding: theme.space[8],
    minHeight,
    maxHeight,
    alignItems: 'center',
  }),
);
