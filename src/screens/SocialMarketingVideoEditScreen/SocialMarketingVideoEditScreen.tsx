import SocialMarketingVideoEdit from 'features/socialMarketing/components/SocialMarketingVideoEdit';
import { useIgniteFeatureEnable } from 'features/socialMarketing/hooks/useIgniteFeatureFlag';
import React from 'react';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function SocialMarketingVideoEditScreen() {
  const isIgniteFeatureEnabled = useIgniteFeatureEnable();

  if (!isIgniteFeatureEnabled) {
    return <NotFoundScreen />;
  }

  return <SocialMarketingVideoEdit />;
}
