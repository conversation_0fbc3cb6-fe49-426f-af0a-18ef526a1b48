import Performance from 'features/performance/phone/Performance';
import PerfCommonScreenPhone from 'features/performance/phone/PerfCommonScreen';
import React from 'react';
import NotFoundScreen from 'screens/NotFoundScreen';
import { country } from 'utils/context';

export default function PerformanceScreenPhone() {
  switch (country) {
    case 'ph':
    case 'id':
      return <Performance />;
    case 'my':
    case 'ib':
      return <PerfCommonScreenPhone />;
    default:
      return <NotFoundScreen />;
  }
}
