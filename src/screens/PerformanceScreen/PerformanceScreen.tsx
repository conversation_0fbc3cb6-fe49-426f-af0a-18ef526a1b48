import React from 'react';
import PerformanceScreenTablet from 'screens/PerformanceScreen/PerformanceScreen.tablet';
import PerformanceScreenPhone from 'screens/PerformanceScreen/PerformanceScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function PerformanceScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <PerformanceScreenTablet />
  ) : (
    <PerformanceScreenPhone />
  );
}
