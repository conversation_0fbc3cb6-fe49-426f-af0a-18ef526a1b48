import SocialMarketingRepost from 'features/socialMarketing/components/SocialMarketingRepost';
import { useIgniteFeatureEnable } from 'features/socialMarketing/hooks/useIgniteFeatureFlag';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function SocialMarketingRepostScreen() {
  const isIgniteFeatureEnabled = useIgniteFeatureEnable();

  if (!isIgniteFeatureEnabled) {
    return <NotFoundScreen />;
  }

  return <SocialMarketingRepost />;
}
