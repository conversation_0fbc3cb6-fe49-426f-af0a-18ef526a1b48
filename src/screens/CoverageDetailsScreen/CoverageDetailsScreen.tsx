import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import CoverageDetailsScreenTablet from './CoverageDetailsScreen.tablet';
import CoverageDetailsScreenPhone from './CoverageDetailsScreen.phone';

export default function CoverageDetailsScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <CoverageDetailsScreenTablet />
  ) : (
    <CoverageDetailsScreenPhone />
  );
}
