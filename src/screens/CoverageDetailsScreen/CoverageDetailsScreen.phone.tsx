import { useNavigation } from '@react-navigation/native';
import IbCoverageDetails from 'features/coverageDetails/ib/phone/CoverageDetails';
import MyCoverageDetails from 'features/coverageDetails/my/phone/CoverageDetails';
import IdCoverageDetails from 'features/coverageDetails/id/phone/CoverageDetails';
import PhCoverageDetails from 'features/coverageDetails/ph/phone/CoverageDetails';
import { memo, useEffect, useMemo } from 'react';
import { country } from 'utils/context';

const CoverageDetailsScreen = () => {
  const navigate = useNavigation();

  const CoverageDetailsComponent = useMemo(() => {
    switch (country) {
      case 'ph':
        return PhCoverageDetails;
      case 'my':
        return MyCoverageDetails;
      case 'ib':
        return IbCoverageDetails;
      case 'id':
        return IdCoverageDetails;
      default:
        return null;
    }
  }, []);

  useEffect(() => {
    if (!CoverageDetailsComponent && navigate.canGoBack()) {
      navigate.goBack();
    }
  }, [CoverageDetailsComponent, navigate]);

  return CoverageDetailsComponent ? <CoverageDetailsComponent /> : null;
};

export default memo(CoverageDetailsScreen);
