import RecognitionDetailsContent from 'features/recognition/components/RecognitionDetails/RecognitionDetailsContent';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import HomeButton from 'navigation/components/ScreenHeader/tablet/HomeButton';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function RecognitionDetailsScreenTablet() {
  const { t } = useTranslation('performance');
  return (
    <>
      <ScreenHeader
        route={'RecognitionDetails'}
        customTitle={t('performance.recognition')}
        isLeftArrowBackShown
        rightChildren={<HomeButton />}
      />
      <RecognitionDetailsContent />
    </>
  );
}
