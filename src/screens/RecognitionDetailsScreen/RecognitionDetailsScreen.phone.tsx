import React from 'react';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import RecognitionDetailsContent from 'features/recognition/components/RecognitionDetails/RecognitionDetailsContent';
import { country } from 'utils/context';

const IS_PH = country === 'ph';

export default function RecognitionDetailsScreenPhone() {
  const {
    params: { title },
  } = useRoute<RouteProp<RootStackParamList, 'RecognitionDetails'>>();

  return (
    <>
      <ScreenHeader
        route={'RecognitionDetails'}
        customTitle={IS_PH ? 'Recognition' : title}
        isLeftArrowBackShown
      />
      <RecognitionDetailsContent />
    </>
  );
}
