import React from 'react';
import { country } from 'utils/context';
import TeamTargetScreenPH from 'features/teamManagement/ph/tablet/teamPanel/TeamTargetLayout';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import HomeButton from 'navigation/components/ScreenHeader/tablet/HomeButton';
import { Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

const TeamTargetScreenHeader = () => {
  const { t } = useTranslation(['teamManagement']);
  return (
    <ScreenHeader
      customTitle={t('teamManagement:teamTarget.title')}
      isLeftArrowBackShown
      route={'TeamManagement'}
    />
  );
};

const TeamTargetContent = () => {
  switch (country) {
    case 'ph':
      return <TeamTargetScreenPH />;
    case 'my':
      return <></>;
    case 'ib':
      return <></>;
    default:
      return <TeamTargetScreenPH />;
  }
};

const TeamTargetScreen = () => {
  const { colors } = useTheme();
  return (
    <Box flex={1} backgroundColor={colors.palette.fwdGreyDark[20]}>
      <TeamTargetScreenHeader />
      <TeamTargetContent />
    </Box>
  );
};

export default TeamTargetScreen;
