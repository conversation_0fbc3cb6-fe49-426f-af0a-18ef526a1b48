import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ProposalScreenTablet from 'screens/ProposalScreen/ProposalScreen.tablet';
import ProposalScreenPhone from 'screens/ProposalScreen/ProposalScreen.phone';

export default function ProposalScreen() {
  // const isTablet = useBoundStore(store => store.isTablet);
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? <ProposalScreenTablet /> : <ProposalScreenPhone />;
}
