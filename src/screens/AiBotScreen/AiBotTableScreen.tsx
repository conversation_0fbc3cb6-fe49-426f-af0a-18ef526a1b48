import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { Platform, TouchableOpacity, View } from 'react-native';
import styled from '@emotion/native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { ScrollView } from 'react-native';
import { useTheme } from '@emotion/react';
import { Icon, addToast } from 'cube-ui-components';
import { Share } from 'react-native';
import { useRef } from 'react';
import ViewShot from 'react-native-view-shot';
import * as Sharing from 'expo-sharing';
import * as MediaLibrary from 'expo-media-library';
import { ACTIVE_OPACITY } from 'features/aiBot/constants/App';

const Container = styled(View)(({ theme: { space } }) => ({
  flex: 1,
}));

const ButtonContainer = styled(View)(({ theme: { space } }) => ({
  flexDirection: 'row',
  justifyContent: 'center',
  gap: space[6],
}));

const TableContainer = styled(ScrollView)(({ theme: { colors } }) => ({
  flex: 1,
  position: 'relative',
  backgroundColor: colors.palette.fwdGrey[50],
}));

const SHOW_TABLE_BUTTONS = false;

export default function AiBotTableScreen() {
  const route = useRoute<RouteProp<RootStackParamList, 'AiBotTable'>>();

  const tableNode = route.params?.tableNode;

  const viewShot = useRef<ViewShot>(null);

  const { space, colors } = useTheme();

  const onPressShare = async () => {
    try {
      const uri = await viewShot.current?.capture?.();

      if (Platform.OS == 'android') {
        await Sharing.shareAsync('file://' + uri);
      } else if (Platform.OS == 'ios') {
        await Share.share({ url: 'file://' + uri });
      }
    } catch (error: any) {
      console.error('Snapshot failed', error.message);

      return;
    }
  };

  async function onSavePress() {
    try {
      await MediaLibrary.getPermissionsAsync();
      await MediaLibrary.requestPermissionsAsync(true);

      const uri = await viewShot.current?.capture?.();

      if (!uri) throw new Error('Not saved');

      await MediaLibrary.saveToLibraryAsync(
        Platform.OS === 'ios' ? uri : `file://${uri}`,
      );

      addToast([
        {
          message: 'Table has been saved to your gallery',
          IconLeft: <Icon.Tick />,
        },
      ]);
    } catch (error: any) {
      console.error('Snapshot failed', error.message);
    }
  }

  return (
    <Container>
      <View>
        <ScreenHeader
          route={'AiBotTable'}
          isLeftArrowBackShown
          customTitle=""
          rightChildren={
            SHOW_TABLE_BUTTONS ? (
              <ButtonContainer>
                <TouchableOpacity onPress={onSavePress} activeOpacity={ACTIVE_OPACITY}>
                  <Icon.Download
                    size={space[6]}
                    fill={colors.palette.fwdDarkGreen[100]}
                  />
                </TouchableOpacity>
                <TouchableOpacity onPress={onPressShare} activeOpacity={ACTIVE_OPACITY}>
                  <Icon.Share
                    size={space[6]}
                    fill={colors.palette.fwdDarkGreen[100]}
                  />
                </TouchableOpacity>
              </ButtonContainer>
            ) : (
              <></>
            )
          }
        />
      </View>
      <TableContainer>
        <View>
          <ScrollView
            style={[
              {
                paddingBottom: space[3],
                overflow: 'visible',
              },
            ]}
            horizontal={true}
            scrollEnabled={true}>
            <ViewShot ref={viewShot}>{tableNode}</ViewShot>
          </ScrollView>
        </View>
      </TableContainer>
    </Container>
  );
}
