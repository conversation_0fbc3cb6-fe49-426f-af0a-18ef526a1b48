import { View } from 'react-native';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { SearchHeader } from 'features/startClaim/components/header/SearchHeader';
import { ItemClaim } from 'features/startClaim/components/itemClaim/ItemClaim';
import { FlashList, ListRenderItem } from '@shopify/flash-list';
import { Button, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { EmptyTracking } from 'features/agentAssist/assets/EmptyTracking';
import { useClaim } from 'features/startClaim/components/hooks/useClaim';
import { ExpressClaimPolicy } from 'types/policy';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function StartClaimScreenPhone() {
  const { sizes, colors, space } = useTheme();
  const { t } = useTranslation('agentAssist');
  const {
    data,
    isLoadingStartClaim,
    isLoading,
    keyword,
    setKeyword,
    onSelectItemData,
    onNext,
    policyNumberSelected,
  } = useClaim();
  const renderItem: ListRenderItem<ExpressClaimPolicy> = ({ item }) => <ItemClaim
    onSelectItemData={onSelectItemData} item={item} policyNumberSelected={policyNumberSelected} />;
  return (
    <Root>
      <ScreenHeader
        route={'StartClaim'}
        isLeftArrowBackShown
      />
      <SearchHeader setKeyword={setKeyword} isLoading={!!keyword && isLoading} />
      <FlashList
        estimatedItemSize={sizes[57]}
        data={data}
        contentContainerStyle={{
          paddingBottom: space[12],
        }}
        extraData={[policyNumberSelected]}
        ListHeaderComponent={
          keyword && data?.length > 0 ? <ResultLabel color={colors.palette.fwdGreyDarker}>
            {t('agentAssist.claim.search.result', { count: data?.length })}
          </ResultLabel> : undefined
        }
        ListEmptyComponent={keyword ? (
          <Empty>
            <EmptyTracking />
            <EmptyLble color={colors.palette.fwdGreyDarker}>
              {t('agentAssist.claim.search.empty')}
              {/*{t('agentAssist.claim.search.empty1')}*/}
            </EmptyLble>
          </Empty>
        ) : undefined}
        renderItem={renderItem}
      />
      {!!policyNumberSelected && <ViewFooter
        edges={['bottom']}>
        <Row gap={space[4]} justifyContent="center">
          <BtnClaim
            loading={isLoadingStartClaim}
            disabled={isLoadingStartClaim}
            text={t('agentAssist.claim.next')}
            onPress={onNext}
            subtext={t('agentAssist.claim.details')}
          />
        </Row>
      </ViewFooter>}
    </Root>
  );
}
const Root = styled(View)(({ theme: { colors } }) => ({
  backgroundColor: colors.background,
  flex: 1,
}));
const Empty = styled(View)(({ theme: { space } }) => ({
  alignItems: 'center',
  marginVertical: space[10],
}));
const ViewFooter = styled(SafeAreaView)(({ theme: { space, colors } }) => ({
  paddingHorizontal: space[4],
  paddingTop: space[4],
  paddingBottom: space[4],
  backgroundColor: colors.background,
  borderTopWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  gap: space[4],
}));
const BtnClaim = styled(Button)(({ theme: { space, colors, borderRadius } }) => ({
  flex: 1
}));
const EmptyLble = styled(Typography.Text)(({ theme: { space, sizes } }) => ({
  textAlign: 'center',
  marginHorizontal: space[19],
  lineHeight: space[6],
  fontSize: sizes[4],
  marginTop: space[2],
}));
const ResultLabel = styled(Typography.Label)(({ theme: { space } }) => ({
  marginLeft: space[4],
  marginTop: space[4],
}));
