import { View } from 'react-native';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import AppTopTabBar from 'components/AppTopTabBar';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { ClaimTracking } from 'features/agentAssist/components/ClaimTracking';
import { OverView } from 'features/agentAssist/components/OverView';
import { SearchComponent } from 'features/agentAssist/components/search/SearchComponent';
import { useOverViewData } from 'features/agentAssist/hooks/useOverViewData';

export default function AgentAssistScreenPhone() {
  const HomeTab = createMaterialTopTabNavigator();
  const { t } = useTranslation('navigation');
  const {isLoading, data, dataOverView, numberStatus, refetch} = useOverViewData();

  return (
    <Root>
      <ScreenHeader
        route={'AgentAssist'}
        showBottomSeparator={false}
        rightChildren={<SearchComponent data={data?.caseList || []} limit={20} offset={0} status={[]} />}
      />
      <HomeTab.Navigator
        tabBar={props => <AppTopTabBar variant="scrollable" {...props} />}
        screenOptions={{ swipeEnabled: false }}>
        <HomeTab.Screen
          name="Overview"
          options={{ tabBarLabel: t('tabScreen.Overview') }}
          children={() => <OverView refetch={refetch} numberStatus={numberStatus} data={dataOverView} isLoading={isLoading} />}
        />
        <HomeTab.Screen
          name="MyTasks"
          options={{ tabBarLabel: t('tabScreen.claim') }}
          children={() => <ClaimTracking refetch={refetch} data={data} isLoading={isLoading} />}
        />
      </HomeTab.Navigator>
    </Root>
  );
}
const Root = styled(View)(({ theme: { colors, space } }) => ({
  backgroundColor: colors.background,
  flex: 1,
}));
