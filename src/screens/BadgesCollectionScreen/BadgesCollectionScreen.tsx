import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import BadgesCollectionScreenPhone from './BadgeCollectionScreen.phone';
import BadgesCollectionScreenTablet from './BadgeCollectionScreen.tablet';

export default function BadgesCollectionScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <BadgesCollectionScreenTablet />
  ) : (
    <BadgesCollectionScreenPhone />
  );
}
