import React, { useEffect, useRef, useState } from 'react';
import { FlatList, ScrollView, View, useWindowDimensions } from 'react-native';
import styled from '@emotion/native';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import HeaderBackButton from 'navigation/components/HeaderBackButton';
import CollectionSummaryCard from 'features/badgesCollection/components/CollectionSummaryCard';
import BadgeCollectionCard, {
  CARD_WIDTH,
} from 'features/badgesCollection/components/BadgeCollectionCard';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useTranslation } from 'react-i18next';
import i18n from 'utils/translation';
import {
  Badge,
  Column,
  H5,
  LargeBody,
  Row,
  Typography,
} from 'cube-ui-components';
import { Theme, useTheme } from '@emotion/react';
import BadgeSVG from 'features/home/<USER>/BadgeSVG';
import { FlashList } from '@shopify/flash-list';
import useBoundStore from 'hooks/useBoundStore';

const MOCK_CARD_DATA = [
  { isCurrentMonth: false },
  { isCurrentMonth: false },
  { isCurrentMonth: false },
  { isCurrentMonth: true },
  { isCurrentMonth: false },
  { isCurrentMonth: false },
];

export default function BadgesCollectionScreenTablet() {
  // Flatlist & scrolling values
  const ref = useRef<FlatList<any>>(null);
  const [curPage, setCurPage] = useState(0);
  const { width } = useWindowDimensions();
  const marginLeft = (width - CARD_WIDTH) / 2;
  const itemSeparatorWidth = 16;
  const theme = useTheme();
  const monthYearArr = getMonthYearArray();

  function generateArray() {
    const array = [];

    for (let i = 1; i <= 30; i++) {
      const element = i + ' Nov';
      array.push(element);
    }

    return array;
  }

  const array = generateArray();
  console.log(monthYearArr);

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  // const mockIsLoading = useBoundStore(state => state.home.mockIsLoading);
  // const isLoading = mockIsLoading;

  // useEffect(() => {
  //   isLoading ? setAppLoading() : setAppIdle();
  // }, [isLoading]);

  useEffect(() => {
    setAppLoading();
    const timer = setTimeout(() => {
      setAppIdle();
    }, 3000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <ScreenHeader route={'BadgesCollection'} isLeftArrowBackShown />

      <ScrollView bounces={false}>
        <View style={{ padding: 16, paddingHorizontal: 114, gap: 24 }}>
          <BadgeSummary />
          {monthYearArr.map(item => {
            return (
              <View
                style={{
                  backgroundColor: theme.colors.background,
                  padding: 24,
                  borderRadius: theme.borderRadius.large,
                }}>
                <Row style={{ marginBottom: 16 }}>
                  <Typography.H7 fontWeight="bold">{item}</Typography.H7>
                </Row>
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    rowGap: 8,
                  }}>
                  {array.map(item => {
                    let randomNum = Math.random();
                    return (
                      <View
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center',

                          width: 70,
                          height: 100,
                        }}>
                        <View style={{ marginBottom: 4 }}>
                          <Badge
                            status={randomNum > 0.5 ? 'active' : 'inactive'}
                          />
                        </View>
                        <Typography.Label
                          fontWeight={'bold'}
                          color={
                            randomNum > 0.5
                              ? theme.colors.primary
                              : theme.colors.palette.fwdGrey[100]
                          }>
                          {item}
                        </Typography.Label>
                      </View>
                    );
                  })}
                </View>
              </View>
            );
          })}
        </View>

        {/* <FlatList
            data={MOCK_CARD_DATA}
            ref={ref}
            bounces={false}
            horizontal
            showsHorizontalScrollIndicator={false}
            onScroll={event => {
              const { contentOffset } = event.nativeEvent;
              const index = Math.round(
                contentOffset.x / (CARD_WIDTH + itemSeparatorWidth),
              );
              setCurPage(index);
            }}
            snapToInterval={CARD_WIDTH + 16}
            decelerationRate={'fast'}
            contentContainerStyle={{
              alignItems: 'center',
              marginLeft: marginLeft,
            }}
            renderItem={({ item, index }) => (
              <View
                style={[
                  index === MOCK_CARD_DATA.length - 1
                    ? { marginRight: marginLeft * 2 }
                    : {},
                ]}>
                <BadgeCollectionCard
                  isActive={curPage === index}
                  isCurrentMonth={item.isCurrentMonth}
                />
              </View>
            )}
            ItemSeparatorComponent={() => (
              <View style={{ width: itemSeparatorWidth }} />
            )}
          />

          <EmptyBottomView /> */}
      </ScrollView>
    </>
  );
}

function BadgeSectionList() {
  const theme = useTheme();
  const monthYearArr = getMonthYearArray();
  const array = new Array(30).fill('1 Jun');
  return (
    <FlashList
      data={monthYearArr}
      keyExtractor={item => item}
      estimatedItemSize={444}
      ItemSeparatorComponent={() => <View style={{ height: 24 }} />}
      renderItem={({ item }) => {
        return (
          <View
            style={{
              backgroundColor: theme.colors.background,
              padding: 24,
              borderRadius: theme.borderRadius.large,
            }}>
            <Row style={{ marginBottom: 16 }}>
              <Typography.H7 fontWeight="bold">{item}</Typography.H7>
            </Row>
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                flexWrap: 'wrap',
                rowGap: 8,
              }}>
              {array.map((_, i) => {
                return (
                  <View
                    key={item + i}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: 16,
                    }}>
                    <View
                      style={{
                        alignSelf: 'center',
                      }}>
                      <Badge status={i === 0 ? 'active' : 'inactive'} />
                    </View>
                    <Typography.Label
                      fontWeight={'bold'}
                      color={
                        i === 0
                          ? theme.colors.primary
                          : theme.colors.palette.fwdGrey[100]
                      }>
                      1 {item.slice(0, 3)}
                    </Typography.Label>
                  </View>
                );
              })}
            </View>
          </View>
        );
      }}
    />
  );
}

const EmptyBottomView = styled.View(({ theme }) => ({
  backgroundColor: 'transparent',
  paddingVertical: theme.space[4],
}));

const BadgeSummary = () => {
  return (
    <Row>
      <View
        style={{
          display: 'flex',
          flexDirection: 'column',
          width: 184,
        }}>
        <Typography.LargeBody>Badges in this month</Typography.LargeBody>
        <H5 fontWeight="bold">1</H5>
      </View>
      <View
        style={{
          display: 'flex',
          flexDirection: 'column',
          width: 184,
        }}>
        <Typography.LargeBody>Total badges</Typography.LargeBody>
        <H5 fontWeight="bold">24</H5>
      </View>
      <View
        style={{
          display: 'flex',
          flexDirection: 'column',
          width: 184,
        }}>
        <Typography.LargeBody>Your best doing month</Typography.LargeBody>
        <H5 fontWeight="bold">May 2022</H5>
      </View>
    </Row>
  );
};

function getMonthYearArray() {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth() + 1; // Adding 1 since getMonth() returns zero-based index

  const monthYearArray = [];

  const monthNames = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];

  for (let month = currentMonth; month >= 1; month--) {
    const shortMonthName = monthNames[month - 1];
    const monthYear = `${shortMonthName} ${currentYear}`;
    monthYearArray.push(monthYear);
  }

  return monthYearArray;
}
