import SocialMarketingLoading from 'features/socialMarketing/components/SocialMarketingLoading';
import { useIgniteFeatureEnable } from 'features/socialMarketing/hooks/useIgniteFeatureFlag';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function SocialMarketingLoadingScreen() {
  const isIgniteFeatureEnabled = useIgniteFeatureEnable();

  if (!isIgniteFeatureEnabled) {
    return <NotFoundScreen />;
  }

  return <SocialMarketingLoading />;
}
