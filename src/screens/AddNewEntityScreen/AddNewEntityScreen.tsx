import React from 'react';
import styled from '@emotion/native';
import AddNewEntityForm from 'features/lead/components/AddEntityForm/AddNewEntityInputForm';
import Animated, { LinearTransition } from 'react-native-reanimated';

export default function AddNewLeadScreen() {
  return (
    <AddNewEntityRootContainer layout={LinearTransition}>
      <AddNewEntityForm />
    </AddNewEntityRootContainer>
  );
}

const AddNewEntityRootContainer = styled(Animated.View)(
  ({ theme: { colors } }) => ({
    flex: 1,
    backgroundColor: colors.background,
  }),
);
