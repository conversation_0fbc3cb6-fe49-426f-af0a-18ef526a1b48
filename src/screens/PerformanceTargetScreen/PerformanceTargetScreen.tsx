import React from 'react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import PerformanceTargetScreenTablet from 'screens/PerformanceTargetScreen/PerformanceTargetScreen.tablet';
import PerformanceTargetScreenPhone from 'screens/PerformanceTargetScreen/PerformanceTargetScreen.phone';

export default function PerformanceTargetScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <PerformanceTargetScreenTablet />
  ) : (
    <PerformanceTargetScreenPhone />
  );
}
