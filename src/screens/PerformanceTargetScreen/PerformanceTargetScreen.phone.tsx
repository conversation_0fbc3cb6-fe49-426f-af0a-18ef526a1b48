import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { AxiosError } from 'axios';
import {
  MAX_PERFORMANCE_TARGET_LENGTH,
  TARGET_SETTING_INVALID_ERROR_CODE,
} from 'constants/errors';
import {
  Box,
  Button,
  H7,
  Icon,
  LoadingIndicator,
  Row,
  Typography,
  addErrorBottomToast,
  addToast,
} from 'cube-ui-components';
import TargetSettingFieldPair, {
  MemoTargetSettingFieldPair,
} from 'features/performance/components/target/TargetSettingFieldPair';
import {
  PerformanceTarget,
  RootPerformanceTargetItem,
  months,
} from 'features/performance/type';
import {
  useGetPerformanceTarget,
  useUpdatePerformanceTarget,
} from 'hooks/usePerformanceTarget';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dimensions,
  FlatList,
  Modal,
  Platform,
  Pressable,
  PressableProps,
  TouchableOpacity,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Animated, {
  FadeIn,
  FadeOut,
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { RootStackParamList, RootStackParamListMap } from 'types';
import { ProcPerformanceTargetResponse } from 'types/performance';
import { country } from 'utils/context';

const tabTypeArr = ['yearly', 'monthly'] as const;
type TabType = (typeof tabTypeArr)[number];

export default function PerformanceTargetScreenPhone() {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['performance', 'common']);
  const { t: tLead } = useTranslation('lead');
  const borderPosition = useSharedValue(0);
  const { bottom } = useSafeAreaInsets();

  const [tabOnFocus, setTabOnFocus] = useState<TabType>('yearly');

  const borderHeight = 1;
  const windowWidth = Dimensions.get('window').width;
  const animatedBarWidth = windowWidth / (tabTypeArr?.length ?? 1);

  useEffect(() => {
    const index = tabTypeArr.indexOf(tabOnFocus);
    borderPosition.value = withTiming(index * animatedBarWidth, {
      duration: 200,
    });
  }, [tabOnFocus]);

  const animatedBorderStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: borderPosition.value }],
    };
  });

  const activeColor = colors.palette.fwdAlternativeOrange[100];

  // ----------- for setting target--------
  const { canGoBack, goBack } =
    useNavigation<NavigationProp<RootStackParamList>>();

  const { mutateAsync, isLoading: isUploading } = useUpdatePerformanceTarget();
  const { data, isLoading } = useGetPerformanceTarget();

  const yearlyData = useMemo(() => {
    return {
      ACE: data?.targetACE,
      Case: data?.targetCASE,
      AFYP: data?.targetAFYP ?? undefined,
    } satisfies PerformanceTarget<RootPerformanceTargetItem>['yearly'];
  }, [data]);

  const monthlyData = useMemo(() => {
    return months.reduce((acc, month, idx) => {
      acc[month] = {
        ACE: data?.monthTarget?.[idx]?.targetACE,
        AFYP: data?.monthTarget?.[idx]?.targetAFYP ?? undefined,
        Case: data?.monthTarget?.[idx]?.targetCASE,
      };
      return acc;
    }, {} as PerformanceTarget<RootPerformanceTargetItem>['monthly']);
  }, [data]);

  const [localYearlyPerfTarget, setLocalYearlyPerfTarget] = useState<
    PerformanceTarget<RootPerformanceTargetItem>['yearly']
  >({ ...yearlyData });

  const [localMonthlyPerfTarget, setLocalMonthlyPerfTarget] = useState<
    PerformanceTarget<RootPerformanceTargetItem>['monthly']
  >({ ...monthlyData });

  const isYearlyTargetUnchanged = Object.keys(localYearlyPerfTarget).every(
    key => {
      const typedKey = key as keyof typeof localYearlyPerfTarget;
      return localYearlyPerfTarget[typedKey] === yearlyData[typedKey];
    },
  );
  const isMonthlyTargetUnchanged = months.every(month => {
    const isUnchanged = Object.keys(localMonthlyPerfTarget[month]).every(
      key => {
        const typedKey =
          key as keyof (typeof localMonthlyPerfTarget)[(typeof months)[number]];

        return (
          localMonthlyPerfTarget[month][typedKey] ===
          monthlyData[month][typedKey]
        );
      },
    );
    return isUnchanged;
  });
  const isButtonDisabled = isYearlyTargetUnchanged && isMonthlyTargetUnchanged;

  const saveAction = () => {
    const monthlyTargets = months.reduce((acc, month, idx) => {
      acc.push({
        month: `${idx + 1}`,
        targetACE: localMonthlyPerfTarget[month].ACE,
        targetAFYP: localMonthlyPerfTarget[month].AFYP,
        targetCASE: localMonthlyPerfTarget[month].Case,
      });
      return acc;
    }, [] as ProcPerformanceTargetResponse['monthTarget']);

    const newDataToUpload = {
      targetYear: data?.targetYear ?? String(new Date().getFullYear()),
      targetACE: country == 'id' ? undefined : localYearlyPerfTarget.ACE,
      targetCASE: localYearlyPerfTarget.Case,
      targetAFYP: country == 'id' ? localYearlyPerfTarget.AFYP : undefined,
      monthTarget: monthlyTargets,
    } satisfies ProcPerformanceTargetResponse;

    mutateAsync(newDataToUpload, {
      onSuccess: () => {
        canGoBack()
          ? goBack()
          : console.log(
              '🚀 ~ file: PerformanceTargetScreen.tablet.tsx:151 ~ mutateAsync ~ canGoBack:',
              canGoBack,
            );

        addToast(
          [
            {
              message: t('performance:performance.editTarget.success.message'),
              IconLeft: <Icon.Tick />,
            },
          ],
          undefined,
          true,
        );
      },
      onError: e => {
        const error = e as AxiosError<
          {
            messageList?: Array<{ code: string; content: string }>;
          },
          unknown
        >;
        if (error.response) {
          const content = error.response.data.messageList?.[0]?.content;
          if (
            country === 'id' &&
            content === TARGET_SETTING_INVALID_ERROR_CODE
          ) {
            addErrorBottomToast([
              {
                message: tLead('minimumRequirement'),
              },
            ]);
            return;
          }
        }
        addToast(
          [
            {
              message: 'Please try again later.',
            },
          ],
          undefined,
          true,
        );
      },
    });
  };

  const onSave = () => saveAction();

  const onReset = () => {
    setLocalMonthlyPerfTarget({ ...monthlyData });
    setLocalYearlyPerfTarget({ ...yearlyData });
  };
  useEffect(() => {
    onReset();
  }, [data]);

  const yearlyError = useMemo(() => {
    return country === 'id' &&
      typeof localYearlyPerfTarget.AFYP === 'number' &&
      localYearlyPerfTarget.AFYP >= Math.pow(10, MAX_PERFORMANCE_TARGET_LENGTH)
      ? t('performance:performance.error.maxLength', {
          length: MAX_PERFORMANCE_TARGET_LENGTH,
        })
      : undefined;
  }, [localYearlyPerfTarget.AFYP, t]);

  const hasMonthError = useMemo(() => {
    for (const month of months) {
      const monthError =
        country === 'id' &&
        typeof localMonthlyPerfTarget[month].AFYP === 'number' &&
        localMonthlyPerfTarget[month].AFYP >=
          Math.pow(10, MAX_PERFORMANCE_TARGET_LENGTH);

      if (monthError) {
        return true;
      }
    }
    return false;
  }, [localMonthlyPerfTarget]);

  const isError = !!yearlyError || hasMonthError;

  const keyExtractor = useCallback((item: string) => item, []);

  return (
    <>
      <ScreenHeaderWithModal
        saveAction={saveAction}
        isTargetEdited={
          (isYearlyTargetUnchanged && isMonthlyTargetUnchanged) === false
        }
      />
      <Row
        bgColor={colors.background}
        borderBottom={borderHeight}
        borderBottomColor={colors.palette.fwdGrey[100]}>
        {tabTypeArr.map(tab => {
          return (
            <TouchableOpacity
              key={tab}
              style={{
                flex: 1,
                minHeight: space[11],
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={() => {
                setTabOnFocus(tab);
              }}>
              <H7
                fontWeight={tab === tabOnFocus ? 'bold' : 'normal'}
                color={tab === tabOnFocus ? activeColor : undefined}>
                {tab == 'monthly'
                  ? t('performance:target.title.monthly')
                  : t('performance:target.title.yearly')}
              </H7>
            </TouchableOpacity>
          );
        })}
        <Animated.View
          style={[
            {
              position: 'absolute',
              bottom: 0,
              height: 3,
              width: animatedBarWidth,
              backgroundColor: activeColor,
            },
            animatedBorderStyle,
          ]}
        />
      </Row>
      {isLoading ? (
        <Box
          alignSelf="center"
          w={space[10]}
          h={space[10]}
          mt={space[6]}
          m={space[3]}>
          <LoadingIndicator size={space[10]} />
        </Box>
      ) : (
        <>
          <Box flex={1} backgroundColor={colors.background}>
            {tabOnFocus == 'monthly' && (
              <KeyboardAwareScrollView
                style={{ flex: 1 }}
                bounces={false}
                enableOnAndroid={true}
                extraHeight={Platform.OS === 'android' ? 44 : 184}>
                <Animated.View
                  layout={LinearTransition}
                  entering={FadeIn}
                  exiting={FadeOut}
                  style={{ flex: 1 }}>
                  <FlatList
                    data={months}
                    keyExtractor={keyExtractor}
                    renderItem={({ item: month, index: idx }) => {
                      const monthError =
                        country === 'id' &&
                        typeof localMonthlyPerfTarget[month].AFYP ===
                          'number' &&
                        localMonthlyPerfTarget[month].AFYP >=
                          Math.pow(10, MAX_PERFORMANCE_TARGET_LENGTH)
                          ? t('performance:performance.error.maxLength', {
                              length: MAX_PERFORMANCE_TARGET_LENGTH,
                            })
                          : undefined;
                      return (
                        <Box
                          py={space[4]}
                          mx={space[4]}
                          gap={space[4]}
                          alignSelf="baseline"
                          borderBottom={idx !== months?.length - 1 ? 1 : 0}
                          borderBottomColor={colors.palette.fwdGrey[100]}>
                          <Typography.H7 fontWeight="bold">
                            {month}
                          </Typography.H7>
                          <MemoTargetSettingFieldPair
                            mode="phone"
                            dataLeft={
                              country === 'id'
                                ? localMonthlyPerfTarget[month].AFYP
                                : localMonthlyPerfTarget[month].ACE
                            }
                            dataRight={localMonthlyPerfTarget[month].Case}
                            dataType={month}
                            saveLocalTarget={setLocalMonthlyPerfTarget}
                            errorLeft={monthError}
                          />
                        </Box>
                      );
                    }}
                  />
                </Animated.View>
              </KeyboardAwareScrollView>
            )}
            {tabOnFocus == 'yearly' && (
              <KeyboardAwareScrollView bounces={false}>
                <Animated.View
                  layout={LinearTransition}
                  entering={FadeIn}
                  exiting={FadeOut}>
                  <Box p={space[4]} gap={space[4]} alignSelf="baseline">
                    <Typography.H7 fontWeight="bold">
                      {new Date().getFullYear()}
                    </Typography.H7>

                    <TargetSettingFieldPair
                      mode="phone"
                      dataLeft={
                        country === 'id'
                          ? localYearlyPerfTarget.AFYP
                          : localYearlyPerfTarget.ACE
                      }
                      dataRight={localYearlyPerfTarget.Case}
                      dataType={'yearly'}
                      saveLocalTarget={setLocalYearlyPerfTarget}
                      errorLeft={yearlyError}
                    />
                  </Box>
                  {/* <PerformanceTargetScreenTablet /> */}
                </Animated.View>
              </KeyboardAwareScrollView>
            )}
          </Box>
          <Row
            px={space[4]}
            pt={space[4]}
            borderTop={1}
            borderTopColor={colors.palette.fwdGrey[100]}
            pb={space[3] + (bottom || space[6])}
            backgroundColor={colors.background}
            gap={space[5]}
            justifyContent="center">
            <ResetButton disabled={isButtonDisabled} onPress={onReset} />
            <Button
              loading={isUploading}
              disabled={isButtonDisabled || isError}
              onPress={onSave}
              variant="primary"
              style={{ flex: 1 }}
              text={t('performance:performance.editTarget.save')}
            />
          </Row>
        </>
      )}
    </>
  );
}

function ResetButton(props: Partial<PressableProps>) {
  const { colors, space, borderRadius } = useTheme();
  const [isPress, setisPress] = useState(false);
  const { t } = useTranslation('performance');

  return (
    <Pressable
      onPressIn={() => setisPress(true)}
      onPressOut={() => setisPress(false)}
      style={{
        flex: 1,
        opacity: props.disabled ? 0.5 : 1,
        minHeight: space[11],
        borderRadius: borderRadius['x-small'],
        borderWidth: 2,
        justifyContent: 'center',
        alignItems: 'center',
        borderColor: colors.primary,
        backgroundColor: isPress ? colors.primary : colors.background,
      }}
      {...props}>
      <Typography.H7
        fontWeight="bold"
        color={isPress ? colors.background : colors.primary}>
        {t('performance.editTarget.reset')}
      </Typography.H7>
    </Pressable>
  );
}

function ScreenHeaderWithModal({
  saveAction,
  isTargetEdited,
}: {
  saveAction: () => void;
  isTargetEdited: boolean;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('performance');

  const [isModalOn, setIsModalOn] = useState(false);

  const { navigate, canGoBack, goBack } =
    useNavigation<NavigationProp<RootStackParamListMap['my']>>();
  // const { onPress, ...restProps } = props;

  const onPressCancel = () => {
    setIsModalOn(false);

    canGoBack() ? goBack() : console.log('cannot go back');
  };
  const onPressSave = () => {
    saveAction();
    setIsModalOn(false);
    if (!canGoBack()) {
      console.log(
        '🚀 ~ file: PerformanceTargetScreen.tablet.tsx:254 ~ onPressSave ~ canGoBack: ',
        canGoBack(),
      );
      return;
    }

    saveAction();

    // TODO: if saveAction succeed, then run then following code
    goBack();
    addToast(
      [
        {
          message: t('performance.editTarget.success.message'),
          IconLeft: <Icon.Tick />,
        },
      ],
      undefined,
      true,
    );
  };

  const onPressHeaderLeftButton = () => {
    if (isTargetEdited) {
      setIsModalOn(true);
    } else {
      canGoBack() ? goBack() : console.log('cannot go back');
    }
  };
  return (
    <>
      <ScreenHeader
        showBottomSeparator={false}
        route={'PerformanceTarget'}
        isLeftArrowBackShown
        customTitle={t('performance.editTarget.screenTitle')}
        onPressDefaultLeftButton={onPressHeaderLeftButton}
      />
      <Modal
        visible={isModalOn}
        transparent
        statusBarTranslucent
        animationType="fade">
        <Box
          flex={1}
          backgroundColor={'rgba(0, 0, 0, 0.5)'}
          alignItems="center"
          justifyContent="center">
          <Box
            maxW={380}
            padding={space[6]}
            borderRadius={borderRadius.large}
            backgroundColor={colors.background}>
            <Row justifyContent="flex-end">
              <TouchableOpacity onPress={() => setIsModalOn(false)}>
                <Icon.Close size={space[6]} fill={colors.secondary} />
              </TouchableOpacity>
            </Row>
            <Box gap={space[4]}>
              <Typography.H6 fontWeight="bold">
                {t('performance.editTarget.exitModal.title')}
              </Typography.H6>
              <Typography.LargeBody>
                {t('performance.editTarget.exitModal.content')}
              </Typography.LargeBody>
            </Box>
            <Row gap={space[4]} mt={space[6]}>
              <Button
                onPress={onPressCancel}
                variant="secondary"
                text={t('performance.editTarget.exitModal.notSave')}
                style={{ flex: 1 }}
                contentStyle={{
                  flexGrow: 1,
                }}
              />
              <Button
                onPress={onPressSave}
                variant="primary"
                text={t('performance.editTarget.exitModal.save')}
                style={{ flex: 1 }}
                contentStyle={{
                  flexGrow: 1,
                }}
              />
            </Row>
          </Box>
        </Box>
      </Modal>
    </>
  );
}
