import {
  TouchableOpacity,
  Modal,
  Pressable,
  PressableProps,
  FlatList,
} from 'react-native';
import React, { useEffect, useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  Icon,
  PictogramIcon,
  Row,
  Typography,
  addErrorBottomToast,
  addToast,
} from 'cube-ui-components';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList, RootStackParamListMap } from 'types';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useTranslation } from 'react-i18next';
import {
  useGetPerformanceTarget,
  useUpdatePerformanceTarget,
} from 'hooks/usePerformanceTarget';
import useBoundStore from 'hooks/useBoundStore';
import { ProcPerformanceTargetResponse } from 'types/performance';
import {
  PerformanceTarget,
  RootPerformanceTargetItem,
  months,
} from 'features/performance/type';
import TargetSettingFieldPair, {
  MemoTargetSettingFieldPair,
} from 'features/performance/components/target/TargetSettingFieldPair';
import { country } from 'utils/context';
import {
  MAX_PERFORMANCE_TARGET_LENGTH,
  TARGET_SETTING_INVALID_ERROR_CODE,
} from 'constants/errors';
import { AxiosError } from 'axios';

// const LabelLeft = 'Target ACE';

export default function PerformanceTargetScreenTablet() {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('performance');
  const { t: tLead } = useTranslation('lead');

  const { top, bottom } = useSafeAreaInsets();
  const { navigate, canGoBack, goBack } =
    useNavigation<NavigationProp<RootStackParamList>>();

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const { mutateAsync, isLoading: isUploading } = useUpdatePerformanceTarget();
  const { data, isLoading } = useGetPerformanceTarget();

  const yearlyData = useMemo(() => {
    return {
      ACE: data?.targetACE,
      Case: data?.targetCASE,
      AFYP: data?.targetAFYP ?? undefined,
    } satisfies PerformanceTarget<RootPerformanceTargetItem>['yearly'];
  }, [data]);

  const monthlyData = useMemo(() => {
    return months.reduce((acc, month, idx) => {
      acc[month] = {
        ACE: data?.monthTarget?.[idx]?.targetACE,
        Case: data?.monthTarget?.[idx]?.targetCASE,
        AFYP: data?.monthTarget?.[idx]?.targetAFYP ?? undefined,
      };
      return acc;
    }, {} as PerformanceTarget<RootPerformanceTargetItem>['monthly']);
  }, [data]);

  const [localYearlyPerfTarget, setLocalYearlyPerfTarget] = useState<
    PerformanceTarget<RootPerformanceTargetItem>['yearly']
  >({ ...yearlyData });

  const [localMonthlyPerfTarget, setLocalMonthlyPerfTarget] = useState<
    PerformanceTarget<RootPerformanceTargetItem>['monthly']
  >({ ...monthlyData });

  const isYearlyTargetUnchanged = Object.keys(localYearlyPerfTarget).every(
    key => {
      const typedKey = key as keyof typeof localYearlyPerfTarget;
      return localYearlyPerfTarget[typedKey] === yearlyData[typedKey];
    },
  );
  const isMonthlyTargetUnchanged = months.every(month => {
    const isUnchanged = Object.keys(localMonthlyPerfTarget[month]).every(
      key => {
        const typedKey =
          key as keyof (typeof localMonthlyPerfTarget)[(typeof months)[number]];

        return (
          localMonthlyPerfTarget[month][typedKey] ===
          monthlyData[month][typedKey]
        );
      },
    );
    return isUnchanged;
  });
  const isButtonDisabled = isYearlyTargetUnchanged && isMonthlyTargetUnchanged;

  const saveAction = () => {
    const monthlyTargets = months.reduce((acc, month, idx) => {
      acc.push({
        month: `${idx + 1}`,
        targetACE: localMonthlyPerfTarget[month].ACE,
        targetCASE: localMonthlyPerfTarget[month].Case,
        targetAFYP:
          country == 'id' ? localMonthlyPerfTarget[month].AFYP : undefined,
      });
      return acc;
    }, [] as ProcPerformanceTargetResponse['monthTarget']);

    const newDataToUpload = {
      targetYear: data?.targetYear ?? String(new Date().getFullYear()),
      targetACE: country == 'id' ? undefined : localYearlyPerfTarget.ACE,
      targetCASE: localYearlyPerfTarget.Case,
      targetAFYP: country == 'id' ? localYearlyPerfTarget.AFYP : undefined,
      monthTarget: monthlyTargets,
    } satisfies ProcPerformanceTargetResponse;

    mutateAsync(newDataToUpload, {
      onSuccess: () => {
        canGoBack()
          ? goBack()
          : console.log(
              '🚀 ~ file: PerformanceTargetScreen.tablet.tsx:151 ~ mutateAsync ~ canGoBack:',
              canGoBack,
            );

        addToast(
          [
            {
              message: t('performance.editTarget.success.message'),
              IconLeft: <Icon.Tick />,
            },
          ],
          undefined,
          true,
        );
      },
      onError: e => {
        const error = e as AxiosError<
          {
            messageList?: Array<{ code: string; content: string }>;
          },
          unknown
        >;
        if (error.response) {
          const content = error.response.data.messageList?.[0]?.content;
          if (
            country === 'id' &&
            content === TARGET_SETTING_INVALID_ERROR_CODE
          ) {
            addErrorBottomToast([
              {
                message: tLead('minimumRequirement'),
              },
            ]);
            return;
          }
        }
        addToast(
          [
            {
              message: 'Please try again later.',
            },
          ],
          undefined,
          true,
        );
      },
    });
  };

  const onSave = () => {
    saveAction();
  };

  const onReset = () => {
    setLocalMonthlyPerfTarget({ ...monthlyData });
    setLocalYearlyPerfTarget({ ...yearlyData });
  };

  useEffect(() => {
    if (isLoading == true) {
      setAppLoading();
    } else setAppIdle();
  }, [isLoading]);

  useEffect(() => {
    onReset();
  }, [data]);

  const yearlyError = useMemo(() => {
    return country === 'id' &&
      typeof localYearlyPerfTarget.AFYP === 'number' &&
      localYearlyPerfTarget.AFYP >= Math.pow(10, MAX_PERFORMANCE_TARGET_LENGTH)
      ? t('performance.error.maxLength', {
          length: MAX_PERFORMANCE_TARGET_LENGTH,
        })
      : undefined;
  }, [localYearlyPerfTarget.AFYP, t]);

  const hasMothError = useMemo(() => {
    for (const month of months) {
      const monthError =
        country === 'id' &&
        typeof localMonthlyPerfTarget[month].AFYP === 'number' &&
        localMonthlyPerfTarget[month].AFYP >=
          Math.pow(10, MAX_PERFORMANCE_TARGET_LENGTH);

      if (monthError) {
        return true;
      }
    }
    return false;
  }, [localMonthlyPerfTarget]);

  const isError = !!yearlyError || hasMothError;

  return (
    <>
      <ScreenHeaderWithModal
        saveAction={saveAction}
        isTargetEdited={
          (isYearlyTargetUnchanged && isMonthlyTargetUnchanged) === false
        }
      />
      <KeyboardAwareScrollView
        style={{
          flex: 1,
        }}
        contentContainerStyle={{
          gap: space[4],
          paddingTop: space[4],
          paddingBottom: space[7],
          paddingHorizontal: space[29],
        }}
        extraHeight={120}>
        <Box
          p={space[6]}
          gap={space[6]}
          backgroundColor={colors.background}
          borderRadius={borderRadius.large}>
          <Row alignItems="center" gap={space[2]}>
            <PictogramIcon.Target size={space[10]} />
            <Typography.H6 fontWeight="bold">
              {t('target.title.yearly')}
            </Typography.H6>
          </Row>
          <Row gap={space[10]}>
            <Box flex={1} gap={space[2]} alignSelf="baseline">
              <Typography.H7 fontWeight="bold">
                {new Date().getFullYear()}
              </Typography.H7>
              <TargetSettingFieldPair
                dataLeft={
                  country === 'id'
                    ? localYearlyPerfTarget.AFYP
                    : localYearlyPerfTarget.ACE
                }
                dataRight={localYearlyPerfTarget.Case}
                dataType={'yearly'}
                saveLocalTarget={setLocalYearlyPerfTarget}
                errorLeft={yearlyError}
              />
            </Box>
            <Box flex={1} />
          </Row>
        </Box>
        <Box
          p={space[6]}
          gap={space[6]}
          backgroundColor={colors.background}
          borderRadius={borderRadius.large}>
          <Row alignItems="center" gap={space[2]}>
            <PictogramIcon.Target size={space[10]} />
            <Typography.H6 fontWeight="bold">
              {t('target.title.monthly')}
            </Typography.H6>
          </Row>
          <Row flexWrap="wrap" w={'100%'}>
            <FlatList
              data={months}
              numColumns={2}
              columnWrapperStyle={{
                gap: space[10],
              }}
              renderItem={({ item: month }) => {
                const monthError =
                  country === 'id' &&
                  typeof localMonthlyPerfTarget[month].AFYP === 'number' &&
                  localMonthlyPerfTarget[month].AFYP >=
                    Math.pow(10, MAX_PERFORMANCE_TARGET_LENGTH)
                    ? t('performance.error.maxLength', {
                        length: MAX_PERFORMANCE_TARGET_LENGTH,
                      })
                    : undefined;
                return (
                  <Box
                    key={month}
                    gap={space[2]}
                    flex={1}
                    alignSelf="baseline"
                    marginBottom={space[6]}>
                    <Typography.H7 fontWeight="bold">{month}</Typography.H7>
                    <MemoTargetSettingFieldPair
                      dataLeft={
                        country === 'id'
                          ? localMonthlyPerfTarget[month].AFYP
                          : localMonthlyPerfTarget[month].ACE
                      }
                      dataRight={localMonthlyPerfTarget[month].Case}
                      dataType={month}
                      saveLocalTarget={setLocalMonthlyPerfTarget}
                      errorLeft={monthError}
                    />
                  </Box>
                );
              }}
            />
          </Row>
        </Box>
      </KeyboardAwareScrollView>
      <Row
        px={space[6]}
        pt={space[4]}
        borderTop={1}
        borderTopColor={colors.palette.fwdGrey[100]}
        pb={bottom <= 0 ? space[4] : bottom}
        backgroundColor={colors.background}
        gap={space[5]}
        justifyContent="flex-end">
        <ResetButton disabled={isButtonDisabled} onPress={onReset} />
        <Button
          loading={isUploading}
          disabled={isButtonDisabled || isError}
          onPress={onSave}
          variant="primary"
          text={t('performance.editTarget.save')}
          contentStyle={{
            minHeight: space[13],
            minWidth: space[50],
          }}
        />
      </Row>
    </>
  );
}

function ResetButton(props: Partial<PressableProps>) {
  const { colors, space, borderRadius } = useTheme();
  const [isPress, setisPress] = useState(false);
  const { t } = useTranslation('performance');

  return (
    <Pressable
      onPressIn={() => setisPress(true)}
      onPressOut={() => setisPress(false)}
      style={{
        opacity: props.disabled ? 0.5 : 1,
        minHeight: space[13],
        minWidth: space[29],
        borderRadius: borderRadius['x-small'],
        borderWidth: 2,
        justifyContent: 'center',
        alignItems: 'center',
        borderColor: colors.primary,
        backgroundColor: isPress ? colors.primary : colors.background,
      }}
      {...props}>
      <Typography.H7
        fontWeight="bold"
        color={isPress ? colors.background : colors.primary}>
        {t('performance.editTarget.reset')}
      </Typography.H7>
    </Pressable>
  );
}

function ScreenHeaderWithModal({
  saveAction,
  isTargetEdited,
}: {
  saveAction: () => void;
  isTargetEdited: boolean;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('performance');

  const [isModalOn, setIsModalOn] = useState(false);

  const { navigate, canGoBack, goBack } =
    useNavigation<NavigationProp<RootStackParamListMap['my']>>();
  // const { onPress, ...restProps } = props;

  const onPressCancel = () => {
    setIsModalOn(false);

    canGoBack() ? goBack() : console.log('cannot go back');
  };
  const onPressSave = () => {
    saveAction();
    setIsModalOn(false);
    if (!canGoBack()) {
      console.log(
        '🚀 ~ file: PerformanceTargetScreen.tablet.tsx:254 ~ onPressSave ~ canGoBack: ',
        canGoBack(),
      );
      return;
    }

    saveAction();

    // TODO: if saveAction succeed, then run then following code
    goBack();
    addToast(
      [
        {
          message: t('performance.editTarget.success.message'),
          IconLeft: <Icon.Tick />,
        },
      ],
      undefined,
      true,
    );
  };

  const onPressHeaderLeftButton = () => {
    if (isTargetEdited) {
      setIsModalOn(true);
    } else {
      canGoBack() ? goBack() : console.log('cannot go back');
    }
  };
  return (
    <>
      <ScreenHeader
        route={'PerformanceTarget'}
        isLeftCrossBackShown
        customTitle={t('performance.editTarget.screenTitle')}
        onPressDefaultLeftButton={onPressHeaderLeftButton}
      />
      <Modal
        visible={isModalOn}
        transparent
        statusBarTranslucent
        animationType="fade">
        <Box
          flex={1}
          backgroundColor={'rgba(0, 0, 0, 0.5)'}
          alignItems="center"
          justifyContent="center">
          <Box
            maxW={380}
            padding={space[6]}
            borderRadius={borderRadius.large}
            backgroundColor={colors.background}>
            <Row justifyContent="flex-end">
              <TouchableOpacity onPress={() => setIsModalOn(false)}>
                <Icon.Close size={space[6]} fill={colors.secondary} />
              </TouchableOpacity>
            </Row>
            <Box gap={space[4]} paddingX={space[6]}>
              <Typography.H6 fontWeight="bold">
                {t('performance.editTarget.exitModal.title')}
              </Typography.H6>
              <Typography.LargeBody>
                {t('performance.editTarget.exitModal.content')}
              </Typography.LargeBody>
            </Box>
            <Row p={space[6]} gap={space[4]}>
              <Button
                onPress={onPressCancel}
                variant="secondary"
                text={t('performance.editTarget.exitModal.notSave')}
                contentStyle={{
                  minHeight: space[10],
                  minWidth: space[32],
                }}
              />
              <Button
                onPress={onPressSave}
                variant="primary"
                text={t('performance.editTarget.exitModal.save')}
                contentStyle={{
                  minHeight: space[10],
                  minWidth: space[32],
                }}
              />
            </Row>
          </Box>
        </Box>
      </Modal>
    </>
  );
}
