import CustomerFactFind from 'features/customerFactFind/CustomerFactFind.tablet';
import CustomerFactFindPhone from 'features/customerFactFind/CustomerFactFind.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const CustomerFactFindScreen = () => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? <CustomerFactFind /> : <CustomerFactFindPhone />;
};

export default CustomerFactFindScreen;
