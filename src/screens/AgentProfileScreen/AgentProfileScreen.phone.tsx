import React from 'react';
import { country } from 'utils/context';
import AgentProfilePhoneComponent from 'features/agentProfile/phone';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function AgentProfilePhoneScreen() {
  switch (country) {
    case 'ph':
    case 'my':
    case 'ib':
    case 'id':
      return <AgentProfilePhoneComponent />;
    default: {
      console.log(`no AgentProfile phone screen for region(${country})`);
      return (
        <NotFoundScreen
          customText={`The screen is either not supported or under development`}
        />
      );
    }
  }
}
