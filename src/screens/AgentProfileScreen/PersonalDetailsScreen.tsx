import React, { useState } from 'react';
import { Al<PERSON>, ScrollView, TouchableOpacity, View } from 'react-native';
import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import { Button, Icon, TextField, XView } from 'cube-ui-components';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import HeaderBackButton from 'navigation/components/HeaderBackButton';
import AvatarPlaceholderSVG from 'features/home/<USER>/AvatarPlaceholderSVG';
import UploadProfileImagePanel from 'features/agentProfile/components/UploadProfileImagePanel';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList, CubeResponse } from 'types';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import ResponsiveView from 'components/ResponsiveView';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { currentCountryCode } from 'constants/defaultValues';
import { country } from 'utils/context';
import { useTranslation } from 'react-i18next';
import { Image } from 'expo-image';
import { useUploadAgentPicture } from 'hooks/useUploadAgentPicture';
import { getQueryKey as getAgentProfileQueryKey } from 'hooks/useGetAgentProfile';
import { useQueryClient } from '@tanstack/react-query';

export default function PersonalDetailsScreen() {
  const { t } = useTranslation(['common', 'agentProfile']);
  const { colors, space, borderRadius } = useTheme();
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const AVATAR_SIZE = isWideScreen ? 168 : 120;
  const ICON_CAMERA_SIZE = isWideScreen ? 28 : 24;

  const [panelVisible, setPanelVisible] = useState(false);

  const { data: agentProfile } = useGetAgentProfile();

  const agentProfilePicture = agentProfile?.agentPhotoUrl || '';
  const [imgToUpload, setImgToUpload] = useState<string>('');

  const { mutateAsync, isLoading } = useUploadAgentPicture();
  const queryClient = useQueryClient();

  return (
    <>
      <ScreenHeader
        route={'PersonalDetails'}
        leftChildren={<HeaderBackButton />}
      />

      <ScrollView
        bounces={false}
        showsVerticalScrollIndicator={false}
        style={[
          { backgroundColor: colors.background, padding: space[4] },
          isNarrowScreen && {
            padding: space[3],
          },
        ]}>
        <ProfileImageContainer wideStyle={{ marginBottom: space[12] }}>
          <TouchableOpacity onPress={() => setPanelVisible(true)}>
            {imgToUpload || agentProfilePicture ? (
              <Image
                style={{
                  height: AVATAR_SIZE,
                  width: AVATAR_SIZE,
                  borderRadius: borderRadius.full,
                }}
                source={imgToUpload || agentProfilePicture}
              />
            ) : (
              <AvatarPlaceholderSVG size={AVATAR_SIZE} />
            )}
            <CameraButtonContainer isWideScreen={isWideScreen}>
              <Icon.Camera size={ICON_CAMERA_SIZE} />
            </CameraButtonContainer>
          </TouchableOpacity>
        </ProfileImageContainer>

        <FormContainer>
          <Input
            disabled
            label={t('agentProfile:agentProfile.personalDetails.agentCode')}
            defaultValue={agentProfile?.agentId}
          />
          <Input
            disabled
            label={t('agentProfile:agentProfile.personalDetails.name')}
            defaultValue={agentProfile?.person?.fullName}
          />
          <Input
            disabled
            label={t('agentProfile:agentProfile.personalDetails.email')}
            defaultValue={agentProfile?.contact?.email}
          />

          <XView style={{ gap: space[4] }}>
            {country !== 'id' && (
              <Input
                disabled
                label={t('agentProfile:agentProfile.personalDetails.code')}
                defaultValue={currentCountryCode}
                style={{ flex: 1 }}
              />
            )}
            <Input
              disabled
              label={t(
                'agentProfile:agentProfile.personalDetails.mobileNumber',
              )}
              defaultValue={agentProfile?.contact?.mobilePhone}
              style={{ flex: 2 }}
            />
          </XView>
          {country !== 'id' && (
            <Input
              disabled
              multiline
              autoExpand
              inputContainerStyle={
                {
                  // height: 'auto',
                  // padding: space[1],
                }
              }
              label={t('agentProfile:agentProfile.personalDetails.address')}
              defaultValue={
                agentProfile?.contact?.workAddress?.join(',  ') || ''
              }
            />
          )}
        </FormContainer>
      </ScrollView>

      <View
        style={[
          {
            backgroundColor: colors.background,
            paddingTop: space[4],
            paddingBottom: space[4] + insets.bottom,
          },
          isNarrowScreen && {
            paddingTop: space[3],
            paddingBottom: space[3] + insets.bottom,
          },
        ]}>
        <Button
          text={t('common:save')}
          loading={isLoading}
          style={[
            {
              width: '100%',
              maxWidth: 400,
              alignSelf: 'center',
              paddingHorizontal: space[4],
            },
            isNarrowScreen && {
              paddingHorizontal: space[3],
            },
          ]}
          onPress={async () => {
            const res = await mutateAsync(imgToUpload);
            if (res.status === 200) {
              const resBody: CubeResponse<{
                matchedCount: number;
                modifiedCount: number;
              }> = JSON.parse(res.body);
              const { modifiedCount } = resBody.responseData;
              modifiedCount >= 0 && navigation.goBack();
              queryClient.invalidateQueries({
                queryKey: getAgentProfileQueryKey(agentProfile?.agentId),
              });
            }
          }}
          disabled={!imgToUpload}
        />
      </View>

      <UploadProfileImagePanel
        visible={panelVisible}
        onClose={() => setPanelVisible(false)}
        setImgToUpload={imgPath => setImgToUpload(imgPath)}
      />
    </>
  );
}

const ProfileImageContainer = styled(ResponsiveView)(({ theme }) => ({
  alignItems: 'center',
  marginTop: theme.space[2],
  marginBottom: theme.space[11],
}));

const FormContainer = styled.View(({ theme }) => ({
  marginBottom: theme.space[12],
  gap: theme.space[4],
}));

const CameraButtonContainer = styled.View<{
  isWideScreen: boolean;
  theme?: Theme;
}>(({ isWideScreen, theme }) => ({
  position: 'absolute',
  right: 0,
  bottom: 0,
  width: isWideScreen ? 48 : 40,
  height: isWideScreen ? 48 : 40,
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.full,
  //
  shadowColor: '#000000',
  shadowOffset: {
    width: 0,
    height: 3,
  },
  shadowOpacity: 0.29,
  shadowRadius: 4.65,
  elevation: 7,
}));

const Input = styled(TextField)(({ theme }) => {
  return {
    marginBottom: theme.space[2],
  };
});
