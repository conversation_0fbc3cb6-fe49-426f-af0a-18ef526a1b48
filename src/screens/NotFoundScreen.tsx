import { View, Text } from 'react-native';
import React from 'react';
import { Box, Button, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useNavigation } from '@react-navigation/native';

export default function NotFoundScreen({
  customText,
}: {
  customText?: string;
}) {
  const { colors, space } = useTheme();

  const { goBack } = useNavigation();

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: colors.background,
        justifyContent: 'center',
      }}>
      <Box alignItems="center">
        <Row mb={space[4]} maxW={300}>
          <Typography.H7>{customText ?? 'Page Not found'}</Typography.H7>
        </Row>
        <Button variant="primary" text="Go back" onPress={() => goBack()} />
      </Box>
    </View>
  );
}
