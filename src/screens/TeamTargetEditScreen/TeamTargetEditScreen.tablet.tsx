import React from 'react';
import { country } from 'utils/context';
import TeamTargetEditScreenPH from 'features/teamManagement/ph/tablet/teamPanel/TeamTargetEditLayout';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import { Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

const TeamTargetEditScreenHeader = () => {
  const { t } = useTranslation(['teamManagement']);
  return (
    <ScreenHeader
      customTitle={t('teamManagement:teamTaget.editTeamTaget.title')}
      isLeftArrowBackShown
      route={'TeamManagement'}
    />
  );
};

const TeamTargetContent = () => {
  switch (country) {
    case 'ph':
      return <TeamTargetEditScreenPH />;
    case 'my':
      return <></>;
    case 'fib':
      return <></>;
    default:
      return <TeamTargetEditScreenPH />;
  }
};

const TeamTargetScreen = () => {
  const { colors } = useTheme();
  return (
    <Box flex={1} backgroundColor={colors.palette.fwdGreyDark[20]}>
      <TeamTargetEditScreenHeader />
      <TeamTargetContent />
    </Box>
  );
};

export default TeamTargetScreen;
