import DashboardSection from 'features/home/<USER>/Overview/DashboardSection/tablet';
import WelcomeSectionLoadingUI from 'features/home/<USER>/Skeleton/tablet/WelcomeSectionLoadingUI';
import WelcomeSectionTablet from 'features/home/<USER>/WelcomeSection/tablet';
import useBoundStore from 'hooks/useBoundStore';
import React from 'react';
import { View } from 'react-native';

export default function OverviewScreenTablet() {
  // TODO-Mario: Remove after integration - Mock skeleton loading
  const mockIsLoading = useBoundStore(store => store.home.mockIsLoading);

  return (
    <View>
      {mockIsLoading ? <WelcomeSectionLoadingUI /> : <WelcomeSectionTablet />}

      <DashboardSection />
    </View>
  );
}
