import useBasePlanInfo from 'features/proposal/hooks/useBasePlanInfo';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCase } from 'hooks/useGetCase';
import { useCallback, useRef, useState } from 'react';
import { PartyRole } from 'types/party';
import { Language } from 'types/quotation';

import { useUpsertQuotation } from 'features/proposal/hooks/useUpsertQuotation';
import { useGetQuotationManually } from 'hooks/useGetQuotation';
import { useTranslation } from 'react-i18next';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { getMailConfig } from 'utils/helper/getMailConfig';

import { SendEmailBody } from 'api/emailApi';
import {
  MailConfig,
  PdfGenerator,
  PdfViewerProps,
} from 'features/pdfViewer/components/PdfViewer';
import useGenerateSIPdf from 'features/proposal/hooks/useGenerateSIPdf';
import { FundValidationFeedback } from 'features/proposal/types';
import { useSaveParty } from 'hooks/useParty';
import useToggle from 'hooks/useToggle';
import { ProductBrochurePdf } from 'types/products';
import { countryModuleSiConfig } from 'utils/config/module';
import { country } from 'utils/context';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { LanguagesKeys } from 'utils/translation';

export const useViewSaleIllustrationPdf = () => {
  const { data: agentProfile } = useGetAgentProfile();
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const basePlanInfo = useBasePlanInfo();
  const isVul = useBoundStore(state => state.quotation?.isVul);
  const isQuickSi = useBoundStore(state => state.quotation?.isQuickSi);
  const [sharable, setSharable] = useState(false);
  const [pdfVisible, showPdf, hidePdf] = useToggle();
  const pdfSIloaded = useRef(false);
  const [title, setTitle] = useState<PdfViewerProps['title']>('');
  const [mailConfig, setMailConfig] = useState<MailConfig>();
  const pdfGenerator = useRef<PdfGenerator>(async () => ({
    base64: '',
    fileName: '',
  }));
  const supportedSIPDFLanguages = countryModuleSiConfig.pdf.supportedLanguages;

  const caseId = useBoundStore(state => state.case.caseId);
  const rawQuotation = useBoundStore(state => state.quotation?.rawQuotation);
  const defaultQuotationName = useBoundStore(
    state => state.quotation?.values?.basePlan?.productName,
  );
  const { onSavedQuotation, updateIsPdfViewed } = useBoundStore(
    state => state.quotationActions,
  );
  const { upsertQuotation } = useUpsertQuotation();
  const { mutateAsync: getQuotation } = useGetQuotationManually();
  const { saveParty } = useSaveParty();

  const { t } = useTranslation(['proposal', 'common', 'pdfViewer']);

  const { data: activeCase } = useGetCase(caseId ?? '');

  const { generateSIPdf, isGeneratingPdf, generatePdfError, resetGeneratePdf } =
    useGenerateSIPdf();
  const owner = activeCase?.parties?.find(p =>
    p.roles.some(v => v === PartyRole.PROPOSER),
  );

  const insuredStore = activeCase?.parties?.find(p =>
    p.roles.some(v => v === PartyRole.INSURED),
  );

  const productName = basePlanInfo?.productName?.en ?? '';
  const baseAmount = basePlanInfo?.sumAssured ?? 0;

  const extractPdfUrl = useCallback(
    (link: string | ProductBrochurePdf[] | undefined) => {
      if (!link) return;
      if (typeof link === 'string') {
        return link;
      } else if (Array.isArray(link)) {
        if (link.length === 1) {
          return link[0]?.url;
        }
        // for product that has packages
        if (link.length > 1) {
          return link.find(bl => bl.value === basePlanInfo?.classes)?.url;
        }
      }
    },
    [basePlanInfo?.classes],
  );

  const plan = rawQuotation?.plans.find(
    item => item?.pid == basePlanInfo?.productId,
  );

  const prepareSiPdfView = useCallback(
    async (
      language: Language = 'en',
      fundsPieChartImage?: string,
      pdfPassword?: string,
      feedback?: FundValidationFeedback,
    ) => {
      if (!caseId || !rawQuotation) {
        return;
      }

      const quotationName =
        rawQuotation?.quotationName ||
        `${
          typeof defaultQuotationName === 'string'
            ? defaultQuotationName
            : defaultQuotationName?.en ?? ''
        } ${dateFormatUtil(new Date())} ${isQuickSi ? 'Quick' : 'Full'}`;

      // TODO: cache pdf
      // TODO: pass form value, insured info state to get pdf request body
      try {
        // save the quotation before generating the pdf
        const newQuotationId = await upsertQuotation(
          caseId,
          {
            ...rawQuotation,
            fundsPieChartImage,
            fundValidationFeedback: feedback,
          },
          quotationName,
          {
            isNewVersion: false,
            isPdfGenerated: true,
          },
        );
        if (!newQuotationId) {
          console.log('failed to save quotation');
          setAppIdle();
          return false;
        }
        const newQuotation = await getQuotation({
          caseId,
          quotationId: newQuotationId,
        });

        onSavedQuotation(newQuotation);

        const brochureLink = extractPdfUrl(plan?.brochureLink);
        const tipsBrochureLink = extractPdfUrl(plan?.tipsBrochureLink);
        const productHighlight = isVul ? plan?.productHighlight : '';

        const riplayLink = extractPdfUrl(plan?.riplayLink);
        const additionalAttachments = [];
        if (productHighlight) {
          additionalAttachments.push({
            attachmentBase64: undefined,
            attachmentUrl: encodeURI(productHighlight),
            attachmentName: t('proposal:attachment.productHighlight'),
            required: true,
            selected: true,
          });
        }

        if (tipsBrochureLink) {
          additionalAttachments.push({
            attachmentBase64: undefined,
            attachmentUrl: encodeURI(
              Array.isArray(tipsBrochureLink)
                ? tipsBrochureLink?.[0].url
                : tipsBrochureLink,
            ),
            attachmentName: t('proposal:attachment.productTipsBrochure'),
            required: true,
            selected: true,
          });
        }

        if (brochureLink) {
          additionalAttachments.push({
            attachmentBase64: undefined,
            attachmentUrl: encodeURI(
              Array.isArray(brochureLink)
                ? brochureLink?.[0].url
                : brochureLink,
            ),
            attachmentName: t('proposal:attachment.productBrochure'),
            required: false,
            selected: true,
          });
        }

        if (riplayLink) {
          additionalAttachments.push({
            attachmentBase64: undefined,
            attachmentUrl: encodeURI(
              Array.isArray(riplayLink) ? riplayLink?.[0].url : riplayLink,
            ),
            attachmentName: t('proposal:attachment.RIPLAY'),
            required: false,
            selected: true,
          });
        }

        const mailConfig = getMailConfig({
          agent: agentProfile,
          insured: insuredStore,
          owner,
          isVul,
          productName,
          sumAssured: baseAmount,
          currency: basePlanInfo?.currency,
          policyNum: newQuotation.proposalNum,
          brochureLink,
          t,
        });
        setMailConfig(
          mailConfig
            ? {
                ...mailConfig,
                title: t('proposal:sendBenefitIllustration.title'),
                additionalAttachments,
              }
            : mailConfig,
        );
        setTitle(
          supportedSIPDFLanguages.map(lang => ({
            title: t('pdfViewer:reviewProposal'),
            option: t(`proposal:pdfModal.${lang as LanguagesKeys}`),
            value: lang as LanguagesKeys,
          })),
        );
        setSharable(true);
        pdfSIloaded.current = true;

        const pdfResponse = await generateSIPdf({
          quotation: newQuotation,
          language,
          pdfPassword,
          pieChartBase64Image: fundsPieChartImage,
        });

        pdfGenerator.current = async (pdfLanguage?: LanguagesKeys) => {
          if (pdfLanguage === language) {
            return {
              fileName: productName.replaceAll(' ', '_') + '_SI',
              attachmentName: t('proposal:attachment.salesIllustration'),
              base64: pdfResponse?.uploadDocument?.base64 || '',
              password: pdfPassword,
            };
          }
          const newPdfResponse = await generateSIPdf({
            quotation: newQuotation,
            language: pdfLanguage as Language,
            pdfPassword,
            pieChartBase64Image: fundsPieChartImage,
          });
          return {
            fileName: productName.replaceAll(' ', '_') + '_SI',
            attachmentName: t('proposal:attachment.salesIllustration'),
            base64: newPdfResponse?.uploadDocument?.base64 || '',
            password: pdfPassword,
          };
        };

        if (
          !countryModuleSiConfig.email.isMailToEnabled[
            isQuickSi ? 'quickQuote' : 'fullQuote'
          ]
        ) {
          updateIsPdfViewed(true);
        }

        // return {
        //   title: t('pdfViewer:reviewProposal'),
        //   fileName: productName.replaceAll(' ', '_') + '_SI',
        //   downloadable: true,
        //   sharable: true,
        //   shareType: 'email' as const,
        //   mailConfig,
        //   base64: pdfResponse?.uploadDocument?.base64,
        //   brochureUrl: extractPdfUrl(plan?.brochureLink),
        //   productHighlightUrl:
        //     isVul &&
        //     (agentProfile?.channel === CHANNELS.BANCA ||
        //       agentProfile?.channel === CHANNELS.AFFINITY)
        //       ? plan?.productHighlight
        //       : '',
        // };
        return true;
      } finally {
        setAppIdle();
      }
    },
    [
      isQuickSi,
      caseId,
      rawQuotation,
      defaultQuotationName,
      isQuickSi,
      upsertQuotation,
      getQuotation,
      onSavedQuotation,
      extractPdfUrl,
      plan?.brochureLink,
      plan?.productHighlight,
      plan?.riplayLink,
      isVul,
      agentProfile,
      insuredStore,
      owner,
      productName,
      baseAmount,
      basePlanInfo?.currency,
      t,
      supportedSIPDFLanguages,
      generateSIPdf,
      setAppIdle,
      updateIsPdfViewed,
    ],
  );

  const viewSiPdf = useCallback(
    async ({
      pdfPassword,
      fundsPieChartImage,
      language = 'en',
      feedback,
    }: {
      pdfPassword?: string;
      fundsPieChartImage?: string;
      language?: Language;
      feedback?: FundValidationFeedback;
    }) => {
      setAppLoading();

      // const isRequiredToReadAllPdf =
      //   countryModuleSiConfig.pdf.isRequiredToReadAll;

      // const siPdfViewData = await prepareSiPdfView(
      //   language,
      //   fundsPieChartImage,
      //   pdfPassword,
      // );

      const isReady = await prepareSiPdfView(
        language,
        fundsPieChartImage,
        pdfPassword,
        feedback,
      );

      if (isReady) showPdf();

      // if (siPdfViewData) {
      //   navigate('PdfViewer', {
      //     ...siPdfViewData,
      //     password: pdfPassword,
      //     isRequiredToReadAllPdf,
      //   });
      // }
    },
    [setAppLoading, prepareSiPdfView, showPdf],
  );

  const viewProductBrochure = useCallback(() => {
    try {
      setTitle(t('pdfViewer:reviewProductBrochure'));
      setMailConfig(
        getMailConfig({
          brochureLink: extractPdfUrl(plan?.brochureLink),
        }),
      );
      setSharable(false);
      pdfSIloaded.current = false;
      pdfGenerator.current = async () => ({
        fileName: productName.replaceAll(' ', '_') + '_SI',
        attachmentName: t('proposal:attachment.productBrochure'),
        url: extractPdfUrl(plan?.brochureLink) || '',
      });
      setAppIdle();
      showPdf();
      // navigate('PdfViewer', {
      //   title: t('pdfViewer:reviewProductBrochure'),
      //   fileName: productName.replaceAll(' ', '_') + '_SI',
      //   downloadable: true,
      //   sharable: false,
      //   shareType: 'email',
      //   mailConfig,
      //   url: extractPdfUrl(plan?.brochureLink),
      // });
    } catch {
      setAppIdle();
    }
  }, [extractPdfUrl, plan?.brochureLink, productName, setAppIdle, showPdf, t]);

  const viewTipsProductBrochure = useCallback(() => {
    try {
      setMailConfig(getMailConfig({}));
      setTitle(t('pdfViewer:reviewProductTipsBrochure'));
      setSharable(false);
      pdfSIloaded.current = false;
      pdfGenerator.current = async () => ({
        fileName: productName.replaceAll(' ', '_') + '_SI',
        attachmentName: t('proposal:attachment.productTipsBrochure'),
        url: extractPdfUrl(plan?.tipsBrochureLink) || '',
      });
      setAppIdle();
      showPdf();
    } catch {
      setAppIdle();
    }
  }, [
    extractPdfUrl,
    plan?.tipsBrochureLink,
    productName,
    setAppIdle,
    showPdf,
    t,
  ]);

  const viewProductHighlight = useCallback(() => {
    try {
      setMailConfig(getMailConfig({}));
      setTitle(t('pdfViewer:reviewProductHighlights'));
      pdfGenerator.current = async () => ({
        url: plan?.productHighlight ?? '',
        attachmentName: t('proposal:attachment.productHighlight'),
        fileName: productName.replaceAll(' ', '_') + '_SI',
      });
      setSharable(false);
      pdfSIloaded.current = false;
      setAppIdle();
      showPdf();
      // navigate('PdfViewer', {
      //   title: t('pdfViewer:reviewProductHighlights'),
      //   fileName: productName.replaceAll(' ', '_') + '_SI',
      //   downloadable: true,
      //   sharable: false,
      //   shareType: 'email',
      //   mailConfig,
      //   url: plan?.productHighlight,
      // });
    } catch {
      setAppIdle();
    }
  }, [plan?.productHighlight, productName, setAppIdle, showPdf, t]);

  const viewRIPLAY = useCallback(() => {
    try {
      setMailConfig(getMailConfig({}));
      setTitle(t('pdfViewer:reviewRIPLAY'));
      pdfGenerator.current = async () => ({
        url: extractPdfUrl(plan?.riplayLink) || '',
        attachmentName: t('proposal:attachment.RIPLAY'),
        fileName: productName.replaceAll(' ', '_') + '_RIPLAY',
      });
      setSharable(false);
      pdfSIloaded.current = false;
      setAppIdle();
      showPdf();
    } catch {
      setAppIdle();
    }
  }, [extractPdfUrl, plan?.riplayLink, productName, setAppIdle, showPdf, t]);

  const handleOnEmailSent = async (body: Omit<SendEmailBody, 'emailBody'>) => {
    updateIsPdfViewed(true);

    /* update the email of the proposer in the case object after the email is sent*/
    if (owner) {
      await saveParty(
        {
          ...owner,
          contacts: { ...owner.contacts, email: body?.emailToRecipients?.[0] },
        },
        { caseId, preventCreatingParty: true },
      );
    }
  };

  const sendPdfButtonLabels =
    country === 'ib'
      ? {
          text: t('pdfViewer:pdsAcknowledged'),
          subtext: t('pdfViewer:sendPdf'),
        }
      : {
          text: t('pdfViewer:sendPdf'),
        };

  return {
    pdfVisible,
    sharable,
    hidePdf,
    showPdf,
    title,
    mailConfig,
    pdfGenerator,
    //for IDN email content
    extraProperties: {
      '1': renderLabelByLanguage(basePlanInfo?.productName) ?? '',
      '2': owner?.person?.name?.fullName ?? '',
      '3': rawQuotation?.proposalNum ?? '',
    },
    viewSiPdf,
    viewProductBrochure,
    viewTipsProductBrochure,
    viewProductHighlight,
    isGeneratingPdf,
    generatePdfError,
    resetGeneratePdf,
    generateSiPdf: prepareSiPdfView,
    updateIsPdfViewed,
    isRequiredToReadAllPdf: isQuickSi
      ? countryModuleSiConfig.pdf.isRequiredToReadAll.quickQuote
      : countryModuleSiConfig.pdf.isRequiredToReadAll.fullQuote,
    handleOnEmailSent,
    sendPdfButtonLabels,
    isPdfSIloaded: pdfSIloaded.current,
    viewRIPLAY,
  };
};
