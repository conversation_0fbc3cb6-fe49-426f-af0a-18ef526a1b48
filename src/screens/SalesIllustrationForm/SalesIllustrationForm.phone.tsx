import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Header } from '@react-navigation/elements';
import {
  NavigationProp,
  RouteProp,
  StackActions,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import DialogPhone from 'components/Dialog.phone';
import TabletFooter from 'components/Footer/TabletFooter';
import InputEventListener from 'components/Input/InputEventListener';
import SaveModal from 'components/SaveModal';
import TopTabView from 'components/TopTabView';
import {
  Box,
  Button,
  Column,
  Icon,
  Row,
  Typography,
  XView,
  addToast,
} from 'cube-ui-components';
import { rgb } from 'd3';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import FlatDurationDropdown from 'features/proposal/components/BasePlanBox/FlatDurationDropdown';
import FlatExtraDropdown from 'features/proposal/components/BasePlanBox/FlatExtraDropdown';
import MultiplierDropdown from 'features/proposal/components/BasePlanBox/MultiplierDropdown';
import { Divider } from 'features/proposal/components/Divider/Divider';
import FundsAllocation from 'features/proposal/components/FundsAllocation';
import { usePieChartContext } from 'features/proposal/components/FundsAllocation/common/PieChartContext';
import TotalFundBar from 'features/proposal/components/FundsAllocation/common/TotalFundBar';
import PdfFilePopup from 'features/proposal/components/PdfFilePopup';
import Rider from 'features/proposal/components/Rider';
import SaveQuotationPanel from 'features/proposal/components/SaveQuotationPanel';
import ViewPdfReminder from 'features/proposal/components/ViewPdfReminder';
import Warning from 'features/proposal/components/Warning/Warning';
import ProposalAgreementModal from 'features/proposal/components/proposalAgreementModal';
import { isShowAlertAffordable } from 'features/proposal/hooks/useQuotationCalculation';
import useQuotationForm from 'features/proposal/hooks/useQuotationForm';
import useQuotationInsureds from 'features/proposal/hooks/useQuotationInsureds';
import useRiderDefinitions from 'features/proposal/hooks/useRiderDefinitions';
import useViewPdfReminder from 'features/proposal/hooks/useViewPdfReminder';
import {
  SiNextButtonAction,
  SiPdf,
  TopUpFilter,
} from 'features/proposal/types';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCase } from 'hooks/useGetCase';
import { useGetFnaByLeadId } from 'hooks/useGetLeadFna';
import useTopTabbarScrollView from 'hooks/useTopTabBar';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable, View } from 'react-native';
import {
  Gesture,
  GestureDetector,
  ScrollView as RNGHScrollView,
} from 'react-native-gesture-handler';
import Animated, {
  Extrapolation,
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { RootStackParamList } from 'types';
import { PartyRole } from 'types/party';
import { ProductId } from 'types/products';
import { BasicConfig, BusinessData, PlanConfig } from 'types/quotation';
import { countryModuleSiConfig } from 'utils/config/module';
import getPdfPasswordFromDob from 'utils/helper/getPdfPasswordFromDob';
import BasePlanBox from '../../features/proposal/components/BasePlanBox';
import StickyHeader from '../../features/proposal/components/StickyHeader';
import { useViewSaleIllustrationPdf } from './useViewSaleIllustrationPdf';

import { SIPdfWarnings } from 'api/proposalApi';
import Input from 'components/Input';
import PdfViewer from 'features/pdfViewer/components/PdfViewer';
import BasePlanDropdown from 'features/proposal/components/BasePlanBox/BasePlanDropdown';
import { PIE_CHART_SIZE_PHONE } from 'features/proposal/components/FundsAllocation/constants';
import SustainabilityCheckModal from 'features/proposal/components/SalesIllutrationModals/SustainabilityCheckModal';
import SimulationFloatingButton from 'features/proposal/components/SimulationTable/SimulationFloatingButton';
import { useBasePlanFieldConfig } from 'features/proposal/hooks/useFields';
import useWatchBasePlanForm from 'features/proposal/hooks/useWatchBasePlanForm';
import useSkipScreen from 'hooks/useSkipScreen';
import GATracking from 'utils/helper/gaTracking';
import BasePlanGenericField from 'features/proposal/components/BasePlanBox/BasePlanGenericField';
import { LocationPermissionSetting } from 'components/Location/LocationPermissionSetting';
import * as Location from 'expo-location';
import { sub } from 'date-fns';
const Container = styled.View(({ theme: { colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[50],
  flex: 1,
}));

const AnimatedScrollView = Animated.createAnimatedComponent(RNGHScrollView);

const SalesIllustrationForm = () => {
  const params =
    useRoute<RouteProp<RootStackParamList, 'SalesIllustrationForm'>>().params;
  const formSource = useMemo(() => {
    if (params?.from) return params.from;
    return 'quick_quote_form';
  }, [params?.from]);
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { name: screenName } =
    useRoute<RouteProp<RootStackParamList, 'SalesIllustrationForm'>>();

  const { colors, space, animation } = useTheme();

  const rawQuotation = useBoundStore(state => state.quotation?.rawQuotation);
  const { owner, mainInsured, isEntityFlow } = useQuotationInsureds();
  const buttonColor = rgb(colors.onPrimary);

  const { t } = useTranslation(['proposal', 'common']);

  const selectedProductCode = useFnaStore(state => state.selectedProductCode);
  const isMultipleInsureds = useFnaStore(
    state => state.isMultipleInsuredsProduct,
  );
  const [isSyncTopUp, setSyncTopUp] = useState<boolean>(false);

  const quotationPid =
    params?.pid || selectedProductCode || rawQuotation?.pid || '';

  const { data: agentProfile } = useGetAgentProfile();

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseData } = useGetCase(caseId);
  const leadId = caseData?.parties?.find(
    p => p.roles.includes(PartyRole.PROPOSER) && !!p.leadId,
  )?.leadId;

  const { data: fnaData } = useGetFnaByLeadId(leadId);
  const hasFnaData = !!fnaData;
  const { getConfigLabel, fieldConfigs } = useBasePlanFieldConfig();

  const {
    form: { control, setValue, getValues, clearErrors, watch },
    defaultQuotationName,
    isQuickSi,
    basePlanInfo,
    basePlanDefinition,
    isAllRidersMandatory,
    fundsDefinitions,
    rpqResult,
    onStartInput,
    onEndInput,
    isLockFormAction,
    isLockSubmitButton,
    isCalculatingQuotation,
    isInitializingQuotation,
    isValidCaseStatus,
    isValidQuotationCalculation,
    isResumeQuotation,
    isUpsertingQuotation,
    isUpsertQuotationError,
    errors: { getCaseError, criticalErrors, initError, approvalWarning },
    showBirthdayReminder,
    upsertQuotation,
    saveSelectedQuotation,
    triggerQuotationCalculation,
    closeReminderDialog: closeBirthdayReminderDialog,
    toggleAgeChangeDialog,
    reminderModal,
    setReminderModal,
    nextButtonAction,
    setNextButtonAction,
    nextButtonLabel,
    nextButtonSubLabel,
    siConfig,
  } = useQuotationForm({
    pid: quotationPid,
    isSyncTopUp,
    hasFnaData,
    lockMainInsuredToProposer:
      isMultipleInsureds || params?.lockMainInsuredToProposer,
  });

  const basePlanValue = watch('basePlan');
  const funds = watch('fundsAllocation');
  const { getPieChartBase64Image } = usePieChartContext();

  const [isShowModalPdf, showModalPdf] = useState(false);
  const [showExitModal, setShowExitModal] = useState(false);
  const [showLocationPerrmissionModal, setShowLocationPermissionModal] =
    useState(false);
  const [isHeaderCollapsed, setIsHeaderCollapsed] = useState(false);
  const [showSaveQuotationPanel, setShowSaveQuotationPanel] = useState<
    'leave' | 'back'
  >();
  const [visibleAgreement, setVisibleAgreement] = useState<boolean>(false);
  const [alertAffordablePremiumDialog, setAlertAffordablePremiumDialog] =
    useState<boolean>(false);

  const [showSustainabilityModal, setSustainabilityModal] =
    useState<boolean>(false);

  const isStartRPQ = siConfig.rpq.enabled && !rpqResult;
  const maxFund = siConfig.maxFund;
  const isProductReselectionEnabled = siConfig.isProductReselectionEnabled;

  /**
   * handling expand/collapse of sticky header
   */
  const headerMaxHeight = useSharedValue(0);
  const headerMinHeight = useSharedValue(0);
  const from = useSharedValue(headerMaxHeight.value);
  const translationY = useSharedValue(headerMaxHeight.value);
  const prevTranslationY = useSharedValue(0);
  const {
    scrollViewRef,
    sectionOffset,
    scrollOffset,
    onSectionLayout,
    handleOnTabPress,
    focusedSectionIndex,
    scrollHandler,
    setGap,
  } = useTopTabbarScrollView();

  useEffect(() => {
    toggleAgeChangeDialog();
  }, [toggleAgeChangeDialog]);

  useFocusEffect(
    useCallback(() => {
      // trigger ga event when form is opened/exited
      GATracking.logCustomEvent(GATracking.EVENTS.SALES_ILLUSTRATION, {
        action_type: GATracking.ACTION_TYPES.SI_OPEN_FORM,
        form_source: formSource,
      });

      return () => {
        GATracking.logCustomEvent(GATracking.EVENTS.SALES_ILLUSTRATION, {
          action_type: GATracking.ACTION_TYPES.SI_EXIT,
          form_source: formSource,
        });
      };
    }, [formSource]),
  );

  useEffect(() => {
    scrollViewRef.current?.setNativeProps({ scrollEnabled: false });
    setGap(space[7]);
  }, []);

  const onPanEnd = (isScrollable: boolean) => {
    setIsHeaderCollapsed(isScrollable);
    scrollViewRef.current?.setNativeProps({ scrollEnabled: isScrollable });
  };

  const panGesture = Gesture.Pan()
    .onStart(() => {
      from.value = translationY.value;
    })
    .onUpdate(e => {
      if (isInitializingQuotation) return;

      if (scrollOffset.value === 0 || from.value === headerMaxHeight.value) {
        translationY.value = Math.min(
          Math.max(
            from.value + e.translationY - prevTranslationY.value,
            headerMinHeight.value,
          ),
          headerMaxHeight.value,
        );
      } else {
        prevTranslationY.value = e.translationY;
      }
    })
    .onEnd(e => {
      if (isInitializingQuotation) return;
      // Expand header if large velocity
      if (
        (e.velocityY > 500 || e.translationY > 100) &&
        scrollOffset.value < 1
      ) {
        translationY.value = withTiming(
          headerMaxHeight.value,
          {
            duration: animation.duration,
          },
          () => runOnJS(onPanEnd)(false),
        );
        from.value = headerMaxHeight.value;
        // else collapse header
      } else if (e.velocityY < -500 || e.translationY < -100) {
        translationY.value = withTiming(
          headerMinHeight.value,
          {
            duration: animation.duration,
          },
          () => runOnJS(onPanEnd)(true),
        );
        from.value = headerMinHeight.value;
        // Do nothing if not enough velocity
      } else {
        translationY.value = withTiming(from.value, {
          duration: animation.duration,
        });
      }
    })
    .onFinalize(() => {
      // stopped touching screen
      prevTranslationY.value = 0;
    })
    .simultaneousWithExternalGesture(scrollViewRef);

  const topupRiderPlanDefinitions = useRiderDefinitions({
    topUpFilter: TopUpFilter.TOPUP_ONLY,
  });

  const selectableRiderDefintions = useRiderDefinitions({
    selectableOnly: true,
    topUpFilter: TopUpFilter.NON_TOPUP_ONLY,
  });

  const tabs = useMemo(() => {
    const ret = [];

    if (
      Array.isArray(topupRiderPlanDefinitions) &&
      topupRiderPlanDefinitions.length > 0
    ) {
      ret.push({ name: 'Topup', title: t('proposal:header.tab.topup') });
    }

    if (
      Array.isArray(selectableRiderDefintions) &&
      selectableRiderDefintions.length > 0
    ) {
      ret.push({ name: 'Rider', title: t('proposal:header.tab.rider') });
    }

    if (Array.isArray(fundsDefinitions) && fundsDefinitions.length > 0) {
      ret.push({ name: 'Fund', title: t('proposal:header.tab.fund') });
    }

    if (ret.length > 0) {
      ret.splice(0, 0, {
        name: 'BasicPlan',
        title: t('proposal:header.tab.basicPlan'),
      });
    }
    return ret;
  }, [
    topupRiderPlanDefinitions,
    selectableRiderDefintions,
    fundsDefinitions,
    t,
  ]);

  const fundIdx = tabs.findIndex(({ name }) => name === 'Fund');

  const shouldDisplayFundBar =
    focusedSectionIndex === fundIdx && funds.length > 0;

  const stickyFundBarStyles = useAnimatedStyle(() => {
    const fundAllocationOffset = sectionOffset[fundIdx] || 0;

    const translateY = interpolate(
      scrollOffset.value,
      [
        0,
        fundAllocationOffset,
        fundAllocationOffset + 0.5 * PIE_CHART_SIZE_PHONE,
      ],
      [0, 0, space[11]],
      Extrapolation.CLAMP,
    );

    const opacity = interpolate(
      scrollOffset.value,
      [
        0,
        fundAllocationOffset,
        fundAllocationOffset + 0.5 * PIE_CHART_SIZE_PHONE,
      ],
      [0, 0, 1],
      Extrapolation.CLAMP,
    );

    return {
      transform: [{ translateY }],
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      opacity: fundAllocationOffset ? opacity : 0,
    };
  }, [sectionOffset, fundIdx, PIE_CHART_SIZE_PHONE]);

  const {
    pdfVisible,
    pdfGenerator,
    title,
    hidePdf,
    mailConfig,
    extraProperties,
    sharable,
    isGeneratingPdf,
    viewSiPdf,
    viewProductBrochure,
    viewTipsProductBrochure,
    viewProductHighlight,
    viewRIPLAY,
    generatePdfError,
    resetGeneratePdf,
    handleOnEmailSent,
    isRequiredToReadAllPdf,
    sendPdfButtonLabels,
  } = useViewSaleIllustrationPdf();

  const pdfPassword = useMemo(() => {
    if (!countryModuleSiConfig.pdf.isPasswordProtected) return undefined;

    const dob = isEntityFlow ? mainInsured?.dob : owner?.dob;
    return getPdfPasswordFromDob(dob?.toString());
  }, [isEntityFlow, mainInsured?.dob, owner?.dob]);

  const baseAndBasicPlanConfig = useMemo(
    () =>
      ({
        ...basePlanDefinition?.planConfig,
        ...basePlanDefinition?.basicConfig,
      } as PlanConfig & BasicConfig),
    [basePlanDefinition],
  );

  const {
    flatDuration: { isFlatDurationShow: requireFlatDuration },
    flatLoading: { isFlatLoadingShow: requireFlatLoading, flatLoadingDataType },
    flatLoadingTerm: { isFlatLoadingTermShow: requireFlatLoadingTerm },
    percLoading: { isPercLoadingShow: requirePercLoading },
    morbidityFlatLoading: {
      isMorbidityFlatLoadingShow: requireMorbidityFlatLoading,
      isMorbidityFlatLoadingDisabled,
    },
    morbidityPercLoading: {
      isMorbidityPercLoadingShow: requireMorbidityPercLoading,
      isMorbidityPercLoadingDisabled,
    },
  } = useWatchBasePlanForm(baseAndBasicPlanConfig);

  const pdfPopupItems = useMemo(() => {
    const items = [
      { title: t('proposal:attachment.proposal'), fileType: SiPdf.SI_PROPOSAL },
      {
        title: t('proposal:attachment.productBrochure'),
        fileType: SiPdf.PRODUCT_BROCHURE,
      },
    ];

    const hasProductTipsBrochure = rawQuotation?.plans?.find?.(
      i => i?.tipsBrochureLink,
    );

    if (hasProductTipsBrochure) {
      items.push({
        title: t('proposal:attachment.productTipsBrochure'),
        fileType: SiPdf.TIPS_PRODUCT_BROCHURE,
      });
    }

    const hasProductHighlight = rawQuotation?.plans?.find?.(
      i => i?.productHighlight,
    );

    if (hasProductHighlight) {
      items.push({
        title: t('proposal:attachment.productHighlights'),
        fileType: SiPdf.PRODUCT_HIGHLIGHTS,
      });
    }

    const hasRIPLAY = rawQuotation?.plans?.find?.(i => i?.riplayLink);
    if (hasRIPLAY) {
      items.push({
        title: 'RIPLAY',
        fileType: SiPdf.RIPLAY,
      });
    }

    return items;
  }, [rawQuotation?.plans, t]);

  const nextButtonDisabled = useMemo(() => {
    if (nextButtonAction === SiNextButtonAction.SEND_EMAIL)
      return isLockFormAction || isInitializingQuotation || isGeneratingPdf;

    return isLockFormAction || isLockSubmitButton || isInitializingQuotation;
  }, [
    isGeneratingPdf,
    isInitializingQuotation,
    isLockFormAction,
    isLockSubmitButton,
    nextButtonAction,
  ]);

  useEffect(() => {
    if (getCaseError) {
      console.warn('get errors from case fetching: ', getCaseError);
      navigation.goBack();
    }
  }, [getCaseError, navigation]);

  useEffect(() => {
    if (!isValidCaseStatus) {
      navigation.goBack();
    }
  }, [isValidCaseStatus, navigation]);

  useEffect(() => {
    if (isSyncTopUp) {
      triggerQuotationCalculation();
    }
  }, [isSyncTopUp]);

  useEffect(() => {
    if (Array.isArray(generatePdfError)) {
      setSustainabilityModal(true);
    }
  }, [generatePdfError]);

  const onFinishQuickQuote = useCallback(async () => {
    await upsertQuotation('__FinishQuickSi__', true);

    GATracking.logCustomEvent(GATracking.EVENTS.SALES_ILLUSTRATION, {
      action_type: GATracking.ACTION_TYPES.SI_COMPLETE,
      form_source: formSource,
    });

    siConfig.flow.screenName !== 'EApp'
      ? GATracking.logCustomEvent('fn_assessment', {
          action_type: 'fna_open_form',
          form_source: 'sales_illustration',
        })
      : GATracking.logCustomEvent('application', {
          action_type: 'eapp_open_form',
          form_source: 'sales_illustration_form ',
        });

    return navigation.dispatch(
      StackActions.replace(siConfig.flow.screenName ?? 'Fna'),
    );
  }, [upsertQuotation, formSource, navigation, siConfig.flow.screenName]);

  const onFinishFullQuote = useCallback(async () => {
    const pieChartBase64Image = await getPieChartBase64Image?.();

    await saveSelectedQuotation({
      quotationName: defaultQuotationName,
      fundsPieChartImage: pieChartBase64Image,
    });

    GATracking.logCustomEvent(GATracking.EVENTS.SALES_ILLUSTRATION, {
      action_type: GATracking.ACTION_TYPES.SI_START_APPLICATION,
      form_source: formSource,
    });

    navigation.dispatch({
      ...StackActions.replace(siConfig.flow.screenName ?? 'EApp'),
    });
  }, [
    getPieChartBase64Image,
    saveSelectedQuotation,
    defaultQuotationName,
    formSource,
    navigation,
    siConfig.flow.screenName,
  ]);

  const { backToBefore } = useSkipScreen();

  const onBack = useCallback(
    (shouleSkipProductionSelection = false) => {
      shouleSkipProductionSelection
        ? backToBefore('ProductSelection')
        : setTimeout(() => {
            navigation.goBack();
          }, animation.duration);

      GATracking.logButtonPress({
        screenName,
        screenClass: 'Sales flow',
        actionType: 'non_cta_button',
        buttonName: shouleSkipProductionSelection
          ? 'Back to root screen'
          : 'Back to product selection',
      });
    },
    [navigation, backToBefore, animation.duration],
  );

  const onSaveQuotation = useCallback(
    async (quotationName: string, isNewVersion: boolean) => {
      await upsertQuotation(quotationName.trim(), isNewVersion);

      const shouleSkipProductionSelection = showSaveQuotationPanel === 'leave';
      setShowSaveQuotationPanel(undefined);

      GATracking.logCustomEvent(GATracking.EVENTS.SALES_ILLUSTRATION, {
        action_type: GATracking.ACTION_TYPES.SI_SAVE,
        form_source: formSource,
      });

      setTimeout(() => {
        addToast(
          [
            {
              message: isNewVersion
                ? t('proposal:savedNewVersion')
                : t('proposal:saved'),
              IconLeft: <Icon.Tick />,
            },
          ],
          false,
          true,
        );
        // application will crash if call reset on modal is open
        //
        onBack(shouleSkipProductionSelection);
      }, animation.duration);
    },
    [upsertQuotation, showSaveQuotationPanel, animation.duration, t, onBack],
  );

  const handleInputEvent = useCallback(
    (name?: string) => {
      onEndInput();
      triggerQuotationCalculation();
    },
    [onEndInput, triggerQuotationCalculation],
  );

  const onPressNextButton = useCallback(async () => {
    if (nextButtonAction === SiNextButtonAction.SEND_EMAIL) {
      showModalPdf(true);
      return;
    }

    if (isQuickSi) {
      await onFinishQuickQuote();
      return;
    }

    if (siConfig.flow.enableFullQuoteAgreement) {
      setVisibleAgreement(true);
      return;
    }

    await onFinishFullQuote();
  }, [
    isQuickSi,
    showModalPdf,
    nextButtonAction,
    setVisibleAgreement,
    onFinishQuickQuote,
    onFinishFullQuote,
    siConfig.flow,
  ]);

  const onAgreementModalClose = useCallback(
    async (isConfirm: boolean, location?: Location.LocationObject) => {
      setVisibleAgreement(false);
      if (isConfirm) {
        const businessData = {
          policyInforDTO: {
            locationOfConfirmingSILongitude: location?.coords.longitude || 0,
            locationOfConfirmingSILatitude: location?.coords.latitude || 0,
          },
          submissionDate: new Date().toISOString(),
        } as BusinessData;
        console.log("businessData in onAgreementModalClose: ", businessData); 
        await upsertQuotation(
          basePlanInfo?.productName?.en ?? 'Full SI',
          false, // no need to save new version, because agent need to review the existing SI PDF before proceed
          true,
          undefined,
          businessData 
        );
        setTimeout(
          () =>
            navigation.dispatch(
              StackActions.replace(siConfig.flow.screenName!),
            ),
          200,
        );
      }
    },
    [upsertQuotation, navigation],
  );

  const handleOnExitPress = useCallback(() => {
    if (isCalculatingQuotation || isInitializingQuotation) return;
    setShowExitModal(true);
  }, [isCalculatingQuotation, isInitializingQuotation]);

  const saveProposalNameError = useCallback(
    (defaultValue: string, currentValue: string, shouCheckSameName: boolean) =>
      isResumeQuotation &&
      ((shouCheckSameName && currentValue.trim() === defaultValue) ||
        isUpsertQuotationError)
        ? t('proposal:error.invalidProposalName')
        : undefined,
    [isResumeQuotation, isUpsertQuotationError],
  );

  const handleOnExitModalConfirm = useCallback(() => {
    if (!isLockFormAction) {
      setShowExitModal(false);
      setShowSaveQuotationPanel('back');
    } else {
      setShowExitModal(false);
    }
  }, [isLockFormAction, setShowExitModal]);

  const handleOnExitModalDeny = useCallback(() => {
    setShowExitModal(false);
    onBack();
  }, [isLockFormAction, setShowExitModal]);

  const reminderDialog = useMemo(() => {
    if (!showBirthdayReminder) {
      return undefined;
    }

    if (showBirthdayReminder) {
      return {
        title: t('proposal:reminder.nearNextBirthday.title'),
        description: t('proposal:reminder.nearNextBirthday.description'),
      };
    }
  }, [showBirthdayReminder, t]);

  const closeReminderDialog = useCallback(() => {
    closeBirthdayReminderDialog();
    resetGeneratePdf();
  }, [closeBirthdayReminderDialog, resetGeneratePdf]);

  useEffect(() => {
    isShowAlertAffordable(
      caseData?.fna?.annualAffordablePremium,
      basePlanInfo?.modalPremium,
      basePlanInfo?.paymentMode,
    ) && setAlertAffordablePremiumDialog(true);
  }, [
    caseData?.fna?.annualAffordablePremium,
    basePlanInfo?.paymentMode,
    basePlanInfo?.modalPremium,
  ]);

  const toggleAlertAffordablePremiumDialog = useCallback(() => {
    setAlertAffordablePremiumDialog(!alertAffordablePremiumDialog);
  }, [alertAffordablePremiumDialog]);

  const isViewPdfDisabled =
    isLockFormAction || isGeneratingPdf || isCalculatingQuotation;

  const { isShowPdfReminder, handleOnCloseReminder } = useViewPdfReminder({
    isDisabled: isViewPdfDisabled,
  });

  const puzzleContents = [
    Array.isArray(topupRiderPlanDefinitions) &&
    topupRiderPlanDefinitions.length > 0
      ? {
          key: 'PuzzleContent-Rider-Topup-Only',
          component: (
            <Rider
              sectionFilter={TopUpFilter.TOPUP_ONLY}
              setValue={setValue}
              getValues={getValues}
              control={control}
              onRiderPlanUpdated={triggerQuotationCalculation}
              productId={basePlanInfo.productId as ProductId}
              isAllRidersMandatory={isAllRidersMandatory}
            />
          ),
        }
      : null,
    Array.isArray(selectableRiderDefintions) &&
    selectableRiderDefintions.length > 0
      ? {
          key: 'PuzzleContent-Rider',
          component: (
            <Rider
              sectionFilter={TopUpFilter.NON_TOPUP_ONLY}
              control={control}
              setValue={setValue}
              getValues={getValues}
              onRiderPlanUpdated={triggerQuotationCalculation}
              productId={basePlanInfo.productId as ProductId}
              isAllRidersMandatory={isAllRidersMandatory}
            />
          ),
        }
      : null,

    Array.isArray(fundsDefinitions) && fundsDefinitions.length > 0
      ? {
          key: 'PuzzleContent-FundsAllocation',
          component: (
            <FundsAllocation
              control={control}
              setValue={setValue}
              leadId={leadId}
              maxFund={maxFund}
              isStartRPQ={isStartRPQ}
              clearErrors={clearErrors}
              onSyncTopUp={setSyncTopUp}
              triggerValidation={triggerQuotationCalculation}
            />
          ),
        }
      : null,
  ].filter(Boolean) as Array<{ key: string; component: React.JSX.Element }>;

  const headerRight = () =>
    isProductReselectionEnabled ? (
      <Row alignItems="center" gap={space[3]}>
        <Pressable
          style={{
            flexDirection: 'row',
            gap: space[1],
            alignItems: 'center',
          }}
          disabled={isViewPdfDisabled}
          onPress={() => showModalPdf(true)}>
          <Icon.DocumentCopy
            fill={buttonColor.toString()}
            height={space[6]}
            width={space[6]}
          />

          <Typography.Body color={buttonColor.toString()} fontWeight="bold">
            ({pdfPopupItems.length})
          </Typography.Body>
        </Pressable>

        <Pressable
          onPress={() =>
            navigation.reset({
              index: 0,
              routes: [{ name: 'Main' }],
            })
          }>
          <Icon.Home size={space[6]} fill={colors.background} />
        </Pressable>
      </Row>
    ) : (
      <Row gap={space[3]}>
        <Pressable
          style={{
            flexDirection: 'row',
            gap: space[1],
            alignItems: 'center',
          }}
          disabled={isViewPdfDisabled}
          onPress={() => showModalPdf(true)}>
          <Icon.DocumentCopy
            fill={buttonColor.toString()}
            height={space[6]}
            width={space[6]}
          />

          <Typography.Body
            color={buttonColor.toString()}
            style={{ fontWeight: 'bold' }}>
            ({pdfPopupItems.length})
          </Typography.Body>
        </Pressable>
      </Row>
    );

  const handleViewSiPdf = async () => {
    const pieChartBase64Image = await getPieChartBase64Image?.();
    viewSiPdf({
      pdfPassword,
      fundsPieChartImage: pieChartBase64Image,
    });
  };

  const loadingList = [
    requireFlatLoading ? (
      <>
        {flatLoadingDataType === 'options' && (
          <FlatExtraDropdown
            value={basePlanValue.flatLoading}
            isLoading={isInitializingQuotation}
            onChange={v =>
              setValue('basePlan.flatLoading', v?.value, {
                shouldDirty: true,
                shouldValidate: true,
              })
            }
          />
        )}
        {flatLoadingDataType === 'number' && (
          <Input
            control={control}
            as={BasePlanGenericField}
            name="basePlan.flatLoading"
            fieldName="flatLoading"
            title={t('proposal:loading.flatExtra')}
            autoCompleteValueType="number"
            hasNumeric
            precision={1}
          />
        )}
      </>
    ) : null,

    requireFlatLoadingTerm ? (
      <>
        <Input
          control={control}
          as={BasePlanGenericField}
          name="basePlan.flatLoadingTerm"
          fieldName="flatLoadingTerm"
          title={t('proposal:loading.flatLoadingTerm')}
          autoCompleteValueType="number"
          hasNumeric
        />
      </>
    ) : null,

    requireFlatDuration ? (
      <FlatDurationDropdown
        premiumTerm={basePlanValue.premiumTerm ?? 1}
        value={basePlanValue.flatDuration}
        isLoading={isInitializingQuotation}
        onChange={v =>
          setValue('basePlan.flatDuration', v, {
            shouldDirty: true,
            shouldValidate: true,
          })
        }
      />
    ) : null,
    requirePercLoading ? (
      <MultiplierDropdown
        value={basePlanValue.percLoading}
        isLoading={isInitializingQuotation}
        onChange={v =>
          setValue('basePlan.percLoading', v?.value, {
            shouldDirty: true,
            shouldValidate: true,
          })
        }
      />
    ) : null,
  ].filter((item): item is React.JSX.Element => Boolean(item));

  const morbidityLoadingList = [
    requireMorbidityFlatLoading ? (
      <Input
        control={control}
        as={BasePlanDropdown}
        name="basePlan.morbidityFlatLoading"
        fieldName="morbidityFlatLoading"
        title={
          getConfigLabel('morbidityFlatLoading') ??
          t('proposal:basicPlan.morbidityFlatLoading')
        }
        isLoading={isInitializingQuotation}
        disabled={isMorbidityFlatLoadingDisabled}
      />
    ) : null,
    requireMorbidityPercLoading ? (
      <Input
        control={control}
        as={BasePlanDropdown}
        name="basePlan.morbidityPercLoading"
        fieldName="morbidityPercLoading"
        title={
          getConfigLabel('morbidityPercLoading') ??
          t('proposal:basicPlan.morbidityPercLoading')
        }
        isLoading={isInitializingQuotation}
        disabled={isMorbidityPercLoadingDisabled}
      />
    ) : null,
  ].filter((item): item is React.JSX.Element => Boolean(item));

  const loadingSection = [
    {
      title: t('proposal:loading.mortality'),
      list: loadingList,
    },
    {
      title: t('proposal:loading.morbidity'),
      list: morbidityLoadingList,
    },
  ].filter(item => item.list.length > 0);

  return (
    <Container>
      <DialogPhone visible={!!reminderDialog}>
        <Typography.H6 fontWeight="bold" style={{ marginBottom: space[3] }}>
          {reminderDialog?.title}
        </Typography.H6>
        <Typography.Body>{reminderDialog?.description}</Typography.Body>
        <Button
          style={{ marginTop: space[6] }}
          text={t('common:ok')}
          onPress={closeReminderDialog}
        />
      </DialogPhone>

      <DialogPhone visible={alertAffordablePremiumDialog}>
        <Typography.H6
          fontWeight="bold"
          style={{
            marginBottom: space[3],
            textAlign: 'center',
          }}>
          {t('proposal:alertAffordable.title')}
        </Typography.H6>
        <Typography.Body style={{ textAlign: 'center' }}>
          {t('proposal:alertAffordable.description')}
        </Typography.Body>
        <Button
          style={{ marginTop: space[6] }}
          text={t('common:ok')}
          onPress={toggleAlertAffordablePremiumDialog}
        />
      </DialogPhone>

      <View>
        <Header
          headerTitle={({ children }) => (
            <Typography.LargeLabel fontWeight="bold" color={colors.background}>
              {children}
            </Typography.LargeLabel>
          )}
          headerTitleStyle={{
            justifyContent: 'center',
            alignItems: 'center',
          }}
          title={t('proposal:header.title')}
          headerRight={headerRight}
          headerLeft={() => (
            <Pressable onPress={handleOnExitPress}>
              {isProductReselectionEnabled ? (
                <Icon.ArrowLeft size={22} fill={colors.background} />
              ) : (
                <Icon.Close size={22} fill={colors.background} />
              )}
            </Pressable>
          )}
          headerLeftContainerStyle={{
            paddingLeft: space[3],
            justifyContent: 'center',
          }}
          headerRightContainerStyle={{
            paddingRight: space[3],
            justifyContent: 'center',
          }}
          headerStyle={{ backgroundColor: colors.primary }}
        />

        <ViewPdfReminder
          isVisible={isShowPdfReminder}
          text={t('proposal:quotationPDF.readyMessage')}
          onRequestClose={handleOnCloseReminder}
          arrowStyle={isProductReselectionEnabled && { right: space[14] }}
        />
      </View>

      <InputEventListener
        onFocus={onStartInput}
        onBlur={handleInputEvent}
        onDropdownPicked={handleInputEvent}
        setFormValue={setValue}>
        <GestureDetector gesture={panGesture}>
          <Box flex={1}>
            <StickyHeader
              pid={quotationPid}
              planName={basePlanInfo?.productName?.en ?? '--'}
              productLine={basePlanInfo?.productLineName?.en}
              logo={basePlanInfo?.thumbnail}
              currency={basePlanInfo?.currency}
              sumAssured={basePlanInfo?.sumAssured}
              modalPremium={basePlanInfo?.modalPremium}
              paymentMode={basePlanInfo?.paymentMode}
              paymentTerm={basePlanInfo?.premiumTerm}
              paymentType={basePlanInfo?.premiumType}
              policyBenefitPeriod={basePlanInfo?.policyTerm}
              owner={basePlanInfo.owner}
              insured={basePlanInfo.insured}
              isInitializing={isInitializingQuotation}
              isCollapsed={isHeaderCollapsed}
              currentHeight={translationY}
              maxHeight={headerMaxHeight}
              minHeight={headerMinHeight}
              animationDisabled={sectionOffset?.length <= 1}
              deathBenefitOption={basePlanInfo?.deathBenefitOption}
            />

            <View>
              {tabs.length > 0 && (
                <TopTabView
                  onTabPress={handleOnTabPress}
                  focusedSectionIndex={focusedSectionIndex}
                  tabs={tabs}
                  scrollEnabled={false}
                />
              )}

              {shouldDisplayFundBar && (
                <Animated.View style={stickyFundBarStyles}>
                  <TotalFundBar control={control} />
                </Animated.View>
              )}
            </View>

            <AnimatedScrollView
              bounces={false}
              ref={scrollViewRef}
              onScroll={scrollHandler}
              scrollEventThrottle={16}
              overScrollMode="never"
              style={{ zIndex: -1 }}>
              <View onLayout={onSectionLayout(0)}>
                <BasePlanBox
                  control={control}
                  basePlanConfig={baseAndBasicPlanConfig}
                  currency={basePlanInfo?.currency ?? '--'}
                  isLoading={isInitializingQuotation}
                  triggerCalculation={triggerQuotationCalculation}>
                  {(requireFlatLoading ||
                    requireFlatDuration ||
                    requirePercLoading) && (
                    <Divider
                      style={{ marginBottom: space[4] }}
                      text={t('proposal:basicPlan.loadingOptional')}
                    />
                  )}

                  {loadingSection.map(item => (
                    <Column gap={space[2]}>
                      {countryModuleSiConfig.siForm.loadingSubtitle.visible && (
                        <Typography.LargeBody
                          fontWeight="bold"
                          color={colors.palette.fwdDarkGreen[100]}>
                          {item.title}
                        </Typography.LargeBody>
                      )}
                      <Box>{item.list.map(item => item)}</Box>
                    </Column>
                  ))}
                </BasePlanBox>
              </View>

              {puzzleContents.map((puzzleContent, index) => {
                const isFirstPuzzle = index === 0;
                return (
                  <View
                    onLayout={onSectionLayout(index + 1)}
                    key={puzzleContent.key}
                    style={[
                      !isFirstPuzzle && {
                        zIndex: puzzleContents.length - index,
                      },
                      { marginHorizontal: space[4] },
                    ]}>
                    <View style={{ marginBottom: 0 }}>
                      {React.cloneElement(puzzleContent.component, {
                        step: index + 2,
                      })}
                    </View>
                  </View>
                );
              })}

              <View style={{ height: space[20] }}></View>
            </AnimatedScrollView>
          </Box>
        </GestureDetector>
      </InputEventListener>

      <TabletFooter>
        {isValidQuotationCalculation && (
          <SimulationFloatingButton pid={quotationPid} />
        )}
        {!!criticalErrors && (
          <Warning>
            {criticalErrors.map(e => (
              <Typography.Body
                color={colors.error}
                textBreakStrategy="balanced"
                lineBreakStrategyIOS="standard">
                {t(`proposal:criticalError.${e.errorCode}`)}
              </Typography.Body>
            ))}
          </Warning>
        )}

        {initError && (
          <Warning>
            <Typography.Body
              color={colors.error}
              textBreakStrategy="balanced"
              lineBreakStrategyIOS="standard">
              {t(`proposal:error.initError`)}
            </Typography.Body>
          </Warning>
        )}

        {approvalWarning && (
          <Warning>
            <Typography.Body
              color={colors.error}
              textBreakStrategy="balanced"
              lineBreakStrategyIOS="standard">
              {t(`proposal:error.approvalWarning`)}
            </Typography.Body>
          </Warning>
        )}

        <XView>
          <Button
            disabled={isLockFormAction}
            loading={isUpsertingQuotation}
            text={t('common:save')}
            variant="secondary"
            style={{ flex: 1 }}
            onPress={() => setShowSaveQuotationPanel('leave')}
          />
          <Button
            disabled={nextButtonDisabled}
            loading={isCalculatingQuotation}
            text={nextButtonLabel}
            style={{ flex: 1, marginLeft: space[3] }}
            subtext={nextButtonSubLabel}
            onPress={onPressNextButton}
          />
        </XView>
      </TabletFooter>

      <ProposalAgreementModal
        handleClose={onAgreementModalClose}
        visible={visibleAgreement}
        goToSettings={() => setShowLocationPermissionModal(true)}
      />

      <LocationPermissionSetting
        isVisible={showLocationPerrmissionModal}
        cancelOnPress={() => setShowLocationPermissionModal(false)}
      />

      <PdfFilePopup
        visible={isShowModalPdf}
        onDismiss={() => showModalPdf(false)}
        onPress={pdfType => {
          pdfType === SiPdf.SI_PROPOSAL && handleViewSiPdf();

          pdfType === SiPdf.PRODUCT_BROCHURE && viewProductBrochure();
          pdfType === SiPdf.TIPS_PRODUCT_BROCHURE && viewTipsProductBrochure();
          pdfType === SiPdf.PRODUCT_HIGHLIGHTS && viewProductHighlight();
          pdfType === SiPdf.RIPLAY && viewRIPLAY();
        }}
        pdfPopupItems={pdfPopupItems}
      />

      <SaveModal
        dialogVisible={showExitModal}
        onCancel={() => setShowExitModal(false)}
        onConfirm={handleOnExitModalConfirm}
        onDeny={handleOnExitModalDeny}
        title={t('proposal:saveDialog.title')}
        subTitle={
          !isLockFormAction
            ? t('proposal:saveDialog.description')
            : t('proposal:exitDialog.description')
        }
        denyLabel={
          !isLockFormAction
            ? t('proposal:saveDialog.dontSave')
            : t('common:yes')
        }
        saveLabel={
          !isLockFormAction ? t('proposal:saveDialog.save') : t('common:no')
        }
      />

      <SaveQuotationPanel
        visible={!!showSaveQuotationPanel}
        onClose={() => setShowSaveQuotationPanel(undefined)}
        onSave={onSaveQuotation}
        preventNewVersion={!isResumeQuotation}
        defaultQuotationName={defaultQuotationName}
        error={saveProposalNameError}
      />

      <DialogPhone visible={reminderModal.visible}>
        <Typography.H6
          fontWeight="bold"
          style={{
            marginBottom: space[4],
          }}>
          {reminderModal.title}
        </Typography.H6>
        <Typography.Body>{reminderModal.description}</Typography.Body>
        <Button
          style={{ marginTop: space[6] }}
          text={t('common:ok')}
          onPress={() => setReminderModal({ ...reminderModal, visible: false })}
        />
      </DialogPhone>

      <SustainabilityCheckModal
        isVisible={showSustainabilityModal}
        warnings={(generatePdfError as SIPdfWarnings[]) ?? []}
        onCancel={() => setSustainabilityModal(false)}
        setValue={setValue}
        triggerCalculation={triggerQuotationCalculation}
      />
      <PdfViewer
        visible={pdfVisible}
        onClose={hidePdf}
        downloadable
        pdfGenerator={pdfGenerator.current}
        title={title}
        sharable={sharable}
        shareType="email"
        mailConfig={mailConfig}
        extra={extraProperties}
        actionOption={
          sharable && isRequiredToReadAllPdf
            ? {
                activeMode: 'end-of-file',
                actionMode: 'send-email',
                onEmailSent: async emailBody => {
                  await handleOnEmailSent(emailBody);

                  GATracking.logCustomEvent(
                    GATracking.EVENTS.SALES_ILLUSTRATION,
                    {
                      action_type: GATracking.ACTION_TYPES.SI_SEND_PDF,
                      form_source: formSource,
                    },
                  );

                  setNextButtonAction(SiNextButtonAction.PROCEED_NEXT_STEP);
                },
                ...sendPdfButtonLabels,
              }
            : undefined
        }
      />
    </Container>
  );
};

export default SalesIllustrationForm;
