import { StyleSheet, Text, View, Button, TextInput } from 'react-native';
import React, { useRef, useState } from 'react';

import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types/navigation';
import { AnimatedModal } from 'navigation/components/AnimatedModal';
import { H4 } from 'cube-ui-components/dist/cjs/components/Typography';

export default function SearchScreen() {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  return (
    <View>
      <Text>SearchScreen</Text>
      <Button
        title="Go back"
        onPress={() => {
          navigation.goBack();
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({});
