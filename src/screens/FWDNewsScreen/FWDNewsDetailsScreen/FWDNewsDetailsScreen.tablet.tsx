import FWDNewsDetailsScreen from 'features/fwdNews/ph/tablet/FWDNewsDetails/FWDNewsDetailsScreen';
import MYFWDNewsDetailsScreen from 'features/fwdNews/my/tablet/FWDNewsDetails/FWDNewsDetailsScreen';
import React from 'react';
import { country } from 'utils/context';

export default function FWDNewsDetailsScreenTablet() {
  switch (country) {
    case 'ph':
      return <FWDNewsDetailsScreen />;
    case 'my':
    case 'ib':
    case 'id':
      return <MYFWDNewsDetailsScreen />;
    default:
      return <FWDNewsDetailsScreen />;
  }
}
