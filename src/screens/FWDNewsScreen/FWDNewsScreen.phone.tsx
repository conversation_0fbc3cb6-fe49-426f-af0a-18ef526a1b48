import { View } from 'react-native';
import React from 'react';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import PhFwdNewsTabs from 'features/fwdNews/ph/phone/FwdNewsTabs';
import MyFwdNewsTabs from 'features/fwdNews/my/phone/FwdNewsTabs';
import { HeaderRightButtons } from 'features/fwdNews/components/HeaderRightButtons';
import { country } from 'utils/context';

const FwdNewsTabs = () => {
  switch (country) {
    case 'ph':
      return <PhFwdNewsTabs />;
    case 'my':
    case 'ib':
    case 'id':
      return <MyFwdNewsTabs />;
    default:
      <></>;
  }
};
export default function FWDNewsScreenPhone() {
  return (
    <View style={{ flex: 1 }}>
      <ScreenHeader
        route={'FWDNews'}
        isLeftArrowBackShown
        rightChildren={<HeaderRightButtons />}
        showBottomSeparator={false}
      />
      <FwdNewsTabs />
    </View>
  );
}
