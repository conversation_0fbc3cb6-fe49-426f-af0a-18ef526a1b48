import FWDNewsBookmarkScreen from 'features/fwdNews/ph/tablet/FWDNewsBookmark/FWDNewsBookmarkScreen';
import FWDNewsBookmarkScreenMY from 'features/fwdNews/my/tablet/FWDNewsBookmark/FWDNewsBookmarkScreen';
import React from 'react';
import { country } from 'utils/context';

const getFWDNewsBookmarkTablet = () => {
  switch (country) {
    case 'ph':
      return <FWDNewsBookmarkScreen />;
    case 'my':
    case 'ib':
    case 'id':
      return <FWDNewsBookmarkScreenMY />;
    default:
      return <FWDNewsBookmarkScreen />;
  }
};

export default function FWDNewsDetailsScreenTablet() {
  return getFWDNewsBookmarkTablet();
}
