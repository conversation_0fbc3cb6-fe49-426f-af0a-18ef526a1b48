import React from 'react';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';
import DueDateReportScreen from 'features/reportGeneration/my/tablet/DueDateReportScreen';

export default function DueDateReportScreenTablet(props: any) {
  switch (country) {
    case 'ph':
      return <NotFoundScreen />;
    case 'my':
      return <DueDateReportScreen {...props} />;
    case 'ib':
      return <NotFoundScreen />;
    default:
      return <NotFoundScreen />;
  }
}
