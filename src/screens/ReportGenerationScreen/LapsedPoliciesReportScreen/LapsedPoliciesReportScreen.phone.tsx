import React from 'react';
import { country } from 'utils/context';
import LapsedPoliciesReportScreenPH from 'features/reportGeneration/ph/LapsedPoliciesReportScreen';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function LapsedPoliciesReportScreenPhone() {
  switch (country) {
    case 'ph':
      return <LapsedPoliciesReportScreenPH />;
    case 'my':
      return <NotFoundScreen />;
    case 'ib':
      return <NotFoundScreen />;
    default:
      return <NotFoundScreen />;
  }
}
