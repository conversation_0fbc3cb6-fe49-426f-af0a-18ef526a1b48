import React from 'react';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';
import LapsedPoliciesReportScreenPH from 'features/reportGeneration/ph/LapsedPoliciesReportScreen';

export default function LapsedPoliciesReportScreenTablet() {
  switch (country) {
    case 'ph':
      return <LapsedPoliciesReportScreenPH />;
    case 'my':
      return <NotFoundScreen />;
    case 'ib':
      return <NotFoundScreen />;
    default:
      return <NotFoundScreen />;
  }
}
