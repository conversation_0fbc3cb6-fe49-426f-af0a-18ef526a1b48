import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ApplicationNotProceedScreenTablet from './ApplicationNotProceedScreen.tablet';
import ApplicationNotProceedScreenPhone from './ApplicationNotProceedScreen.phone';

export default function ApplicationNotProceedScreen(props: any) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <ApplicationNotProceedScreenTablet {...props} />
  ) : (
    <ApplicationNotProceedScreenPhone />
  );
}
