import React from 'react';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';
import ApplicationNotProceedScreen from 'features/reportGeneration/my/tablet/ApplicationNotProceedScreen';

export default function ApplicationNotProceedScreenTablet(props: any) {
  switch (country) {
    case 'ph':
      return <NotFoundScreen />;
    case 'my':
      return <ApplicationNotProceedScreen {...props} />;
    case 'ib':
      return <NotFoundScreen />;
    default:
      return <NotFoundScreen />;
  }
}
