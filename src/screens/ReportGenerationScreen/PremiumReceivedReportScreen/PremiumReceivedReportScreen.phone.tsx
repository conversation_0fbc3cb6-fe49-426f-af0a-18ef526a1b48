import React from 'react';
import PremiumReceivedReportScreenPH from 'features/reportGeneration/ph/PremiumReceivedReportScreen';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function PremiumReceivedReportScreenPhone() {
  switch (country) {
    case 'ph':
      return <PremiumReceivedReportScreenPH />;
    case 'my':
      return <NotFoundScreen />;
    case 'ib':
      return <NotFoundScreen />;
    default:
      return <NotFoundScreen />;
  }
}
