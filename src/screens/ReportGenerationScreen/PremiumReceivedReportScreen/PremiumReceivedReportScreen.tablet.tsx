import React from 'react';
import { country } from 'utils/context';
import PremiumReceivedReportScreenPH from 'features/reportGeneration/ph/PremiumReceivedReportScreen';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function PremiumReceivedReportScreenTablet() {
  switch (country) {
    case 'ph':
      return <PremiumReceivedReportScreenPH />;
    case 'my':
      return <NotFoundScreen />;
    case 'ib':
      return <NotFoundScreen />;
    default:
      return <NotFoundScreen />;
  }
}
