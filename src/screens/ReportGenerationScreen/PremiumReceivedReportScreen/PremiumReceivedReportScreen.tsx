import PremiumReceivedReportScreenTablet from './PremiumReceivedReportScreen.tablet';
import PremiumReceivedReportScreenPhone from './PremiumReceivedReportScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function PremiumReceivedReportScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <PremiumReceivedReportScreenTablet />
  ) : (
    <PremiumReceivedReportScreenPhone />
  );
}
