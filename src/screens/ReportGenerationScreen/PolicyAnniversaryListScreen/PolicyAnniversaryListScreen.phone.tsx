import React from 'react';
import { country } from 'utils/context';
import PolicyAnniversaryListScreenPH from 'features/reportGeneration/ph/phone/PolicyAnniversaryListScreen';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function PolicyAnniversaryListScreenPhone() {
  switch (country) {
    case 'ph':
      return <PolicyAnniversaryListScreenPH />;
    case 'my':
      return <NotFoundScreen />;
    case 'ib':
      return <NotFoundScreen />;
    default:
      return <NotFoundScreen />;
  }
}
