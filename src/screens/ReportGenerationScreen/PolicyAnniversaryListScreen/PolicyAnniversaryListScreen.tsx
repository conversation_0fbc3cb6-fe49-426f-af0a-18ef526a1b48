
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import PolicyAnniversaryListScreenTablet from './PolicyAnniversaryListScreen.tablet';
import PolicyAnniversaryListScreenPhone from './PolicyAnniversaryListScreen.phone';

export default function ReportGenerationListScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <PolicyAnniversaryListScreenTablet />
  ) : (
    <PolicyAnniversaryListScreenPhone />
  );
}
