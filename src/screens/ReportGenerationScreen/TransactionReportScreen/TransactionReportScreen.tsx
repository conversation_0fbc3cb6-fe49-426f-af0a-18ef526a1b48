import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import TransactionReportScreenTablet from './TransactionReportScreen.tablet';
import TransactionReportScreenPhone from './TransactionReportScreen.phone';
import { TransactionReportScreenProps } from 'features/reportGeneration/my/util/type';

export default function TransactionReportScreen(
  props: TransactionReportScreenProps,
) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <TransactionReportScreenTablet {...props} />
  ) : (
    <TransactionReportScreenPhone />
  );
}
