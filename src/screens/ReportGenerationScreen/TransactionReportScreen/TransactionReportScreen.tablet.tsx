import React from 'react';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';
import TransactionReportScreen from 'features/reportGeneration/my/tablet/TransactionReportScreen';
import { TransactionReportScreenProps } from 'features/reportGeneration/my/util/type';

export default function TransactionReportScreenTablet(
  props: TransactionReportScreenProps,
) {
  switch (country) {
    case 'ph':
      return <NotFoundScreen />;
    case 'my':
      return <TransactionReportScreen {...props} />;
    case 'ib':
      return <NotFoundScreen />;
    default:
      return <NotFoundScreen />;
  }
}
