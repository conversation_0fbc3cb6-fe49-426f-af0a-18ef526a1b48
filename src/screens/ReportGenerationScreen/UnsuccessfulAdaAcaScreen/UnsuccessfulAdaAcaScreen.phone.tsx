import React from 'react';
import { country } from 'utils/context';
import UnsuccessfulAdaAcaScreenPH from 'features/reportGeneration/ph/UnsuccessfulAdaAcaScreen';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function UnsuccessfulAdaAcaScreenPhone() {
  switch (country) {
    case 'ph':
      return <UnsuccessfulAdaAcaScreenPH />;
    case 'my':
      return <NotFoundScreen />;
    case 'ib':
      return <NotFoundScreen />;
    default:
      return <NotFoundScreen />;
  }
}
