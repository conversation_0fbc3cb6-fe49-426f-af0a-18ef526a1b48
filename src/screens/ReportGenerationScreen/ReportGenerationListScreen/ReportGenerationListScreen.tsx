import ReportGenerationListScreenTablet from './ReportGenerationListScreen.tablet';
import ReportGenerationListScreenPhone from './ReportGenerationListScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function ReportGenerationListScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <ReportGenerationListScreenTablet />
  ) : (
    <ReportGenerationListScreenPhone />
  );
}
