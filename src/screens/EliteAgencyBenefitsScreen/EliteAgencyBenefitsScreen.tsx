import React from 'react';
import EliteAgencyBenefitsScreenTablet from 'screens/EliteAgencyBenefitsScreen/EliteAgencyBenefitsScreen.tablet';
import EliteAgencyBenefitsScreenPhone from 'screens/EliteAgencyBenefitsScreen/EliteAgencyBenefitsScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function EliteAgencyBenefitsScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <EliteAgencyBenefitsScreenTablet />
  ) : (
    <EliteAgencyBenefitsScreenPhone />
  );
}
