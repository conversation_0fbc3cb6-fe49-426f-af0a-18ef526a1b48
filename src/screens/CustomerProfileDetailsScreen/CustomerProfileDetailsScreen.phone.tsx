import { country } from 'utils/context';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import CustomerProfileDetailsPH from 'features/lead/ph/LeadProfile/phone/CustomerProfileDetailsScreen';
import CustomerProfileDetailsMY from 'features/lead/my/LeadProfile/phone/CustomerProfileDetails';
import CustomerProfileDetailsIB from 'features/lead/phone/ib/CustomerProfileDetails';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function CustomerProfileDetailsScreenPhone({
  route,
}: {
  route: RouteProp<RootStackParamList, 'CustomerProfileDetails'>;
}) {
  switch (country) {
    case 'ib':
      return <CustomerProfileDetailsIB route={route} />;
    case 'ph':
      return <CustomerProfileDetailsPH route={route} />;
    case 'my':
      return <CustomerProfileDetailsMY route={route} />;
    default:
      return <NotFoundScreen />;
  }
}
