import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import {
  NativeStackScreenProps,
  createNativeStackNavigator,
} from '@react-navigation/native-stack';
import { Box, Icon, Row, Typography } from 'cube-ui-components';
import ApplicationCurrentStatusSVG from 'features/eRecruit/assets/ApplicationCurrentStatusSVG';
import ApplicationFilledStatusSVG from 'features/eRecruit/assets/ApplicationFilledStatusSVG';
import ApplicationNoStatusSVG from 'features/eRecruit/assets/ApplicationNoStatusSVG';
import { useGetReviewAgentsApplicationDetails } from 'features/eRecruit/hooks/useGetReviewAgentsApplicationDetails';
import DocumentsLayout from 'features/eRecruit/ib/tablet/ReviewAgentsApplication/DocumentsLayout';
import PersonalDetailsLayout from 'features/eRecruit/ib/tablet/ReviewAgentsApplication/PersonalDetailsLayout';
import { useRootStackNavigation } from 'hooks/useRootStack';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import { Dispatch, Fragment, SetStateAction, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import {
  ERecruitReviewAgentsApplicationParamList,
  ReviewAgentsApplicationParamList,
} from 'types';

export type ERReviewAgentsApplicationProps = NativeStackScreenProps<
  ERecruitReviewAgentsApplicationParamList,
  'ERecruitReviewAgentsApplication'
>;

export type ProgressBarItem = {
  label: string;
  name: 'personalDetails' | 'documents';
  completed: boolean;
  onfocus: boolean;
  hightlighted: boolean;
};
export type ProgressBarConfig = ProgressBarItem[];

const FormStack =
  createNativeStackNavigator<ReviewAgentsApplicationParamList>();

export default function ERecruitReviewAgentsApplicationScreenTablet({
  route,
  navigation,
}: ERReviewAgentsApplicationProps) {
  const applicationId = route.params.applicationId;
  const fromScreen = route.params.fromScreen;
  console.log('applicationId', applicationId);
  const { colors, space } = useTheme();
  const { t } = useTranslation('eRecruit');
  const { navigate } = useRootStackNavigation();
  const [barStatus, setBarStatus] = useState<ProgressBarConfig>([
    {
      label: 'Personal details',
      name: 'personalDetails',
      completed: false,
      onfocus: true,
      hightlighted: true,
    },
    {
      label: 'Documents',
      name: 'documents',
      completed: false,
      onfocus: false,
      hightlighted: false,
    },
  ]);
  return (
    <View style={{ flex: 1 }}>
      <ScreenHeader
        route={'ERecruitReviewAgentsApplication'}
        isLeftArrowBackShown
        customTitle="Review application"
        showBottomSeparator={false}
        rightChildren={
          <TouchableOpacity
            onPress={() => {
              navigate('Main', { screen: 'Home' });
            }}>
            <Row
              style={{
                alignItems: 'center',
                gap: space[1],
              }}>
              <Icon.Home fill={colors.secondary} />
              <Typography.H7 fontWeight="bold">
                {t('eRecruit.home')}
              </Typography.H7>
            </Row>
          </TouchableOpacity>
        }
      />
      <ProgressBar
        barStatus={barStatus}
        setBarStatus={setBarStatus}
        navigation={navigation}
      />
      <FormStack.Navigator
        screenOptions={{
          headerShown: false,
        }}>
        <FormStack.Screen name="personalDetails">
          {() => (
            <PersonalDetailsLayout
              setBarStatus={setBarStatus}
              navigation={navigation}
              applicationId={applicationId}
            />
          )}
        </FormStack.Screen>
        <FormStack.Screen name="documents">
          {() => (
            <DocumentsLayout
              applicationId={applicationId}
              setBarStatus={setBarStatus}
              fromScreen={fromScreen}
            />
          )}
        </FormStack.Screen>
      </FormStack.Navigator>
    </View>
  );
}

function ProgressBar({
  barStatus,
  setBarStatus,
  navigation,
}: {
  barStatus: ProgressBarConfig;
  setBarStatus: Dispatch<SetStateAction<ProgressBarConfig>>;
  navigation: any;
}) {
  const { colors, space } = useTheme();

  return (
    <Row
      backgroundColor={colors.background}
      borderBottomColor={colors.palette.fwdGrey[100]}
      borderBottomWidth={1}
      paddingY={space[2]}
      paddingX={space[60]}
      alignItems="center"
      justifyContent="space-between"
      gap={space[2]}>
      {barStatus.map((barStatusItem, index) => {
        return (
          <Fragment
            key={'ProgressBar_' + barStatusItem.label + barStatusItem.name}>
            <BarTabButton
              isFocus={barStatusItem.onfocus}
              disabled={!barStatusItem.completed}
              onPress={() => {
                setBarStatus(currentStatus =>
                  currentStatus.map((item, index) => ({
                    ...item,
                    onfocus: item.name === barStatusItem.name,
                  })),
                );
                navigation.navigate(barStatusItem.name);
              }}>
              {barStatusItem.completed ? (
                <ApplicationFilledStatusSVG />
              ) : barStatusItem.onfocus && !barStatusItem.completed ? (
                <ApplicationCurrentStatusSVG />
              ) : (
                <ApplicationNoStatusSVG />
              )}
              <BarTabLabel
                fontWeight="bold"
                isHighlight={barStatusItem.hightlighted}>
                {barStatusItem.label}
              </BarTabLabel>
            </BarTabButton>
            {barStatus.length - 1 !== index ? (
              <Box
                height={2}
                flex={1}
                backgroundColor={colors.palette.fwdGrey[100]}
              />
            ) : (
              <></>
            )}
          </Fragment>
        );
      })}
    </Row>
  );
}

const BarTabButton = styled.TouchableOpacity(
  ({ theme, isFocus }: { theme?: Theme; isFocus: boolean }) => ({
    flexDirection: 'row',
    gap: theme?.space[1],
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: theme?.space[2],
    paddingVertical: theme?.space[1],
    borderRadius: 100,
    backgroundColor: isFocus
      ? theme?.colors.palette.fwdOrange[20]
      : theme?.colors.background,
  }),
);

const BarTabLabel = styled(Typography.H8)(
  ({ theme, isHighlight }: { theme?: Theme; isHighlight: boolean }) => ({
    color: isHighlight
      ? theme?.colors.palette.fwdOrange[100]
      : theme?.colors.palette.fwdGrey[100],
  }),
);
