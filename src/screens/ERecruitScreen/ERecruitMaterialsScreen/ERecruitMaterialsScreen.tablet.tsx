import MaterialsScreen from 'features/eRecruit/tablet/MaterialsScreen';
import React from 'react';
import NotFoundScreen from 'screens/NotFoundScreen';
import { country } from 'utils/context';

export default function ERecruitMaterialsScreenTablet() {
  switch (country) {
    case 'ph':
      return <MaterialsScreen />;
    case 'my':
      return <NotFoundScreen />;
    case 'ib':
      return <NotFoundScreen />;
    case 'id':
      return <MaterialsScreen />;
    default:
      return <NotFoundScreen />;
  }
}
