import React from 'react';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';
import MaterialDetailsScreen from 'features/eRecruit/tablet/MaterialDetailsScreen';

export default function ERecruitMaterialDetailsScreenTablet() {
  switch (country) {
    case 'ph':
      return <MaterialDetailsScreen />;
    case 'my':
      return <NotFoundScreen />;
    case 'ib':
      return <NotFoundScreen />;
    case 'id':
      return <MaterialDetailsScreen />;
    default:
      return <NotFoundScreen />;
  }
}
