import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ERecruitMaterialDetailsScreenPhone from './ERecruitMaterialDetailsScreen.phone';
import ERecruitMaterialDetailsScreenTablet from './ERecruitMaterialDetailsScreen.tablet';

export default function ERecruitMaterialDetailsScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <ERecruitMaterialDetailsScreenTablet />
  ) : (
    <ERecruitMaterialDetailsScreenPhone />
  );
}
