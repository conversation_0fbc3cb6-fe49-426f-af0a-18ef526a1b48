
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ERecruitCheckAppScreenTablet from './ERecruitCheckAppScreen.tablet';
import ERecruitCheckAppScreenPhone from './ERecruitCheckAppScreen.phone';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ERecruitAppCheckParamList } from 'types';


type ERAppCheckingProps = NativeStackScreenProps<
  ERecruitAppCheckParamList,
  'ERecruitCheckApplication'
>;

export default function ERecruitCheckAppScreen(props: ERAppCheckingProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    < ERecruitCheckAppScreenTablet {...props} />
  ) : (
    < ERecruitCheckAppScreenPhone />
  );
}
