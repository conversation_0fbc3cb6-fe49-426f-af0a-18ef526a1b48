import styled from '@emotion/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TabView } from 'react-native-tab-view';
import { useState } from 'react';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamListMap } from 'types';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import { useGetERecruitCheckApp } from 'features/eRecruit/hooks/useGetERecruitCheckApp';
import ProgressBar from 'features/eRecruit/ib/phone/components/CheckApplicationProgressBar/ProgressBar';

export default function ERecruitCheckAppScreenPhone() {
  const { space } = useTheme();
  const [containerSize, setContainerSize] = useState<{
    height?: number;
    width?: number;
  }>({});
  const [topOffset, setTopOffset] = useState(0);
  const route =
    useRoute<RouteProp<RootStackParamListMap['ib'], 'ERecruitApplication'>>();

  const { setIndex, navigationState, renderScene } = useGetERecruitCheckApp();

  const registrationStagingId = route.params?.registrationStagingId ?? '';
  const { t } = useTranslation('eRecruit');
  const { data: recruitmentData } = useGetApplicationData(
    registrationStagingId ?? '',
  );

  return (
    <Container
      isTablet={false}
      edges={['top']}
      style={containerSize}
      onLayout={e => {
        if (!containerSize.height || !containerSize.width) {
          setContainerSize(e.nativeEvent.layout);
        }
      }}>
      <ProgressBar onChangeTopOffset={setTopOffset} />
      <TabView
        swipeEnabled={false}
        navigationState={navigationState}
        onIndexChange={setIndex}
        renderScene={renderScene}
        lazy
        renderTabBar={() => null}
        sceneContainerStyle={{
          marginTop: 0,
        }}
        style={{
          marginTop: space[24],
        }}
      />
    </Container>
  );
}

const Container = styled(SafeAreaView)<{ isTablet: boolean }>(
  ({ theme, isTablet }) => {
    return {
      flex: 1,
      backgroundColor: isTablet
        ? theme.colors.background
        : theme.colors.surface,
    };
  },
);
