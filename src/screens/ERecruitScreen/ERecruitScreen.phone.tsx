import React from 'react';
import { country } from 'utils/context';
import MainScreenPH from 'features/eRecruit/ph/phone/MainScreenPH';
import MainScreenIB from 'features/eRecruit/ib/phone/MainScreenIB';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function ERecruitScreenPhone() {
  switch (country) {
    case 'ph':
      return <MainScreenPH />;
    case 'my':
    case 'ib':
    case 'id':
      return <MainScreenIB />;
    default:
      return <NotFoundScreen />;
  }
}
