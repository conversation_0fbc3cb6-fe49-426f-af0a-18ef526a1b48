import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ERecruitExamTableScreenPhone from './ERecruitExamTableScreen.phone';
import ERecruitExamTableScreenTablet from './ERecruitExamTableScreen.tablet';

export default function ERecruitExamTableScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <ERecruitExamTableScreenTablet />
  ) : (
    <ERecruitExamTableScreenPhone />
  );
}
