import { NativeStackScreenProps } from '@react-navigation/native-stack';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { RootStackParamListMap } from 'types';
import ERecruitCandidateProfileScreenPhone from './ERecruitCandidateProfileScreen.phone';
import ERecruitCandidateProfileScreenTablet from './ERecruitCandidateProfileScreen.tablet';

export type ERAppStatusProps =
  | NativeStackScreenProps<
      RootStackParamListMap['my'],
      'ERecruitCandidateProfile'
    >
  | NativeStackScreenProps<
      RootStackParamListMap['ib'],
      'ERecruitCandidateProfile'
    >
  | NativeStackScreenProps<RootStackParamListMap['ph'], 'CandidateProfile'>;

export default function ERecruitERecruitCandidateProfileScreen(
  props: ERAppStatusProps,
) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <ERecruitCandidateProfileScreenTablet {...props} />
  ) : (
    <ERecruitCandidateProfileScreenPhone {...props} />
  );
}
