import React from 'react';
import { country } from 'utils/context';
import { ERAppStatusProps } from './ERecruitCandidateProfileScreen';
import NotFoundScreen from 'screens/NotFoundScreen';
import PHCandidateProfileScreen from 'features/eRecruit/ph/phone/CandidateProfileScreen';
import FIBCandidateProfileLayout from 'features/eRecruit/ib/phone/FIBCandidateProfileLayout';
import IDNCandidateProfileLayout from 'features/eRecruit/id/phone/IDNCandidateProfileLayout';

export default function ERecruitCandidateProfileScreenPhone(
  props: ERAppStatusProps,
) {
  switch (country) {
    case 'ph':
      return <PHCandidateProfileScreen />;
    case 'my':
    case 'ib':
      return <FIBCandidateProfileLayout {...props} />;
    case 'id':
      return <IDNCandidateProfileLayout {...props} />;
    default:
      return <NotFoundScreen />;
  }
}
