import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ERecruitCandidateListScreenPhone from './ERecruitCandidateListScreen.phone';
import ERecruitCandidateListScreenTablet from './ERecruitCandidateListScreen.tablet';

export default function ERecruitCandidateListScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <ERecruitCandidateListScreenTablet />
  ) : (
    <ERecruitCandidateListScreenPhone />
  );
}
