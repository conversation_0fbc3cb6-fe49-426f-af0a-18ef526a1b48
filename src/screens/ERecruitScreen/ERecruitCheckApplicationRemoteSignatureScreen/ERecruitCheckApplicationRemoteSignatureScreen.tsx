import React from 'react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ERecruitCheckApplicationRemoteSignatureScreenPhone from './ERecruitCheckApplicationRemoteSignatureScreen.phone';

export default function ERecruitCheckApplicationRemoteSignatureScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <></>
  ) : (
    <ERecruitCheckApplicationRemoteSignatureScreenPhone />
  );
}
