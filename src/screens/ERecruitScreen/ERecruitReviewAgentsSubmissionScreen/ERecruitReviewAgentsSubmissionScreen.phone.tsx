import ERecruitReviewAgentSubmissionLayout from 'features/eRecruit/ib/phone/ERecruitReviewAgentSubmissionLayout';
import { country } from 'utils/context';

export default function ERecruitReviewAgentsSubmissionScreenPhone() {
  switch (country) {
    case 'ph':
      return <></>;
    case 'my':
      return <></>;
    case 'ib':
    case 'id':
      return <ERecruitReviewAgentSubmissionLayout />;
    default:
      return <></>;
  }
}
