import React from 'react';
import DocumentsScreenLayout from 'features/Document/tablet/DocumentsScreenLayout';
import IDDocumentsScreenLayout from 'features/Document/id/tablet/DocumentsScreenLayout';
import { country } from 'utils/context';

function DocumentsScreenTablet() {
  switch (country) {
    case 'id':
      return <IDDocumentsScreenLayout />;
    case 'ph':
    case 'my':
    case 'ib':
    default:
      return <DocumentsScreenLayout />;
  }
}

export default DocumentsScreenTablet;
