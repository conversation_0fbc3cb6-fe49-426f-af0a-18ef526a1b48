import React from 'react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import DocumentsScreenPhone from './DocumentsScreen.phone';
import DocumentsScreenTablet from './DocumentsScreen.tablet';

export default function DocumentsScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <DocumentsScreenTablet />
  ) : (
    <DocumentsScreenPhone />
  );
};
