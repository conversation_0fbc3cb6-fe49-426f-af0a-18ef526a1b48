import React from 'react';
import { country } from 'utils/context';
import DocumentsScreenLayout from 'features/Document/phone/DocumentsScreenLayout';
import IDDocumentsScreenLayout from 'features/Document/id/phone/DocumentsScreenLayout';

export default function DocumentsScreenPhone() {
  switch (country) {
    case 'id':
      return <IDDocumentsScreenLayout />;
    case 'ph':
    case 'my':
    case 'ib':
    default:
      return <DocumentsScreenLayout />;
  }
}
