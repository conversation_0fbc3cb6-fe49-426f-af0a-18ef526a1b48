import React from 'react';
import EliteAgencyDetailsScreenTablet from 'screens/EliteAgencyDetailsScreen/EliteAgencyDetailsScreen.tablet';
import EliteAgencyDetailsScreenPhone from 'screens/EliteAgencyDetailsScreen/EliteAgencyDetailsScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function EliteAgencyDetailsScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <EliteAgencyDetailsScreenTablet />
  ) : (
    <EliteAgencyDetailsScreenPhone />
  );
}
