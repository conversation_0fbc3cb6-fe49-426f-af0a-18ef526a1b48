import React from 'react';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { useTranslation } from 'react-i18next';
import EliteAgencyDetailsContent from 'features/recognition/EliteAgencyDetaislContent';
import { View } from 'react-native';
import HeaderToolTip from 'navigation/components/HeaderToolTip';
import { Body } from 'cube-ui-components';

export default function EliteAgencyDetailsScreen() {
  const { t } = useTranslation('recognition');

  return (
    <>
      <ScreenHeader
        route={'EliteAgencyDetails'}
        customTitle={t('recognition.eliteAgent.details.title')}
        isLeftArrowBackShown
        rightChildren={<HeaderToolTip children={<ToolTipChildren />} />}
      />
      <EliteAgencyDetailsContent />
    </>
  );
}

const ToolTipChildren = () => {
  const { t } = useTranslation('recognition');

  return (
    <View style={{ flexShrink: 1 }}>
      <Body>{t('recognition.eliteAgent.tooltip.content')}</Body>
    </View>
  );
};
