import React from 'react';
import { country } from 'utils/context';
import PoliciesScreenTabletPH from 'features/policy/ph/tablet/PoliciesScreenTablet';
import PolicyMainTabNavigationTablet from 'features/policy/components/TabNavigationScreen/tablet';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function PoliciesScreenTablet() {
  switch (country) {
    case 'ph':
      return <PoliciesScreenTabletPH />;
    case 'my':
    case 'ib':
    case 'id':
      return <PolicyMainTabNavigationTablet />;
    default:
      return <NotFoundScreen />;
  }
}
