import React from 'react';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';
import PoliciesSelectApproveScreenIB from 'features/policy/ib/phone/PoliciesSelectApproveScreen';

export default function PoliciesSelectApproveScreenPhone() {
  switch (country) {
    case 'ph':
      return <NotFoundScreen />;
    case 'my':
      return <NotFoundScreen />;
    case 'ib':
      return <PoliciesSelectApproveScreenIB />;
    default:
      return <NotFoundScreen />;
  }
}
