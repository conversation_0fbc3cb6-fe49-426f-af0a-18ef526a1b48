import React from 'react';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';
import POSListScreenPH, {
  POSProps,
} from 'features/policy/ph/phone/POSListScreen';
import PolicyServicingListScreenIB from 'features/policy/ib/phone/PolicyServicingListScreen';

/**
 * POS / Policy servicing
 */
export default function POSListScreenPhone() {
  switch (country) {
    case 'ph':
      return <POSListScreenPH />;
    case 'my':
    case 'ib':
    case 'id':
      return <PolicyServicingListScreenIB />;
    default:
      return <NotFoundScreen />;
  }
}
