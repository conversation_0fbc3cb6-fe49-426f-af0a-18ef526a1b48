import POSListScreenTablet from './POSListScreen.tablet';
import POSListScreenPhone from './POSListScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { POSProps } from 'features/policy/ph/phone/POSListScreen';

export default function POSListScreen(props: POSProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? <POSListScreenTablet /> : <POSListScreenPhone />;
}
