import NewBusinessListScreenTablet from './NewBusinessListScreen.tablet';
import NewBusinessListScreenPhone from './NewBusinessListScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { NewBusinessProps } from 'features/policy/ph/phone/NewBusinessListScreen';

export default function NewBusinessListScreen(props: NewBusinessProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <NewBusinessListScreenTablet />
  ) : (
    <NewBusinessListScreenPhone {...props} />
  );
}
