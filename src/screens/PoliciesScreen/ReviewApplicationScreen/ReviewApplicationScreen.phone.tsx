import React from 'react';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';
import ReviewApplicationScreenIB from 'features/policy/ib/phone/ReviewApplicationScreen';

export default function ReviewApplicationScreenPhone() {
  switch (country) {
    case 'ib':
      return <ReviewApplicationScreenIB />;
    case 'my':
    case 'ph':
    default:
      return <NotFoundScreen />;
  }
}
