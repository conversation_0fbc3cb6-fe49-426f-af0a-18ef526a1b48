import React, { useState, useRef, useImperativeHandle } from 'react';
import { Pressable, StyleProp, TextInput, View, ViewStyle } from 'react-native';
import {
  Column,
  Row,
  SmallLabel,
  TextFieldProps,
  TextFieldRef,
  HighlightShadowV2,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import useToggle from 'hooks/useToggle';
import isEmpty from 'lodash/isEmpty';
import HintText from './HintText';

const FnaInput = React.forwardRef(
  (
    {
      label,
      labelRight,
      hint,
      error,
      left,
      right,
      onChange,
      onFocus: onFocusProp,
      onBlur: onBlurProp,
      style,
      highlight,
      ...props
    }: {
      value?: string;
      onChange?: (value: string) => void;
      labelRight?: React.ReactNode;
    } & TextFieldProps,
    ref: React.Ref<TextFieldRef>,
  ) => {
    const { colors, space } = useTheme();
    const inputRef = useRef<TextInput>(null);
    const [isFocused, setFocused, setBlur] = useToggle(false);
    const isControlled = props.value !== undefined;
    const validInputValue = isControlled ? props.value : props.defaultValue;
    const [uncontrolledValue, setUncontrolledValue] = useState(validInputValue);
    const value = isControlled ? props.value : uncontrolledValue;

    useImperativeHandle(ref, () => inputRef.current as TextInput);

    const onChangeText = (text: string) => {
      onChange?.(text);
      if (props.value === undefined) {
        setUncontrolledValue(text);
      }
      props?.onChangeText?.(text);
    };

    return (
      <View style={style as StyleProp<ViewStyle>}>
        <Container onPress={() => inputRef?.current?.focus()}>
          <Column flex={1}>
            <Row alignItems="center" gap={space[2]}>
              <SmallLabel fontWeight="medium" color={colors.placeholder}>
                {label ? label : ' '}
              </SmallLabel>
              {labelRight}
            </Row>
            <Row alignItems="flex-end">
              {left && (
                <IconContainer>
                  {React.isValidElement(left)
                    ? left
                    : React.createElement(left)}
                </IconContainer>
              )}
              <CustomInput
                ref={inputRef}
                selectionColor={colors.palette.fwdOrange[100]}
                autoCorrect={false}
                {...props}
                value={value}
                onChangeText={onChangeText}
                onFocus={() => {
                  setFocused();
                  onFocusProp?.();
                }}
                onBlur={() => {
                  setBlur();
                  onBlurProp?.();
                }}
                placeholder={isFocused ? '' : props.placeholder}
                placeholderTextColor={colors.palette.fwdGreyDark}
                style={props.inputStyle}
              />
              {!isFocused && right && (
                <IconContainer>
                  {React.isValidElement(right)
                    ? right
                    : React.createElement(right)}
                </IconContainer>
              )}
            </Row>
          </Column>
        </Container>
        <HighlightShadowV2 disabled={isFocused || !highlight || Boolean(value)}>
          <BottomLine
            isFocused={isFocused}
            highlight={highlight}
            value={value}
          />
        </HighlightShadowV2>
        {(!isEmpty(hint) || !isEmpty(error)) && (
          <HintText status={error ? 'error' : 'default'}>
            {error || hint}
          </HintText>
        )}
      </View>
    );
  },
);

const Container = styled(Pressable)(() => ({
  flexDirection: 'row',
  alignItems: 'flex-end',
}));

const CustomInput = styled(TextInput)(({ theme: { typography } }) => ({
  paddingTop: 10,
  paddingBottom: 9,
  fontSize: typography.h7.size,
  lineHeight: typography.h7.lineHeight,
  fontFamily: 'FWDCircularTT-Bold',
  flex: 1,
}));

const IconContainer = styled(View)(() => ({
  alignItems: 'flex-start',
  marginBottom: 9,
}));

const BottomLine = styled.View<{
  isFocused: boolean;
  highlight?: boolean;
  value?: string;
}>(({ theme: { colors }, isFocused, highlight, value }) => ({
  borderBottomWidth: 1,
  borderBottomColor: isFocused
    ? colors.primary
    : highlight && !value
    ? colors.primary
    : colors.palette.fwdGrey[100],
}));

export default FnaInput;
