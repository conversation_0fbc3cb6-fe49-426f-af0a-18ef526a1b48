import { useSaveFna } from 'features/fna/hooks/useSaveFna';
import React, { useCallback } from 'react';
import { CreateLeadRequest } from 'types';
import { country } from 'utils/context';
import CommonCreateLead from './common/CreateLead';
import PHCreateLead from './ph/PHCreateLead';

interface Props {
  visible: boolean;
  onDismiss: () => void;
  onFinish: () => void;
}

export interface InternalCreateLeadProps extends Omit<Props, 'onFinish'> {
  onCreateLead: (
    leadRequest: CreateLeadRequest,
    onSuccess?: () => void,
  ) => Promise<void>;
}

export default function CreateLead({ visible, onDismiss, onFinish }: Props) {
  const { saveFna } = useSaveFna();
  const onCreateLead = useCallback(
    async (leadRequest: CreateLeadRequest, onSuccess?: () => void) => {
      await saveFna({
        isCompleted: false,
        leadRequest: leadRequest,
      });
      onSuccess?.();
      onFinish();
    },
    [saveFna, onFinish],
  );

  return (
    <>
      {Component && (
        <Component
          visible={visible}
          onDismiss={onDismiss}
          onCreateLead={onCreateLead}
        />
      )}
    </>
  );
}

const Component = {
  ib: CommonCreateLead,
  ph: PHCreateLead,
  my: CommonCreateLead,
  id: CommonCreateLead,
}[country];
