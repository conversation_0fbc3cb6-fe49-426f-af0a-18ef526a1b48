import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Button, H6, Row } from 'cube-ui-components';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAvoidingView } from 'react-native';
import { CreateLeadModalProps } from '../CreateLeadModal';

export default function CreateLeadModalTablet({
  visible,
  isLoading,
  isValid,
  onDismiss,
  onSave,
  children,
  style,
}: CreateLeadModalProps) {
  const { t } = useTranslation(['fna']);
  const theme = useTheme();

  return (
    <CFFModal visible={visible} dismissable onDismiss={onDismiss}>
      <KeyboardAvoidingViewContainer behavior="padding">
        <Box
          style={style}
          w={794}
          backgroundColor={theme.colors.background}
          p={theme.space[12]}
          gap={theme.space[6]}
          borderRadius={theme.borderRadius.large}>
          <H6 fontWeight="bold">{t('fna:saveFna')}</H6>
          {children}
          <Row gap={theme.space[5]} justifyContent="center">
            <Button
              size="medium"
              variant="secondary"
              text={t('fna:cancel')}
              style={{ width: 200 }}
              onPress={onDismiss}
            />
            <Button
              size="medium"
              text={t('fna:save')}
              style={{ width: 200 }}
              disabled={!isValid}
              loading={isLoading}
              onPress={() => onSave()}
              gaParams={{
                eventType: 'lead_created',
                formSource: 'fn_assessment_form',
              }}
            />
          </Row>
        </Box>
      </KeyboardAvoidingViewContainer>
    </CFFModal>
  );
}

const KeyboardAvoidingViewContainer = styled(KeyboardAvoidingView)(() => ({
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  alignItems: 'center',
}));
