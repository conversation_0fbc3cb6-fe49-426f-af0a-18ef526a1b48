import React from 'react';
import CreateLeadModalPhone from './phone/CreateLeadModal.phone';
import CreateLeadModalTablet from './tablet/CreateLeadModal.tablet';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { StyleProp, ViewStyle } from 'react-native';

export interface CreateLeadModalProps {
  visible: boolean;
  isLoading: boolean;
  isValid: boolean;
  onDismiss: () => void;
  onSave: (onSuccess?: () => void) => void;
  style?: StyleProp<ViewStyle>;
  children: React.ReactNode;
}

export default function CreateLeadModal(props: CreateLeadModalProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const Component = isTabletMode ? CreateLeadModalTablet : CreateLeadModalPhone;
  return <Component {...props} />;
}
