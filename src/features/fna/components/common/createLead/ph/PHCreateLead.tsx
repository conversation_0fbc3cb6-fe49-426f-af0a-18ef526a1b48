import { useTheme } from '@emotion/react';
import Input from 'components/Input/Input';
import { Box, Dropdown, Row, TextField } from 'cube-ui-components';
import React, { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CountryCode } from 'types/optionList';
import AutocompletePopup from 'components/AutocompletePopup';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useValidationYupResolver } from 'utils/validation';
import {
  PHCreateLeadFormSchemaType,
  phCreateLeadFormValidationSchema,
  phInitialCreateLeadFormData,
} from 'features/fna/utils/validation/createLeadValidation/ph/createLeadValidation';
import { useForm } from 'react-hook-form';
import CreateLeadModal from '../modal/CreateLeadModal';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { InternalCreateLeadProps } from '../CreateLead';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';

export default function PHCreateLead({
  visible,
  onDismiss,
  onCreateLead,
}: InternalCreateLeadProps) {
  const { t } = useTranslation(['fna']);
  const theme = useTheme();

  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  const resolver = useValidationYupResolver(phCreateLeadFormValidationSchema);
  const {
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
    setValue,
  } = useForm<PHCreateLeadFormSchemaType>({
    mode: 'onBlur',
    defaultValues: phInitialCreateLeadFormData,
    resolver: resolver,
  });

  const onSave = useCallback(
    (onSuccess?: () => void) => {
      handleSubmit(async data => {
        await onCreateLead(
          {
            firstName: data.firstName,
            lastName: data.lastName,
            mobilePhoneCountryCode: data.countryCode,
            mobilePhoneNumber: data.mobileNumber,
            email: data.email,
          },
          onSuccess,
        );
      })();
    },
    [handleSubmit, onCreateLead],
  );

  const firstName = useFnaStore(state => state.lifeJourney.firstName);
  const lastName = useFnaStore(state => state.lifeJourney.lastName);
  useEffect(() => {
    setValue('firstName', firstName, { shouldValidate: true });
    setValue('lastName', lastName || '', { shouldValidate: true });
  }, [firstName, lastName, setValue]);

  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <CreateLeadModal
      visible={visible}
      isValid={isValid}
      onDismiss={onDismiss}
      onSave={onSave}
      isLoading={isSubmitting}>
      <Box
        mt={isTabletMode ? 0 : theme.space[5]}
        gap={theme.space[5]}
        flexDirection={isTabletMode ? 'row' : 'column'}>
        <Input
          control={control}
          as={TextField}
          name="firstName"
          label={t('fna:firstName')}
          style={isTabletMode && { flex: 1 }}
        />
        <Input
          control={control}
          as={TextField}
          name="lastName"
          label={t('fna:lastName')}
          style={isTabletMode && { flex: 1 }}
        />
      </Box>
      <Box
        flexDirection={isTabletMode ? 'row' : 'column'}
        mt={isTabletMode ? theme.space[2] : theme.space[5]}
        gap={theme.space[5]}>
        <Row
          gap={theme.space[isTabletMode ? 3 : 4]}
          flex={isTabletMode ? 1 : 0}>
          <Input
            control={control}
            as={
              isTabletMode
                ? AutocompletePopup<CountryCode, string>
                : Dropdown<CountryCode, string>
            }
            searchable
            disabled={isFetchingOptionList}
            name="countryCode"
            label={t('fna:countryCode')}
            data={optionList?.COUNTRY_CODE.options ?? []}
            getItemValue={item => item.value}
            getItemLabel={item => `+${item.label.split('-')[0]}`}
            style={{ width: 109 }}
          />
          <Input
            control={control}
            as={TextField}
            name="mobileNumber"
            label={t('fna:mobileNumber')}
            keyboardType="number-pad"
            returnKeyType="done"
            style={{ flex: 1 }}
          />
        </Row>
        <Input
          control={control}
          as={TextField}
          name="email"
          label={t('fna:email')}
          style={isTabletMode && { flex: 1 }}
        />
      </Box>
    </CreateLeadModal>
  );
}
