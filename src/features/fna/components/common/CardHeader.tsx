import { Button, H6, Row } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { StyleProp, ViewStyle } from 'react-native';

export const CardHeader = ({
  title,
  icon,
  hasRightBtn,
  onPressRightBtn,
  btnDisabled,
  style,
}: {
  hasRightBtn?: boolean;
  btnDisabled?: boolean;
  onPressRightBtn?: () => void;
  title: string;
  icon: React.ReactNode;
  style?: StyleProp<ViewStyle>;
}) => {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();

  return (
    <Row
      alignItems="center"
      alignContent="center"
      marginBottom={space[3]}
      style={style}>
      <Row
        flex={3}
        alignItems="center"
        alignContent="center"
        style={{ gap: space[1] }}>
        {icon}
        <H6 style={{ flex: 1 }} fontWeight="bold">
          {title}
        </H6>
      </Row>
      {hasRightBtn && (
        <Button
          style={{ flex: 1 }}
          text={t('fna:start')}
          size="small"
          onPress={onPressRightBtn}
          disabled={btnDisabled}
        />
      )}
    </Row>
  );
};
