import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Button } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MainTabParamList, RootStackParamList } from 'types';
export interface Props {
  disabled?: boolean;
  visible?: boolean;
  onPress: () => void;
  keyboardShown?: boolean;
  buttonTitle?: string;
  loading?: boolean;
}

export default function FormFooter({
  disabled,
  onPress,
  keyboardShown,
  buttonTitle,
  visible,
  loading,
}: Props) {
  const { space, colors } = useTheme();
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { t } = useTranslation(['fna']);
  const { isWideScreen } = useWindowAdaptationHelpers();
  const navigation =
    useNavigation<NavigationProp<RootStackParamList & MainTabParamList>>();

  const previousRouteName = useMemo(() => {
    const state = navigation.getState();

    if (state && state.routes && state.routes.length > 1) {
      const previousRoute = state.routes[state.routes.length - 2];
      return previousRoute.name;
    }

    return null; // No previous route
  }, [navigation]);

  const source = useMemo(() => {
    switch (previousRouteName) {
      case 'SalesIllustrationForm':
        return 'sales_illustration';
      case 'LeadProfile':
        return 'menu_leads_and_customers';
      case 'Main':
      case 'Home':
        return 'homepage_cta';
    }
  }, [previousRouteName]);

  if (!visible) {
    return null;
  }
  return (
    <View
      style={{
        display: keyboardShown ? 'none' : 'flex',
        paddingHorizontal: space[4],
        paddingTop: space[4],
        paddingBottom: space[4] + bottomInset,
        backgroundColor: colors.background,
        borderTopWidth: 1,
        borderColor: colors.palette.fwdGrey[100],
        justifyContent: 'center',
      }}>
      <Button
        text={buttonTitle ?? t('fna:done')}
        disabled={disabled}
        onPress={onPress}
        loading={loading}
        style={{ maxWidth: isWideScreen ? 400 : undefined }}
        gaParams={{
          eventType: 'fn_assessment',
          formSource: source || '',
          actionType: 'fna_view_plans',
        }}
      />
    </View>
  );
}
