import React, { JSXElementConstructor, ReactElement } from 'react';
import { Modal, View } from 'react-native';
import {
  Box,
  Button,
  Column,
  Row,
  Switch,
  Typography,
} from 'cube-ui-components';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Concern, ConcernId } from '../../types/concern';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { GoalState, GoalStateItem } from './GoalsPlanning';

type Props = {
  isModalVisible: boolean;
  onDismiss: () => void;
  goals: Concern[];
  switchState: GoalState;
  setSwitchState: (value: GoalState) => void;
  onDonePress: () => void;
};

type GoalItem = {
  number: number | null;
  icon: ReactElement<any, string | JSXElementConstructor<any>> | undefined; // eslint-disable-line @typescript-eslint/no-explicit-any
  title: string | null;
  input1: JSX.Element;
  input2: JSX.Element;
};

const GoalsPlanningTablet = ({
  isModalVisible,
  onDismiss,
  goals,
  switchState,
  setSwitchState,
  onDonePress,
}: Props) => {
  const { space, sizes, colors, borderRadius } = useTheme();
  const { t } = useTranslation(['fna']);
  const insets = useSafeAreaInsets();

  return (
    <Modal visible={isModalVisible} transparent={true} animationType="slide">
      <MainContainer>
        <ModalContainer>
          <GoalsContainer>
            <Column
              pb={insets.bottom}
              backgroundColor={colors.background}
              paddingTop={space[6]}
              gap={space[2]}
              borderTopLeftRadius={borderRadius.large}
              borderTopRightRadius={borderRadius.large}>
              <Column paddingY={space[6]} paddingX={space[14]} gap={space[5]}>
                <Typography.H5 fontWeight={'bold'}>
                  {t('fna:goals.popup.title')}
                </Typography.H5>
                <ScrollView>
                  <GoalHeader
                    header1={t('fna:goals.popup.alreadyPlanned')}
                    header2={t('fna:goals.popup.toBeDiscussed')}
                  />
                  <Column gap={space[3]}>
                    {goals.map(({ id, icon }, idx) => (
                      <GoalItem
                        key={`${id}_${idx}`}
                        number={idx + 1}
                        icon={icon}
                        title={t(`fna:lifeStage.concern.recommendation.${id}`)}
                        input1={
                          <SwitchGoal
                            switchState={switchState}
                            setSwitchState={setSwitchState}
                            goalStateKey={id}
                            goalStateItemKey={'alreadyPlanned'}
                          />
                        }
                        input2={
                          <SwitchGoal
                            switchState={switchState}
                            setSwitchState={setSwitchState}
                            goalStateKey={id}
                            goalStateItemKey={'toBeDiscussed'}
                          />
                        }
                      />
                    ))}
                  </Column>
                </ScrollView>
              </Column>
              <Row
                alignItems="center"
                justifyContent="flex-end"
                gap={space[5]}
                paddingY={space[4]}
                paddingX={space[6]}
                borderTopWidth={1}
                borderColor={colors.palette.fwdGrey[100]}>
                <Button
                  text={t('back')}
                  variant="secondary"
                  onPress={onDismiss}
                  style={{ minWidth: sizes[29] }}
                />
                <Button
                  variant="primary"
                  text={t('next')}
                  subtext={t('fna:calculateNeed')}
                  onPress={onDonePress}
                  style={{ minWidth: sizes[50] }}
                />
              </Row>
            </Column>
          </GoalsContainer>
        </ModalContainer>
      </MainContainer>
    </Modal>
  );
};

const GoalHeader = ({
  header1,
  header2,
}: {
  header1: string;
  header2: string;
}) => {
  const { space } = useTheme();
  return (
    <Row padding={space[2]}>
      <View style={{ flex: 7 }} />
      <Row flex={3} justifyContent="space-around">
        <Typography.H7>{header1}</Typography.H7>
        <Typography.H7>{header2}</Typography.H7>
      </Row>
    </Row>
  );
};

const GoalItem = ({ number, icon, title, input1, input2 }: GoalItem) => {
  const { space, sizes, colors, borderRadius } = useTheme();
  return (
    <Row
      alignItems="center"
      padding={space[4]}
      borderRadius={borderRadius.large}
      borderWidth={1}
      borderColor={colors.palette.fwdGrey[100]}>
      <Row flex={7} alignItems="center" paddingX={space[4]} gap={space[2]}>
        <Box
          backgroundColor={colors.palette.black}
          alignItems="center"
          justifyContent="center"
          boxSize={sizes[7]}
          borderRadius={borderRadius.full}>
          <Typography.H8 fontWeight={'bold'} color={colors.palette.white}>
            {number}
          </Typography.H8>
        </Box>
        <Row gap={space[1]} alignItems="center" flex={1}>
          {icon}
          <Typography.H7 fontWeight={'bold'}>
            {title?.replace('\n', ' ')}
          </Typography.H7>
        </Row>
      </Row>
      <Row flex={3} justifyContent="space-around" alignItems="center">
        {input1}
        {input2}
      </Row>
    </Row>
  );
};

const SwitchGoal = ({
  goalStateKey,
  goalStateItemKey,
  switchState,
  setSwitchState,
}: {
  goalStateKey: ConcernId;
  goalStateItemKey: keyof GoalStateItem;
  switchState: GoalState;
  setSwitchState: (state: GoalState) => void;
}) => {
  const { t } = useTranslation('fna');
  const checked = Boolean(switchState[goalStateKey]?.[goalStateItemKey]);

  return (
    <Switch
      label={checked ? t('yes') : t('no')}
      onChange={value => {
        setTimeout(() => {
          const newState = {
            ...switchState,
            [goalStateKey]: {
              ...switchState[goalStateKey],
              [goalStateItemKey]: value,
            },
          };
          setSwitchState(newState);
        }, 100);
      }}
      checked={checked}
    />
  );
};

export const MainContainer = styled(View)(({ theme: { space } }) => ({
  flex: 1,
  backgroundColor: 'rgba(0,0,0,0.5)',
}));

export const ModalContainer = styled(View)(({ theme: { borderRadius } }) => ({
  flex: 1,
  justifyContent: 'flex-end',
}));

export const GoalsContainer = styled(View)(
  ({ theme: { space, borderRadius, colors } }) => ({
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
    paddingTop: space[2],
    backgroundColor: colors.palette.fwdOrange[100],
  }),
);

export default GoalsPlanningTablet;
