import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';
import { ConcernId } from 'features/fna/types/concern';
import {
  GoalFormProps,
  GoalKey,
  GoalSummaryProps,
} from 'features/fna/types/goal';
import React from 'react';
import GoalGroupPhone from './GoalGroup.phone';
import GoalGroupTablet from './GoalGroup.tablet';
import { FnaState } from 'features/fna/utils/store/fnaStore';
import { SvgProps } from 'react-native-svg';

export interface GoalGroupProps<Tab extends GoalKey> {
  initialTab?: string;
  tabs: ({
    id: ConcernId;
    type: Tab;
    title: string;
    icon: React.ComponentType<SvgProps>;
    form?: React.ComponentType<GoalFormProps<FnaState[Tab]>>;
    illustration?: React.ComponentType<GoalSummaryProps<FnaState[Tab]>>;
  } & GoalFormProps<FnaState[Tab]>)[];
  isGroupCompleted?: boolean;
  onDone?: () => void;
  title: string;
  scrollable?: boolean;
}

export default function GoalGroup<Tab extends GoalKey>({
  initialTab,
  tabs,
  isGroupCompleted,
  onDone,
  title,
  scrollable,
}: GoalGroupProps<Tab>) {
  return (
    <DeviceBasedRendering
      tablet={
        <GoalGroupTablet
          tabs={tabs}
          title={title}
          isGroupCompleted={isGroupCompleted}
          onDone={onDone}
          scrollable={scrollable}
          initialTab={initialTab}
        />
      }
      phone={
        <GoalGroupPhone
          tabs={tabs}
          title={title}
          isGroupCompleted={isGroupCompleted}
          onDone={onDone}
          scrollable={scrollable}
          initialTab={initialTab}
        />
      }
    />
  );
}
