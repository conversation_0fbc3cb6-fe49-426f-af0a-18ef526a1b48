import React, { RefObject } from 'react';
import { ScrollViewProps } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

export const GoalContext = React.createContext<{
  onMeasureIllustrationHeight: (height: number) => void;
  setStickyElement: (stickyElement: React.ReactNode | null) => void;
  scrollViewRef: RefObject<KeyboardAwareScrollView | null>;
  addOnScrollListener: (listener: ScrollViewProps['onScroll']) => void;
} | null>(null);
