import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Column, Picker, Row } from 'cube-ui-components';
import { GoalKey } from 'features/fna/types/goal';
import React, { useMemo, useState } from 'react';
import { Platform, TouchableOpacity } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { RootStackParamList } from 'types';
import { cloneDeep } from 'utils/helper/objectUtil';
import { GoalGroupProps } from './GoalGroup';
import { GoalContext } from './GoalContext';
import FnaHeader from '../../header/FnaHeader';

export default function GoalGroup<Tab extends GoalKey>({
  initialTab,
  tabs,
  isGroupCompleted,
  onDone,
  title,
}: GoalGroupProps<Tab>) {
  const { space, colors } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const [activeTabIndex, setActiveTabIndex] = useState<number>(() =>
    initialTab ? tabs.findIndex(tab => tab.id === initialTab) : 0,
  );
  const {
    form: Form,
    illustration: Illustration,
    formData,
    setFormData,
  } = tabs[activeTabIndex];

  const pickerItems = useMemo(
    () => tabs.map((tab, idx) => ({ label: tab.title, value: String(idx) })),
    [tabs],
  );

  return (
    <GoalContext.Provider value={null}>
      <Box flex={1} bgColor={colors.background}>
        <FnaHeader title={title} onClose={navigation.goBack} />
        <Content>
          <GoalContent>
            <Picker
              type="chip"
              size="medium"
              gap={space[2]}
              items={pickerItems}
              value={String(activeTabIndex)}
              onChange={value => setActiveTabIndex(Number(value))}
            />
            <KeyboardAwareScrollView
              keyboardShouldPersistTaps="handled"
              keyboardDismissMode="interactive"
              enableOnAndroid
              extraScrollHeight={
                Platform.OS === 'android' ? -space[2] : space[9]
              }
              keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
              style={{ flex: 1 }}>
              {Form && <Form formData={formData} setFormData={setFormData} />}
              <Box h={space[4]} />
            </KeyboardAwareScrollView>
          </GoalContent>

          {Illustration && (
            <Box flex={1}>
              <Illustration
                formData={formData}
                setFormData={setFormData}
                isGroupCompleted={isGroupCompleted}
                onNextTab={() =>
                  setActiveTabIndex(activeIdx => {
                    if (activeIdx === tabs.length - 1) return 0;
                    return activeIdx + 1;
                  })
                }
                onDone={onDone}
              />
            </Box>
          )}
        </Content>
      </Box>
    </GoalContext.Provider>
  );
}

const Content = styled(Row)(() => ({
  flex: 1,
  backgroundColor: '#f9f9f9',
}));

const GoalContent = styled(Column)(
  ({ theme: { space, colors, elevation } }) => ({
    flex: 2,
    backgroundColor: colors.palette.fwdGrey[20],
    paddingVertical: space[4],
    paddingHorizontal: space[8],
    ...cloneDeep(elevation[0]),
  }),
);

const IconButton = styled(TouchableOpacity)(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  paddingHorizontal: theme.space[2],
  paddingVertical: 10,
  gap: theme.space[1],
}));
