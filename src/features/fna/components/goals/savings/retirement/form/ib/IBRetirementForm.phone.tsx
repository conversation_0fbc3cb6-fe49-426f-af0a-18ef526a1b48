import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  H6,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { RetirementFormProps } from './IBRetirementForm';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import DecimalTextField from 'components/DecimalTextField';
import ExpectedPostRetirementCalculator from '../../calculator/ExpectedPostRetirementCalculator';
import CalculatorIcon from 'features/fna/components/icons/CalculatorIcon';
import { ExpenseMode } from 'types/case';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import RetireLogo from 'features/fna/components/illustrations/RetireLogo';

export default function IBRetirementFormPhone({
  formData: retirementGoal,
  setFormData: updateRetirementGoal,
  yearsToReceiveRetirementAllowance,
  onCalculatorPress,
  updateAnnualIncomeByCalculation,
  hideCalculator,
  calculatorVisible,
  updateAnnualIncome,
  updateYearsToReplace,
  updateCoverageAmount,
  updateHasSavings,
  updateOtherCoverageAmount,
}: RetirementFormProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors } = useTheme();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Box py={space[4]} px={space[isNarrowScreen ? 3 : 4]}>
      <Row mb={space[6]} alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.retirement.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <RetireLogo width={114} height={79} />
        </Box>
      </Row>
      <Box mt={space[4]}>
        <FormContainer
          enabled={Boolean(retirementGoal.enabled)}
          pointerEvents={retirementGoal.enabled ? 'auto' : 'none'}>
          <Row>
            <Row flex={1}>
              <LargeLabel>1.</LargeLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.retirement.numberOfYearsToRetirement')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:savingsGoals.retirement.years')}
              value={yearsToReceiveRetirementAllowance}
              style={{ flex: 1 }}
              maxLength={2}
              disabled
            />
          </Row>
          <Row mt={space[5]} my={space[3]}>
            <Row flex={1}>
              <LargeLabel>2.</LargeLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.retirement.annualIncome')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={retirementGoal.annualIncome}
              onChange={updateAnnualIncome}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <TouchableOpacity onPress={onCalculatorPress}>
            <Row alignItems="center" gap={space[1]}>
              <CalculatorIcon fill={colors.palette.fwdAlternativeOrange[100]} />
              <LargeLabel
                color={colors.palette.fwdAlternativeOrange[100]}
                fontWeight="bold">
                {t('fna:protectionGoals.income.calculator')}
              </LargeLabel>
            </Row>
          </TouchableOpacity>

          <Row mt={space[5]}>
            <Row flex={1}>
              <LargeLabel>3.</LargeLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.retirement.yearsToReplace')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:savingsGoals.retirement.years')}
              style={{ flex: 1 }}
              maxLength={2}
              value={retirementGoal.yearsToReplace}
              onChange={updateYearsToReplace}
            />
          </Row>
          <Row mt={space[5]}>
            <Row flex={1}>
              <LargeLabel>4.</LargeLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.retirement.existingRetirementIncome')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              value={retirementGoal.coverageAmount}
              onChange={updateCoverageAmount}
              style={{ flex: 1 }}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <Box mt={space[5]}>
            <Checkbox
              label={t('fna:savingsGoals.retirement.iHaveOtherSources')}
              checked={retirementGoal.hasSavings ?? undefined}
              onChange={updateHasSavings}
              labelStyle={{
                flex: 1,
                marginTop: -space[1] - 2,
              }}
              style={{
                alignItems: 'flex-start',
              }}
            />
          </Box>
          {retirementGoal.hasSavings && (
            <Row mt={space[5]}>
              <Row flex={1}>
                <LargeLabel>5.</LargeLabel>
                <LargeLabel style={{ flex: 1 }}>
                  {t('fna:savingsGoals.retirement.otherSourceOfIncome')}
                </LargeLabel>
              </Row>
              <Box w={space[3]} />
              <DecimalTextField
                label={t('fna:amount.label')}
                value={retirementGoal.otherCoverageAmount}
                onChange={updateOtherCoverageAmount}
                style={{ flex: 1 }}
                max={MAX_AMOUNT_VALUE}
              />
            </Row>
          )}
        </FormContainer>

        <ExpectedPostRetirementCalculator
          onDone={updateAnnualIncomeByCalculation}
          handleClose={hideCalculator}
          expenses={retirementGoal.expenses}
          inflationRate={retirementGoal.inflationRate}
          investmentRate={retirementGoal.investmentRate}
          yearsToReceiveRetirementAllowance={
            yearsToReceiveRetirementAllowance || 0
          }
          visible={calculatorVisible}
          expenseMode={retirementGoal.expenseMode || ExpenseMode.Monthly}
        />
      </Box>
      <Divider />
      <Box mb={space[10]} mt={space[4]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </Box>
  );
}

const Divider = styled(View)(({ theme }) => {
  return {
    marginTop: theme.space[6],
    marginBottom: theme.space[2],
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));
