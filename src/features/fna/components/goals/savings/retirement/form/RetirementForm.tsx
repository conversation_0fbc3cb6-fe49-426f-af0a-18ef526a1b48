import { Box } from 'cube-ui-components';
import { MAX_AGE_TO_RECEIVE_RETIREMENT } from 'features/fna/constants/goalCalculation';
import { GoalFormProps } from 'features/fna/types/goal';
import { FnaState, useFnaStore } from 'features/fna/utils/store/fnaStore';
import React, { useEffect, useMemo } from 'react';
import { country } from 'utils/context';
import { calculateAge } from 'utils/helper/calculateAge';
import { shallow } from 'zustand/shallow';
import IBRetirementForm from './ib/IBRetirementForm';
import PHRetirementForm from './ph/PHRetirementForm';

export interface Props extends GoalFormProps<FnaState['retirementGoal']> {
  dob?: Date | null;
  updateAgeRetire?: (ageToRetire: number) => void;
  yearsToReceiveRetirementAllowance: number | null;
}

export interface FormProps extends Props {
  updateAgeRetire: (ageToRetire: number) => void;
}

export default function RetirementForm(props: Props) {
  const { ageToRetire, dob, updateAgeRetire } = useFnaStore(
    state => ({
      ageToRetire: state.lifeJourney.ageToRetire,
      dob: state.lifeJourney.dob,
      updateAgeRetire: state.updateAgeRetire,
    }),
    shallow,
  );

  const { formData: retirementGoal, setFormData: updateRetirementGoal } = props;

  const yearsToReceiveRetirementAllowance = useMemo(() => {
    switch (country) {
      case 'my':
      case 'ib':
      case 'id':
        if (!ageToRetire || !dob) return null;
        return Math.max(ageToRetire - calculateAge(dob), 0);
      case 'ph':
        if (!retirementGoal.ageToRetire) return null;
        return MAX_AGE_TO_RECEIVE_RETIREMENT - retirementGoal.ageToRetire;
      default:
        return null;
    }
  }, [ageToRetire, dob, retirementGoal.ageToRetire]);

  useEffect(() => {
    if (retirementGoal.ageToRetire && dob) {
      updateRetirementGoal({
        yearsToAchieve: Math.max(
          retirementGoal.ageToRetire - calculateAge(dob),
          0,
        ),
      });
    }
  }, [retirementGoal.ageToRetire, dob, updateRetirementGoal]);

  useEffect(() => {
    updateRetirementGoal({ yearsToRetire: yearsToReceiveRetirementAllowance });
  }, [updateRetirementGoal, yearsToReceiveRetirementAllowance]);

  return (
    <Box>
      {Form && (
        <Form
          {...props}
          updateAgeRetire={updateAgeRetire}
          yearsToReceiveRetirementAllowance={yearsToReceiveRetirementAllowance}
          dob={dob}
        />
      )}
    </Box>
  );
}

const Form = {
  ib: IBRetirementForm,
  ph: PHRetirementForm,
  my: IBRetirementForm,
  id: IBRetirementForm,
}[country];
