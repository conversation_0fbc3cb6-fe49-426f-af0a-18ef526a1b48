import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  H6,
  LargeLabel,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import Autocomplete from 'components/Autocomplete';
import {
  booleanToYesNoValue,
  yesNoValueToBoolean,
} from 'features/fna/utils/helper/fnaUtils';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { calculateAge } from 'utils/helper/calculateAge';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import DecimalTextField from 'components/DecimalTextField';
import type { RetirementFormProps } from './PHRetirementForm';
import RetireLogo from 'features/fna/components/illustrations/RetireLogo';

export default function PHRetirementFormTablet({
  formData: retirementGoal,
  setFormData: updateRetirementGoal,
  dob,
  updateAgeRetire,
  yearsToReceiveRetirementAllowance,
}: RetirementFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();

  return (
    <Box>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.retirement.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <RetireLogo />
        </Box>
      </Row>
      <Content>
        <Row>
          <Row flex={1.8} alignSelf="flex-start" alignItems="center">
            <FieldLabel>1.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.retirement.ageToRetire')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <Autocomplete
            data={[50, 55, 60, 65].filter(item =>
              dob ? item > calculateAge(dob) : true,
            )}
            getItemLabel={item => item.toString()}
            getItemValue={item => item}
            label={t('fna:savingsGoals.retirement.age')}
            value={retirementGoal.ageToRetire}
            onChange={ageToRetire => {
              if (ageToRetire !== null) {
                updateRetirementGoal({ ageToRetire });
                updateAgeRetire?.(Number(ageToRetire));
              }
            }}
            style={{ flex: 1 }}
          />
        </Row>
        <Row mt={space[3]}>
          <Row flex={1.8} alignSelf="flex-start" alignItems="center">
            <FieldLabel>2.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.retirement.monthlyAllowance')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            style={{ flex: 1 }}
            value={retirementGoal.monthlyAllowance}
            onChange={monthlyAllowance =>
              updateRetirementGoal({ monthlyAllowance })
            }
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
        <Row mt={space[3]}>
          <Row flex={1.8} alignSelf="flex-start" alignItems="center">
            <FieldLabel>3.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.retirement.yearsToReceive')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <TextField
            label={t('fna:savingsGoals.retirement.years')}
            value={String(yearsToReceiveRetirementAllowance ?? '')}
            disabled
            keyboardType="numeric"
            style={{ flex: 1 }}
          />
        </Row>
        <Row mt={space[5]}>
          <Row flex={1.8} alignSelf="flex-start" alignItems="center">
            <FieldLabel>4.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.retirement.anyProgramStarted')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <Row flex={1} justifyContent="space-between">
            <RadioButtonGroup
              value={booleanToYesNoValue(retirementGoal.hasSavings)}
              onChange={value => {
                const hasSavings = yesNoValueToBoolean(value);
                if (hasSavings !== retirementGoal.hasSavings) {
                  updateRetirementGoal({
                    hasSavings,
                    coverageAmount: null,
                  });
                }
              }}>
              <RadioButton
                style={{ flex: 1 }}
                label={t('fna:yes')}
                value="yes"
              />
              <RadioButton style={{ flex: 1 }} label={t('fna:no')} value="no" />
            </RadioButtonGroup>
          </Row>
        </Row>
        {retirementGoal.hasSavings && (
          <Row mt={space[7]}>
            <Row flex={1.8} alignSelf="flex-start" alignItems="center">
              <FieldLabel>4.1.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.retirement.currentProgram')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              value={retirementGoal.coverageAmount}
              onChange={coverageAmount =>
                updateRetirementGoal({ coverageAmount })
              }
              style={{ flex: 1 }}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
        )}
      </Content>
    </Box>
  );
}

const Content = styled(View)(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
