import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  H6,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { RetirementFormProps } from './IBRetirementForm';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import DecimalTextField from 'components/DecimalTextField';
import ExpectedPostRetirementCalculator from '../../calculator/ExpectedPostRetirementCalculator';
import RetireLogo from 'features/fna/components/illustrations/RetireLogo';
import CalculatorIcon from 'features/fna/components/icons/CalculatorIcon';
import { ExpenseMode } from 'types/case';

export default function IBRetirementFormTablet({
  formData: retirementGoal,
  setFormData: updateRetirementGoal,
  yearsToReceiveRetirementAllowance,
  onCalculatorPress,
  updateAnnualIncomeByCalculation,
  hideCalculator,
  calculatorVisible,
  updateAnnualIncome,
  updateYearsToReplace,
  updateCoverageAmount,
  updateHasSavings,
  updateOtherCoverageAmount,
}: RetirementFormProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors, borderRadius } = useTheme();

  return (
    <>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.retirement.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <RetireLogo />
        </Box>
      </Row>
      <Content>
        <FormContainer
          enabled={Boolean(retirementGoal.enabled)}
          pointerEvents={retirementGoal.enabled ? 'auto' : 'none'}>
          <Row>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>1.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.retirement.numberOfYearsToRetirement')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:savingsGoals.retirement.years')}
              value={yearsToReceiveRetirementAllowance}
              style={{ flex: 1 }}
              maxLength={2}
              disabled
            />
          </Row>
          <Row mt={space[5]}>
            <Row flex={1.8} alignSelf="center" alignItems="center">
              <Row flex={1}>
                <FieldLabel>2.</FieldLabel>
                <Box w={space[3]} />
                <LargeLabel style={{ flex: 1 }}>
                  {t('fna:savingsGoals.retirement.annualIncome')}
                </LargeLabel>
              </Row>
              <TouchableOpacity
                style={{
                  alignSelf: 'center',
                  borderWidth: 2,
                  borderRadius: borderRadius.full,
                  borderColor: colors.palette.fwdOrange[100],
                  padding: space[2],
                }}
                onPress={onCalculatorPress}>
                <CalculatorIcon />
              </TouchableOpacity>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={retirementGoal.annualIncome}
              onChange={updateAnnualIncome}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <Row mt={space[5]}>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>3.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.retirement.yearsToReplace')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:savingsGoals.retirement.years')}
              style={{ flex: 1 }}
              maxLength={2}
              value={retirementGoal.yearsToReplace}
              onChange={updateYearsToReplace}
            />
          </Row>
          <Row mt={space[5]}>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>4.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.retirement.existingRetirementIncome')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              value={retirementGoal.coverageAmount}
              onChange={updateCoverageAmount}
              style={{ flex: 1 }}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <Row mt={space[5]}>
            <Box w={space[2]} />
            <Checkbox
              label={t('fna:savingsGoals.retirement.iHaveOtherSources')}
              checked={retirementGoal.hasSavings ?? undefined}
              onChange={updateHasSavings}
            />
          </Row>
          {retirementGoal.hasSavings && (
            <Row mt={space[5]}>
              <Row flex={1.8} alignItems="center">
                <FieldLabel>5.</FieldLabel>
                <Box w={space[3]} />
                <LargeLabel style={{ flex: 1 }}>
                  {t('fna:savingsGoals.retirement.otherSourceOfIncome')}
                </LargeLabel>
              </Row>
              <Box w={space[3]} />
              <DecimalTextField
                label={t('fna:amount.label')}
                value={retirementGoal.otherCoverageAmount}
                onChange={updateOtherCoverageAmount}
                style={{ flex: 1 }}
                max={MAX_AMOUNT_VALUE}
              />
            </Row>
          )}
        </FormContainer>

        <ExpectedPostRetirementCalculator
          onDone={updateAnnualIncomeByCalculation}
          handleClose={hideCalculator}
          expenses={retirementGoal.expenses}
          inflationRate={retirementGoal.inflationRate}
          investmentRate={retirementGoal.investmentRate}
          yearsToReceiveRetirementAllowance={
            yearsToReceiveRetirementAllowance || 0
          }
          visible={calculatorVisible}
          expenseMode={retirementGoal.expenseMode || ExpenseMode.Monthly}
        />
      </Content>
      <Box mt={space[4]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </>
  );
}

const Content = styled(View)(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
