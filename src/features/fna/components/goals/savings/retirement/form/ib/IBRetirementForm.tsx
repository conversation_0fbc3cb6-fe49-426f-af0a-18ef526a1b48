import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { GoalFormProps } from 'features/fna/types/goal';
import {
  FnaState,
  initialFnaState,
  initialRetirementExpenses,
  useFnaStore,
} from 'features/fna/utils/store/fnaStore';
import React, { useCallback, useEffect } from 'react';
import { calculateAge } from 'utils/helper/calculateAge';
import RetirementFormPhone from './IBRetirementForm.phone';
import RetirementFormTablet from './IBRetirementForm.tablet';
import {
  MAX_AGE_RETIREMENT,
  MAX_AGE_TO_RECEIVE_RETIREMENT,
} from 'features/fna/constants/goalCalculation';
import { useGetAverageAnnualIncome } from 'features/fna/hooks/useGetAverageAnnualIncome';
import { Keyboard } from 'react-native';
import useToggle from 'hooks/useToggle';
import {
  DEFAULT_INFLATION_RATE,
  DEFAULT_INVESTMENT_RATE,
} from '../../calculator/ExpectedPostRetirementCalculator';
import { FormProps } from '../RetirementForm';
import { countryModuleFnaConfig } from 'utils/config/module';

export interface RetirementFormProps
  extends GoalFormProps<FnaState['retirementGoal']> {
  updateAgeRetire?: (ageToRetire: number) => void;
  yearsToReceiveRetirementAllowance: number | null;
  onCalculatorPress: () => void;
  updateAnnualIncomeByCalculation: (
    result: Pick<
      FnaState['retirementGoal'],
      'expenseMode' | 'expenses' | 'annualIncome'
    >,
  ) => void;
  hideCalculator: () => void;
  calculatorVisible: boolean;
  updateSkipThisGoal: (disabled: boolean) => void;
  updateAnnualIncome: (amount: number | null) => void;
  updateYearsToReplace: (amount: number | null) => void;
  updateCoverageAmount: (amount: number | null) => void;
  updateHasSavings: (amount: boolean) => void;
  updateOtherCoverageAmount: (amount: number | null) => void;
}

export default function IBRetirementForm({
  formData: retirementGoal,
  setFormData: updateRetirementGoal,
  dob,
  updateAgeRetire,
  yearsToReceiveRetirementAllowance,
}: FormProps) {
  const ageToRetire = useFnaStore(state => state.lifeJourney.ageToRetire);
  const avgAnnualIncome = useGetAverageAnnualIncome();
  const updateAnnualIncomeByCalculation = useCallback(
    (
      result: Pick<
        FnaState['retirementGoal'],
        'expenseMode' | 'expenses' | 'annualIncome'
      >,
    ) => {
      updateRetirementGoal(result);
    },
    [updateRetirementGoal],
  );
  const [calculatorVisible, showCalculator, hideCalculator] = useToggle(false);

  useEffect(() => {
    if (retirementGoal.enabled && countryModuleFnaConfig.hasDefaultGoalValue) {
      const age = dob ? calculateAge(dob) : 0;
      updateRetirementGoal({
        yearsToReplace:
          retirementGoal.yearsToReplace === null
            ? Math.max(
                MAX_AGE_TO_RECEIVE_RETIREMENT -
                  (age > MAX_AGE_RETIREMENT ? age : ageToRetire),
                0,
              )
            : retirementGoal.yearsToReplace,
        coverageAmount:
          retirementGoal.coverageAmount === null
            ? 0
            : retirementGoal.coverageAmount,
        annualIncome:
          retirementGoal.annualIncome === null
            ? avgAnnualIncome
            : retirementGoal.annualIncome,
      });
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const onCalculatorPress = useCallback(() => {
    Keyboard.dismiss();
    showCalculator();
  }, [showCalculator]);

  const updateSkipThisGoal = (disabled: boolean) => {
    if (disabled) {
      updateRetirementGoal({
        ...initialFnaState.retirementGoal,
        targetAmount: 0,
        gapAmount: 0,
        coverageAmount: 0,
        enabled: !disabled,
      });
    } else {
      const age = dob ? calculateAge(dob) : 0;
      updateRetirementGoal({
        yearsToReplace:
          retirementGoal.yearsToReplace === null
            ? Math.max(
                MAX_AGE_TO_RECEIVE_RETIREMENT -
                  (age > MAX_AGE_RETIREMENT ? age : ageToRetire),
                0,
              )
            : retirementGoal.yearsToReplace,
        coverageAmount:
          retirementGoal.coverageAmount === null
            ? 0
            : retirementGoal.coverageAmount,
        annualIncome:
          retirementGoal.annualIncome === null
            ? avgAnnualIncome
            : retirementGoal.annualIncome,
        targetAmount: null,
        gapAmount: null,
        enabled: !disabled,
      });
    }
  };

  const updateAnnualIncome = (value: number | null) => {
    updateRetirementGoal({
      annualIncome: value,
      expenses: initialRetirementExpenses,
      inflationRate: DEFAULT_INFLATION_RATE,
      investmentRate: DEFAULT_INVESTMENT_RATE,
    });
  };

  const updateYearsToReplace = (value: number | null) => {
    updateRetirementGoal({ yearsToReplace: value });
  };

  const updateCoverageAmount = (value: number | null) => {
    updateRetirementGoal({ coverageAmount: value });
  };

  const updateHasSavings = (hasSavings: boolean) => {
    if (hasSavings !== retirementGoal.hasSavings) {
      updateRetirementGoal({
        hasSavings,
        otherCoverageAmount: null,
      });
    }
  };

  const updateOtherCoverageAmount = (value: number | null) => {
    updateRetirementGoal({ otherCoverageAmount: value });
  };

  return (
    <DeviceBasedRendering
      tablet={
        <RetirementFormTablet
          formData={retirementGoal}
          setFormData={updateRetirementGoal}
          updateAgeRetire={updateAgeRetire}
          yearsToReceiveRetirementAllowance={yearsToReceiveRetirementAllowance}
          onCalculatorPress={onCalculatorPress}
          updateAnnualIncomeByCalculation={updateAnnualIncomeByCalculation}
          hideCalculator={hideCalculator}
          calculatorVisible={calculatorVisible}
          updateSkipThisGoal={updateSkipThisGoal}
          updateAnnualIncome={updateAnnualIncome}
          updateYearsToReplace={updateYearsToReplace}
          updateCoverageAmount={updateCoverageAmount}
          updateHasSavings={updateHasSavings}
          updateOtherCoverageAmount={updateOtherCoverageAmount}
        />
      }
      phone={
        <RetirementFormPhone
          formData={retirementGoal}
          setFormData={updateRetirementGoal}
          updateAgeRetire={updateAgeRetire}
          yearsToReceiveRetirementAllowance={yearsToReceiveRetirementAllowance}
          onCalculatorPress={onCalculatorPress}
          updateAnnualIncomeByCalculation={updateAnnualIncomeByCalculation}
          hideCalculator={hideCalculator}
          calculatorVisible={calculatorVisible}
          updateSkipThisGoal={updateSkipThisGoal}
          updateAnnualIncome={updateAnnualIncome}
          updateYearsToReplace={updateYearsToReplace}
          updateCoverageAmount={updateCoverageAmount}
          updateHasSavings={updateHasSavings}
          updateOtherCoverageAmount={updateOtherCoverageAmount}
        />
      }
    />
  );
}
