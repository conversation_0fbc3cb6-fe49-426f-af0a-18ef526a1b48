import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  ExtraLargeBody,
  H6,
  Icon,
  LargeBody,
  LargeLabel,
  <PERSON>er,
  PictogramIcon,
  Row,
  Typography,
} from 'cube-ui-components';
import React, { memo, useCallback, useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { formatCurrency } from 'utils';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Platform, TouchableOpacity } from 'react-native';
import styled from '@emotion/native';
import { ExpenseMode } from 'types/case';
import GraphIcon from 'features/fna/components/icons/GraphIcon';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import {
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  BottomSheetFooterProps,
  BottomSheetFooter,
  useBottomSheetInternal,
  KEYBOARD_STATE,
} from '@gorhom/bottom-sheet';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import DialogPhone from 'components/Dialog.phone';
import { Portal } from '@gorhom/portal';
// @ts-expect-error no export member
import { listenToKeyboardEvents } from 'react-native-keyboard-aware-scroll-view';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import Animated, {
  TransformStyleTypes,
  useAnimatedStyle,
} from 'react-native-reanimated';
import { useFnaSnapPoints } from 'features/fna/hooks/useFnaSnapPoint';
import {
  ExpectedPostRetirementCalculatorProps,
  ICON_BY_EXPENSE_KEY,
  Input,
} from './ExpectedPostRetirementCalculator';

const FooterContext = React.createContext<number | null>(null);

const KeyboardAwareBottomSheetScrollView = listenToKeyboardEvents(
  BottomSheetScrollView,
);

export default memo(function ExpectedPostRetirementCalculator({
  handleClose,
  visible,
  expenseMode,
  setExpenseMode,
  expenseModeList,
  updateTotal,
  expenses,
  inflationRate,
  setInflationRate,
  investmentRate,
  setInvestmentRate,
  total,
  onSubmitRef,
}: ExpectedPostRetirementCalculatorProps) {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['common', 'fna']);
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useFnaSnapPoints();

  const [futureValueTooltipVisible, setFutureValueTooltipVisible] =
    useState(false);

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          setFutureValueTooltipVisible={setFutureValueTooltipVisible}
          onDone={() => {
            onSubmitRef.current();
            bottomSheetProps.bottomSheetRef.current?.close();
          }}
          {...props}
        />
      );
    },
    [
      setFutureValueTooltipVisible,
      onSubmitRef,
      bottomSheetProps.bottomSheetRef,
    ],
  );

  return (
    <>
      {visible && (
        <Portal>
          <FooterContext.Provider value={total}>
            <BottomSheetModalProvider>
              <BottomSheetModal
                onDismiss={handleClose}
                index={0}
                snapPoints={snapPoints}
                {...bottomSheetProps}
                style={{ padding: 0 }}
                footerComponent={renderFooter}>
                <KeyboardAwareBottomSheetScrollView
                  keyboardDismissMode="interactive"
                  enableAutomaticScroll={true}
                  enableOnAndroid={true}
                  extraScrollHeight={
                    Platform.OS === 'android' ? space[4] : -space[9]
                  }
                  keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
                  style={{
                    backgroundColor: colors.background,
                    paddingHorizontal: space[isNarrowScreen ? 3 : 4],
                  }}
                  stickyHeaderIndices={[0]}>
                  <Row
                    alignItems="center"
                    pb={space[4]}
                    ml={-space[2]}
                    bgColor={colors.background}>
                    <PictogramIcon.Calculate size={space[10]} />
                    <LargeBody fontWeight="bold" color={colors.primary}>
                      {t(
                        'fna:savingsGoals.retirement.annualIncome.calculator.title',
                      )}
                    </LargeBody>
                  </Row>
                  <Box>
                    <Box>
                      <Row alignItems="center" justifyContent="space-between">
                        <Typography.LargeBody>
                          {t(
                            'fna:savingsGoals.retirement.annualIncome.calculator.expense',
                          )}
                        </Typography.LargeBody>
                        <Picker
                          type="chip"
                          size="medium"
                          gap={space[2]}
                          items={expenseModeList}
                          value={expenseMode}
                          onChange={value => {
                            setExpenseMode(value as ExpenseMode);
                            updateTotal(
                              expenses,
                              value as ExpenseMode,
                              inflationRate,
                            );
                          }}
                        />
                      </Row>
                      <Box mb={space[2]} />
                      <Box flex={1}>
                        {expenses.map((expense, idx) => (
                          <Input
                            key={expense.type}
                            title={t(
                              `fna:savingsGoals.retirement.annualIncome.calculator.${expense.type}`,
                            )}
                            icon={React.createElement(
                              ICON_BY_EXPENSE_KEY[expense.type],
                            )}
                            value={expense.amount}
                            onChange={amount =>
                              (expenses[idx] = {
                                ...expenses[idx],
                                amount,
                              })
                            }
                            onBlur={() =>
                              updateTotal(
                                [...expenses],
                                expenseMode,
                                inflationRate,
                              )
                            }
                          />
                        ))}
                      </Box>
                      <Box flex={1}>
                        <Input
                          title={t(
                            'fna:savingsGoals.retirement.annualIncome.calculator.inflationRate',
                          )}
                          inputLabel={t(
                            'fna:savingsGoals.retirement.annualIncome.calculator.inflationRate.label',
                          )}
                          icon={React.createElement(GraphIcon)}
                          value={inflationRate}
                          onChange={amount => setInflationRate(amount || 0)}
                          onBlur={() =>
                            updateTotal(
                              [...expenses],
                              expenseMode,
                              inflationRate,
                            )
                          }
                          isPercentage
                        />
                      </Box>
                      <Box mb={space[2]} />
                      <Box flex={1}>
                        <Input
                          title={t(
                            'fna:savingsGoals.retirement.annualIncome.calculator.investmentRate',
                          )}
                          inputLabel={t(
                            'fna:savingsGoals.retirement.annualIncome.calculator.investmentRate.label',
                          )}
                          icon={React.createElement(GraphIcon)}
                          value={investmentRate}
                          onChange={amount => setInvestmentRate(amount || 0)}
                          isPercentage
                        />
                      </Box>
                    </Box>
                    <Box h={space[4]} />
                    <BottomSheetFooterSpace />
                  </Box>
                </KeyboardAwareBottomSheetScrollView>
              </BottomSheetModal>
            </BottomSheetModalProvider>
          </FooterContext.Provider>

          <DialogPhone visible={futureValueTooltipVisible}>
            <Box>
              <CloseButtonDialog
                onPress={() => setFutureValueTooltipVisible(false)}>
                <Icon.Close fill={colors.onBackground} />
              </CloseButtonDialog>
              <H6 fontWeight="bold">
                {t(
                  'fna:savingsGoals.retirement.annualIncome.calculator.tooltip.title',
                )}
              </H6>
              <Row mt={space[4]}>
                <Box flex={1}>
                  <LargeLabel>
                    {t(
                      'fna:savingsGoals.retirement.annualIncome.calculator.tooltip.content',
                    )}
                  </LargeLabel>
                </Box>
              </Row>
            </Box>
          </DialogPhone>
        </Portal>
      )}
    </>
  );
});

interface FormFooterProps extends BottomSheetFooterProps {
  setFutureValueTooltipVisible: (value: boolean) => void;
  onDone: () => void;
}

const FormFooter = ({
  setFutureValueTooltipVisible,
  onDone,
  ...props
}: FormFooterProps) => {
  const targetAmount = useContext(FooterContext);
  const { space, colors } = useTheme();
  const { t } = useTranslation(['fna']);
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();

  const { animatedKeyboardState } = useBottomSheetInternal();
  const [footerHeight, setFooterHeight] = useState(0);
  const animatedStyle = useAnimatedStyle(
    () => ({
      transform: [
        {
          translateY:
            animatedKeyboardState.value === KEYBOARD_STATE.SHOWN
              ? footerHeight
              : 0,
        },
      ] as TransformStyleTypes,
    }),
    [animatedKeyboardState.value, footerHeight],
  );

  return (
    <BottomSheetFooter {...props}>
      <Animated.View
        onLayout={e => setFooterHeight(e.nativeEvent.layout.height)}
        style={[
          animatedStyle,
          {
            paddingHorizontal: space[isNarrowScreen ? 3 : 4],
            paddingTop: space[4],
            paddingBottom: space[4] + bottomInset,
            backgroundColor: colors.background,
            borderTopWidth: 1,
            borderColor: colors.palette.fwdGrey[100],
            justifyContent: 'center',
          },
        ]}>
        <Row alignItems="center" mb={space[6]}>
          <LargeBody style={{ flex: 1 }}>
            {t(
              'fna:savingsGoals.retirement.annualIncome.calculator.totalExpense',
            )}
          </LargeBody>
          <Row flex={1} alignItems="center" justifyContent="flex-end">
            <TouchableOpacity
              onPress={() => setFutureValueTooltipVisible(true)}>
              <Box mr={space[1]}>
                <Icon.InfoCircle
                  size={space[6]}
                  fill={colors.palette.fwdAlternativeOrange[100]}
                />
              </Box>
            </TouchableOpacity>

            <ExtraLargeBody fontWeight="bold" color={colors.primary}>
              {t('fna:currency')}
              {typeof targetAmount === 'number'
                ? ` ${formatCurrency(targetAmount, 2)}`
                : ''}
            </ExtraLargeBody>

            {typeof targetAmount !== 'number' ? (
              <Box mt={space[3]}>
                <ExtraLargeBody fontWeight="bold" color={colors.primary}>
                  --
                </ExtraLargeBody>
              </Box>
            ) : (
              <></>
            )}
          </Row>
        </Row>
        <Button
          text={t('fna:done')}
          disabled={typeof targetAmount !== 'number'}
          onPress={onDone}
          style={{ maxWidth: isWideScreen ? 400 : undefined }}
        />
      </Animated.View>
    </BottomSheetFooter>
  );
};

const CloseButtonDialog = styled.TouchableOpacity(() => ({
  flexDirection: 'row',
  justifyContent: 'flex-end',
}));
