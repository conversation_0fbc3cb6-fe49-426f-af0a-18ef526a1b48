import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { Expense, FnaState } from 'features/fna/utils/store/fnaStore';
import ExpectedPostRetirementCalculatorTablet from './ExpectedPostRetirementCalculator.tablet';
import ExpectedPostRetirementCalculatorPhone from './ExpectedPostRetirementCalculator.phone';
import { ExpenseMode, ExpenseType } from 'types/case';
import { calculateTotalExpense } from 'features/fna/utils/helper/fnaUtils';
import useLatest from 'hooks/useLatest';
import { useTranslation } from 'react-i18next';
import { Box, Icon, LargeBody, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import DecimalTextField from 'components/DecimalTextField';
import {
  MAX_AMOUNT_VALUE,
  MAX_INFLATION_VALUE as MAX_PERCENTAGE_VALUE,
} from 'constants/inputAmount';
import { StyleSheet } from 'react-native';

export const ICON_BY_EXPENSE_KEY = {
  [ExpenseType.Housing]: Icon.House,
  [ExpenseType.Transportation]: Icon.Transportation,
  [ExpenseType.Travel]: Icon.Travel2,
  [ExpenseType.RegularCommitment]: Icon.Wallet,
  [ExpenseType.Food]: Icon.Food,
  [ExpenseType.Healthcare]: Icon.Health,
  [ExpenseType.Other]: Icon.Gift,
};

interface Props {
  handleClose: () => void;
  onDone: (
    result: Pick<
      FnaState['retirementGoal'],
      | 'expenseMode'
      | 'expenses'
      | 'annualIncome'
      | 'inflationRate'
      | 'investmentRate'
    >,
  ) => void;
  expenses: Expense[];
  expenseMode: ExpenseMode;
  visible?: boolean;
  yearsToReceiveRetirementAllowance: number;
  inflationRate: number | null;
  investmentRate: number | null;
}

export interface ExpectedPostRetirementCalculatorProps {
  handleClose: () => void;
  visible?: boolean;
  expenseMode: ExpenseMode;
  setExpenseMode: (value: ExpenseMode) => void;
  expenseModeList: {
    value: ExpenseMode;
    label: string;
  }[];
  total: number | null;
  updateTotal: (
    expenses: Expense[],
    expenseMode: ExpenseMode,
    inflationRate: number,
  ) => void;
  expenses: Expense[];
  inflationRate: number;
  setInflationRate: (value: number) => void;
  investmentRate: number;
  setInvestmentRate: (value: number) => void;
  onSubmitRef: {
    readonly current: () => void;
  };
}

export const DEFAULT_INFLATION_RATE = 4;
export const DEFAULT_INVESTMENT_RATE = 3;

export default function ExpectedPostRetirementCalculator({
  handleClose,
  onDone,
  visible,
  expenses: expensesProp,
  expenseMode: expenseModeProp,
  yearsToReceiveRetirementAllowance,
  inflationRate: inflationRateProp,
  investmentRate: investmentRateProp,
}: Props) {
  const { t } = useTranslation(['common', 'fna']);
  const [expenseMode, setExpenseMode] = useState<ExpenseMode>(expenseModeProp);
  const [inflationRate, setInflationRate] = useState<number>(
    inflationRateProp ?? DEFAULT_INFLATION_RATE,
  );
  const [investmentRate, setInvestmentRate] = useState<number>(
    investmentRateProp ?? DEFAULT_INVESTMENT_RATE,
  );
  const [expenses, setExpenses] = useState(() => [...expensesProp]);
  const [total, setTotal] = useState<number | null>(() =>
    calculateTotalExpense(expensesProp),
  );

  const updateTotal = useCallback(
    (expenses: Expense[], expenseMode: ExpenseMode, inflationRate: number) => {
      let totalExpense = calculateTotalExpense(expenses) || 0;
      totalExpense =
        expenseMode === ExpenseMode.Monthly ? totalExpense * 12 : totalExpense;

      const total =
        totalExpense *
        (1 + inflationRate / 100) ** yearsToReceiveRetirementAllowance;

      setTotal(Math.round(total * 100) / 100);
      setExpenses(expenses);
    },
    [yearsToReceiveRetirementAllowance],
  );

  useEffect(() => {
    if (visible) {
      updateTotal(
        [...expensesProp],
        expenseModeProp,
        inflationRateProp ?? DEFAULT_INFLATION_RATE,
      );
      setExpenseMode(expenseModeProp);
      setInflationRate(inflationRateProp ?? DEFAULT_INFLATION_RATE);
      setInvestmentRate(investmentRateProp ?? DEFAULT_INVESTMENT_RATE);
    }
  }, [
    expensesProp,
    expenseModeProp,
    visible,
    updateTotal,
    inflationRateProp,
    investmentRateProp,
  ]);

  const onSubmitRef = useLatest(() => {
    onDone({
      expenses,
      expenseMode,
      inflationRate,
      investmentRate,
      annualIncome: total,
    });
    handleClose();
  });

  const expenseModeList = useMemo(() => {
    return [
      {
        value: ExpenseMode.Monthly,
        label: t('fna:savingsGoals.retirement.annualIncome.calculator.monthly'),
      },
      {
        value: ExpenseMode.Yearly,
        label: t('fna:savingsGoals.retirement.annualIncome.calculator.yearly'),
      },
    ];
  }, [t]);

  return (
    <DeviceBasedRendering
      tablet={
        <ExpectedPostRetirementCalculatorTablet
          handleClose={handleClose}
          visible={visible}
          expenseMode={expenseMode}
          setExpenseMode={setExpenseMode}
          expenseModeList={expenseModeList}
          total={total}
          updateTotal={updateTotal}
          expenses={expenses}
          inflationRate={inflationRate}
          setInflationRate={setInflationRate}
          investmentRate={investmentRate}
          setInvestmentRate={setInvestmentRate}
          onSubmitRef={onSubmitRef}
        />
      }
      phone={
        <ExpectedPostRetirementCalculatorPhone
          handleClose={handleClose}
          visible={visible}
          expenseMode={expenseMode}
          setExpenseMode={setExpenseMode}
          expenseModeList={expenseModeList}
          total={total}
          updateTotal={updateTotal}
          expenses={expenses}
          inflationRate={inflationRate}
          setInflationRate={setInflationRate}
          investmentRate={investmentRate}
          setInvestmentRate={setInvestmentRate}
          onSubmitRef={onSubmitRef}
        />
      }
    />
  );
}

export const Input = memo(
  ({
    onChange,
    value,
    icon,
    title,
    inputLabel,
    onBlur,
    isPercentage,
  }: {
    onChange: (value: number | null) => void;
    value: number | null;
    icon: JSX.Element;
    title: string;
    inputLabel?: string;
    onBlur?: () => void;
    isPercentage?: boolean;
  }) => {
    const { t } = useTranslation(['fna']);
    const theme = useTheme();
    return (
      <Row mt={theme.space[5]}>
        <Row flex={1} alignItems="flex-start" pr={theme.space[3]}>
          {icon}
          <Box w={theme.space[3]} />
          <LargeBody style={styles.flex1}>{title}</LargeBody>
        </Row>
        <DecimalTextField
          label={inputLabel || t('fna:amount.label')}
          keyboardType="number-pad"
          precision={isPercentage ? 0 : 2}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          style={styles.flex1}
          max={isPercentage ? MAX_PERCENTAGE_VALUE : MAX_AMOUNT_VALUE}
        />
      </Row>
    );
  },
);

const styles = StyleSheet.create({
  flex1: {
    flex: 1,
  },
  px12: {
    paddingHorizontal: 12,
  },
});
