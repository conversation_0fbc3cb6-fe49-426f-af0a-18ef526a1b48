import { useTheme } from '@emotion/react';
import { Body, Box, Icon, Row } from 'cube-ui-components';
import {
  currencyKFormat,
  getDisplayYear,
} from 'features/fna/utils/helper/fnaUtils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import GoalSummary from '../../../goalSummary/GoalSummary';
import { InternalSavingsSummaryProps } from './SavingsSummary';
import FemaleIncomeLogo from 'features/fna/components/illustrations/FemaleIncomeLogo';
import RichFemaleIncomeLogo from 'features/fna/components/illustrations/RichFemaleIncomeLogo';

export default function SavingsSummary({
  formData: savingsGoal,
  isGroupCompleted,
  onNextTab,
  onDone,
  summary,
  isCompleted,
}: InternalSavingsSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  const { space, colors, sizes } = useTheme();

  return (
    <GoalSummary
      title={t('fna:savingsGoals.savings.totalFunds')}
      summary={summary}
      isCompleted={isCompleted}
      isGroupCompleted={isGroupCompleted}
      onNext={onNextTab}
      onDone={onDone}>
      <Box px={space[2]} pt={space[2]} flex={1} justifyContent="space-between">
        <Row alignItems="center" justifyContent="space-around">
          <Box alignItems="center">
            <Body color={colors.secondaryVariant}>
              {t('common:withCurrency', {
                amount:
                  typeof summary.coverage.value === 'number'
                    ? currencyKFormat(summary.coverage.value)
                    : '--',
              })}
            </Body>
            <Body color={colors.secondaryVariant}>
              {t('fna:savingsGoals.savings.invest')}
            </Body>
          </Box>
          <Icon.ChevronRight
            size={sizes[4]}
            fill={colors.palette.fwdGreyDark}
          />
          <Box alignItems="center">
            <Body fontWeight="bold">
              {getDisplayYear(savingsGoal.yearsToAchieve, t)}
            </Body>
          </Box>
          <Icon.ChevronRight
            size={sizes[4]}
            fill={colors.palette.fwdGreyDark}
          />
          <Box alignItems="center">
            <Body color={colors.secondaryVariant}>
              {t('common:withCurrency', {
                amount:
                  typeof summary.target.value === 'number'
                    ? currencyKFormat(summary.target.value)
                    : '--',
              })}
            </Body>
            <Body color={colors.secondaryVariant}>
              {t('fna:savingsGoals.savings.invest')}
            </Body>
          </Box>
        </Row>
        <Row alignItems="flex-end" justifyContent="space-between">
          <FemaleIncomeLogo />
          <RichFemaleIncomeLogo />
        </Row>
      </Box>
    </GoalSummary>
  );
}
