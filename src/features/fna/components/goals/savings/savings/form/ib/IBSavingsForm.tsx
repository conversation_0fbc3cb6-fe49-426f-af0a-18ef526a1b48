import React, { useEffect } from 'react';
import SavingsFormTablet from './IBSavingsForm.tablet';
import SavingsFormPhone from './IBSavingsForm.phone';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { SavingsFormProps } from '../SavingsForm';
import { FnaState, initialFnaState } from 'features/fna/utils/store/fnaStore';
import { GoalFormProps } from 'features/fna/types/goal';
import { countryModuleFnaConfig } from 'utils/config/module';

export type IBSavingsFormProps = GoalFormProps<FnaState['savingsGoal']> & {
  updateSkipThisGoal: (disabled: boolean) => void;
  updatePurpose: (amount: string) => void;
  updateTargetAmount: (amount: number | null) => void;
  updateYearsToAchieve: (amount: number | null) => void;
  updateCoverageAmount: (amount: number | null) => void;
};

const DEFAULT_PURPOSE = 'Custom goal';

export default function SavingsForm({
  formData: savingsGoal,
  setFormData: updateSavingsGoal,
}: SavingsFormProps) {
  useEffect(() => {
    if (savingsGoal.enabled && countryModuleFnaConfig.hasDefaultGoalValue) {
      updateSavingsGoal({
        purpose: DEFAULT_PURPOSE,
        targetAmount:
          savingsGoal.targetAmount === null ? 0 : savingsGoal.targetAmount,
        yearsToAchieve:
          savingsGoal.yearsToAchieve === null ? 0 : savingsGoal.yearsToAchieve,
        coverageAmount:
          savingsGoal.coverageAmount === null ? 0 : savingsGoal.coverageAmount,
      });
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const updateSkipThisGoal = (disabled: boolean) => {
    if (disabled) {
      updateSavingsGoal({
        ...initialFnaState.savingsGoal,
        targetAmount: 0,
        yearsToAchieve: 0,
        coverageAmount: 0,
        enabled: !disabled,
      });
    } else {
      updateSavingsGoal({
        targetAmount: 0,
        yearsToAchieve: 0,
        coverageAmount: 0,
        enabled: !disabled,
      });
    }
  };

  const updatePurpose = (value: string) => {
    updateSavingsGoal({ purpose: value });
  };

  const updateTargetAmount = (value: number | null) => {
    updateSavingsGoal({ targetAmount: value });
  };

  const updateYearsToAchieve = (value: number | null) => {
    updateSavingsGoal({ yearsToAchieve: value });
  };

  const updateCoverageAmount = (value: number | null) => {
    updateSavingsGoal({ coverageAmount: value });
  };

  return (
    <DeviceBasedRendering
      tablet={
        <SavingsFormTablet
          formData={savingsGoal}
          setFormData={updateSavingsGoal}
          updateSkipThisGoal={updateSkipThisGoal}
          updatePurpose={updatePurpose}
          updateTargetAmount={updateTargetAmount}
          updateYearsToAchieve={updateYearsToAchieve}
          updateCoverageAmount={updateCoverageAmount}
        />
      }
      phone={
        <SavingsFormPhone
          formData={savingsGoal}
          setFormData={updateSavingsGoal}
          updateSkipThisGoal={updateSkipThisGoal}
          updatePurpose={updatePurpose}
          updateTargetAmount={updateTargetAmount}
          updateYearsToAchieve={updateYearsToAchieve}
          updateCoverageAmount={updateCoverageAmount}
        />
      }
    />
  );
}
