import DeviceBasedRendering from 'components/DeviceBasedRendering';
import type { InternalEducationSummaryProps } from '../EducationSummary';
import IBEducationSummaryTablet from './IBEducationSummary.tablet';
import IBEducationSummaryPhone from './IBEducationSummary.phone';

export default function IBEducationSummary(
  props: InternalEducationSummaryProps,
) {
  return (
    <DeviceBasedRendering
      tablet={<IBEducationSummaryTablet {...props} />}
      phone={<IBEducationSummaryPhone {...props} />}
    />
  );
}
