import type {
  GoalSummaryProps,
  Summary as SummaryType,
} from 'features/fna/types/goal';
import { checkEducationCompletion } from 'features/fna/utils/helper/checkGoalCompletion';
import { FnaState, useFnaStore } from 'features/fna/utils/store/fnaStore';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { country } from 'utils/context';
import { shallow } from 'zustand/shallow';
import IBEducationSummary from './ib/IBEducationSummary';
import PHEducationSummary from './ph/PHEducationSummary';

export interface InternalEducationSummaryProps
  extends GoalSummaryProps<FnaState['educationGoal']> {
  summary: SummaryType;
  isCompleted: boolean;
}

export default function EducationSummary({
  formData: educationGoal,
  setFormData: updateEducationGoal,
  isGroupCompleted,
  onNextTab,
  onDone,
}: GoalSummaryProps<FnaState['educationGoal']>) {
  const { t } = useTranslation(['fna']);
  const { adviceType, concerns } = useFnaStore(
    state => ({
      adviceType: state.adviceType,
      concerns: state.lifeJourney.concerns,
    }),
    shallow,
  );

  const isCompleted = checkEducationCompletion(
    educationGoal,
    adviceType,
    concerns,
  );

  const summary = useMemo(
    () => ({
      enabled: educationGoal.enabled,
      target: {
        title: t('fna:savingsGoals.retirement.totalNeeds'),
        value: educationGoal.targetAmount,
      },
      coverage: {
        title: t('fna:savingsGoals.totalCurrentAssets'),
        value: educationGoal.coverageAmount,
      },
      gap: {
        title: t('fna:savingsGoals.retirement.totalGap'),
        value: educationGoal.gapAmount,
      },
    }),
    [
      educationGoal.enabled,
      educationGoal.targetAmount,
      educationGoal.coverageAmount,
      educationGoal.gapAmount,
      t,
    ],
  );

  return (
    <>
      {Summary && (
        <Summary
          formData={educationGoal}
          setFormData={updateEducationGoal}
          isGroupCompleted={isGroupCompleted}
          onNextTab={onNextTab}
          onDone={onDone}
          summary={summary}
          isCompleted={isCompleted}
        />
      )}
    </>
  );
}

const Summary = {
  ib: IBEducationSummary,
  ph: PHEducationSummary,
  my: IBEducationSummary,
  id: IBEducationSummary,
}[country];
