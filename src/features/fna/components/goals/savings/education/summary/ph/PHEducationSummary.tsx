import DeviceBasedRendering from 'components/DeviceBasedRendering';
import type { InternalEducationSummaryProps } from '../EducationSummary';
import PHEducationSummaryTablet from './PHEducationSummary.tablet';
import PHEducationSummaryPhone from './PHEducationSummary.phone';

export default function PHEducationSummary(
  props: InternalEducationSummaryProps,
) {
  return (
    <DeviceBasedRendering
      tablet={<PHEducationSummaryTablet {...props} />}
      phone={<PHEducationSummaryPhone {...props} />}
    />
  );
}
