import { useTheme } from '@emotion/react';
import { Body, Box, Icon, Row } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import GoalSummary from '../../../../goalSummary/GoalSummary';
import KidsUntilUniversity from 'features/fna/components/illustrations/KidsUntilUniversity';
import KidsDuringUniversity from 'features/fna/components/illustrations/KidsDuringUniversity';
import { getDisplayYear } from 'features/fna/utils/helper/fnaUtils';
import { InternalEducationSummaryProps } from '../EducationSummary';

export default function PHEducationSummaryPhone({
  formData: educationGoal,
  isGroupCompleted,
  onNextTab,
  onDone,
  summary,
  isCompleted,
}: InternalEducationSummaryProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors, sizes } = useTheme();

  return (
    <GoalSummary
      title={t('fna:savingsGoals.education.totalFunds')}
      summary={summary}
      isCompleted={isCompleted}
      isGroupCompleted={isGroupCompleted}
      onNext={onNextTab}
      onDone={onDone}>
      <Box flex={1} px={space[2]} pt={space[2]} justifyContent="space-between">
        <Row alignItems="center" justifyContent="space-around">
          <Box alignItems="center">
            <Body fontWeight="bold">
              {getDisplayYear(educationGoal.yearsToCollege, t)}
            </Body>
            <Body>{t('fna:savingsGoals.education.untilUniversity')}</Body>
          </Box>
          <Icon.ChevronRight
            size={sizes[4]}
            fill={colors.palette.fwdGreyDark}
          />
          <Box alignItems="center">
            <Body fontWeight="bold">
              {getDisplayYear(educationGoal.yearsInCollege, t)}
            </Body>
            <Body>{t('fna:savingsGoals.education.duringUniversity')}</Body>
          </Box>
        </Row>
        <Row alignItems="flex-end" justifyContent="space-between">
          <KidsUntilUniversity />
          <KidsDuringUniversity />
        </Row>
      </Box>
    </GoalSummary>
  );
}
