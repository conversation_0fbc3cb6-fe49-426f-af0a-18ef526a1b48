import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DecimalTextField from 'components/DecimalTextField';
import Tooltip from 'components/Tooltip';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import {
  Box,
  Checkbox,
  H6,
  Icon,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import * as Crypto from 'expo-crypto';
import IllustrationEducationIcon from 'features/fna/components/illustrations/IllustrationEducationIcon';
import { useGetMaxKids } from 'features/fna/hooks/useGetMaxKids';
import {
  initialEducationKidGoal,
  KidGoal,
} from 'features/fna/utils/store/fnaStore';
import { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import KidForm from '../../input/kidForm/KidForm';
import KidsTab from '../../input/kidsTab/KidsTab';
import type { IBEducationFormProps } from './IBEducationForm';

export default function IBEducationFormTablet({
  tabOfKids,
  calculateTotalFunds,
  formData,
  setFormData,
  onToggleSkipGoal,
  onChangeCoverageAmount,
}: IBEducationFormProps) {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['fna']);

  const maxKids = useGetMaxKids();

  const addNewGoal = () => {
    const kids = formData.goals.length + 1;
    const goals = formData.goals;
    const newGoals: KidGoal[] = [];
    for (let i = 0; i < kids; i++) {
      if (typeof goals[i] !== 'undefined') {
        newGoals.push(goals[i]);
      } else {
        newGoals.push({
          dependentId: Crypto.randomUUID(),
          ...initialEducationKidGoal,
        });
      }
    }
    setFormData({
      numberOfKids: kids,
      goals: newGoals,
    });
    calculateTotalFunds(newGoals);
  };

  const removeGoal = (index: number) => {
    const newGoals = formData.goals.filter((_, i) => i !== index);
    setFormData({
      numberOfKids: newGoals.length,
      goals: newGoals,
    });
    calculateTotalFunds(newGoals);
  };

  return (
    <Box>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.education.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <IllustrationEducationIcon />
        </Box>
      </Row>

      <Row
        alignItems="center"
        gap={space[1]}
        py={space[1]}
        mt={-32}
        mb={space[3]}>
        <Checkbox
          label={t('fna:goal.not.have.children')}
          checked={!formData.enabled}
          onChange={onToggleSkipGoal}
          disabled
          labelStyle={{ opacity: 0.5 }}
          style={{ opacity: 0.5 }}
        />
        <Tooltip
          content={t('fna:goal.skipChildEducation.tooltip')}
          icon={<Icon.InfoCircle />}
        />
      </Row>

      <Content>
        <FormContainer
          enabled={Boolean(formData.enabled)}
          pointerEvents={formData.enabled ? 'auto' : 'none'}>
          <Row rowGap={space[1]} flexWrap="wrap">
            {tabOfKids.map(tab => {
              return (
                <KidsTab
                  key={'KID_TABS_' + tab?.index}
                  text={tab?.label}
                  onRemove={() => removeGoal(tab?.index)}
                  isRemovable={tabOfKids.length > 1}
                />
              );
            })}
            <KidsTab
              key={'KID_TABS_ADD'}
              icon={<Icon.Plus fill={colors.secondary} />}
              onPress={addNewGoal}
              isRemovable={false}
              disabled={tabOfKids.length >= maxKids}
            />
          </Row>

          {tabOfKids.map((item, idx) => (
            <Fragment key={item.index}>
              <KidForm
                index={item.index}
                formData={formData}
                setFormData={setFormData}
                calculateTotalFunds={calculateTotalFunds}
              />
              {idx !== tabOfKids.length - 1 && (
                <Box h={1} backgroundColor={'#D9D9D9'} mt={space[5]} />
              )}
            </Fragment>
          ))}
          <Box h={1} backgroundColor={'#D9D9D9'} mt={space[5]} />
          <Row mt={30} alignItems="center">
            <Box flex={1.8} alignSelf="center">
              <Row>
                <FieldLabel>{tabOfKids.length * 2 + 1}.</FieldLabel>
                <Box w={space[3]} />
                <LargeLabel style={{ flex: 1 }}>
                  {t('fna:savingsGoals.education.howMuchHaveYouSaved')}
                </LargeLabel>
              </Row>
            </Box>
            <Box w={space[5]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              value={formData.coverageAmount}
              onChange={onChangeCoverageAmount}
              style={{ flex: 1 }}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
        </FormContainer>
      </Content>
      <Box mt={space[4]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </Box>
  );
}

const Content = styled.View(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
