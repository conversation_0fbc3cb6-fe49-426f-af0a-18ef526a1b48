import DeviceBasedRendering from 'components/DeviceBasedRendering';
import {
  IB_DEFAULT_EDUCATION_TARGET_AMOUNT,
  UNIVERSITY_AGE,
} from 'features/fna/constants/goalCalculation';
import { initialFnaState } from 'features/fna/utils/store/fnaStore';
import React from 'react';
import type { EducationFormProps } from '../EducationForm';
import IDEducationFormPhone from './IDEducationForm.phone';
import IDEducationFormTablet from './IDEducationForm.tablet';

export interface IDEducationFormProps extends EducationFormProps {
  onToggleSkipGoal: (disabled: boolean) => void;
  onChangeCoverageAmount: (amount: number | null) => void;
}

export default function IDEducationForm(props: EducationFormProps) {
  const { formData } = props;

  const onToggleSkipGoal = (disabled: boolean) => {
    if (disabled) {
      props.setFormData({
        ...initialFnaState.educationGoal,
        targetAmount: 0,
        gapAmount: 0,
        enabled: !disabled,
        alreadyPlanned: props.formData.alreadyPlanned,
        toBeDiscussed: props.formData.toBeDiscussed,
      });
    } else {
      props.calculateTotalFunds(
        [
          {
            ...initialFnaState.educationGoal.goals[0],
            childAge: 0,
            targetAmount: IB_DEFAULT_EDUCATION_TARGET_AMOUNT,
            yearsToAchieve: UNIVERSITY_AGE,
          },
        ],
        0,
      );
      props.setFormData({
        enabled: !disabled,
      });
    }
  };

  const onChangeCoverageAmount = (coverageAmount: number | null) => {
    props.setFormData({
      coverageAmount,
    });
    props.calculateTotalFunds(props.formData.goals, coverageAmount);
  };

  return (
    <DeviceBasedRendering
      tablet={
        <IDEducationFormTablet
          {...props}
          onToggleSkipGoal={onToggleSkipGoal}
          onChangeCoverageAmount={onChangeCoverageAmount}
        />
      }
      phone={
        <IDEducationFormPhone
          {...props}
          onToggleSkipGoal={onToggleSkipGoal}
          onChangeCoverageAmount={onChangeCoverageAmount}
        />
      }
    />
  );
}
