import styled from '@emotion/native';
import { ScrollView } from 'react-native';
import KidsTab from '../../input/kidsTab/KidsTab';
import { Box, H6, Icon, Row } from 'cube-ui-components';
import { useGetMaxKids } from 'features/fna/hooks/useGetMaxKids';
import { useTheme } from '@emotion/react';
import { Fragment } from 'react';
import KidForm from '../../input/kidForm/KidForm';
import {
  initialEducationKidGoal,
  KidGoal,
} from 'features/fna/utils/store/fnaStore';
import * as Crypto from 'expo-crypto';
import IllustrationEducationIcon from 'features/fna/components/illustrations/IllustrationEducationIcon';
import { useTranslation } from 'react-i18next';
import type { EducationFormProps } from '../EducationForm';

export default function PHEducationFormTablet({
  tabOfKids,
  calculateTotalFunds,
  formData,
  setFormData,
}: EducationFormProps) {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['fna']);

  const maxKids = useGetMaxKids();

  const addNewGoal = () => {
    const kids = formData.goals.length + 1;
    const goals = formData.goals;
    const newGoals: KidGoal[] = [];
    for (let i = 0; i < kids; i++) {
      if (typeof goals[i] !== 'undefined') {
        newGoals.push(goals[i]);
      } else {
        newGoals.push({
          dependentId: Crypto.randomUUID(),
          ...initialEducationKidGoal,
        });
      }
    }
    setFormData({
      numberOfKids: kids,
      goals: newGoals,
    });
    calculateTotalFunds(newGoals);
  };

  const removeGoal = (index: number) => {
    const newGoals = formData.goals.filter((_, i) => i !== index);
    setFormData({
      numberOfKids: newGoals.length,
      goals: newGoals,
    });
    calculateTotalFunds(newGoals);
  };

  return (
    <Box>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.education.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <IllustrationEducationIcon />
        </Box>
      </Row>
      <Content>
        <Row rowGap={space[1]} flexWrap="wrap">
          {tabOfKids.map(tab => {
            return (
              <KidsTab
                key={'KID_TABS_' + tab?.index}
                text={tab?.label}
                onRemove={() => removeGoal(tab?.index)}
                isRemovable={tabOfKids.length > 1}
              />
            );
          })}
          <KidsTab
            key={'KID_TABS_ADD'}
            icon={<Icon.Plus fill={colors.secondary} />}
            onPress={addNewGoal}
            isRemovable={false}
            disabled={tabOfKids.length >= maxKids}
          />
        </Row>

        {tabOfKids.map((item, idx) => (
          <Fragment key={item.index}>
            <KidForm
              index={item.index}
              formData={formData}
              setFormData={setFormData}
              calculateTotalFunds={calculateTotalFunds}
            />
            {idx !== tabOfKids.length - 1 && (
              <Box h={1} backgroundColor={'#D9D9D9'} mt={space[5]} />
            )}
          </Fragment>
        ))}
      </Content>
    </Box>
  );
}

const Content = styled.View(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));
