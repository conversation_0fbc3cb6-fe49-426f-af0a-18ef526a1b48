import DeviceBasedRendering from 'components/DeviceBasedRendering';
import {
  IB_DEFAULT_EDUCATION_TARGET_AMOUNT,
  UNIVERSITY_AGE,
} from 'features/fna/constants/goalCalculation';
import { initialFnaState } from 'features/fna/utils/store/fnaStore';
import { useEffect } from 'react';
import { countryModuleFnaConfig } from 'utils/config/module';
import type { EducationFormProps } from '../EducationForm';
import IBEducationFormPhone from './IBEducationForm.phone';
import IBEducationFormTablet from './IBEducationForm.tablet';

export interface IBEducationFormProps extends EducationFormProps {
  onToggleSkipGoal: (disabled: boolean) => void;
  onChangeCoverageAmount: (amount: number | null) => void;
}

export default function IBEducationForm(props: EducationFormProps) {
  const { formData } = props;

  useEffect(() => {
    if (props.formData.enabled && countryModuleFnaConfig.hasDefaultGoalValue) {
      const newKidGoals = [...props.formData.goals].map(kidGoal => {
        let gapAmount = undefined;
        if (
          typeof kidGoal.targetAmount === 'number' &&
          kidGoal.hasSavings &&
          kidGoal.coverageAmount
        ) {
          const targetAmount = kidGoal.targetAmount ?? 0;
          const coverageAmount = kidGoal.coverageAmount ?? 0;
          gapAmount = targetAmount - coverageAmount;
        } else {
          gapAmount = kidGoal.targetAmount;
        }
        return {
          ...kidGoal,
          targetAmount:
            kidGoal.targetAmount === null
              ? IB_DEFAULT_EDUCATION_TARGET_AMOUNT
              : kidGoal.targetAmount,
          yearsToAchieve:
            kidGoal.yearsToAchieve === null
              ? Math.max(0, UNIVERSITY_AGE - (kidGoal.childAge ?? 0))
              : kidGoal.yearsToAchieve,
          childAge: kidGoal.childAge === null ? 0 : kidGoal.childAge,
          gapAmount,
        };
      });
      props.calculateTotalFunds(
        newKidGoals,
        props.formData.coverageAmount === null
          ? 0
          : props.formData.coverageAmount,
      );
    }
  }, [props.formData.goals.length]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    const { goals } = formData;
    if (!goals?.[0]?.dependentId) {
      onToggleSkipGoal(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.goals]);

  const onToggleSkipGoal = (disabled: boolean) => {
    if (disabled) {
      props.setFormData({
        ...initialFnaState.educationGoal,
        targetAmount: 0,
        gapAmount: 0,
        enabled: !disabled,
        alreadyPlanned: props.formData.alreadyPlanned,
        toBeDiscussed: props.formData.toBeDiscussed,
      });
    } else {
      props.calculateTotalFunds(
        [
          {
            ...initialFnaState.educationGoal.goals[0],
            childAge: 0,
            targetAmount: IB_DEFAULT_EDUCATION_TARGET_AMOUNT,
            yearsToAchieve: UNIVERSITY_AGE,
          },
        ],
        0,
      );
      props.setFormData({
        enabled: !disabled,
      });
    }
  };

  const onChangeCoverageAmount = (coverageAmount: number | null) => {
    props.setFormData({
      coverageAmount,
    });
    props.calculateTotalFunds(props.formData.goals, coverageAmount);
  };

  return (
    <DeviceBasedRendering
      tablet={
        <IBEducationFormTablet
          {...props}
          onToggleSkipGoal={onToggleSkipGoal}
          onChangeCoverageAmount={onChangeCoverageAmount}
        />
      }
      phone={
        <IBEducationFormPhone
          {...props}
          onToggleSkipGoal={onToggleSkipGoal}
          onChangeCoverageAmount={onChangeCoverageAmount}
        />
      }
    />
  );
}
