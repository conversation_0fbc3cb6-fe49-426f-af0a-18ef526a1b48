import DeviceBasedRendering from 'components/DeviceBasedRendering';
import type { EducationFormProps } from '../EducationForm';
import PHEducationFormTablet from './PHEducationForm.tablet';
import PHEducationFormPhone from './PHEducationForm.phone';

export default function PHEducationForm(props: EducationFormProps) {
  return (
    <DeviceBasedRendering
      tablet={<PHEducationFormTablet {...props} />}
      phone={<PHEducationFormPhone {...props} />}
    />
  );
}
