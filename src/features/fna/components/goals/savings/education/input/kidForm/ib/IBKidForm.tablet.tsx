import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DecimalTextField from 'components/DecimalTextField';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import { Box, Icon, LargeLabel, Picker, Row } from 'cube-ui-components';
import FnaInput from 'features/fna/components/common/FnaInput';
import CalculatorIcon from 'features/fna/components/icons/CalculatorIcon';
import FemaleIcon from 'features/fna/components/icons/FemaleIcon';
import MaleIcon from 'features/fna/components/icons/MaleIcon';
import BoyAvatar from 'features/fna/components/illustrations/BoyAvatar';
import GirlAvatar from 'features/fna/components/illustrations/GirlAvatar';
import NonGenderAvatar from 'features/fna/components/illustrations/NonGenderAvatar';
import { maxNameLength } from 'features/fna/utils/validation/fnaValidation';
import useToggle from 'hooks/useToggle';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { Gender } from 'types/person';
import UniversityExpensesCalculator from '../../calculator/UniversityExpensesCalculator';
import { IBKidFormProps } from './IBKidForm';

export default function IBKidFormTablet({
  index,
  formData: educationGoal,
  updateFormData,
  nameError,
  onChangeName,
  onFocusName,
  onBlurName,
  onChangeGender,
  onChangeAge,
  onBlurAge,
  onChangeTargetAmount,
  onChangeYearsToAchieve,
}: IBKidFormProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors, borderRadius } = useTheme();
  const [visibleCalculator, showCalculator, hideCalculator] = useToggle();
  const kidGoal = educationGoal.goals[index];
  const gender = kidGoal.gender;
  const disabledCalculator = typeof kidGoal.yearsToAchieve !== 'number';

  return (
    <Box>
      <Row mt={space[5]} alignItems="center">
        {gender === Gender.FEMALE ? (
          <GirlAvatar />
        ) : gender === Gender.MALE ? (
          <BoyAvatar />
        ) : (
          <NonGenderAvatar />
        )}
        <FnaInput
          label={t('fna:name')}
          placeholder={t('fna:savingsGoals.education.kidName')}
          right={<Icon.Create size={20} />}
          value={kidGoal.firstName}
          onChange={onChangeName}
          maxLength={maxNameLength}
          error={nameError}
          style={{ flex: 1, marginLeft: space[3], marginRight: space[5] }}
          onBlur={onBlurName}
          onFocus={onFocusName}
        />
        <Picker
          type="chip"
          size="small"
          label={t('fna:gender')}
          items={[
            {
              value: Gender.MALE,
              label: t('fna:male'),
              icon: () => <MaleIcon />,
            },
            {
              value: Gender.FEMALE,
              label: t('fna:female'),
              icon: () => <FemaleIcon />,
            },
          ]}
          value={gender as Gender}
          onChange={onChangeGender}
          textPosition="right"
        />
        <FnaInput
          label={t('fna:savingsGoals.education.age')}
          right={<Icon.Create size={20} />}
          maxLength={2}
          keyboardType="decimal-pad"
          value={String(kidGoal.childAge ?? '')}
          style={{ marginLeft: space[6], width: 80 }}
          onChange={onChangeAge}
          onBlur={onBlurAge}
        />
      </Row>
      <Row mt={space[6]} alignItems="center">
        <Box flex={1.8} alignSelf="center">
          <Row>
            <FieldLabel>{index * 2 + 1}.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.education.yearsToCollege')}
            </LargeLabel>
          </Row>
        </Box>
        <Box w={space[5]} />
        <DecimalTextField
          label={t('fna:savingsGoals.education.years')}
          value={kidGoal.yearsToAchieve}
          onChange={onChangeYearsToAchieve}
          style={{ flex: 1 }}
          maxLength={2}
          precision={0}
        />
      </Row>
      <Row mt={18} alignItems="center">
        <Row flex={1.8} alignSelf="stretch" alignItems="center">
          <Row alignSelf="flex-start" flex={1}>
            <FieldLabel>{index * 2 + 2}.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.education.totalTuitionFundNeeded')}
            </LargeLabel>
          </Row>
          <TouchableOpacity
            disabled={disabledCalculator}
            style={{
              opacity: disabledCalculator ? 0.5 : 1,
              alignSelf: 'center',
              borderWidth: 2,
              borderRadius: borderRadius.full,
              borderColor: disabledCalculator
                ? colors.placeholder
                : colors.primary,
              padding: space[2],
              backgroundColor: disabledCalculator
                ? colors.surface
                : colors.background,
            }}
            onPress={showCalculator}>
            <CalculatorIcon
              fill={
                disabledCalculator ? colors.palette.fwdGreyDark : colors.primary
              }
            />
          </TouchableOpacity>
        </Row>
        <Box w={space[5]} />
        <DecimalTextField
          label={t('fna:savingsGoals.education.tuitionFund')}
          style={{ flex: 1 }}
          value={kidGoal.targetAmount}
          onChange={onChangeTargetAmount}
          max={MAX_AMOUNT_VALUE}
        />
      </Row>
      <UniversityExpensesCalculator
        visible={visibleCalculator}
        kidGoal={kidGoal}
        onDismiss={hideCalculator}
        onDone={kidGoal => {
          updateFormData(kidGoal, true);
        }}
      />
    </Box>
  );
}

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
