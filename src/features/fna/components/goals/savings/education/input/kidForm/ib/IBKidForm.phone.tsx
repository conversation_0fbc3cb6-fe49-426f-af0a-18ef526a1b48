import {
  Box,
  LargeLabel,
  Row,
  Icon,
  LargeBody,
  <PERSON><PERSON>ield,
  Picker,
} from 'cube-ui-components';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { TouchableOpacity } from 'react-native';
import { UniversityExpensesCalculator } from '../../calculator/UniversityExpensesCalculator';
import DecimalTextField from '../../../../../../../../../components/DecimalTextField';
import CalculatorIcon from 'features/fna/components/icons/CalculatorIcon';
import { maxNameLength } from 'features/fna/utils/validation/fnaValidation';
import { KIDS_ORDINAL_LIST } from '../../../../../../../constants/educationConstants';
import { TFuncKey } from 'i18next';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import { IBKidFormProps } from './IBKidForm';
import { Gender } from 'types/person';
import MaleIcon from 'features/fna/components/icons/MaleIcon';
import FemaleIcon from 'features/fna/components/icons/FemaleIcon';

export default function IBKidFormPhone({
  index,
  formData: educationGoal,
  updateFormData,
  nameError,
  onChangeName,
  onFocusName,
  onBlurName,
  onChangeGender,
  onChangeAge,
  onBlurAge,
  onChangeTargetAmount,
  onChangeYearsToAchieve,
}: IBKidFormProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors } = useTheme();
  const [visibleCalculator, setVisibleCalculator] = useState(false);
  const kidGoal = educationGoal.goals[index];
  const disabledCalculator = typeof kidGoal.yearsToAchieve !== 'number';

  return (
    <Box>
      <Row alignItems="center" mt={space[4]}>
        <Icon.Kid size={space[6]} />
        <Box width={space[1]} />
        <LargeBody color={colors.primary} fontWeight="bold">
          {index === 0
            ? t('fna:savingsGoals.education.kidInformation')
            : t('fna:savingsGoals.education.kidsInformation', {
                label: t(KIDS_ORDINAL_LIST[index] as TFuncKey<['fna']>),
              })}
        </LargeBody>
      </Row>
      <Row mt={space[6]}>
        <Row flex={1}>
          <LargeLabel>{index * 5 + 1}.</LargeLabel>
          <LargeLabel>{t('fna:savingsGoals.education.yourKidName')}</LargeLabel>
        </Row>
        <Box w={space[3]} />
        <TextField
          label={t('fna:name')}
          value={kidGoal.firstName}
          style={{ flex: 1 }}
          onChange={onChangeName}
          maxLength={maxNameLength}
          error={nameError}
          onBlur={onBlurName}
          onFocus={onFocusName}
        />
      </Row>
      <Row mt={space[6]} alignItems="center">
        <Row flex={1}>
          <LargeLabel>{index * 5 + 2}.</LargeLabel>
          <LargeLabel>{t('fna:savingsGoals.education.gender')}</LargeLabel>
        </Row>
        <Box w={space[3]} />
        <Picker
          type="chip"
          size="large"
          textPosition="right"
          items={[
            {
              value: Gender.MALE,
              label: t('fna:male'),
              icon: MaleIcon,
            },
            {
              value: Gender.FEMALE,
              label: t('fna:female'),
              icon: FemaleIcon,
            },
          ]}
          value={kidGoal.gender as Gender}
          onChange={onChangeGender}
        />
      </Row>
      <Row mt={space[6]}>
        <Row flex={1}>
          <LargeLabel>{index * 5 + 3}.</LargeLabel>
          <LargeLabel>{t('fna:savingsGoals.education.yourKidAge')}</LargeLabel>
        </Row>
        <Box w={space[3]} />
        <TextField
          label={t('fna:savingsGoals.education.age')}
          maxLength={2}
          keyboardType="decimal-pad"
          returnKeyType="done"
          value={String(kidGoal.childAge ?? '')}
          style={{ flex: 1 }}
          onChange={onChangeAge}
          onBlur={onBlurAge}
        />
      </Row>
      <Row mt={space[6]}>
        <Row flex={1}>
          <LargeLabel>{index * 5 + 4}.</LargeLabel>
          <LargeLabel>
            {t('fna:savingsGoals.education.yearsToCollege')}
          </LargeLabel>
        </Row>
        <Box w={space[3]} />
        <DecimalTextField
          label={t('fna:savingsGoals.education.years')}
          value={kidGoal.yearsToAchieve}
          onChange={onChangeYearsToAchieve}
          style={{ flex: 1 }}
          maxLength={2}
          precision={0}
        />
      </Row>
      <Row mt={space[6]}>
        <Row flex={1}>
          <LargeLabel>{index * 5 + 5}.</LargeLabel>
          <Box flex={1}>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.education.totalTuitionFundNeeded')}
            </LargeLabel>
            <Box h={space[1]} />
            <TouchableOpacity
              disabled={disabledCalculator}
              style={{
                alignSelf: 'flex-start',
                opacity: disabledCalculator ? 0.5 : 1,
              }}
              onPress={() => setVisibleCalculator(true)}>
              <Row alignItems="center" alignSelf="flex-start" gap={space[1]}>
                <CalculatorIcon
                  size={20}
                  fill={colors.palette.fwdAlternativeOrange[100]}
                />
                <LargeLabel
                  fontWeight="bold"
                  color={colors.palette.fwdAlternativeOrange[100]}>
                  {t('fna:savingsGoals.education.calculator')}
                </LargeLabel>
              </Row>
            </TouchableOpacity>
          </Box>
        </Row>
        <Box w={space[3]} />
        <DecimalTextField
          label={t('fna:savingsGoals.education.tuitionFund')}
          style={{ flex: 1 }}
          value={kidGoal.targetAmount}
          onChange={onChangeTargetAmount}
          max={MAX_AMOUNT_VALUE}
        />
      </Row>

      <UniversityExpensesCalculator
        kidGoal={kidGoal}
        onDismiss={() => setVisibleCalculator(false)}
        onDone={kidGoal => {
          updateFormData(kidGoal, true);
        }}
        visible={visibleCalculator}
      />
      {educationGoal.numberOfKids !== index + 1 ? (
        <Box backgroundColor={'#D9D9D9'} h={1} mt={space[5]} />
      ) : (
        <Box h={space[5]} />
      )}
    </Box>
  );
}
