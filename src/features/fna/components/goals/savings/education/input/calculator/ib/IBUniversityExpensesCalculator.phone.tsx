import { useTheme } from '@emotion/react';
import {
  BottomSheetFooter,
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetProps,
  BottomSheetScrollView,
  KEYBOARD_STATE,
  useBottomSheetDynamicSnapPoints,
  useBottomSheetInternal,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import {
  Body,
  Box,
  Button, ExtraLargeBody, Icon,
  LargeBody,
  LargeLabel,
  PictogramIcon,
  Row,
  TextField
} from 'cube-ui-components';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import useLatest from 'hooks/useLatest';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useCallback, useContext, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  LayoutChangeEvent, Platform
} from 'react-native';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { formatCurrency } from 'utils';
import DecimalTextField from '../../../../../../../../../components/DecimalTextField';
import BottomSheetFooterSpace from 'features/eApp/components/phone/common/BottomSheetFooterSpace';
// @ts-expect-error no export member
import { listenToKeyboardEvents } from 'react-native-keyboard-aware-scroll-view';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import type { IBUniversityExpensesProps } from './IBUniversityExpensesCalculator';
import GraphIcon from 'features/fna/components/icons/GraphIcon';
import Tooltip from 'components/Tooltip';

const FooterContext = React.createContext<number | null>(null);

const KeyboardAwareBottomSheetScrollView = listenToKeyboardEvents(
  BottomSheetScrollView,
);

export const IBUniversityExpensesCalculatorPhone = ({
  visible,
  kidGoal,
  onDismiss,
  onDone,
  universityValue,
  onFocusUniversityLocation,
  onChangeAnnualTuitionCosts,
  onChangeAnnualLivingExpenses,
  onChangeInflationRate,
  onChangeYearsInCollege,
}: IBUniversityExpensesProps) => {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['common', 'fna']);

  const bottomSheetProps = useBottomSheet();

  const onDoneRef = useLatest(onDone);

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          onDone={() => {
            bottomSheetProps.bottomSheetRef.current?.close();
            onDoneRef.current();
          }}
          {...props}
        />
      );
    },
    [onDoneRef, bottomSheetProps.bottomSheetRef],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);
  const onLayout = useCallback(
    (e: LayoutChangeEvent) => {
      handleContentLayout(e);
    },
    [handleContentLayout],
  );

  return (
    <>
      {visible && (
        <Portal>
          <FooterContext.Provider value={kidGoal.targetAmount}>
            <BottomSheetModalProvider>
              <BottomSheetModal
                onDismiss={onDismiss}
                index={0}
                snapPoints={animatedSnapPoints as BottomSheetProps['snapPoints']}
                handleHeight={animatedHandleHeight}
                contentHeight={animatedContentHeight}
                {...bottomSheetProps}
                style={{ padding: 0 }}
                footerComponent={renderFooter}>
                <KeyboardAwareBottomSheetScrollView
                  keyboardDismissMode="interactive"
                  enableAutomaticScroll={true}
                  enableOnAndroid={true}
                  extraScrollHeight={
                    Platform.OS === 'android' ? space[4] : space[9]
                  }
                  keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
                  style={{
                    backgroundColor: colors.background,
                  }}
                  onLayout={onLayout}
                  stickyHeaderIndices={[0]}>
                  <Row
                    alignItems="center"
                    pb={space[4]}
                    px={space[isNarrowScreen ? 3 : 4]}
                    bgColor={colors.background}>
                    <PictogramIcon.Calculate size={space[10]} />
                    <LargeBody fontWeight="bold" color={colors.primary}>
                      {t(
                        'fna:savingsGoals.education.universityExpensesCalculator',
                      )}
                    </LargeBody>
                  </Row>
                  <Box
                    style={{
                      paddingHorizontal: space[isNarrowScreen ? 3 : 4],
                    }}>
                    <Row mt={space[2]} gap={space[3]}>
                      <Row flex={1} gap={space[3]}>
                        <Icon.Book />
                        <Box flex={1}>
                          <LargeLabel>
                            {t('fna:savingsGoals.education.universityLocation')}
                          </LargeLabel>
                        </Box>
                      </Row>
                      <TextField
                        label={t('fna:savingsGoals.education.country')}
                        right={<Icon.Dropdown />}
                        value={universityValue}
                        onFocus={onFocusUniversityLocation}
                        showSoftInputOnFocus={false}
                        style={{ flex: 1 }}
                      />
                    </Row>
                    <Row mt={space[6]} gap={space[3]}>
                      <Row flex={1} gap={space[3]}>
                        <Icon.Book />
                        <Box flex={1}>
                          <LargeLabel>
                            {t('fna:savingsGoals.education.tuitionFee')}
                          </LargeLabel>
                        </Box>
                      </Row>
                      <DecimalTextField
                        label={t('fna:amount.label')}
                        value={kidGoal.annualTuitionCosts}
                        onChange={onChangeAnnualTuitionCosts}
                        style={{ flex: 1 }}
                        max={MAX_AMOUNT_VALUE}
                      />
                    </Row>
                    <Row mt={space[5]} gap={space[3]}>
                      <Row flex={1} alignItems="center" gap={space[3]}>
                        <Icon.Coin2 />
                        <Box flex={1}>
                          <LargeLabel>
                            {t('fna:savingsGoals.education.livingCosts')}
                          </LargeLabel>
                        </Box>
                      </Row>
                      <DecimalTextField
                        label={t('fna:amount.label')}
                        value={kidGoal.annualLivingExpenses}
                        onChange={onChangeAnnualLivingExpenses}
                        style={{ flex: 1 }}
                        max={MAX_AMOUNT_VALUE}
                      />
                    </Row>
                    <Row mt={space[5]} gap={space[3]}>
                      <Row flex={1} alignItems="center" gap={space[3]}>
                        <GraphIcon />
                        <Box flex={1}>
                          <LargeLabel>
                            {t('fna:savingsGoals.education.inflationRate')}
                          </LargeLabel>
                        </Box>
                      </Row>
                      <DecimalTextField
                        label={t('fna:savingsGoals.education.inflationRate')}
                        value={kidGoal.inflationRate}
                        onChange={onChangeInflationRate}
                        style={{ flex: 1 }}
                        max={100}
                        precision={2}
                      />
                    </Row>
                    <Row mt={space[5]} gap={space[3]}>
                      <Row flex={1} alignItems="center" gap={space[3]}>
                        <Icon.Calendar />
                        <Box flex={1}>
                          <LargeLabel>
                            {t('fna:savingsGoals.education.yearsInSchool')}
                          </LargeLabel>
                        </Box>
                      </Row>
                      <DecimalTextField
                        label={t('fna:savingsGoals.education.years')}
                        value={kidGoal.yearsInCollege}
                        onChange={onChangeYearsInCollege}
                        style={{ flex: 1 }}
                        precision={0}
                        maxLength={2}
                      />
                    </Row>
                    <Box h={space[4]} />
                    <BottomSheetFooterSpace />
                  </Box>
                </KeyboardAwareBottomSheetScrollView>
              </BottomSheetModal>
            </BottomSheetModalProvider>
          </FooterContext.Provider>
        </Portal>
      )}
    </>
  );
};

interface FormFooterProps extends BottomSheetFooterProps {
  onDone: () => void;
}

const FormFooter = ({ onDone, ...props }: FormFooterProps) => {
  const targetAmount = useContext(FooterContext);
  const { space, colors } = useTheme();
  const { t } = useTranslation(['fna']);
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();

  const { animatedKeyboardState } = useBottomSheetInternal();
  const [footerHeight, setFooterHeight] = useState(0);
  const animatedStyle = useAnimatedStyle(
    () => ({
      transform: [
        {
          translateY:
            animatedKeyboardState.value === KEYBOARD_STATE.SHOWN
              ? footerHeight
              : 0,
        },
      ],
    }),
    [animatedKeyboardState.value, footerHeight],
  );

  return (
    <BottomSheetFooter {...props}>
      <Animated.View
        onLayout={e => setFooterHeight(e.nativeEvent.layout.height)}
        style={[
          animatedStyle,
          {
            paddingHorizontal: space[isNarrowScreen ? 3 : 4],
            paddingTop: space[4],
            paddingBottom: space[4] + bottomInset,
            backgroundColor: colors.background,
            borderTopWidth: 1,
            borderColor: colors.palette.fwdGrey[100],
            justifyContent: 'center',
          },
        ]}>
        <Row alignItems="center" mb={space[6]} gap={space[4]}>
          <LargeBody style={{ flexGrow: 1, flexShrink: 1 }}>
            {t('fna:savingsGoals.education.inflationRate.tooltip')}:
          </LargeBody>
          <Row flexGrow={1} alignItems="center" justifyContent="flex-end">
            <Box mr={space[1]}>
              <Tooltip
                icon={
                  <Icon.InfoCircle
                    size={24}
                    fill={colors.palette.fwdAlternativeOrange[100]}
                  />
                }
                placement="overlay"
                title={t(
                  'fna:savingsGoals.education.inflationRate.tooltip.title',
                )}
                content={
                  <Body>
                    {t(
                      'fna:savingsGoals.education.inflationRate.tooltip.content',
                    )}
                  </Body>
                }
              />
            </Box>

            <ExtraLargeBody fontWeight="bold" color={colors.primary}>
              {t('fna:currency')}
              {typeof targetAmount === 'number'
                ? ` ${formatCurrency(targetAmount, 2)}`
                : ''}
            </ExtraLargeBody>

            {typeof targetAmount !== 'number' && (
              <Box mt={space[3]}>
                <ExtraLargeBody fontWeight="bold" color={colors.primary}>
                  --
                </ExtraLargeBody>
              </Box>
            )}
          </Row>
        </Row>
        <Button
          text={t('fna:done')}
          disabled={typeof targetAmount !== 'number'}
          onPress={onDone}
          style={{ maxWidth: isWideScreen ? 400 : undefined }}
        />
      </Animated.View>
    </BottomSheetFooter>
  );
};

export default IBUniversityExpensesCalculatorPhone;
