import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Body,
  Box,
  LargeLabel,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import CalculatorIcon from 'features/fna/components/icons/CalculatorIcon';
import {
  booleanToYesNoValue,
  yesNoValueToBoolean,
} from 'features/fna/utils/helper/fnaUtils';
import {
  maxNameLength,
  nameSchema,
} from 'features/fna/utils/validation/fnaValidation';
import { TFuncKey } from 'i18next';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { calculateAge } from 'utils/helper/calculateAge';
import { dateFormatWithSlashUtil } from 'utils/helper/formatUtil';
import DatePickerCalendar from 'components/DatePickerCalendar';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import type { InternalKidFormProps } from '../KidForm';
import DecimalTextField from 'components/DecimalTextField';
import UniversityExpensesCalculator from '../../calculator/UniversityExpensesCalculator';
import {
  MAX_EDUCATION_DATE_OF_BIRTH,
  MIN_EDUCATION_DATE_OF_BIRTH,
} from 'features/fna/constants/educationConstants';
import { UNIVERSITY_AGE } from 'features/fna/constants/goalCalculation';

export default function PHKidFormTablet({
  index,
  formData: educationGoal,
  updateFormData,
}: InternalKidFormProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors, borderRadius } = useTheme();
  const [visibleCalculator, setVisibleCalculator] = useState(false);
  const [nameError, setNameError] = useState<string | undefined>(undefined);
  const kidGoal = educationGoal.goals[index];

  return (
    <Box>
      <Row mt={space[5]}>
        <Row flex={1.8} alignSelf="flex-start" alignItems="center">
          <FieldLabel>1.</FieldLabel>
          <Box w={space[3]} />
          <LargeLabel>
            {t('fna:savingsGoals.education.yourChildName')}
          </LargeLabel>
        </Row>
        <Box w={space[5]} />
        <TextField
          label={t('fna:name')}
          value={kidGoal.firstName}
          style={{ flex: 1 }}
          onChange={value => {
            updateFormData({
              ...kidGoal,
              firstName: value,
            });
          }}
          maxLength={maxNameLength}
          error={nameError}
          onBlur={() => {
            if (kidGoal.firstName.length === 0) return;
            try {
              nameSchema.validateSync(kidGoal.firstName);
            } catch (e) {
              setNameError(t((e as Error).message as TFuncKey<['fna']>));
            }
          }}
          onFocus={() => {
            setNameError(undefined);
          }}
        />
      </Row>
      <Row mt={space[3]} alignItems="center">
        <Box flex={1.8} alignSelf="flex-start">
          <Row>
            <FieldLabel>2.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.education.yourChildBirthDate')}
            </LargeLabel>
          </Row>
          {typeof kidGoal.childAge === 'number' && (
            <Box marginTop={space[1]} marginLeft={space[3]}>
              <Body color={colors.palette.fwdDarkGreen['50']}>
                {UNIVERSITY_AGE - (kidGoal.childAge ?? 0) <= 1
                  ? t('fna:savingsGoals.education.yearBeforeCollege', {
                      age: UNIVERSITY_AGE - (kidGoal.childAge ?? 0),
                    })
                  : t('fna:savingsGoals.education.yearsBeforeCollege', {
                      age: UNIVERSITY_AGE - (kidGoal.childAge ?? 0),
                    })}
              </Body>
            </Box>
          )}
        </Box>
        <Box w={space[5]} />
        <Box style={{ gap: space[1], flex: 1 }}>
          <DatePickerCalendar
            label={t('fna:savingsGoals.education.dateOfBirth')}
            hint={t('fna:mmddyyyy')}
            minDate={MIN_EDUCATION_DATE_OF_BIRTH}
            maxDate={MAX_EDUCATION_DATE_OF_BIRTH}
            value={kidGoal.dateOfBirth || undefined}
            formatDate={dateFormatWithSlashUtil}
            onChange={date => {
              const age = calculateAge(date);
              updateFormData(
                {
                  ...kidGoal,
                  childAge: age,
                  dateOfBirth: date,
                  targetAmount: null,
                  coverageAmount: null,
                  gapAmount: null,
                  collegeType: null,
                  yearsInCollege: null,
                  annualTuitionCosts: null,
                  hasSavings: null,
                },
                true,
              );
            }}
          />
        </Box>
      </Row>
      <Row mt={space[3]}>
        <Row flex={1.8} alignSelf="stretch" alignItems="center">
          <Row alignSelf="flex-start" flex={1}>
            <FieldLabel>3.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.education.totalTuitionFundNeeded')}
            </LargeLabel>
          </Row>
          <TouchableOpacity
            style={{
              alignSelf: 'center',
              borderWidth: 2,
              borderRadius: borderRadius.full,
              borderColor: colors.palette.fwdOrange[100],
              padding: space[2],
            }}
            onPress={() => setVisibleCalculator(true)}>
            <CalculatorIcon />
          </TouchableOpacity>
        </Row>
        <Box w={space[5]} />
        <DecimalTextField
          label={t('fna:amount.label')}
          style={{ flex: 1 }}
          value={kidGoal.targetAmount}
          onChange={targetAmount => {
            updateFormData(
              {
                ...kidGoal,
                targetAmount: targetAmount,
              },
              true,
            );
          }}
          max={MAX_AMOUNT_VALUE}
        />
      </Row>
      <Row mt={space[5]}>
        <Row flex={1.8} alignItems="center">
          <FieldLabel>4.</FieldLabel>
          <Box w={space[3]} />
          <LargeLabel style={{ flex: 1 }}>
            {t('fna:savingsGoals.education.haveYouStartedSavingForYourChild')}
          </LargeLabel>
        </Row>
        <Box w={space[5]} />
        <Row flex={1}>
          <RadioButtonGroup
            value={booleanToYesNoValue(kidGoal.hasSavings)}
            onChange={value => {
              const hasSavings = yesNoValueToBoolean(value);
              if (hasSavings !== kidGoal.hasSavings) {
                updateFormData(
                  {
                    ...kidGoal,
                    hasSavings,
                    coverageAmount: null,
                  },
                  true,
                );
              }
            }}>
            <RadioButton style={{ flex: 1 }} label={t('fna:yes')} value="yes" />
            <RadioButton style={{ flex: 1 }} label={t('fna:no')} value="no" />
          </RadioButtonGroup>
        </Row>
      </Row>
      {kidGoal.hasSavings && (
        <Row mt={space[7]}>
          <Row flex={1.8} alignSelf="flex-start" alignItems="center">
            <FieldLabel>4.1.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.education.howMuchHaveYouSaved')}
            </LargeLabel>
          </Row>
          <Box w={space[5]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            value={kidGoal.coverageAmount}
            onChange={coverageAmount => {
              updateFormData(
                {
                  ...kidGoal,
                  coverageAmount: coverageAmount,
                },
                true,
              );
            }}
            style={{ flex: 1 }}
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
      )}

      <UniversityExpensesCalculator
        kidGoal={kidGoal}
        onDismiss={() => setVisibleCalculator(false)}
        onDone={data => {
          updateFormData(
            {
              ...kidGoal,
              annualTuitionCosts: data.annualTuitionCosts,
              collegeType: data.collegeType,
              targetAmount: data.targetAmount,
              yearsInCollege: data.yearsInCollege,
            },
            true,
          );
        }}
        visible={visibleCalculator}
      />
    </Box>
  );
}

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
