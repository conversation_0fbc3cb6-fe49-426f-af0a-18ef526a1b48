import { GoalFormProps } from 'features/fna/types/goal';
import { FnaState, KidGoal } from 'features/fna/utils/store/fnaStore';
import React from 'react';
import { country } from 'utils/context';
import IBKidForm from './ib/IBKidForm';
import PHKidForm from './ph/PHKidForm';

export interface InternalKidFormProps
  extends GoalFormProps<FnaState['educationGoal']> {
  index: number;
  updateFormData: (goal: KidGoal, recalculate?: boolean) => void;
}

interface KidFormProps extends GoalFormProps<FnaState['educationGoal']> {
  index: number;
  calculateTotalFunds: (newGoal: FnaState['educationGoal']['goals']) => void;
}

export default function KidForm({
  index,
  formData: educationGoal,
  setFormData: updateEducationGoal,
  calculateTotalFunds,
}: KidFormProps) {
  const updateFormData = (goal: KidGoal, recalculate = false) => {
    const newKidGoals: FnaState['educationGoal']['goals'] = [
      ...educationGoal.goals,
    ];
    newKidGoals[index] = goal;
    if (recalculate) {
      let gapAmount = undefined;
      if (
        typeof newKidGoals[index].targetAmount === 'number' &&
        newKidGoals[index].hasSavings &&
        newKidGoals[index].coverageAmount
      ) {
        const targetAmount = newKidGoals[index].targetAmount ?? 0;
        const coverageAmount = newKidGoals[index].coverageAmount ?? 0;
        gapAmount = targetAmount - coverageAmount;
      } else {
        gapAmount = newKidGoals[index].targetAmount;
      }
      newKidGoals[index].gapAmount = gapAmount;
      calculateTotalFunds(newKidGoals);
    } else {
      updateEducationGoal({
        goals: newKidGoals,
      });
    }
  };

  return (
    <>
      {Form && (
        <Form
          index={index}
          formData={educationGoal}
          setFormData={updateEducationGoal}
          updateFormData={updateFormData}
        />
      )}
    </>
  );
}

const Form = {
  ib: IBKidForm,
  ph: PHKidForm,
  my: IBKidForm,
  id: IBKidForm,
}[country];
