import React from 'react';
import { Typography } from 'cube-ui-components';
import styled from '@emotion/native';
import { KidsTabProps } from './KidsTab';

export default function KidsTab({
  disabled,
  text,
  onPress,
  isFocused,
}: KidsTabProps) {
  return (
    <TagContainer disabled={disabled} onPress={onPress} isFocused={isFocused}>
      <Typography.LargeBody fontWeight={'medium'}>{text}</Typography.LargeBody>
    </TagContainer>
  );
}

const TagContainer = styled.TouchableOpacity<KidsTabProps>(
  ({ disabled, isFocused, theme }) => ({
    opacity: disabled ? 0.5 : 1,
    minHeight: theme.sizes[9],
    backgroundColor: isFocused
      ? theme.colors.primaryVariant3
      : theme.colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.space[1],
    paddingHorizontal: theme.space[4],
    borderWidth: isFocused ? 2 : 1,
    borderColor: isFocused
      ? theme.colors.primary
      : theme.colors.palette.fwdGrey[100],
    borderRadius: theme.borderRadius.full,
    marginRight: theme.sizes[2],
  }),
);
