import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { getOptionListValue } from 'constants/optionList';
import {
  Body,
  Box,
  Button,
  Center,
  H6,
  Icon,
  LargeBody,
  LargeLabel,
  PictogramIcon,
  RadioButton,
  Row,
  TextField,
} from 'cube-ui-components';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import {
  MysFlagIcon,
  UsaFlagIcon,
  GbrFlagIcon,
  SgpFlagIcon,
  JpnFlagIcon,
  KorFlagIcon,
  ChnFlagIcon,
  TwnFlagIcon,
  IndFlagIcon,
  IdnFlagIcon,
  AusFlagIcon,
  NzlFlagIcon,
} from 'features/fna/components/icons/FlagIcons';
import { useGetOptionList } from 'hooks/useGetOptionList';
import useToggle from 'hooks/useToggle';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import {
  FlatList,
  ScrollView,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { CountryUni } from 'types/optionList';
import { formatCurrency } from 'utils';
import Svg, { Path } from 'react-native-svg';
import MarkdownTextMore from 'components/MarkdownTextMore';
import { InternalUniversityLocationPickerProps } from './UniversityLocationPicker';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import { useBottomSheet } from 'features/fna/hooks/useBottomSheet';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import UniversityEmptyIcon from './UniversityEmptyIcon';
import { COUNTRY_FLAGS } from './countryFlags';
import UnTickIcon from 'features/fna/components/icons/UnTickIcon';
import FnaConfirmFooter from 'features/fna/components/common/FnaConfirmFooter';
import useLatest from 'hooks/useLatest';
import { Portal } from '@gorhom/portal';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';

export default function UniversityLocationPickerPhone({
  visible,
  filteredList,
  searchInputRef,
  query,
  setQuery,
  universityLocation,
  setUniversityLocation,
  onFocusSearchInput,
  onBlurSearchInput,
  onDone,
  onDismiss,
}: InternalUniversityLocationPickerProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors } = useTheme();
  const { height } = useSafeAreaFrame();
  const { top } = useSafeAreaInsets();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useMemo(() => [height - top], [height, top]);

  const onDoneRef = useLatest(onDone);
  const isActionDisabled = !universityLocation;
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FnaConfirmFooter
          disabled={isActionDisabled}
          onPress={() => {
            bottomSheetProps.bottomSheetRef.current?.close();
            onDoneRef.current();
          }}
          buttonTitle={t('fna:done')}
          {...props}
        />
      );
    },
    [isActionDisabled, t, bottomSheetProps.bottomSheetRef, onDoneRef],
  );

  return (
    <>
      {visible && (
        <Portal>
          <BottomSheetModalProvider>
            <BottomSheetModal
              snapPoints={snapPoints}
              onDismiss={onDismiss}
              index={0}
              {...bottomSheetProps}
              style={{ padding: 0 }}
              footerComponent={renderFooter}>
              <Row
                alignItems="center"
                px={space[isNarrowScreen ? 2 : 3]}
                gap={space[1]}>
                <PictogramIcon.Building size={40} />
                <LargeLabel fontWeight="bold" color={colors.primary}>
                  {t('fna:savingsGoals.education.universityLocation.title')}
                </LargeLabel>
              </Row>
              <BottomSheetKeyboardAwareScrollView bottomOffset={space[8]}>
                <Box mb={space[3]} px={space[isNarrowScreen ? 3 : 4]}>
                  <MarkdownTextMore
                    fullText={t(
                      'fna:savingsGoals.education.disclaimer.expanded',
                    )}
                    shortText={t(
                      'fna:savingsGoals.education.disclaimer.collapsed.phone',
                    )}
                    style={{ marginVertical: space[4] }}
                  />
                  <TextField
                    ref={searchInputRef}
                    label={t(
                      'fna:savingsGoals.education.universityLocation.search',
                    )}
                    left={<Icon.Search size={24} fill={colors.placeholder} />}
                    right={
                      <TouchableOpacity
                        onPress={() => {
                          setQuery('');
                          searchInputRef.current?.clear();
                        }}
                        hitSlop={ICON_HIT_SLOP}>
                        <Icon.CloseCircle size={20} fill={colors.secondary} />
                      </TouchableOpacity>
                    }
                    autoCorrect={false}
                    onFocus={onFocusSearchInput}
                    onBlur={onBlurSearchInput}
                    returnKeyType="done"
                    onSubmitEditing={event => setQuery(event.nativeEvent.text)}
                  />
                </Box>
                <UniversityLocationList
                  data={filteredList}
                  keyExtractor={getOptionListValue}
                  ItemSeparatorComponent={Separator}
                  renderItem={info => (
                    <UniversityLocation
                      item={info.item}
                      selected={info.item.value === universityLocation}
                      onPress={() => setUniversityLocation(info.item.value)}
                    />
                  )}
                  ListEmptyComponent={EmptyResult}
                />
                <Box h={space[4]} />
              </BottomSheetKeyboardAwareScrollView>
              <BottomSheetFooterSpace />
            </BottomSheetModal>
          </BottomSheetModalProvider>
        </Portal>
      )}
    </>
  );
}

const UniversityLocationList = styled(FlatList)(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
  };
}) as unknown as typeof FlatList;

const UniversityLocation = ({
  item,
  selected,
  onPress,
}: {
  item: CountryUni;
  selected?: boolean;
  onPress?: () => void;
}) => {
  const { t } = useTranslation(['fna']);
  const { space, colors } = useTheme();

  return (
    <UniversityLocationPressable onPress={onPress}>
      <Row alignItems="center">
        <Row gap={space[1]} alignItems="center" flex={1}>
          {React.createElement(
            COUNTRY_FLAGS[item.value as keyof typeof COUNTRY_FLAGS],
          )}
          <LargeLabel fontWeight="bold" style={{ flex: 1 }}>
            {item.label}
          </LargeLabel>
        </Row>
        {selected ? (
          <Icon.TickCircle size={24} fill={colors.palette.alertGreen} />
        ) : (
          <UnTickIcon size={24} />
        )}
      </Row>
      <Body color={colors.palette.fwdGreyDarkest}>
        {t('fna:savingsGoals.education.avgTuitionFee')}
      </Body>
      <LargeBody style={{ marginTop: space[1] }}>{`${formatCurrency(
        item.avgTtFee,
      )} (${formatCurrency(item.avgTtFeeMin)} - ${formatCurrency(
        item.avgTtFeeMax,
      )})`}</LargeBody>
      <Box h={1} backgroundColor={'rgba(51, 51, 51, 0.3)'} my={space[2]} />
      <Body color={colors.palette.fwdGreyDarkest}>
        {t('fna:savingsGoals.education.avgLivingExpenses')}
      </Body>
      <LargeBody style={{ marginTop: space[1] }}>{`${formatCurrency(
        item.avgLivingExp,
      )} (${formatCurrency(item.avgLivingExpMin)} - ${formatCurrency(
        item.avgLivingExpMax,
      )})`}</LargeBody>
    </UniversityLocationPressable>
  );
};

const Separator = styled.View(({ theme }) => ({
  height: theme.space[3],
}));

const UniversityLocationPressable = styled.TouchableOpacity(({ theme }) => ({
  padding: theme.space[3],
  borderRadius: theme.borderRadius.large,
  borderWidth: 1,
  backgroundColor: theme.colors.palette.fwdGrey[20],
  borderColor: theme.colors.palette.fwdGrey[100],
}));

const EmptyResult = () => {
  const { t } = useTranslation(['fna']);
  const { space, colors } = useTheme();
  return (
    <Center mt={space[6]}>
      <UniversityEmptyIcon />
      <Box mt={space[4]}>
        <LargeBody style={{ textAlign: 'center' }} color={colors.placeholder}>
          {t('fna:savingsGoals.education.search.msg1')}
        </LargeBody>
        <LargeBody style={{ textAlign: 'center' }} color={colors.placeholder}>
          {t('fna:savingsGoals.education.search.msg2')}
        </LargeBody>
      </Box>
    </Center>
  );
};
