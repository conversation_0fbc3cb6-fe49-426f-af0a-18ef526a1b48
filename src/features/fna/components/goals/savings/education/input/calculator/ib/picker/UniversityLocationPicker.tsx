import { useTheme } from '@emotion/react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { useGetOptionList } from 'hooks/useGetOptionList';
import useToggle from 'hooks/useToggle';
import React, { RefObject, useEffect, useMemo, useRef, useState } from 'react';
import { TextInput } from 'react-native';
import { CountryUni } from 'types/optionList';
import UniversityLocationPickerPhone from './UniversityLocationPicker.phone';
import UniversityLocationPickerTablet from './UniversityLocationPicker.tablet';

interface Props {
  visible: boolean;
  universityLocation: string;
  onChangeUniversityLocation: (
    value: string,
    avgAnnualTuitionFee: number,
    avgAnnualLivingCosts: number,
  ) => void;
  onDismiss: () => void;
}

export interface InternalUniversityLocationPickerProps {
  visible: boolean;
  filteredList: CountryUni<string>[];
  searchColor: string;
  searchInputRef: RefObject<TextInput>;
  query: string;
  setQuery: (query: string) => void;
  universityLocation: string;
  setUniversityLocation: (value: string) => void;
  isFocusingSearchInput: boolean;
  onFocusSearchInput: () => void;
  onBlurSearchInput: () => void;
  onDone: () => void;
  onDismiss: () => void;
}

export default function UniversityLocationPicker({
  visible,
  universityLocation: universityLocationProp,
  onChangeUniversityLocation,
  onDismiss,
}: Props) {
  const { colors } = useTheme();
  const [query, setQuery] = useState('');
  const [universityLocation, setUniversityLocation] = useState(
    universityLocationProp,
  );
  const [isFocusing, setFocus, setBlur] = useToggle();

  useEffect(() => {
    if (visible) {
      setUniversityLocation(universityLocationProp);
    }
  }, [universityLocationProp, visible]);

  const { data: optionList } = useGetOptionList();

  const filteredList = useMemo(() => {
    if (query) {
      return (
        optionList?.COUNTRY_UNI?.options?.filter(o =>
          o.label.trim().toLowerCase().includes(query.trim().toLowerCase()),
        ) || []
      );
    }
    return optionList?.COUNTRY_UNI?.options || [];
  }, [optionList?.COUNTRY_UNI?.options, query]);

  const searchColor = !isFocusing && query ? colors.secondary : colors.primary;
  const searchInputRef = useRef<TextInput>(null);

  const onDone = () => {
    const selectedItem = optionList?.COUNTRY_UNI?.options?.find(
      o => o.value === universityLocation,
    );
    if (selectedItem) {
      onChangeUniversityLocation(
        universityLocation,
        selectedItem.avgTtFee,
        selectedItem.avgLivingExp,
      );
      onDismiss();
    }
  };

  return (
    <DeviceBasedRendering
      tablet={
        <UniversityLocationPickerTablet
          visible={visible}
          filteredList={filteredList}
          searchColor={searchColor}
          searchInputRef={searchInputRef}
          query={query}
          setQuery={setQuery}
          universityLocation={universityLocation}
          setUniversityLocation={setUniversityLocation}
          isFocusingSearchInput={isFocusing}
          onFocusSearchInput={setFocus}
          onBlurSearchInput={setBlur}
          onDone={onDone}
          onDismiss={onDismiss}
        />
      }
      phone={
        <UniversityLocationPickerPhone
          visible={visible}
          filteredList={filteredList}
          searchColor={searchColor}
          searchInputRef={searchInputRef}
          query={query}
          setQuery={setQuery}
          universityLocation={universityLocation}
          setUniversityLocation={setUniversityLocation}
          isFocusingSearchInput={isFocusing}
          onFocusSearchInput={setFocus}
          onBlurSearchInput={setBlur}
          onDone={onDone}
          onDismiss={onDismiss}
        />
      }
    />
  );
}
