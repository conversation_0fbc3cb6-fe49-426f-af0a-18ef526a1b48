import DeviceBasedRendering from 'components/DeviceBasedRendering';
import type { InternalUniversityExpensesCalculatorProps } from '../UniversityExpensesCalculator';
import PHUniversityExpensesCalculatorTablet from './PHUniversityExpensesCalculator.tablet';
import PHUniversityExpensesCalculatorPhone from './PHUniversityExpensesCalculator.phone';

export default function PHUniversityExpensesCalculator(
  props: InternalUniversityExpensesCalculatorProps,
) {
  return (
    <DeviceBasedRendering
      tablet={<PHUniversityExpensesCalculatorTablet {...props} />}
      phone={<PHUniversityExpensesCalculatorPhone {...props} />}
    />
  );
}
