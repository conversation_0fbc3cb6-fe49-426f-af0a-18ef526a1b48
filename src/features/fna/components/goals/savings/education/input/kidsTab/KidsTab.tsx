import DeviceBasedRendering from 'components/DeviceBasedRendering';
import React from 'react';
import KidsTabPhone from './KidsTab.phone';
import KidsTabTablet from './KidsTab.tablet';

export interface KidsTabProps {
  disabled?: boolean;
  text?: string;
  onPress?: () => void;
  isFocused?: boolean;
  icon?: React.ReactNode;
  onRemove?: () => void;
  isRemovable?: boolean;
}

export default function KidsTab({
  disabled,
  text,
  onPress,
  isFocused,
  icon,
  onRemove,
  isRemovable,
}: KidsTabProps) {
  return (
    <DeviceBasedRendering
      tablet={
        <KidsTabTablet
          disabled={disabled}
          text={text}
          onPress={onPress}
          icon={icon}
          onRemove={onRemove}
          isRemovable={isRemovable}
        />
      }
      phone={
        <KidsTabPhone
          disabled={disabled}
          text={text}
          onPress={onPress}
          isFocused={isFocused}
        />
      }
    />
  );
}
