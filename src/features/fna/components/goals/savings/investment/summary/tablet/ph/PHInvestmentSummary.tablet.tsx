import { useTheme } from '@emotion/react';
import { Body, Box, Icon, Row } from 'cube-ui-components';
import {
  currencyKFormat,
  getDisplayYear,
} from 'features/fna/utils/helper/fnaUtils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { InvestmentSummaryProps } from '../../InvestmentSummary';

export default function PHInvestmentSummaryTablet({
  formData: investmentGoal,
  summary,
}: Pick<InvestmentSummaryProps, 'formData' | 'summary'>) {
  const { t } = useTranslation(['common', 'fna']);
  const { colors, sizes } = useTheme();
  return (
    <Row alignItems="center" justifyContent="space-around">
      <Box alignItems="center">
        <Body color={colors.secondaryVariant}>
          {t('common:withCurrency', {
            amount:
              typeof summary.coverage.value === 'number'
                ? currencyKFormat(summary.coverage.value)
                : '--',
          })}
        </Body>
        <Body color={colors.secondaryVariant}>{t('fna:current')}</Body>
      </Box>
      <Icon.ChevronRight size={sizes[4]} fill={colors.palette.fwdGreyDark} />
      <Box alignItems="center">
        <Body fontWeight="bold">
          {getDisplayYear(investmentGoal.yearsToAchieve, t)}
        </Body>
        <Body>{t('fna:savingsGoals.investment.toReachYouGoal')}</Body>
      </Box>
      <Icon.ChevronRight size={sizes[4]} fill={colors.palette.fwdGreyDark} />
      <Box alignItems="center">
        <Body color={colors.secondaryVariant}>
          {t('common:withCurrency', {
            amount:
              typeof summary.target.value === 'number'
                ? currencyKFormat(summary.target.value)
                : '--',
          })}
        </Body>
        <Body color={colors.secondaryVariant}>{t('fna:target')}</Body>
      </Box>
    </Row>
  );
}
