import { useTheme } from '@emotion/react';
import { Body, Box, Icon, Row } from 'cube-ui-components';
import { currencyKFormat } from 'features/fna/utils/helper/fnaUtils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { InvestmentSummaryProps } from '../../InvestmentSummary';
import { InvestmentDurationKey } from 'features/fna/utils/store/fnaStore';

export default function IBInvestmentSummaryTablet({
  formData: investmentGoal,
  summary,
}: Pick<InvestmentSummaryProps, 'formData' | 'summary'>) {
  const { t } = useTranslation(['common', 'fna']);
  const { colors, sizes } = useTheme();
  return (
    <Row alignItems="center" justifyContent="space-around">
      <Box alignItems="center">
        <Body color={colors.secondaryVariant}>
          {t('common:withCurrency', {
            amount:
              typeof summary.coverage.value === 'number'
                ? currencyKFormat(summary.coverage.value)
                : '--',
          })}
        </Body>
        <Body color={colors.secondaryVariant}>
          {t('fna:savingsGoals.investment.invest')}
        </Body>
      </Box>
      <Icon.ChevronRight size={sizes[4]} fill={colors.palette.fwdGreyDark} />
      <Box alignItems="center">
        <Body fontWeight="bold">
          {investmentGoal.investmentDuration ===
          InvestmentDurationKey.LESS_THAN_FIVE_YEARS
            ? t('fna:savingsGoals.investment.LessThan5Years')
            : t('fna:savingsGoals.investment.5YearsAndAbove')}
        </Body>
      </Box>
      <Icon.ChevronRight size={sizes[4]} fill={colors.palette.fwdGreyDark} />
      <Box alignItems="center">
        <Body color={colors.secondaryVariant}>
          {t('common:withCurrency', {
            amount:
              typeof summary.gap.value === 'number'
                ? currencyKFormat(summary.gap.value)
                : '--',
          })}
        </Body>
        <Body color={colors.secondaryVariant}>
          {t('fna:savingsGoals.investment.invest')}
        </Body>
      </Box>
    </Row>
  );
}
