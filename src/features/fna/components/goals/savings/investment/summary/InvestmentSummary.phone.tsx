import { useTheme } from '@emotion/react';
import { Body, Box, Icon, Row } from 'cube-ui-components';
import SavingTimeLifeCurrent from 'features/fna/components/illustrations/SavingTimeLifeCurrent';
import SavingTimeLifeNext from 'features/fna/components/illustrations/SavingTimeLifeNext';
import {
  currencyKFormat,
  getDisplayYear,
} from 'features/fna/utils/helper/fnaUtils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import GoalSummary from '../../../goalSummary/GoalSummary';
import { InvestmentSummaryProps } from './InvestmentSummary';

export default function InvestmentSummary({
  formData: investmentGoal,
  isGroupCompleted,
  onNextTab,
  onDone,
  summary,
  isCompleted,
}: InvestmentSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  const { space, colors, sizes } = useTheme();

  return (
    <GoalSummary
      title={t('fna:savingsGoals.investment.totalFunds')}
      summary={summary}
      isCompleted={isCompleted}
      isGroupCompleted={isGroupCompleted}
      onNext={onNextTab}
      onDone={onDone}>
      <Box flex={1} px={space[2]} pt={space[2]} justifyContent="space-between">
        <Row alignItems="center" justifyContent="space-around">
          <Box alignItems="center">
            <Body color={colors.secondaryVariant}>
              {t('common:withCurrency', {
                amount:
                  typeof summary.coverage.value === 'number'
                    ? currencyKFormat(summary.coverage.value)
                    : '--',
              })}
            </Body>
            <Body color={colors.secondaryVariant}>{t('fna:current')}</Body>
          </Box>
          <Icon.ChevronRight
            size={sizes[4]}
            fill={colors.palette.fwdGreyDark}
          />
          <Box alignItems="center">
            <Body fontWeight="bold">
              {getDisplayYear(investmentGoal.yearsToAchieve, t)}
            </Body>
            <Body>{t('fna:savingsGoals.investment.toReachYouGoal')}</Body>
          </Box>
          <Icon.ChevronRight
            size={sizes[4]}
            fill={colors.palette.fwdGreyDark}
          />
          <Box alignItems="center">
            <Body color={colors.secondaryVariant}>
              {t('common:withCurrency', {
                amount:
                  typeof summary.target.value === 'number'
                    ? currencyKFormat(summary.target.value)
                    : '--',
              })}
            </Body>
            <Body color={colors.secondaryVariant}>{t('fna:target')}</Body>
          </Box>
        </Row>
        <Row alignItems="flex-end" justifyContent="space-between">
          <SavingTimeLifeCurrent />
          <SavingTimeLifeNext />
        </Row>
      </Box>
    </GoalSummary>
  );
}
