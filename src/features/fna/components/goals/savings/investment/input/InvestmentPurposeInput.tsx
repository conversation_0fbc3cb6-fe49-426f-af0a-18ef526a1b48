import { useTheme } from '@emotion/react';
import {
  BottomSheetFooter,
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { BottomSheetScrollViewMethods } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetScrollable/types';
import { Portal } from '@gorhom/portal';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import {
  Box,
  Button,
  Icon,
  RadioButton,
  TextField,
  TextFieldRef,
  Typography,
  RadioButtonGroup,
  H6,
} from 'cube-ui-components';
import { useBottomSheet } from 'features/fna/hooks/useBottomSheet';
import { FnaState } from 'features/fna/utils/store/fnaStore';
import useToggle from 'hooks/useToggle';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Keyboard,
  LayoutChangeEvent,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
// @ts-expect-error no export member
import { listenToKeyboardEvents } from 'react-native-keyboard-aware-scroll-view';
import useLatest from 'hooks/useLatest';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import styled from '@emotion/native';
import DialogTablet from 'components/Dialog.tablet';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { SharedValue } from 'react-native-reanimated';

const KeyboardAwareBottomSheetScrollView = listenToKeyboardEvents(
  BottomSheetScrollView,
);

export const InvestmentPurposeInput = ({
  investmentGoal,
  updateInvestmentGoal,
}: {
  investmentGoal: FnaState['investmentGoal'];
  updateInvestmentGoal: (data: Partial<FnaState['investmentGoal']>) => void;
}) => {
  const { t } = useTranslation(['fna']);
  const [visible, show, hide] = useToggle();

  const investmentPurposeList = useMemo(
    () => [
      t('fna:savingsGoals.investment.homeOwnership'),
      t('fna:savingsGoals.investment.carOwnership'),
      t('fna:savingsGoals.investment.businessVenture'),
      t('fna:savingsGoals.investment.travelExpense'),
    ],
    [t],
  );
  const [selectedOption, setSelectedOption] = useState(investmentGoal.purpose);
  const selectedPurpose = investmentPurposeList.find(i => i === selectedOption);
  const isOtherOptionSelected = Boolean(
    selectedOption !== null && !selectedPurpose,
  );
  const currentPurpose = investmentPurposeList.find(
    i => i === investmentGoal.purpose,
  );

  const onDone = useCallback(
    (option?: string | null) => {
      if (option) {
        updateInvestmentGoal({ purpose: option });
      } else if (selectedOption) {
        updateInvestmentGoal({ purpose: selectedOption });
      }
    },
    [selectedOption, updateInvestmentGoal],
  );

  return (
    <>
      <TextField
        style={{ flex: 1 }}
        inputStyle={
          Platform.OS === 'android' ? { padding: 0, height: '100%' } : {}
        }
        label={t('fna:savingsGoals.investment.saveFor')}
        value={currentPurpose || investmentGoal.purpose || ''}
        onFocus={() => {
          setSelectedOption(investmentGoal.purpose);
          show();
          Keyboard.dismiss();
        }}
        onBlur={Keyboard.dismiss}
        right={
          <TouchableOpacity hitSlop={ICON_HIT_SLOP} onPress={show}>
            <Icon.Dropdown />
          </TouchableOpacity>
        }
        multiline
        autoExpand
        showSoftInputOnFocus={false}
      />
      <DeviceBasedRendering
        phone={
          visible && (
            <InvestmentPurposeBottomSheet
              investmentPurposeList={investmentPurposeList}
              selectedOption={selectedOption}
              isOtherOptionSelected={isOtherOptionSelected}
              hide={hide}
              currentOption={investmentGoal.purpose}
              setSelectedOption={setSelectedOption}
              onDone={onDone}
            />
          )
        }
        tablet={
          <InvestmentPurposeModal
            visible={visible}
            investmentPurposeList={investmentPurposeList}
            selectedOption={selectedOption}
            isOtherOptionSelected={isOtherOptionSelected}
            hide={hide}
            currentOption={investmentGoal.purpose}
            setSelectedOption={setSelectedOption}
            onDone={onDone}
          />
        }
      />
    </>
  );
};

interface PopupProps {
  visible?: boolean;
  investmentPurposeList: string[];
  selectedOption: string | null;
  isOtherOptionSelected: boolean;
  hide: () => void;
  onDone: (option?: string | null) => void;
  currentOption: string | null;
  setSelectedOption: (value: string | null) => void;
}

const InvestmentPurposeBottomSheet = ({
  investmentPurposeList,
  selectedOption,
  isOtherOptionSelected,
  hide,
  onDone,
  currentOption,
  setSelectedOption,
}: PopupProps) => {
  const { t } = useTranslation(['fna']);
  const theme = useTheme();
  const { bottom } = useSafeAreaInsets();
  const bottomSheetProps = useBottomSheet();
  const scrollViewRef = useRef<BottomSheetScrollViewMethods>(null);
  const inputRef = useRef<TextFieldRef>(null);

  const onDismiss = useCallback(() => {
    hide();
    setSelectedOption(currentOption);
  }, [currentOption, hide, setSelectedOption]);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const contentHeight = useRef(0);
  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  const disabled = selectedOption === null || selectedOption === '';
  const onDoneRef = useLatest(onDone);
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <BottomSheetFooter {...props}>
          <Box
            backgroundColor={theme.colors.background}
            padding={theme.space[4]}
            paddingBottom={theme.space[4] + bottom}
            borderTop={1}
            borderTopColor={theme.colors.palette.fwdGrey[100]}>
            <Button
              text={t('fna:done')}
              disabled={disabled}
              onPress={() => {
                onDoneRef.current();
                bottomSheetProps.bottomSheetRef.current?.close();
              }}
            />
          </Box>
        </BottomSheetFooter>
      );
    },
    [
      theme.colors.background,
      theme.colors.palette.fwdGrey,
      theme.space,
      bottom,
      t,
      disabled,
      onDoneRef,
      bottomSheetProps.bottomSheetRef,
    ],
  );

  const keyboardShown = useKeyboardShown();
  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          {...bottomSheetProps}
          onDismiss={onDismiss}
          style={{ padding: 0 }}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          footerComponent={renderFooter}>
          <KeyboardAwareBottomSheetScrollView
            keyboardDismissMode="interactive"
            enableOnAndroid={true}
            extraScrollHeight={
              Platform.OS === 'android' ? theme.space[4] : theme.space[9]
            }
            keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
            style={{
              backgroundColor: theme.colors.background,
            }}
            onLayout={(e: LayoutChangeEvent) => {
              if (keyboardShown) {
                if (contentHeight.current <= e.nativeEvent.layout.height) {
                  handleContentLayout(e);
                }
              } else {
                handleContentLayout(e);
              }
              contentHeight.current = e.nativeEvent.layout.height;
            }}
            stickyHeaderIndices={[0]}>
            <Box
              mx={theme.space[isNarrowScreen ? 3 : 4]}
              py={theme.space[4]}
              backgroundColor={theme.colors.background}>
              <Typography.H6 fontWeight="bold">
                {t('fna:savingsGoals.investment.whatToSave')}
              </Typography.H6>
            </Box>
            <Box mx={theme.space[isNarrowScreen ? 3 : 4]}>
              {investmentPurposeList.map((option, index) => {
                return (
                  <Box
                    key={index}
                    pt={index === 0 ? theme.space[2] : theme.space[6]}
                    pb={theme.space[6]}
                    borderBottom={1}
                    borderBottomColor={theme.colors.palette.fwdGrey[100]}>
                    <RadioButton
                      value={option}
                      selected={option === selectedOption}
                      label={option}
                      onSelect={() => {
                        setSelectedOption(option);
                      }}
                    />
                  </Box>
                );
              })}
              <Box py={theme.space[6]}>
                <RadioButton
                  value={'OTHER'}
                  selected={isOtherOptionSelected}
                  label={t('fna:savingsGoals.investment.other')}
                  onSelect={() => {
                    setTimeout(() => {
                      scrollViewRef.current?.scrollToEnd();
                      inputRef.current?.focus();
                    }, 200);
                    setSelectedOption('');
                  }}
                />
                {isOtherOptionSelected && (
                  <Box
                    overflow="hidden"
                    h={theme.sizes[18]}
                    py={theme.space[4]}
                    pl={theme.space[8]}>
                    <TextField
                      ref={inputRef}
                      value={selectedOption ?? undefined}
                      onChange={value => {
                        setSelectedOption(value);
                      }}
                      label={t('fna:savingsGoals.investment.specificTarget')}
                    />
                  </Box>
                )}
              </Box>
            </Box>
            <BottomSheetFooterSpace />
          </KeyboardAwareBottomSheetScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
};

const InvestmentPurposeModal = ({
  visible,
  investmentPurposeList,
  selectedOption,
  isOtherOptionSelected,
  hide,
  onDone,
  setSelectedOption,
}: PopupProps) => {
  const { t } = useTranslation(['fna']);
  const { space, sizes } = useTheme();
  const inputRef = useRef<TextFieldRef>(null);

  const onDismiss = useCallback(() => {
    setSelectedOption(selectedOption);
    onDone(selectedOption);
    hide();
  }, [hide, onDone, selectedOption, setSelectedOption]);

  return (
    <DialogContainer dismissable onDismiss={onDismiss} visible={visible}>
      <Box p={space[6]} w={762}>
        <Title fontWeight="bold">
          {t('fna:savingsGoals.investment.whatToSave')}
        </Title>
        <RadioButtonGroup
          value={selectedOption}
          onChange={value => {
            setSelectedOption(value);
            onDone(value);
            hide();
          }}>
          {investmentPurposeList.map(item => (
            <Box key={item}>
              <Option label={item} value={item} />
              <Divider />
            </Box>
          ))}
        </RadioButtonGroup>
        <Option
          value={'OTHER'}
          selected={isOtherOptionSelected}
          label={t('fna:savingsGoals.investment.other')}
          onSelect={() => {
            setTimeout(() => {
              inputRef.current?.focus();
            }, 200);
            setSelectedOption('');
          }}
        />
        {isOtherOptionSelected && (
          <Box overflow="hidden" h={sizes[18]} py={space[4]} pl={space[8]}>
            <TextField
              ref={inputRef}
              value={selectedOption ?? undefined}
              onChange={value => {
                setSelectedOption(value);
              }}
              label={t('fna:savingsGoals.investment.specificTarget')}
            />
          </Box>
        )}
      </Box>
    </DialogContainer>
  );
};

const DialogContainer = styled(DialogTablet)(({ theme: { space } }) => ({
  marginHorizontal: space[20],
  marginVertical: space[14],
}));

const Title = styled(H6)(({ theme: { space } }) => ({
  marginBottom: space[4],
}));

const Option = styled(RadioButton)(() => ({
  marginVertical: 10,
}));

const Divider = styled.View(({ theme }) => ({
  marginVertical: theme.space[3],
  borderBottomWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
}));
