import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  Dropdown,
  H6,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { View } from 'react-native';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import DecimalTextField from 'components/DecimalTextField';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import InvestmentLogo from 'features/fna/components/illustrations/InvestmentLogo';
import { IDInvestmentFormProps } from './IDInvestmentForm';

export default function IDInvestmentForm({
  formData: investmentGoal,
  setFormData: updateInvestmentGoal,
  updateInitialInvestmentAmount,
  updateRegularInvestmentAmount,
  updateInvestmentDuration,
  updateMonthlyPayout,
  updatePayoutPeriod,
  investmentDurationList,
}: IDInvestmentFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Box py={space[4]} px={space[isNarrowScreen ? 3 : 4]}>
      <Row mb={space[10]} alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.investment.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <InvestmentLogo width={114} height={79} />
        </Box>
      </Row>

      <FormContainer
        enabled={Boolean(investmentGoal.enabled)}
        pointerEvents={investmentGoal.enabled ? 'auto' : 'none'}>
        <Row>
          <Row flex={1}>
            <LargeLabel>1.</LargeLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.investment.initialAmount')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            style={{ flex: 1 }}
            value={investmentGoal.initialInvestmentAmount}
            onChange={updateInitialInvestmentAmount}
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
        <Row mt={space[5]}>
          <Row flex={1}>
            <LargeLabel>2.</LargeLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.investment.regularAmount')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            style={{ flex: 1 }}
            value={investmentGoal.regularInvestmentAmount}
            onChange={updateRegularInvestmentAmount}
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
        <Row mt={space[7]}>
          <Row flex={1}>
            <LargeLabel>3.</LargeLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.investment.investmentDuration')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <Dropdown
            data={investmentDurationList}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            getDisplayedLabel={item => item.label}
            label={t('fna:savingsGoals.investment.duration')}
            modalTitle={t('fna:savingsGoals.investment.investmentDuration')}
            value={investmentGoal.investmentDuration}
            onChange={updateInvestmentDuration}
            style={{ flex: 1 }}
            multiline
            autoExpand
          />
        </Row>
        <Row mt={space[7]}>
          <Row flex={1}>
            <LargeLabel>4.</LargeLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.investment.ExpectedMonthlyPayout')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            style={{ flex: 1 }}
            value={investmentGoal.monthlyPayout}
            onChange={updateMonthlyPayout}
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
        <Row mt={space[5]}>
          <Row flex={1}>
            <LargeLabel>5.</LargeLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.investment.PayOutPeriod')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:savingsGoals.investment.years')}
            style={{ flex: 1 }}
            maxLength={2}
            precision={0}
            value={investmentGoal.payoutPeriod}
            onChange={updatePayoutPeriod}
          />
        </Row>
      </FormContainer>
      <Divider />
      <Box mb={space[10]} mt={space[4]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </Box>
  );
}

const Divider = styled(View)(({ theme }) => {
  return {
    marginTop: theme.space[6],
    marginBottom: theme.space[2],
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));
