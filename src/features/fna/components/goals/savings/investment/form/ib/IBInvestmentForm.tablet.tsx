import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  H6,
  LargeBody,
  LargeLabel,
  Picker,
  Row,
} from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { View } from 'react-native';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import DecimalTextField from 'components/DecimalTextField';
import { IBInvestmentFormProps } from './IBInvestmentForm';
import InvestmentLogo from 'features/fna/components/illustrations/InvestmentLogo';

export default function IBInvestmentForm({
  formData: investmentGoal,
  setFormData: updateInvestmentGoal,
  updateInitialInvestmentAmount,
  updateRegularInvestmentAmount,
  updateInvestmentDuration,
  updateMonthlyPayout,
  updatePayoutPeriod,
  investmentDurationList,
}: IBInvestmentFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();

  return (
    <>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.investment.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <InvestmentLogo />
        </Box>
      </Row>

      <Content>
        <FormContainer
          enabled={Boolean(investmentGoal.enabled)}
          pointerEvents={investmentGoal.enabled ? 'auto' : 'none'}>
          <Row>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>1.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.investment.initialAmount')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={investmentGoal.initialInvestmentAmount}
              onChange={updateInitialInvestmentAmount}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <Row mt={space[5]}>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>2.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.investment.regularAmount')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={investmentGoal.regularInvestmentAmount}
              onChange={updateRegularInvestmentAmount}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <Row mt={space[7]}>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>3.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.investment.investmentDuration')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <Picker
              value={investmentGoal.investmentDuration || ''}
              onChange={updateInvestmentDuration}
              type="chip"
              items={investmentDurationList}
              size="medium"
            />
          </Row>
          <Row mt={space[7]}>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>4.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.investment.ExpectedMonthlyPayout')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={investmentGoal.monthlyPayout}
              onChange={updateMonthlyPayout}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <Row mt={space[5]}>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>5.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.investment.PayOutPeriod')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:savingsGoals.investment.years')}
              style={{ flex: 1 }}
              maxLength={2}
              precision={0}
              value={investmentGoal.payoutPeriod}
              onChange={updatePayoutPeriod}
            />
          </Row>
        </FormContainer>
      </Content>
      <Box mt={space[4]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </>
  );
}

const Content = styled(View)(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
