import { useTheme } from '@emotion/react';
import {
  Box,
  H6,
  LargeLabel,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import { FnaState } from 'features/fna/utils/store/fnaStore';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { GoalFormProps } from 'features/fna/types/goal';
import { View } from 'react-native';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import DecimalTextField from 'components/DecimalTextField';
import { InvestmentPurposeInput } from '../../input/InvestmentPurposeInput';
import {
  booleanToYesNoValue,
  yesNoValueToBoolean,
} from 'features/fna/utils/helper/fnaUtils';
import InvestmentLogo from 'features/fna/components/illustrations/InvestmentLogo';

export default function PHInvestmentForm({
  formData: investmentGoal,
  setFormData: updateInvestmentGoal,
}: GoalFormProps<FnaState['investmentGoal']>) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();

  return (
    <Box>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.investment.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <InvestmentLogo />
        </Box>
      </Row>
      <Content>
        <Row>
          <Row flex={1.8} alignSelf="flex-start" alignItems="center">
            <FieldLabel>1.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.investment.whatToSave')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <InvestmentPurposeInput
            investmentGoal={investmentGoal}
            updateInvestmentGoal={updateInvestmentGoal}
          />
        </Row>
        <Row mt={space[3]}>
          <Row flex={1.8} alignSelf="flex-start" alignItems="center">
            <FieldLabel>2.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.investment.yearsToAchieve')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <TextField
            label={t('fna:savingsGoals.investment.years')}
            value={investmentGoal.yearsToAchieve ?? ''}
            maxLength={2}
            keyboardType="numeric"
            style={{ flex: 1 }}
            onChange={value => {
              const yearsToAchieve = Number(value);
              if (!isNaN(yearsToAchieve)) {
                updateInvestmentGoal({ yearsToAchieve });
              }
            }}
            error={
              investmentGoal?.yearsToAchieve &&
              (Number(investmentGoal?.yearsToAchieve) < 1 ||
                Number(investmentGoal?.yearsToAchieve) > 99)
                ? t('fna:savingsGoals.investment.invalid')
                : undefined
            }
          />
        </Row>
        <Row mt={space[5]}>
          <Row flex={1.8} alignSelf="flex-start" alignItems="center">
            <FieldLabel>3.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.investment.targetAmountToAchieve')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            style={{ flex: 1 }}
            value={investmentGoal.targetAmount}
            onChange={targetAmount => updateInvestmentGoal({ targetAmount })}
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
        <Row mt={space[5]}>
          <Row flex={1.8} alignSelf="flex-start" alignItems="center">
            <FieldLabel>4.</FieldLabel>
            <Box w={space[3]} />
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.investment.startedSaving')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <Row flex={1} justifyContent="space-between">
            <RadioButtonGroup
              value={booleanToYesNoValue(investmentGoal.hasSavings)}
              onChange={value => {
                const hasSavings = yesNoValueToBoolean(value);
                if (hasSavings !== investmentGoal.hasSavings) {
                  updateInvestmentGoal({
                    hasSavings,
                    coverageAmount: null,
                  });
                }
              }}>
              <RadioButton
                style={{ flex: 1 }}
                label={t('fna:savingsGoals.investment.yes')}
                value={'yes'}
              />
              <RadioButton
                style={{ flex: 1 }}
                label={t('fna:savingsGoals.investment.no')}
                value={'no'}
              />
            </RadioButtonGroup>
          </Row>
        </Row>
        {investmentGoal.hasSavings && (
          <Row mt={space[7]}>
            <Row flex={1.8} alignSelf="flex-start" alignItems="center">
              <FieldLabel>5.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.investment.amountSaving')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              value={investmentGoal.coverageAmount}
              onChange={savingsAmount =>
                updateInvestmentGoal({ coverageAmount: savingsAmount })
              }
              style={{ flex: 1 }}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
        )}
      </Content>
    </Box>
  );
}

const Content = styled(View)(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
