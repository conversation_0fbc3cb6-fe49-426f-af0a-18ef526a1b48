import {
  FnaState,
  initialFnaState,
  InvestmentDurationKey,
} from 'features/fna/utils/store/fnaStore';
import React, { useEffect, useMemo } from 'react';

import { GoalFormProps } from 'features/fna/types/goal';
import InvestmentFormPhone from './IBInvestmentForm.phone';
import InvestmentFormTablet from './IBInvestmentForm.tablet';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { useTranslation } from 'react-i18next';
import { countryModuleFnaConfig } from 'utils/config/module';

export type IBInvestmentFormProps = GoalFormProps<
  FnaState['investmentGoal']
> & {
  updateSkipThisGoal: (disabled: boolean) => void;
  updateInitialInvestmentAmount: (amount: number | null) => void;
  updateRegularInvestmentAmount: (amount: number | null) => void;
  updateInvestmentDuration: (amount: '' | InvestmentDurationKey | string) => void;
  updateMonthlyPayout: (amount: number | null) => void;
  updatePayoutPeriod: (amount: number | null) => void;
  investmentDurationList: {
    value: InvestmentDurationKey;
    label: string;
  }[];
};

export default function InvestmentForm({
  formData: investmentGoal,
  setFormData: updateInvestmentGoal,
}: GoalFormProps<FnaState['investmentGoal']>) {
  const { t } = useTranslation(['fna']);

  const investmentDurationList = useMemo(() => {
    return [
      {
        value: InvestmentDurationKey.LESS_THAN_FIVE_YEARS,
        label: t('fna:savingsGoals.investment.LessThan5Years'),
      },
      {
        value: InvestmentDurationKey.FIVE_YEARS_AND_ABOVE,
        label: t('fna:savingsGoals.investment.5YearsAndAbove'),
      },
    ];
  }, [t]);

  useEffect(() => {
    if (
      investmentGoal.enabled &&
      countryModuleFnaConfig.hasDefaultGoalValue
    ) {
      updateInvestmentGoal({
        initialInvestmentAmount:
          investmentGoal.initialInvestmentAmount === null
            ? 0
            : investmentGoal.initialInvestmentAmount,
        regularInvestmentAmount:
          investmentGoal.regularInvestmentAmount === null
            ? 0
            : investmentGoal.regularInvestmentAmount,
        investmentDuration:
          investmentGoal.investmentDuration === ''
            ? InvestmentDurationKey.LESS_THAN_FIVE_YEARS
            : investmentGoal.investmentDuration,
        yearsToAchieve:
          investmentGoal.yearsToAchieve === null
            ? 5
            : investmentGoal.yearsToAchieve,
        monthlyPayout:
          investmentGoal.monthlyPayout === null
            ? 0
            : investmentGoal.monthlyPayout,
        payoutPeriod:
          investmentGoal.payoutPeriod === null
            ? 0
            : investmentGoal.payoutPeriod,
      });
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const updateSkipThisGoal = (disabled: boolean) => {
    if (disabled) {
      updateInvestmentGoal({
        ...initialFnaState.investmentGoal,
        targetAmount: 0,
        yearsToAchieve: 0,
        gapAmount: 0,
        coverageAmount: 0,
        enabled: !disabled,
      });
    } else {
      updateInvestmentGoal({
        initialInvestmentAmount: 0,
        regularInvestmentAmount: 0,
        investmentDuration: InvestmentDurationKey.LESS_THAN_FIVE_YEARS,
        yearsToAchieve: 5,
        monthlyPayout: 0,
        payoutPeriod: 0,
        enabled: !disabled,
      });
    }
  };

  const updateInitialInvestmentAmount = (value: number | null) => {
    updateInvestmentGoal({ initialInvestmentAmount: value });
  };

  const updateRegularInvestmentAmount = (value: number | null) => {
    updateInvestmentGoal({ regularInvestmentAmount: value });
  };

  const updateInvestmentDuration = (value: '' | InvestmentDurationKey | string) => {
    updateInvestmentGoal({
      investmentDuration: value as InvestmentDurationKey,
      yearsToAchieve: 5,
    });
  };

  const updateMonthlyPayout = (value: number | null) => {
    updateInvestmentGoal({ monthlyPayout: value });
  };

  const updatePayoutPeriod = (value: number | null) => {
    updateInvestmentGoal({ payoutPeriod: value });
  };

  return (
    <DeviceBasedRendering
      tablet={
        <InvestmentFormTablet
          formData={investmentGoal}
          setFormData={updateInvestmentGoal}
          updateSkipThisGoal={updateSkipThisGoal}
          updateInitialInvestmentAmount={updateInitialInvestmentAmount}
          updateRegularInvestmentAmount={updateRegularInvestmentAmount}
          updateInvestmentDuration={updateInvestmentDuration}
          updateMonthlyPayout={updateMonthlyPayout}
          updatePayoutPeriod={updatePayoutPeriod}
          investmentDurationList={investmentDurationList}
        />
      }
      phone={
        <InvestmentFormPhone
          formData={investmentGoal}
          setFormData={updateInvestmentGoal}
          updateSkipThisGoal={updateSkipThisGoal}
          updateInitialInvestmentAmount={updateInitialInvestmentAmount}
          updateRegularInvestmentAmount={updateRegularInvestmentAmount}
          updateInvestmentDuration={updateInvestmentDuration}
          updateMonthlyPayout={updateMonthlyPayout}
          updatePayoutPeriod={updatePayoutPeriod}
          investmentDurationList={investmentDurationList}
        />
      }
    />
  );
}
