import React, { JSXElementConstructor, ReactElement, useCallback } from 'react';
import { View } from 'react-native';
import {
  Box,
  Column,
  H6,
  Row,
  SmallLabel,
  Switch,
  Typography,
} from 'cube-ui-components';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Portal } from '@gorhom/portal';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { Concern, ConcernId } from '../../types/concern';
import { useTranslation } from 'react-i18next';
import { GoalState, GoalStateItem } from './GoalsPlanning';
import { useFnaSnapPoints } from 'features/fna/hooks/useFnaSnapPoint';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FnaConfirmFooter from '../common/FnaConfirmFooter';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { t, TFuncKey } from 'i18next';

type Props = {
  isModalVisible: boolean;
  onDismiss: () => void;
  goals: Concern[];
  switchState: GoalState;
  setSwitchState: (value: GoalState) => void;
  onDonePress: () => void;
};

type GoalItem = {
  number: number | null;
  icon: ReactElement<any, string | JSXElementConstructor<any>> | undefined; // eslint-disable-line @typescript-eslint/no-explicit-any
  title: string | null;
  input1: JSX.Element;
  input2: JSX.Element;
};

const GoalsPlanningPhone = ({
  isModalVisible,
  onDismiss,
  goals,
  switchState,
  setSwitchState,
  onDonePress,
}: Props) => {
  const { space } = useTheme();
  const { t } = useTranslation(['fna']);
  const snapPoints = useFnaSnapPoints();
  const bottomSheetProps = useBottomSheet();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FnaConfirmFooter
          {...props}
          buttonTitle={t('next')}
          onPress={onDonePress}
          subtext={t('fna:calculateNeed')}
        />
      );
    },
    [onDonePress, t],
  );

  return (
    <>
      {isModalVisible && (
        <Portal>
          <BottomSheetModalProvider>
            <BottomSheetModal
              onDismiss={onDismiss}
              index={0}
              snapPoints={snapPoints}
              {...bottomSheetProps}
              style={{ padding: 0 }}
              footerComponent={renderFooter}
              handleStyle={{ display: 'flex' }}
              enablePanDownToClose={false}
              enableDismissOnClose>
              <Box
                paddingX={space[isNarrowScreen ? 3 : 4]}
                paddingY={space[4]}
                justifyContent="space-between">
                <Box>
                  <H6 fontWeight="bold">{t('fna:goals.popup.title')}</H6>
                </Box>
              </Box>
              <BottomSheetScrollView
                keyboardDismissMode="on-drag"
                style={{
                  paddingHorizontal: space[isNarrowScreen ? 3 : 4],
                }}>
                <GoalHeader
                  header1={t('fna:goals.popup.alreadyPlanned')}
                  header2={t('fna:goals.popup.toBeDiscussed')}
                />
                <Column gap={space[3]} marginBottom={space[4]}>
                  {goals.map(({ id, icon }, idx) => (
                    <GoalItem
                      key={`${id}_${idx}`}
                      number={idx}
                      icon={icon}
                      title={t(
                        `fna:lifeStage.concern.recommendation.${id}` as TFuncKey<
                          ['fna']
                        >,
                      )}
                      input1={
                        <SwitchGoal
                          switchState={switchState}
                          setSwitchState={setSwitchState}
                          goalStateKey={id}
                          goalStateItemKey={'alreadyPlanned'}
                        />
                      }
                      input2={
                        <SwitchGoal
                          switchState={switchState}
                          setSwitchState={setSwitchState}
                          goalStateKey={id}
                          goalStateItemKey={'toBeDiscussed'}
                        />
                      }
                    />
                  ))}
                </Column>
                <BottomSheetFooterSpace />
              </BottomSheetScrollView>
            </BottomSheetModal>
          </BottomSheetModalProvider>
        </Portal>
      )}
    </>
  );
};

const GoalHeader = ({
  header1,
  header2,
}: {
  header1: string;
  header2: string;
}) => {
  const { space } = useTheme();
  return (
    <Row paddingY={space[2]}>
      <View style={{ flex: 3 }} />
      <Row
        style={{ flex: 7 }}
        justifyContent="space-around"
        gap={space[3]}
        paddingX={space[3]}>
        <Typography.Body>{header1}</Typography.Body>
        <Typography.Body>{header2}</Typography.Body>
      </Row>
    </Row>
  );
};

const GoalItem = ({ number, icon, title, input1, input2 }: GoalItem) => {
  const { space, colors, borderRadius } = useTheme();
  return (
    <Row
      alignItems="center"
      paddingX={space[3]}
      paddingY={space[6]}
      borderRadius={borderRadius.large}
      borderWidth={1}
      borderColor={colors.palette.fwdGrey[100]}>
      <Box
        position={'absolute'}
        top={0}
        left={0}
        backgroundColor={
          (number || 0) < 3
            ? colors.palette.fwdYellow[50]
            : colors.palette.fwdGrey[50]
        }
        alignItems="center"
        justifyContent="center"
        paddingX={space[2]}
        paddingY={space[1]}
        borderTopLeftRadius={borderRadius.large}
        borderBottomRightRadius={borderRadius.small}>
        <SmallLabel fontWeight="medium">
          {t(`fna:lifeStage.concern.priority.${number as 0 | 1 | 2}`)}
        </SmallLabel>
      </Box>
      <Row flex={3} alignItems="center" gap={space[2]}>
        <Column gap={space[1]} alignItems="center" flex={1}>
          {icon}
          <Typography.H7 fontWeight={'bold'} style={{ textAlign: 'center' }}>
            {title}
          </Typography.H7>
        </Column>
      </Row>
      <Row
        flex={7}
        justifyContent="space-around"
        alignItems="center"
        gap={space[3]}>
        {input1}
        {input2}
      </Row>
    </Row>
  );
};

const SwitchGoal = ({
  goalStateKey,
  goalStateItemKey,
  switchState,
  setSwitchState,
}: {
  goalStateKey: ConcernId;
  goalStateItemKey: keyof GoalStateItem;
  switchState: GoalState;
  setSwitchState: (state: GoalState) => void;
}) => {
  const { t } = useTranslation('fna');
  const checked = Boolean(switchState[goalStateKey]?.[goalStateItemKey]);

  return (
    <Switch
      label={checked ? t('yes') : t('no')}
      onChange={value => {
        setTimeout(() => {
          const newState = {
            ...switchState,
            [goalStateKey]: {
              ...switchState[goalStateKey],
              [goalStateItemKey]: value,
            },
          };
          setSwitchState(newState);
        }, 100);
      }}
      checked={checked}
    />
  );
};

export const MainContainer = styled(View)(({ theme: { space } }) => ({
  flex: 1,
  backgroundColor: 'rgba(0,0,0,0.5)',
}));

export const ModalContainer = styled(View)(({ theme: { borderRadius } }) => ({
  flex: 1,
  justifyContent: 'flex-end',
}));

export const GoalsContainer = styled(View)(
  ({ theme: { space, borderRadius, colors } }) => ({
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
    paddingTop: space[2],
    backgroundColor: colors.palette.fwdOrange[100],
  }),
);

export default GoalsPlanningPhone;
