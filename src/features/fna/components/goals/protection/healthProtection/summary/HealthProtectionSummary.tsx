import { GoalSummaryProps, Summary } from 'features/fna/types/goal';
import { checkHealthProtectionCompletion } from 'features/fna/utils/helper/checkGoalCompletion';
import { FnaState } from 'features/fna/utils/store/fnaStore';
import React from 'react';
import { country } from 'utils/context';
import IBHealthProtectionSummary from './ib/IBHealthProtectionSummary';
import PHHealthProtectionSummary from './ph/PHHealthProtectionSummary';
import { useHealthSummary } from 'features/fna/hooks/useHealthSummary';

export interface InternalHealthProtectionSummaryProps
  extends GoalSummaryProps<FnaState['healthProtectionGoal']> {
  summary: Summary;
  isCompleted: boolean;
}

export default function HealthProtectionSummary({
  formData: healthGoal,
  setFormData: updateHealthGoal,
  isGroupCompleted,
  onDone,
  onNextTab,
}: GoalSummaryProps<FnaState['healthProtectionGoal']>) {
  const isCompleted = checkHealthProtectionCompletion(healthGoal);

  const summary = useHealthSummary(healthGoal, updateHealthGoal);

  return (
    <>
      {Form && (
        <Form
          formData={healthGoal}
          setFormData={updateHealthGoal}
          isGroupCompleted={isGroupCompleted}
          onNextTab={onNextTab}
          onDone={onDone}
          summary={summary}
          isCompleted={isCompleted}
        />
      )}
    </>
  );
}

const Form = {
  ib: IBHealthProtectionSummary,
  ph: PHHealthProtectionSummary,
  my: IBHealthProtectionSummary,
  id: IBHealthProtectionSummary,
}[country];
