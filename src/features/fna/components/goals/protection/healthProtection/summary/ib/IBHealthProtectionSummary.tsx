import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { InternalHealthProtectionSummaryProps } from '../HealthProtectionSummary';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import IBHealthProtectionSummaryPhone from './IBHealthProtectionSummary.phone';
import IBHealthProtectionSummaryTablet from './IBHealthProtectionSummary.tablet';

type HospitalisationProps = {
  target: {
    title: string;
    value: number | null | undefined;
  };
  coverage: {
    title: string;
    value: number | null | undefined;
  };
  gap: {
    title: string;
    value: number | null | undefined;
  };
};

type CriticalIllnessProps = {
  target: {
    title: string;
    value: number | null | undefined;
  };
  coverage: {
    title: string;
    value: number | null | undefined;
  };
  gap: {
    title: string;
    value: number | null | undefined;
  };
};

export type IBHealthProtectionSummaryProps =
  InternalHealthProtectionSummaryProps & {
    hospitalisation: HospitalisationProps;
    criticalIllness: CriticalIllnessProps;
    hasGapValue: boolean;
  };

export default function IBHealthProtectionSummary({
  isGroupCompleted,
  onDone,
  onNextTab,
  summary,
  isCompleted,
  formData: healthGoal,
  setFormData,
}: InternalHealthProtectionSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);

  const hospitalisation = useMemo(
    () => ({
      target: {
        title: t('fna:protectionGoals.health.totalNeeds'),
        value: healthGoal.hospitalisation?.targetAmount,
      },
      coverage: {
        title: t('fna:protectionGoals.totalCurrentCoverage'),
        value: healthGoal.hospitalisation?.coverageAmount,
      },
      gap: {
        title: t('fna:protectionGoals.health.totalGap'),
        value: healthGoal.hospitalisation?.gapAmount,
      },
    }),
    [
      t,
      healthGoal.hospitalisation?.targetAmount,
      healthGoal.hospitalisation?.coverageAmount,
      healthGoal.hospitalisation?.gapAmount,
    ],
  );

  const criticalIllness = useMemo(
    () => ({
      target: {
        title: t('fna:protectionGoals.health.totalNeeds'),
        value: healthGoal.criticalIllness?.targetAmount,
      },
      coverage: {
        title: t('fna:protectionGoals.totalCurrentCoverage'),
        value: healthGoal.criticalIllness?.coverageAmount,
      },
      gap: {
        title: t('fna:protectionGoals.health.totalGap'),
        value: healthGoal.criticalIllness?.gapAmount,
      },
    }),
    [
      t,
      healthGoal.criticalIllness?.targetAmount,
      healthGoal.criticalIllness?.coverageAmount,
      healthGoal.criticalIllness?.gapAmount,
    ],
  );

  const hasGapValue = typeof criticalIllness.gap.value === 'number';

  return (
    <DeviceBasedRendering
      tablet={
        <IBHealthProtectionSummaryTablet
          formData={healthGoal}
          setFormData={setFormData}
          isGroupCompleted={isGroupCompleted}
          onNextTab={onNextTab}
          onDone={onDone}
          summary={summary}
          isCompleted={isCompleted}
          hasGapValue={hasGapValue}
          hospitalisation={hospitalisation}
          criticalIllness={criticalIllness}
        />
      }
      phone={
        <IBHealthProtectionSummaryPhone
          formData={healthGoal}
          setFormData={setFormData}
          isGroupCompleted={isGroupCompleted}
          onNextTab={onNextTab}
          onDone={onDone}
          summary={summary}
          isCompleted={isCompleted}
          hasGapValue={hasGapValue}
          hospitalisation={hospitalisation}
          criticalIllness={criticalIllness}
        />
      }
    />
  );
}