import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { Body, Box, Column, Icon, Row, Text } from 'cube-ui-components';
import { View } from 'react-native';

import { currencyKFormat } from 'features/fna/utils/helper/fnaUtils';
import GoalSummary from '../../../../goalSummary/GoalSummary';
import { InternalHealthProtectionSummaryProps } from '../HealthProtectionSummary';
import FemaleWithHeart from 'features/fna/components/illustrations/FemaleWithHeart';
import FemaleWithTarget from 'features/fna/components/illustrations/FemaleWithTarget';

export default function PHHealthProtectionSummaryTablet({
  isGroupCompleted,
  onDone,
  onNextTab,
  summary,
  isCompleted,
}: InternalHealthProtectionSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  const { space, colors, sizes } = useTheme();

  return (
    <Box
      px={space[2]}
      pt={space[2]}
      justifyContent="space-between"
      backgroundColor={colors.background}>
      <Column>
        <Row alignItems="center" justifyContent="space-around">
          <Box alignItems="center" flex={1}>
            <CurrentTargetTitle fontWeight="bold" color={colors.secondary}>
              {t('common:withCurrency', {
                amount:
                  typeof summary.coverage.value === 'number'
                    ? currencyKFormat(summary.coverage.value)
                    : '--',
              })}
            </CurrentTargetTitle>
            <Body color={colors.secondary}>{t('fna:current')}</Body>
          </Box>
          <Icon.ChevronRight
            size={sizes[4] + 2}
            fill={colors.palette.fwdGreyDark}
          />
          <Box alignItems="center" flex={1}>
            <CurrentTargetTitle fontWeight="bold" color={colors.secondary}>
              {t('common:withCurrency', {
                amount:
                  typeof summary.target.value === 'number'
                    ? currencyKFormat(summary.target.value)
                    : '--',
              })}
            </CurrentTargetTitle>
            <Body color={colors.secondary}>
              {t('fna:protectionGoals.health.target')}
            </Body>
          </Box>
        </Row>
        <Row alignItems="center" justifyContent="space-around">
          <FemaleWithHeart />
          <View />
          <FemaleWithTarget />
        </Row>
      </Column>
    </Box>
  );
}

const CurrentTargetTitle = styled(Text)(() => ({
  fontSize: 15,
  lineHeight: 19,
}));
