import React, {
  useC<PERSON>back,
  useContext,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFooter,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import {
  Box,
  Button,
  H6,
  Icon,
  LargeLabel,
  Row,
  SmallLabel,
  SmallBody,
} from 'cube-ui-components';
import { LayoutChangeEvent, Pressable } from 'react-native';
import Animated, {
  interpolate,
  SharedValue,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
} from 'react-native-reanimated';
import { useBottomSheet } from 'features/fna/hooks/useBottomSheet';
import { useTheme } from '@emotion/react';
import { BottomSheetDefaultFooterProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetFooter/types';
import {
  useSafeAreaInsets,
  useSafeAreaFrame,
} from 'react-native-safe-area-context';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import DialogPhone from 'components/Dialog.phone';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import useToggle from 'hooks/useToggle';
import useLatest from 'hooks/useLatest';
import { Portal } from '@gorhom/portal';
import { currencyKFormat } from 'features/fna/utils/helper/fnaUtils';
import { GoalContext } from 'features/fna/components/goals/goalGroup/GoalContext';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { Summary } from 'features/fna/types/goal';

interface Props {
  title: string;
  summary: Summary;
  isCompleted?: boolean;
  isGroupCompleted?: boolean;
  onNext?: () => void;
  onDone?: () => void;
  children?: React.ReactNode;
  disabledNext: boolean;
  disabledDone: boolean;
  hospitalisationGapAmount?: number | null;
  criticalIllnessGapAmount?: number | null;
}

export default function HealProtectionGoalSummary({
  title,
  isCompleted,
  onNext,
  onDone,
  children,
  disabledNext,
  disabledDone,
  hospitalisationGapAmount,
  criticalIllnessGapAmount,
}: Props) {
  const { t } = useTranslation(['fna', 'common']);
  const { space, colors, borderRadius } = useTheme();
  const bottomSheetProps = useBottomSheet();
  const { bottom: bottomInset, top: topInset } = useSafeAreaInsets();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { height: screenHeight } = useSafeAreaFrame();

  const initialSnapPoints = useMemo(() => {
    return ['COLLAPSED', 'EXPANDED'];
  }, []);
  const animatedHandleHeight = useSharedValue(-999);
  const animatedContentHeight = useSharedValue(0);
  const animatedSnapPoints = useDerivedValue(() => {
    if (
      animatedHandleHeight.value === -999 ||
      animatedContentHeight.value === 0
    ) {
      return initialSnapPoints.map(() => -999);
    }
    const collapsedHeight =
      animatedContentHeight.value + animatedHandleHeight.value;
    const expandedHeight = screenHeight - topInset;

    return initialSnapPoints.map(snapPoint =>
      snapPoint === 'COLLAPSED' ? collapsedHeight : expandedHeight,
    );
  }, [
    animatedContentHeight.value,
    animatedHandleHeight.value,
    initialSnapPoints,
    screenHeight,
    topInset,
  ]);
  const measured = useRef(false);
  const goalContext = useContext(GoalContext);
  const handleContentLayout = (e: LayoutChangeEvent) => {
    animatedContentHeight.value = e.nativeEvent.layout.height;
    goalContext?.onMeasureIllustrationHeight?.(e.nativeEvent.layout.height);
  };

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const bottomSheetPosition = useSharedValue(0);

  const gapDetailAnimatedStyles = useAnimatedStyle(() => {
    const height = interpolate(
      bottomSheetPosition.value,
      [-1, 0, 1],
      [space[29], space[29], 0],
    );

    const opacity = interpolate(
      bottomSheetPosition.value,
      [-1, 0, 1],
      [1, 1, 0],
    );

    return {
      height,
      opacity,
      overflow: 'hidden',
    };
  });

  const timelineAnimatedStyles = useAnimatedStyle(() => {
    const height = interpolate(
      bottomSheetPosition.value,
      [-1, 0, 1],
      [0, 0, screenHeight - space[22]],
    );

    const opacity = interpolate(
      bottomSheetPosition.value,
      [-1, 0, 1],
      [0, 0, 1],
    );

    return {
      height,
      opacity,
      overflow: 'hidden',
      marginTop: space[3],
      borderRadius: space[4],
    };
  });

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop {...props} pressBehavior="none" opacity={0}>
        <BackdropPressable
          onPress={() => bottomSheetProps.bottomSheetRef.current?.collapse()}
        />
      </BottomSheetBackdrop>
    ),
    [bottomSheetProps.bottomSheetRef],
  );

  const onNextRef = useLatest(onNext);
  const onDoneRef = useLatest(onDone);
  const renderFooter = useCallback(
    (props: BottomSheetDefaultFooterProps) => (
      <BottomSheetFooter {...props}>
        <Row
          mb={bottomInset + space[4]}
          bgColor={
            isCompleted
              ? colors.palette.fwdOrange[20]
              : colors.palette.fwdGrey[50]
          }>
          <Box flex={1} ml={space[4]} mr={space[2]}>
            <Button
              text={t('fna:nextGoal')}
              variant="secondary"
              disabled={disabledNext}
              onPress={() => onNextRef.current?.()}
            />
          </Box>
          <Box flex={1} mr={space[4]} ml={space[2]}>
            <Button
              text={t('fna:done')}
              disabled={disabledDone}
              onPress={() => {
                onDoneRef?.current?.();
                navigation.goBack();
              }}
            />
          </Box>
        </Row>
      </BottomSheetFooter>
    ),
    [
      bottomInset,
      space,
      isCompleted,
      colors.palette.fwdOrange,
      colors.palette.fwdGrey,
      t,
      disabledNext,
      disabledDone,
      onNextRef,
      onDoneRef,
      navigation,
    ],
  );

  return (
    <BottomSheetModalProvider>
      <BottomSheetModal
        {...bottomSheetProps}
        style={{ padding: 0 }}
        backgroundStyle={{
          backgroundColor: isCompleted
            ? colors.palette.fwdOrange[20]
            : colors.palette.fwdGrey[50],
        }}
        handleIndicatorStyle={{
          backgroundColor: isCompleted
            ? colors.palette.fwdOrange[50]
            : colors.palette.fwdGrey[100],
          width: space[10],
          height: space[1],
        }}
        snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
        handleHeight={animatedHandleHeight}
        contentHeight={animatedContentHeight}
        animatedIndex={bottomSheetPosition}
        backdropComponent={renderBackdrop}
        footerComponent={renderFooter}>
        <Box
          px={space[isNarrowScreen ? 3 : 4]}
          onLayout={e => {
            if (measured.current) {
              if (bottomSheetPosition.value <= 0) {
                handleContentLayout(e);
              }
            } else {
              handleContentLayout(e);
              measured.current = true;
            }
          }}>
          <Row alignItems="center">
            <LargeLabel style={{ flex: 1 }} fontWeight="bold">
              {title}
            </LargeLabel>
          </Row>
          <Animated.View style={gapDetailAnimatedStyles}>
            <Box h={space[3]} />
            <SmallLabel>{t('fna:protectionGoals.health.totalGap')}</SmallLabel>
            <Row gap={space[2]} mt={space[3]} justifyContent="space-between">
              <Box
                flex={1}
                overflow="hidden"
                borderRadius={borderRadius['small']}>
                <Box
                  bgColor={colors.palette.fwdYellow[50]}
                  alignItems="center"
                  py={space[1]}>
                  <SmallLabel fontWeight="medium">
                    {t('fna:protectionGoals.health.hospitalisation')}
                  </SmallLabel>
                </Box>
                <Row p={space[3]} bgColor={colors.palette.white}>
                  <SmallBody color={colors.palette.alertRed}>
                    {t('common:currencySymbol')}
                  </SmallBody>
                  <H6
                    fontWeight="medium"
                    color={colors.palette.alertRed}
                    style={{ marginLeft: space[1] }}>
                    {typeof hospitalisationGapAmount === 'number'
                      ? `${currencyKFormat(
                          hospitalisationGapAmount,
                        ).toUpperCase()}`
                      : '--'}
                  </H6>
                </Row>
              </Box>
              <Box
                flex={1}
                overflow="hidden"
                borderRadius={borderRadius['small']}>
                <Box
                  bgColor={colors.palette.fwdLightGreen['50']}
                  alignItems="center"
                  py={space[1]}>
                  <SmallLabel fontWeight="medium">
                    {t('fna:protectionGoals.health.diagnosis.critical.illness')}
                  </SmallLabel>
                </Box>
                <Row p={space[3]} bgColor={colors.palette.white}>
                  <SmallBody color={colors.palette.alertRed}>
                    {t('common:currencySymbol')}
                  </SmallBody>
                  <H6
                    fontWeight="medium"
                    color={colors.palette.alertRed}
                    style={{ marginLeft: space[1] }}>
                    {typeof criticalIllnessGapAmount === 'number'
                      ? `${currencyKFormat(
                          criticalIllnessGapAmount,
                        ).toUpperCase()}`
                      : '--'}
                  </H6>
                </Row>
              </Box>
            </Row>
          </Animated.View>

          {children && (
            <Animated.View style={timelineAnimatedStyles}>
              <BottomSheetScrollView>{children}</BottomSheetScrollView>
            </Animated.View>
          )}
          <BottomSheetFooterSpace />
        </Box>
      </BottomSheetModal>
    </BottomSheetModalProvider>
  );
}

const BackdropPressable = styled(Pressable)(() => ({
  width: '100%',
  height: '100%',
}));
