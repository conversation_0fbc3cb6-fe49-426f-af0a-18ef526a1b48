import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import {
  Body,
  Box,
  Button,
  Column,
  H5,
  H6,
  Icon,
  Label,
  LargeLabel,
  PictogramIcon,
  Row,
  SmallBody,
  Text,
} from 'cube-ui-components';
import styled from '@emotion/native';
import Animated, { SlideInRight } from 'react-native-reanimated';
import { ScrollView, View } from 'react-native';

import { RootStackParamList } from 'types';
import useLatest from 'hooks/useLatest';
import HealthProtectionCriticalCurrent from 'features/fna/components/illustrations/HealthProtectionCriticalCurrent';
import HealthProtectionCriticalFuture from 'features/fna/components/illustrations/HealthProtectionCriticalFuture';
import HealthProtectionHospitalCurrent from 'features/fna/components/illustrations/HealthProtectionHospitalCurrent';
import HealthProtectionHospitalFuture from 'features/fna/components/illustrations/HealthProtectionHospitalFuture';
import { currencyKFormat } from 'features/fna/utils/helper/fnaUtils';
import type { IBHealthProtectionSummaryProps } from './IBHealthProtectionSummary';

export default function IBHealthProtectionSummaryTablet({
  onDone,
  onNextTab,
  isCompleted,
  hospitalisation,
  criticalIllness,
}: IBHealthProtectionSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  const { space, sizes, colors, borderRadius } = useTheme();

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const onNextRef = useLatest(onNextTab);
  const onDoneRef = useLatest(onDone);

  return (
    <Content>
      <Animated.View entering={SlideInRight} style={{ flex: 1 }}>
        <Box
          backgroundColor={
            isCompleted
              ? colors.palette.fwdOrange[20]
              : colors.palette.fwdGrey[50]
          }
          flex={1}
          flexDirection="column"
          px={space[4]}
          pt={space[5]}>
          <Row alignItems="center">
            <PictogramIcon.Cash2 size={sizes[10]} />
            <Box w={10} />
            <LargeLabel style={{ flex: 1 }} fontWeight="bold">
              {t('fna:protectionGoals.health.totalFunds')}
            </LargeLabel>
          </Row>
          <ScrollView>
            <Box
              padding={space[3]}
              paddingBottom={0}
              bgColor={colors.palette.white}
              borderRadius={borderRadius.small}
              borderBottomColor={colors.palette.fwdYellow[100]}
              borderBottomWidth={space[2]}
              mt={space[6]}>
              <Box
                bgColor={colors.palette.fwdYellow[50]}
                alignItems="center"
                py={space[1]}
                mb={space[3]}>
                <Label>{t('fna:protectionGoals.health.hospitalisation')}</Label>
              </Box>
              <TotalGapContainer active={isCompleted}>
                <Row
                  alignItems="center"
                  justifyContent="space-between"
                  borderRadius={space[2]}>
                  <Label>{hospitalisation.target.title}</Label>
                  <Row>
                    <SmallBody color={colors.palette.fwdBlue[100]}>
                      {t('common:currencySymbol')}
                    </SmallBody>
                    <H6
                      fontWeight="bold"
                      color={colors.palette.fwdBlue[100]}
                      style={{ marginLeft: space[1] }}>
                      {typeof hospitalisation.target.value === 'number'
                        ? `${currencyKFormat(hospitalisation.target.value)}`
                        : '--'}
                    </H6>
                  </Row>
                </Row>
                <Row
                  mt={space[2]}
                  alignItems="center"
                  justifyContent="space-between"
                  borderRadius={space[2]}>
                  <Label>{hospitalisation.coverage.title}</Label>
                  <Row>
                    <SmallBody color={colors.secondary}>
                      {t('common:currencySymbol')}
                    </SmallBody>
                    <H6
                      fontWeight="bold"
                      color={colors.secondary}
                      style={{ marginLeft: space[1] }}>
                      {typeof hospitalisation.coverage.value === 'number'
                        ? `${currencyKFormat(hospitalisation.coverage.value)}`
                        : '--'}
                    </H6>
                  </Row>
                </Row>
                <Row
                  h={1}
                  bgColor={colors.palette.fwdGrey[100]}
                  my={space[2]}
                />
                <Row
                  alignItems="center"
                  justifyContent="space-between"
                  borderRadius={space[2]}>
                  <Row alignItems={'center'}>
                    <Label>{hospitalisation.gap.title}</Label>
                    <Box width={sizes[1]} />
                  </Row>
                  <Row>
                    <Body color={colors.palette.alertRed}>
                      {t('common:currencySymbol')}
                    </Body>
                    <H5
                      fontWeight="bold"
                      color={colors.palette.alertRed}
                      style={{ marginLeft: space[1] }}>
                      {typeof hospitalisation.gap.value === 'number'
                        ? `${currencyKFormat(hospitalisation.gap.value)}`
                        : '--'}
                    </H5>
                  </Row>
                </Row>
              </TotalGapContainer>

              {isCompleted && (
                <Box
                  px={space[2]}
                  pt={space[1]}
                  justifyContent="space-between"
                  backgroundColor={colors.background}>
                  <Column>
                    <Row alignItems="center" justifyContent="space-around">
                      <Box alignItems="center" flex={1}>
                        <CurrentTargetTitle color={colors.secondaryVariant}>
                          {t('common:withCurrency', {
                            amount:
                              typeof hospitalisation.coverage.value === 'number'
                                ? `${currencyKFormat(
                                    hospitalisation.coverage.value,
                                  )}`
                                : '--',
                          })}
                        </CurrentTargetTitle>
                        <Body color={colors.secondaryVariant}>
                          {t('fna:protectionGoals.health.current.protection')}
                        </Body>
                      </Box>
                      <Icon.ChevronRight
                        size={sizes[4] + 2}
                        fill={colors.palette.fwdGreyDark}
                      />
                      <Box alignItems="center" flex={1}>
                        <CurrentTargetTitle
                          fontWeight="bold"
                          color={colors.secondary}>
                          {t('common:withCurrency', {
                            amount:
                              typeof hospitalisation.target.value === 'number'
                                ? `${currencyKFormat(
                                    hospitalisation.target.value,
                                  )}`
                                : '--',
                          })}
                        </CurrentTargetTitle>
                        <Body color={colors.secondary}>
                          {t('fna:protectionGoals.health.future.protection')}
                        </Body>
                      </Box>
                    </Row>
                    <Row
                      mt={space[4]}
                      alignItems="center"
                      justifyContent="space-around">
                      <HealthProtectionHospitalCurrent />
                      <View />
                      <HealthProtectionHospitalFuture />
                    </Row>
                  </Column>
                </Box>
              )}
            </Box>

            <Box
              padding={space[3]}
              paddingBottom={0}
              bgColor={colors.palette.white}
              borderRadius={borderRadius.small}
              borderBottomColor={colors.palette.fwdLightGreen[50]}
              borderBottomWidth={space[2]}
              mt={space[4]}>
              <Box
                bgColor={colors.palette.fwdLightGreen[50]}
                alignItems="center"
                py={space[1]}
                mb={space[3]}>
                <Label>
                  {t('fna:protectionGoals.health.diagnosis.critical.illness')}
                </Label>
              </Box>
              <TotalGapContainer active={isCompleted}>
                <Row
                  alignItems="center"
                  justifyContent="space-between"
                  borderRadius={space[2]}>
                  <Label>{criticalIllness.target.title}</Label>
                  <Row>
                    <SmallBody color={colors.palette.fwdBlue[100]}>
                      {t('common:currencySymbol')}
                    </SmallBody>
                    <H6
                      fontWeight="bold"
                      color={colors.palette.fwdBlue[100]}
                      style={{ marginLeft: space[1] }}>
                      {typeof criticalIllness.target.value === 'number'
                        ? `${currencyKFormat(criticalIllness.target.value)}`
                        : '--'}
                    </H6>
                  </Row>
                </Row>
                <Row
                  mt={space[2]}
                  alignItems="center"
                  justifyContent="space-between"
                  borderRadius={space[2]}>
                  <Label>{criticalIllness.coverage.title}</Label>
                  <Row>
                    <SmallBody color={colors.secondary}>
                      {t('common:currencySymbol')}
                    </SmallBody>
                    <H6
                      fontWeight="bold"
                      color={colors.secondary}
                      style={{ marginLeft: space[1] }}>
                      {typeof criticalIllness.coverage.value === 'number'
                        ? `${currencyKFormat(criticalIllness.coverage.value)}`
                        : '--'}
                    </H6>
                  </Row>
                </Row>
                <Row
                  h={1}
                  bgColor={colors.palette.fwdGrey[100]}
                  my={space[2]}
                />
                <Row
                  alignItems="center"
                  justifyContent="space-between"
                  borderRadius={space[2]}>
                  <Row alignItems={'center'}>
                    <Label>{criticalIllness.gap.title}</Label>
                    <Box width={sizes[1]} />
                  </Row>
                  <Row>
                    <Body color={colors.palette.alertRed}>
                      {t('common:currencySymbol')}
                    </Body>
                    <H5
                      fontWeight="bold"
                      color={colors.palette.alertRed}
                      style={{ marginLeft: space[1] }}>
                      {typeof criticalIllness.gap.value === 'number'
                        ? `${currencyKFormat(criticalIllness.gap.value)}`
                        : '--'}
                    </H5>
                  </Row>
                </Row>
              </TotalGapContainer>

              {isCompleted && (
                <Box
                  px={space[2]}
                  pt={space[1]}
                  justifyContent="space-between"
                  backgroundColor={colors.background}>
                  <Column>
                    <Row alignItems="center" justifyContent="space-around">
                      <Box alignItems="center" flex={1}>
                        <CurrentTargetTitle color={colors.secondaryVariant}>
                          {t('common:withCurrency', {
                            amount:
                              typeof criticalIllness.coverage.value === 'number'
                                ? `${currencyKFormat(
                                    criticalIllness.coverage.value,
                                  )}`
                                : '--',
                          })}
                        </CurrentTargetTitle>
                        <Body color={colors.secondaryVariant}>
                          {t('fna:protectionGoals.health.current.protection')}
                        </Body>
                      </Box>
                      <Icon.ChevronRight
                        size={sizes[4] + 2}
                        fill={colors.palette.fwdGreyDark}
                      />
                      <Box alignItems="center" flex={1}>
                        <CurrentTargetTitle
                          fontWeight="bold"
                          color={colors.secondary}>
                          {t('common:withCurrency', {
                            amount:
                              typeof criticalIllness.target.value === 'number'
                                ? `${currencyKFormat(
                                    criticalIllness.target.value,
                                  )}`
                                : '--',
                          })}
                        </CurrentTargetTitle>
                        <Body color={colors.secondary}>
                          {t('fna:protectionGoals.health.future.protection')}
                        </Body>
                      </Box>
                    </Row>
                    <Row
                      mt={space[4]}
                      alignItems="center"
                      justifyContent="space-around">
                      <HealthProtectionCriticalCurrent />
                      <View />
                      <HealthProtectionCriticalFuture />
                    </Row>
                  </Column>
                </Box>
              )}
            </Box>
          </ScrollView>
        </Box>
        <Footer>
          <Box flex={1}>
            <Button
              text={t('fna:nextGoal')}
              variant="secondary"
              onPress={() => onNextRef.current?.()}
            />
          </Box>
          <Box w={space[4]} />
          <Box flex={1}>
            <Button
              text={t('fna:done')}
              disabled={!isCompleted}
              onPress={() => {
                onDoneRef?.current?.();
                navigation.goBack();
              }}
            />
          </Box>
        </Footer>
      </Animated.View>
    </Content>
  );
}

const TotalGapContainer = styled.View<{ active?: boolean }>(
  ({ theme: { space, colors, borderRadius }, active }) => ({
    padding: space[3],
    marginBottom: space[3],
    backgroundColor: active
      ? colors.palette.fwdOrange[5]
      : colors.palette.fwdGrey[20],
    borderRadius: borderRadius.small,
  }),
);

const Footer = styled(Row)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  paddingVertical: space[5],
  paddingHorizontal: space[4],
}));

const Content = styled(Column)(() => ({
  flex: 1,
}));

const CurrentTargetTitle = styled(Text)(() => ({
  fontSize: 15,
  lineHeight: 19,
}));
