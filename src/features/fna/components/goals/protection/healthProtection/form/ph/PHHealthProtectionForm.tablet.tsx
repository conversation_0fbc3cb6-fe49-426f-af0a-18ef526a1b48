import { useTheme } from '@emotion/react';
import {
  Box,
  LargeBody,
  LargeLabel,
  RadioButton,
  RadioButtonGroup,
  Row,
  H6,
} from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { View } from 'react-native';

import {
  booleanToYesNoValue,
} from 'features/fna/utils/helper/fnaUtils';
import DecimalTextField from 'components/DecimalTextField';
import Autocomplete from 'components/Autocomplete';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import IllustrationHealthProtectionIcon from 'features/fna/components/illustrations/IllustrationHealthProtectionIcon';
import type { PHHealthProtectionFormProps } from './PHHealthProtectionForm';

export default function PHHealthProtectionFormTablet({
  formData: healthGoal,
  setFormData: updateHealthGoal,
  levelOfHospitals,
  isExistingHealthInsPolicies,
  updateTargetAmount,
  updateHaveExistingHealth,
  updateInsuranceCoverageAmount,
  updateHealthEmergenciesAmount,
}: PHHealthProtectionFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();

  return (
    <Box>
      <Row alignItems="center" justifyContent="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:protectionGoals.health.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <IllustrationHealthProtectionIcon />
        </Box>
      </Row>
      <Content>
        <ContentContainer>
          <Row flex={1.8}>
            <FieldLabel>1.</FieldLabel>
            <LargeBody style={{ flex: 1 }}>
              {t('fna:protectionGoals.health.levelOfHospital.title')}
            </LargeBody>
          </Row>
          <Box w={space[3]} />
          <Row flex={1} justifyContent="space-between" alignItems="flex-start">
            <Autocomplete
              data={levelOfHospitals}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              getDisplayedLabel={item => item.displayLabel}
              label={t('fna:protectionGoals.health.levelOfHospital')}
              value={healthGoal.targetAmount ?? undefined}
              onChange={updateTargetAmount}
              style={{ flex: 2 }}
            />
          </Row>
        </ContentContainer>
        <ContentContainer mt={space[3]}>
          <Row flex={1.8}>
            <FieldLabel>2.</FieldLabel>
            <LargeBody style={{ flex: 1 }}>
              {t('fna:protectionGoals.health.existingHealth')}
            </LargeBody>
          </Row>
          <Box w={space[3]} />
          <Row flex={1} justifyContent="space-between" alignItems="flex-start">
            <RadioButtonGroup
              value={booleanToYesNoValue(healthGoal.hasSavings)}
              onChange={updateHaveExistingHealth}>
              <RadioButton
                style={{ flex: 1 }}
                label={t('fna:yes')}
                value="yes"
              />
              <RadioButton style={{ flex: 1 }} label={t('fna:no')} value="no" />
            </RadioButtonGroup>
          </Row>
        </ContentContainer>
        {isExistingHealthInsPolicies && (
          <ContentContainer mt={space[5]}>
            <Row flex={1.8}>
              <FieldLabel>2.1.</FieldLabel>
              <LargeBody style={{ flex: 1 }}>
                {t('fna:protectionGoals.health.existingHealth.total')}
              </LargeBody>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={healthGoal.insuranceCoverageAmount}
              onChange={updateInsuranceCoverageAmount}
              max={MAX_AMOUNT_VALUE}
            />
          </ContentContainer>
        )}
        <ContentContainer mt={space[3]}>
          <Row flex={1.8}>
            <FieldLabel>3.</FieldLabel>
            <LargeBody style={{ flex: 1 }}>
              {t('fna:protectionGoals.health.aside')}
            </LargeBody>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            style={{ flex: 1 }}
            value={healthGoal.healthEmergenciesAmount}
            onChange={updateHealthEmergenciesAmount}
            max={MAX_AMOUNT_VALUE}
          />
        </ContentContainer>
      </Content>
    </Box>
  );
}

const ContentContainer = styled(Row)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
}));

const Content = styled(View)(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
