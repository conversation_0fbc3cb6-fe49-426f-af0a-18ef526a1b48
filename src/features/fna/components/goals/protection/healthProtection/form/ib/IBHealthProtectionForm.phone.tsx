import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import React from 'react';

import {
  Box,
  H6,
  LargeLabel,
  Row,
  Checkbox,
  SmallLabel,
  LargeBody,
} from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import HealthProtectionLogo from 'features/fna/components/illustrations/HealthProtectionLogo';
import DecimalTextField from 'components/DecimalTextField';
import type { IBHealthProtectionFormProps } from './IBHealthProtectionForm';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';

const HealthProtectionFieldLabel = () => {
  const { t } = useTranslation(['fna']);
  const { space, colors, borderRadius } = useTheme();
  return (
    <Row mt={space[4]} justifyContent="space-between" gap={space[4]}>
      <Box
        flex={1}
        backgroundColor={colors.palette.fwdYellow['50']}
        p={space[1]}
        borderRadius={borderRadius['x-small']}
        alignItems="center">
        <SmallLabel fontWeight="medium">
          {t('fna:protectionGoals.health.hospitalisation')}
        </SmallLabel>
      </Box>
      <Box
        flex={1}
        backgroundColor={colors.palette.fwdLightGreen['50']}
        p={space[1]}
        borderRadius={borderRadius['x-small']}
        alignItems="center">
        <SmallLabel fontWeight="medium">
          {t('fna:protectionGoals.health.diagnosis.critical.illness')}
        </SmallLabel>
      </Box>
    </Row>
  );
};

export default function IBHealthProtectionFormPhone({
  formData: healthGoal,
  setFormData: updateHealthGoal,
  levelOfHospitals,
  isExistingHealthInsPolicies: isExistingHealth,
  updateHospitalisationTargetAmount,
  updateCriticalIllnessTargetAmount,
  updateHaveExisting,
  updateHospitalisationCoverageAmount,
  updateHospitalisationExpiryAge,
  updateCriticalIllnessCoverageAmount,
  updateCriticalIllnessExpiryAge,
}: IBHealthProtectionFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Box py={space[4]} px={space[isNarrowScreen ? 3 : 4]}>
      <Row mb={space[6]} alignItems="center" justifyContent="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:protectionGoals.health.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <HealthProtectionLogo />
        </Box>
      </Row>
      <FormContainer
        enabled={Boolean(healthGoal.enabled)}
        pointerEvents={healthGoal.enabled ? 'auto' : 'none'}>
        <ContentContainer>
          <Row>
            <LargeLabel>1.</LargeLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:protectionGoals.health.amount.needed')}
            </LargeLabel>
          </Row>
          <HealthProtectionFieldLabel />
          <Row mt={space[4]} justifyContent="space-between" gap={space[4]}>
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={healthGoal?.hospitalisation?.targetAmount}
              onChange={updateHospitalisationTargetAmount}
              max={MAX_AMOUNT_VALUE}
            />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={healthGoal?.criticalIllness?.targetAmount}
              onChange={updateCriticalIllnessTargetAmount}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
        </ContentContainer>
        <Box mt={space[7]} pr={space[4]}>
          <Checkbox
            label={t('fna:protectionGoals.existing.life.insurance')}
            checked={Boolean(healthGoal.hasSavings)}
            onChange={updateHaveExisting}
          />
        </Box>
        {isExistingHealth && (
          <ContentContainer mt={space[7]}>
            <Row>
              <LargeLabel>2.</LargeLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:protectionGoals.health.amount.existing.protection')}
              </LargeLabel>
            </Row>
            <HealthProtectionFieldLabel />
            <Row mt={space[4]} justifyContent="space-between" gap={space[4]}>
              <DecimalTextField
                label={t('fna:amount.label')}
                style={{ flex: 1 }}
                value={healthGoal?.hospitalisation?.coverageAmount}
                onChange={updateHospitalisationCoverageAmount}
                max={MAX_AMOUNT_VALUE}
              />
              <DecimalTextField
                label={t('fna:amount.label')}
                style={{ flex: 1 }}
                value={healthGoal?.criticalIllness?.coverageAmount}
                onChange={updateCriticalIllnessCoverageAmount}
                max={MAX_AMOUNT_VALUE}
              />
            </Row>
          </ContentContainer>
        )}
        {isExistingHealth && (
          <ContentContainer mt={space[7]}>
            <Row>
              <LargeLabel>3.</LargeLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:protectionGoals.health.expiry.age')}
              </LargeLabel>
            </Row>
            <HealthProtectionFieldLabel />
            <Row
              mt={space[4]}
              justifyContent="space-between"
              gap={space[5]}
              alignItems="flex-start">
              <DecimalTextField
                label={t('fna:protectionGoals.health.age')}
                style={{ flex: 1 }}
                value={healthGoal?.hospitalisation?.expiryAge}
                onChange={updateHospitalisationExpiryAge}
                maxLength={2}
              />
              <DecimalTextField
                label={t('fna:protectionGoals.health.age')}
                style={{ flex: 1 }}
                value={healthGoal?.criticalIllness?.expiryAge}
                onChange={updateCriticalIllnessExpiryAge}
                maxLength={2}
              />
            </Row>
          </ContentContainer>
        )}
      </FormContainer>
      <Divider />
      <Box mb={space[10]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </Box>
  );
}

const ContentContainer = styled(Box)(({ theme: { space } }) => ({
  marginTop: space[4],
  paddingTop: space[2],
  paddingBottom: space[2],
}));

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));

const Divider = styled(Box)(({ theme }) => {
  return {
    marginVertical: theme.space[7],
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});
