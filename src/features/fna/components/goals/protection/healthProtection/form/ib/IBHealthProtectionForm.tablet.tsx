import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  LargeBody,
  LargeLabel,
  Row,
  H6,
} from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { View } from 'react-native';

import DecimalTextField from 'components/DecimalTextField';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import IllustrationHealthProtectionIcon from 'features/fna/components/illustrations/IllustrationHealthProtectionIcon';
import type { IBHealthProtectionFormProps } from './IBHealthProtectionForm';

export default function IBHealthProtectionFormTablet({
  formData: healthGoal,
  setFormData: updateHealthGoal,
  isExistingHealthInsPolicies: isExistingHealth,
  updateHospitalisationTargetAmount,
  updateCriticalIllnessTargetAmount,
  updateHaveExisting,
  updateHospitalisationCoverageAmount,
  updateHospitalisationExpiryAge,
  updateCriticalIllnessCoverageAmount,
  updateCriticalIllnessExpiryAge,
}: IBHealthProtectionFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();

  return (
    <Box>
      <Row alignItems="center" justifyContent="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:protectionGoals.health.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <IllustrationHealthProtectionIcon />
        </Box>
      </Row>
      <Content>
        <FormContainer
          enabled={Boolean(healthGoal.enabled)}
          pointerEvents={healthGoal.enabled ? 'auto' : 'none'}>
          <ContentContainer gap={space[1]}>
            <Row flex={1} />
            <Row
              flex={2}
              justifyContent="space-between"
              gap={space[5]}
              alignItems="flex-start">
              <Box flex={1}>
                <LargeLabel>
                  {t('fna:protectionGoals.health.hospitalisation')}
                </LargeLabel>
              </Box>
              <Box flex={1}>
                <LargeLabel>
                  {t('fna:protectionGoals.health.diagnosis.critical.illness')}
                </LargeLabel>
              </Box>
            </Row>
          </ContentContainer>
          <ContentContainer mt={space[6]} gap={space[2]}>
            <Row flex={1}>
              <FieldLabel>1.</FieldLabel>
              <LargeBody style={{ flex: 1 }}>
                {t('fna:protectionGoals.health.amount.needed')}
              </LargeBody>
            </Row>
            <Row
              flex={2}
              justifyContent="space-between"
              gap={space[5]}
              alignItems="flex-start">
              <DecimalTextField
                label={t('fna:amount.label')}
                style={{ flex: 1 }}
                value={healthGoal?.hospitalisation?.targetAmount}
                onChange={updateHospitalisationTargetAmount}
                max={MAX_AMOUNT_VALUE}
              />
              <DecimalTextField
                label={t('fna:amount.label')}
                style={{ flex: 1 }}
                value={healthGoal?.criticalIllness?.targetAmount}
                onChange={updateCriticalIllnessTargetAmount}
                max={MAX_AMOUNT_VALUE}
              />
            </Row>
          </ContentContainer>
          <Box mt={space[7]} mx={space[3]}>
            <Checkbox
              label={t('fna:protectionGoals.health.existing.protection')}
              checked={Boolean(healthGoal.hasSavings)}
              onChange={updateHaveExisting}
            />
          </Box>
          {isExistingHealth && (
            <ContentContainer mt={space[7]}>
              <Row flex={1}>
                <FieldLabel>2.</FieldLabel>
                <LargeBody style={{ flex: 1 }}>
                  {t('fna:protectionGoals.health.amount.existing.protection')}
                </LargeBody>
              </Row>
              <Row
                flex={2}
                justifyContent="space-between"
                gap={space[5]}
                alignItems="flex-start">
                <DecimalTextField
                  label={t('fna:amount.label')}
                  style={{ flex: 1 }}
                  value={healthGoal?.hospitalisation?.coverageAmount}
                  onChange={updateHospitalisationCoverageAmount}
                  max={MAX_AMOUNT_VALUE}
                />
                <DecimalTextField
                  label={t('fna:amount.label')}
                  style={{ flex: 1 }}
                  value={healthGoal?.criticalIllness?.coverageAmount}
                  onChange={updateCriticalIllnessCoverageAmount}
                  max={MAX_AMOUNT_VALUE}
                />
              </Row>
            </ContentContainer>
          )}
          {isExistingHealth && (
            <ContentContainer mt={space[7]}>
              <Row flex={1}>
                <FieldLabel>3.</FieldLabel>
                <LargeBody style={{ flex: 1 }}>
                  {t('fna:protectionGoals.health.expiry.age')}
                </LargeBody>
              </Row>
              <Row
                flex={2}
                justifyContent="space-between"
                gap={space[5]}
                alignItems="flex-start">
                <DecimalTextField
                  label={t('fna:protectionGoals.health.age')}
                  style={{ flex: 1 }}
                  value={healthGoal?.hospitalisation?.expiryAge}
                  onChange={updateHospitalisationExpiryAge}
                  maxLength={2}
                />
                <DecimalTextField
                  label={t('fna:protectionGoals.health.age')}
                  style={{ flex: 1 }}
                  value={healthGoal?.criticalIllness?.expiryAge}
                  onChange={updateCriticalIllnessExpiryAge}
                  maxLength={2}
                />
              </Row>
            </ContentContainer>
          )}
        </FormContainer>
      </Content>
      <Box mt={space[4]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </Box>
  );
}

const ContentContainer = styled(Row)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
}));

const Content = styled(View)(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
