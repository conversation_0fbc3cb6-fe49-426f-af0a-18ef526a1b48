import React from 'react';
import PHHealthProtectionFormPhone from './PHHealthProtectionForm.phone';
import PHHealthProtectionFormTablet from './PHHealthProtectionForm.tablet';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { InternalHealthProtectionFormProps } from '../HealthProtectionForm';
import {
  yesNoValueToBoolean,
} from 'features/fna/utils/helper/fnaUtils';

export type PHHealthProtectionFormProps = InternalHealthProtectionFormProps & {
  updateTargetAmount: (targetAmount: number | null) => void;
  updateHaveExistingHealth: (value: any) => void;
  updateInsuranceCoverageAmount: (
    insuranceCoverageAmount: number | null,
  ) => void;
  updateHealthEmergenciesAmount: (
    healthEmergenciesAmount: number | null,
  ) => void;
};

export default function PHHealthProtectionForm({
  formData: healthGoal,
  setFormData: updateHealthGoal,
  levelOfHospitals,
  isExistingHealthInsPolicies,
}: InternalHealthProtectionFormProps) {

  const updateTargetAmount = (targetAmount: number | null) => {
    if (targetAmount !== null) {
      updateHealthGoal({ targetAmount });
    }
  };

  const updateHaveExistingHealth = (value: any) => {
    const hasSavings = yesNoValueToBoolean(value);
    if (hasSavings !== healthGoal.hasSavings) {
      updateHealthGoal({
        hasSavings,
        insuranceCoverageAmount: null,
      });
    }
  };

  const updateInsuranceCoverageAmount = (
    insuranceCoverageAmount: number | null,
  ) => {
    updateHealthGoal({ insuranceCoverageAmount });
  };

  const updateHealthEmergenciesAmount = (
    healthEmergenciesAmount: number | null,
  ) => {
    updateHealthGoal({ healthEmergenciesAmount });
  };

  return (
    <DeviceBasedRendering
      tablet={
        <PHHealthProtectionFormTablet
          formData={healthGoal}
          setFormData={updateHealthGoal}
          levelOfHospitals={levelOfHospitals}
          isExistingHealthInsPolicies={isExistingHealthInsPolicies}
          updateTargetAmount={updateTargetAmount}
          updateHaveExistingHealth={updateHaveExistingHealth}
          updateInsuranceCoverageAmount={updateInsuranceCoverageAmount}
          updateHealthEmergenciesAmount={updateHealthEmergenciesAmount}
        />
      }
      phone={
        <PHHealthProtectionFormPhone
          formData={healthGoal}
          setFormData={updateHealthGoal}
          levelOfHospitals={levelOfHospitals}
          isExistingHealthInsPolicies={isExistingHealthInsPolicies}
          updateTargetAmount={updateTargetAmount}
          updateHaveExistingHealth={updateHaveExistingHealth}
          updateInsuranceCoverageAmount={updateInsuranceCoverageAmount}
          updateHealthEmergenciesAmount={updateHealthEmergenciesAmount}
        />
      }
    />
  );
}
