import { useTheme } from '@emotion/react';
import { Body, Box, Column, Icon, Row } from 'cube-ui-components';
import FemaleCoin from 'features/fna/components/illustrations/FemaleCoin';
import TargetMoney from 'features/fna/components/illustrations/TargetMoney';
import { currencyKFormat } from 'features/fna/utils/helper/fnaUtils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import GoalSummary from '../../../goalSummary/GoalSummary';
import { LegacyPlanningSummaryProps } from './LegacyPlanningSummary';

export default function LegacyPlanningSummary({
  isGroupCompleted,
  onNextTab,
  onDone,
  summary,
  isCompleted,
}: LegacyPlanningSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  const { space, colors, sizes } = useTheme();

  return (
    <GoalSummary
      title={t('fna:protectionGoals.legacyPlanning.totalFunds')}
      summary={summary}
      isCompleted={isCompleted}
      isGroupCompleted={isGroupCompleted}
      onNext={onNextTab}
      onDone={onDone}>
      <Box
        flex={1}
        px={space[2]}
        pt={space[2]}
        justifyContent="space-between"
        backgroundColor={colors.palette.white}>
        <Column>
          <Row alignItems="center" justifyContent="space-around">
            <Box alignItems="center">
              <Body color={colors.secondary} fontWeight="bold">
                {t('common:withCurrency', {
                  amount:
                    typeof summary.coverage.value === 'number'
                      ? currencyKFormat(summary.coverage.value)
                      : '--',
                })}
              </Body>
              <Body color={colors.secondary}>{t('fna:current')}</Body>
            </Box>
            <Icon.ChevronRight
              size={sizes[4]}
              fill={colors.palette.fwdGreyDark}
            />
            <Box alignItems="center">
              <Body color={colors.secondary} fontWeight="bold">
                {t('common:withCurrency', {
                  amount:
                    typeof summary.target.value === 'number'
                      ? currencyKFormat(summary.target.value)
                      : '--',
                })}
              </Body>
              <Body color={colors.secondary}>
                {t('fna:protectionGoals.target')}
              </Body>
            </Box>
          </Row>
          <Row alignItems="center" justifyContent="space-around">
            <FemaleCoin />
            <View />
            <TargetMoney />
          </Row>
        </Column>
      </Box>
    </GoalSummary>
  );
}
