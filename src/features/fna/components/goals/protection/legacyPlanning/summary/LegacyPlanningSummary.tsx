import { GoalSummaryProps, Summary } from 'features/fna/types/goal';
import { checkLegacyPlanningCompletion } from 'features/fna/utils/helper/checkGoalCompletion';
import { FnaState } from 'features/fna/utils/store/fnaStore';
import React, { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import LegacyPlanningSummaryPhone from './LegacyPlanningSummary.phone';
import LegacyPlanningSummaryTablet from './LegacyPlanningSummary.tablet';
import DeviceBasedRendering from 'components/DeviceBasedRendering';

export interface LegacyPlanningSummaryProps
  extends GoalSummaryProps<FnaState['legacyPlanningGoal']> {
  summary: Summary;
  isCompleted: boolean;
}

export default function LegacyPlanningSummary({
  formData: legacyGoal,
  setFormData: updateLegacyGoal,
  isGroupCompleted,
  onNextTab,
  onDone,
}: GoalSummaryProps<FnaState['legacyPlanningGoal']>) {
  const { t } = useTranslation(['fna']);

  const gap = useMemo(() => {
    if (typeof legacyGoal.targetAmount !== 'number') return null;
    if (legacyGoal.coverageAmount) {
      return Math.max(0, legacyGoal.targetAmount - legacyGoal.coverageAmount);
    } else {
      return legacyGoal.targetAmount;
    }
  }, [legacyGoal.targetAmount, legacyGoal.coverageAmount]);

  const isCompleted = checkLegacyPlanningCompletion(legacyGoal);

  const summary = useMemo(
    () => ({
      enabled: legacyGoal.enabled,
      target: {
        title: t('fna:protectionGoals.legacyPlanning.totalNeeds'),
        value: legacyGoal.targetAmount,
      },
      coverage: {
        title: t('fna:protectionGoals.totalCurrentCoverage'),
        value: legacyGoal.coverageAmount,
      },
      gap: {
        title: t('fna:protectionGoals.legacyPlanning.totalGap'),
        value: gap,
      },
    }),
    [
      legacyGoal.enabled,
      legacyGoal.targetAmount,
      legacyGoal.coverageAmount,
      t,
      gap,
    ],
  );

  useEffect(() => {
    updateLegacyGoal({ targetAmount: legacyGoal.targetAmount });
  }, [legacyGoal.targetAmount, updateLegacyGoal]);

  useEffect(() => {
    updateLegacyGoal({ gapAmount: gap });
  }, [gap, updateLegacyGoal]);

  return (
    <DeviceBasedRendering
      tablet={
        <LegacyPlanningSummaryTablet
          formData={legacyGoal}
          setFormData={updateLegacyGoal}
          isGroupCompleted={isGroupCompleted}
          onNextTab={onNextTab}
          onDone={onDone}
          summary={summary}
          isCompleted={isCompleted}
        />
      }
      phone={
        <LegacyPlanningSummaryPhone
          formData={legacyGoal}
          setFormData={updateLegacyGoal}
          isGroupCompleted={isGroupCompleted}
          onNextTab={onNextTab}
          onDone={onDone}
          summary={summary}
          isCompleted={isCompleted}
        />
      }
    />
  );
}
