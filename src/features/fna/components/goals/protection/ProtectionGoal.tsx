import { PictogramIcon } from 'cube-ui-components';
import {
  checkHealthProtectionCompletion,
  checkIncomeProtectionCompletion,
  checkLegacyPlanningCompletion,
  checkLoanCoverageCompletion,
} from 'features/fna/utils/helper/checkGoalCompletion';
import { FnaState, useFnaStore } from 'features/fna/utils/store/fnaStore';
import sortBy from 'lodash/sortBy';
import React, { useMemo, useReducer } from 'react';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import GoalGroup, { GoalGroupProps } from '../goalGroup/GoalGroup';
import HealthProtectionForm from './healthProtection/form/HealthProtectionForm';
import HealthProtectionSummary from './healthProtection/summary/HealthProtectionSummary';
import IncomeProtectionForm from './incomeProtection/form/IncomeProtectionForm';
import IncomeProtectionSummary from './incomeProtection/summary/IncomeProtectionSummary';
import LegacyPlanningForm from './legacyPlanning/form/LegacyPlanningForm';
import LegacyPlanningSummary from './legacyPlanning/summary/LegacyPlanningSummary';
import LoanCoverageForm from './loanCoverage/form/LoanCoverageForm';
import LoanCoverageSummary from './loanCoverage/summary/LoanCoverageSummary';
import { GoalKey } from 'features/fna/types/goal';
import { countryModuleFnaConfig } from 'utils/config/module';
import MoneyBagIcon from '../../icons/MoneyBagIcon';
import MedicineIcon from '../../icons/MedicineIcon';
import NuturingFinanceIcon from '../../icons/NuturingFinanceIcon';
import MoneyUnderUmbrellaIcon from '../../icons/MoneyUnderUmbrellaIcon';
import { useRootStackNRoute } from 'hooks/useRootStack';

export interface InternalProtectionGoalProps {
  tabPriority: GoalGroupProps<GoalKey>['tabs'];
  isGroupCompleted: boolean;
  onDone: () => void;
}

export default function ProtectionGoal() {
  const { t } = useTranslation(['fna']);
  const tab = useRootStackNRoute<'ProtectionGoal'>()?.params?.tab;

  const {
    storeIncomeGoal,
    updateStoreIncomeGoal,
    storeHealthGoal,
    updateStoreHealthGoal,
    storeLegacyGoal,
    updateStoreLegacyGoal,
    storeLoanGoal,
    updateStoreLoanGoal,
    concerns,
  } = useFnaStore(
    state => ({
      storeIncomeGoal: state.incomeProtectionGoal,
      updateStoreIncomeGoal: state.updateIncomeProtectionGoal,
      storeHealthGoal: state.healthProtectionGoal,
      updateStoreHealthGoal: state.updateHealthProtectionGoal,
      storeLegacyGoal: state.legacyPlanningGoal,
      updateStoreLegacyGoal: state.updateLegacyPlanningGoal,
      storeLoanGoal: state.loanCoverageGoal,
      updateStoreLoanGoal: state.updateLoanCoverageGoal,
      concerns: state.lifeJourney.concerns,
    }),
    shallow,
  );

  const [incomeGoal, setIncomeGoal] = useReducer(
    (
      state: FnaState['incomeProtectionGoal'],
      data: Partial<FnaState['incomeProtectionGoal']>,
    ) => ({
      ...state,
      ...data,
    }),
    storeIncomeGoal,
  );
  const [healthGoal, setHealthGoal] = useReducer(
    (
      state: FnaState['healthProtectionGoal'],
      data: Partial<FnaState['healthProtectionGoal']>,
    ) => ({
      ...state,
      ...data,
    }),
    storeHealthGoal,
  );
  const [legacyGoal, setLegacyGoal] = useReducer(
    (
      state: FnaState['legacyPlanningGoal'],
      data: Partial<FnaState['legacyPlanningGoal']>,
    ) => ({
      ...state,
      ...data,
    }),
    storeLegacyGoal,
  );
  const [loanGoal, setLoanGoal] = useReducer(
    (
      state: FnaState['loanCoverageGoal'],
      data: Partial<FnaState['loanCoverageGoal']>,
    ) => ({
      ...state,
      ...data,
    }),
    storeLoanGoal,
  );

  const onDone = () => {
    updateStoreIncomeGoal(incomeGoal);
    updateStoreHealthGoal(healthGoal);
    updateStoreLegacyGoal(legacyGoal);
    updateStoreLoanGoal(loanGoal);
  };

  const tabs = useMemo(() => {
    const concerns = Array.from(
      new Set([
        ...countryModuleFnaConfig.concerns.items,
        ...countryModuleFnaConfig.concerns.additionalItems,
      ]),
    );
    const dataTabs = concerns
      .map(concern => {
        switch (concern) {
          case 'INCOME_PROTECTION':
            return {
              id: 'INCOME_PROTECTION',
              type: 'incomeProtectionGoal',
              title: t('fna:protectIncome'),
              icon: MoneyBagIcon,
              form: IncomeProtectionForm,
              illustration: IncomeProtectionSummary,
              formData: incomeGoal,
              setFormData: setIncomeGoal,
            };
          case 'HEALTH_PROTECTION':
            return {
              id: 'HEALTH_PROTECTION',
              type: 'healthProtectionGoal',
              title: t('fna:healthProtection'),
              icon: MedicineIcon,
              form: HealthProtectionForm,
              illustration: HealthProtectionSummary,
              formData: healthGoal,
              setFormData: setHealthGoal,
            };
          case 'LEGACY_PLANNING':
            return {
              id: 'LEGACY_PLANNING',
              type: 'legacyPlanningGoal',
              title: t('fna:protectionGoals.legacyPlanning'),
              icon: NuturingFinanceIcon,
              form: LegacyPlanningForm,
              illustration: LegacyPlanningSummary,
              formData: legacyGoal,
              setFormData: setLegacyGoal,
            };
          case 'LOAN_PROTECTION':
            return {
              id: 'LOAN_PROTECTION',
              type: 'loanCoverageGoal',
              title: t('fna:protectionGoals.loan'),
              icon: MoneyUnderUmbrellaIcon,
              form: LoanCoverageForm,
              illustration: LoanCoverageSummary,
              formData: loanGoal,
              setFormData: setLoanGoal,
            };
        }
      })
      .filter(Boolean) as GoalGroupProps<GoalKey>['tabs'];

    return dataTabs;
  }, [healthGoal, incomeGoal, legacyGoal, loanGoal, t]);

  const tabPriority = useMemo(() => {
    if (concerns.length > 0) {
      return sortBy(tabs, item => {
        if (concerns.includes(item.id)) {
          return concerns.indexOf(item.id);
        } else {
          return 10;
        }
      });
    }
    return tabs;
  }, [concerns, tabs]);

  const isGroupCompleted = useMemo(() => {
    let isCompleted = true;
    countryModuleFnaConfig.concerns.items.forEach(concern => {
      switch (concern) {
        case 'INCOME_PROTECTION':
          isCompleted &&= checkIncomeProtectionCompletion(incomeGoal);
          break;
        case 'HEALTH_PROTECTION':
          isCompleted &&= checkHealthProtectionCompletion(healthGoal);
          break;
        case 'LEGACY_PLANNING':
          isCompleted &&= checkLegacyPlanningCompletion(legacyGoal);
          break;
        case 'LOAN_PROTECTION':
          isCompleted &&= checkLoanCoverageCompletion(loanGoal);
          break;
        default:
          break;
      }
    });
    return isCompleted;
  }, [healthGoal, incomeGoal, legacyGoal, loanGoal]);

  return (
    <GoalGroup
      tabs={tabPriority}
      onDone={onDone}
      title={t('fna:protectionGoals.title')}
      isGroupCompleted={isGroupCompleted}
      scrollable
      initialTab={tab}
    />
  );
}
