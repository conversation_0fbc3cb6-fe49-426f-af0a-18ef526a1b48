import React, { memo, useCallback, useState } from 'react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import {
  Expense,
  FnaState,
} from 'features/fna/utils/store/fnaStore';
import FamilyExpenseCalculatorPhone from './FamilyExpenseCalculator.phone';
import FamilyExpenseCalculatorTablet from './FamilyExpenseCalculator.tablet';
import { Icon } from 'cube-ui-components';
import { calculateTotalExpense } from 'features/fna/utils/helper/fnaUtils';
import { ExpenseType } from 'types/case';

export const ICON_BY_EXPENSE_KEY = {
  [ExpenseType.Housing]: Icon.House,
  [ExpenseType.Transportation]: Icon.Transportation,
  [ExpenseType.Travel]: Icon.Travel,
  [ExpenseType.RegularCommitment]: Icon.Wallet,
  [ExpenseType.Food]: Icon.Food,
  [ExpenseType.Healthcare]: Icon.Health,
  [ExpenseType.Other]: Icon.Gift,
};

export interface InternalFamilyExpenseCalculatorProps {
  handleClose: () => void;
  onDone: (
    result: Pick<
      FnaState['incomeProtectionGoal'],
      'monthlyExpenses' | 'expenses'
    >,
  ) => void;
  total: number | null;
  expenses: Expense[];
  updateTotal: (expenses: Expense[]) => void;
  visible?: boolean;
}

interface Props {
  handleClose: () => void;
  onDone: (
    result: Pick<
      FnaState['incomeProtectionGoal'],
      'monthlyExpenses' | 'expenses'
    >,
  ) => void;
  visible?: boolean;
  expenses: Expense[];
}

export default memo(function FamilyExpenseCalculator({
  handleClose,
  onDone,
  visible,
  expenses: expensesProp,
}: Props) {
  const [expenses, setExpenses] = useState(expensesProp);

  const [total, setTotal] = useState<number | null>(() =>
    calculateTotalExpense(expensesProp),
  );

  const updateTotal = useCallback(
    (expenses: Expense[]) => {
      setExpenses(expenses);
      setTotal(calculateTotalExpense(expenses));
    },
    [],
  );

  return (
    <DeviceBasedRendering
      tablet={
        <FamilyExpenseCalculatorTablet
          handleClose={handleClose}
          onDone={onDone}
          visible={visible}
          total={total}
          expenses={expenses}
          updateTotal={updateTotal}
        />
      }
      phone={
        <FamilyExpenseCalculatorPhone
          handleClose={handleClose}
          onDone={onDone}
          expenses={expenses}
          total={total}
          updateTotal={updateTotal}
        />
      }
    />
  );
});
