import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  H6,
  LargeLabel,
  RadioButton,
  RadioButtonGroup,
  Row,
} from 'cube-ui-components';
import CalculatorIcon from 'features/fna/components/icons/CalculatorIcon';
import { booleanToYesNoValue } from 'features/fna/utils/helper/fnaUtils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { InternalIncomeProtectionFormProps } from '../IncomeProtectionForm';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import DecimalTextField from 'components/DecimalTextField';
import IllustrationIncomeProtectionIcon from 'features/fna/components/illustrations/IllustrationIncomeProtectionIcon';
import FamilyExpenseCalculator from '../../calculator/FamilyExpenseCalculator';

export function PHIncomeProtectionFormTablet({
  formData: incomeGoal,
  onCalculatorPress,
  updateMonthlyExpenses,
  updateHasSavings,
  updateCoverageAmount,
  updateMonthlyExpensesCalculation,
  calculatorVisible,
  hideCalculator,
}: InternalIncomeProtectionFormProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors, borderRadius } = useTheme();

  return (
    <Box>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:protectionGoals.income.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <IllustrationIncomeProtectionIcon />
        </Box>
      </Row>
      <Content>
        <Row>
          <Row flex={1.8} alignItems="center">
            <Row flex={1}>
              <FieldLabel>1.</FieldLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:protectionGoals.income.monthlyExpenses')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <TouchableOpacity
              style={{
                alignSelf: 'center',
                borderWidth: 2,
                borderRadius: borderRadius.full,
                borderColor: colors.palette.fwdOrange[100],
                padding: space[2],
              }}
              onPress={onCalculatorPress}>
              <CalculatorIcon />
            </TouchableOpacity>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            value={incomeGoal.monthlyExpenses}
            onChange={updateMonthlyExpenses}
            style={{ flex: 1 }}
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
        <Row mt={space[5]} alignItems="center">
          <Row flex={1.8}>
            <FieldLabel>2.</FieldLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:protectionGoals.income.existingPolicies')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <Row flex={1} justifyContent="space-between">
            <RadioButtonGroup
              value={booleanToYesNoValue(incomeGoal.hasSavings)}
              onChange={updateHasSavings}>
              <RadioButton
                style={{ flex: 1 }}
                label={t('fna:yes')}
                value="yes"
              />
              <RadioButton style={{ flex: 1 }} label={t('fna:no')} value="no" />
            </RadioButtonGroup>
          </Row>
        </Row>
        {incomeGoal.hasSavings && (
          <Row mt={space[7]} alignItems="center">
            <Row flex={1.8} alignSelf="center">
              <FieldLabel>2.1.</FieldLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:protectionGoals.income.howMuch')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              value={incomeGoal.coverageAmount}
              onChange={updateCoverageAmount}
              style={{ flex: 1 }}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
        )}
        <FamilyExpenseCalculator
          onDone={updateMonthlyExpensesCalculation}
          handleClose={hideCalculator}
          expenses={incomeGoal.expenses}
          visible={calculatorVisible}
        />
      </Content>
    </Box>
  );
}

const Content = styled(View)(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
