import { GoalFormProps } from 'features/fna/types/goal';
import { yesNoValueToBoolean } from 'features/fna/utils/helper/fnaUtils';
import { FnaState } from 'features/fna/utils/store/fnaStore';
import useToggle from 'hooks/useToggle';
import React, { useCallback } from 'react';
import { Keyboard } from 'react-native';
import { country } from 'utils/context';
import IBIncomeProtectionForm from './ib/IBIncomeProtectionForm';
import PHIncomeProtectionForm from './ph/PHIncomeProtectionForm';

export interface InternalIncomeProtectionFormProps
  extends GoalFormProps<FnaState['incomeProtectionGoal']> {
  onCalculatorPress: () => void;
  updateMonthlyExpenses: (monthlyExpenses: number | null) => void;
  updateHasSavings: (value: string) => void;
  updateCoverageAmount: (coverageAmount: number | null) => void;
  calculatorVisible: boolean;
  updateMonthlyExpensesCalculation: (
    result: Pick<
      FnaState['incomeProtectionGoal'],
      'monthlyExpenses' | 'expenses'
    >,
  ) => void;
  hideCalculator: () => void;
  updateAnnualIncome?: (value: number | null) => void;
}

export default function IncomeProtectionForm({
  formData: incomeGoal,
  setFormData: updateIncomeGoal,
}: GoalFormProps<FnaState['incomeProtectionGoal']>) {
  const updateMonthlyExpenses = useCallback(
    (monthlyExpenses: number | null) => {
      updateIncomeGoal({ monthlyExpenses: monthlyExpenses });
    },
    [updateIncomeGoal],
  );

  const updateAnnualIncome = (annualIncome: number | null) => {
    updateIncomeGoal({ annualIncome });
  };

  const updateMonthlyExpensesCalculation = useCallback(
    (result: Pick<FnaState['incomeProtectionGoal'], 'expenses'>) => {
      updateIncomeGoal(result);
    },
    [updateIncomeGoal],
  );

  const updateHasSavings = useCallback(
    (value: string) => {
      const hasSavings = yesNoValueToBoolean(value);
      if (hasSavings !== incomeGoal.hasSavings) {
        updateIncomeGoal({
          hasSavings,
          coverageAmount: null,
        });
      }
    },
    [updateIncomeGoal, incomeGoal.hasSavings],
  );

  const updateCoverageAmount = useCallback(
    (coverageAmount: number | null) => {
      updateIncomeGoal({ coverageAmount });
    },
    [updateIncomeGoal],
  );

  const [calculatorVisible, showCalculator, hideCalculator] = useToggle(false);

  const onCalculatorPress = useCallback(() => {
    Keyboard.dismiss();
    showCalculator();
  }, [showCalculator]);

  return (
    <>
      {Form && (
        <Form
          formData={incomeGoal}
          setFormData={updateIncomeGoal}
          onCalculatorPress={onCalculatorPress}
          updateMonthlyExpenses={updateMonthlyExpenses}
          updateHasSavings={updateHasSavings}
          updateCoverageAmount={updateCoverageAmount}
          updateMonthlyExpensesCalculation={updateMonthlyExpensesCalculation}
          calculatorVisible={calculatorVisible}
          hideCalculator={hideCalculator}
          updateAnnualIncome={updateAnnualIncome}
        />
      )}
    </>
  );
}

const Form = {
  ib: IBIncomeProtectionForm,
  ph: PHIncomeProtectionForm,
  my: IBIncomeProtectionForm,
  id: IBIncomeProtectionForm,
}[country];
