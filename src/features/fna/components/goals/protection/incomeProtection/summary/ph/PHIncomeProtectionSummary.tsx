import React from 'react';
import { useTranslation } from 'react-i18next';
import GoalSummary from '../../../../goalSummary/GoalSummary';
import { IncomeProtectionSummaryProps } from '../IncomeProtectionSummary';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { PHIncomeProtectionSummaryTablet } from './PHIncomeProtectionSummary.tablet';
import { PHIncomeProtectionSummaryPhone } from './PHIncomeProtectionSummary.phone';

export default function PHIncomeProtectionSummary({
  isGroupCompleted,
  onNextTab,
  onDone,
  summary,
  isCompleted,
  formData,
  setFormData,
}: IncomeProtectionSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);

  return (
    <GoalSummary
      title={t('fna:protectionGoals.income.totalFunds')}
      summary={summary}
      isCompleted={isCompleted}
      isGroupCompleted={isGroupCompleted}
      onNext={onNextTab}
      onDone={onDone}>
      <DeviceBasedRendering
        tablet={
          <PHIncomeProtectionSummaryTablet
            formData={formData}
            setFormData={setFormData}
            isGroupCompleted={isGroupCompleted}
            onNextTab={onNextTab}
            onDone={onDone}
            summary={summary}
            isCompleted={isCompleted}
          />
        }
        phone={
          <PHIncomeProtectionSummaryPhone
            formData={formData}
            setFormData={setFormData}
            isGroupCompleted={isGroupCompleted}
            onNextTab={onNextTab}
            onDone={onDone}
            summary={summary}
            isCompleted={isCompleted}
          />
        }
      />
    </GoalSummary>
  );
}
