import { useTheme } from '@emotion/react';
import { Body, Box, Icon, Row } from 'cube-ui-components';
import FemaleIncomeLogo from 'features/fna/components/illustrations/FemaleIncomeLogo';
import RichFemaleIncomeLogo from 'features/fna/components/illustrations/RichFemaleIncomeLogo';
import { currencyKFormat } from 'features/fna/utils/helper/fnaUtils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { IncomeProtectionSummaryProps } from '../IncomeProtectionSummary';

export function PHIncomeProtectionSummaryTablet({
  isGroupCompleted,
  onNextTab,
  onDone,
  summary,
  isCompleted,
}: IncomeProtectionSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  const { space, colors, sizes } = useTheme();

  return (
    <Row
      px={space[2]}
      pt={space[2]}
      alignItems="center"
      justifyContent="space-around">
      <Box
        flex={1}
        alignSelf="stretch"
        alignItems="center"
        justifyContent="flex-end">
        <Body fontWeight="bold" color={colors.onBackground}>
          {t('common:withCurrency', {
            amount:
              typeof summary.coverage.value === 'number'
                ? currencyKFormat(summary.coverage.value)
                : '--',
          })}
        </Body>
        <Box h={space[1]} />
        <Body color={colors.onBackground}>{t('fna:current')}</Body>
        <Box h={space[1] / 2} />
        <FemaleIncomeLogo />
      </Box>
      <Icon.ChevronRight
        size={sizes[4] + 2}
        fill={colors.palette.fwdGreyDark}
      />
      <Box
        flex={1}
        alignSelf="stretch"
        alignItems="center"
        justifyContent="flex-end">
        <Body fontWeight="bold" color={colors.onBackground}>
          {t('common:withCurrency', {
            amount:
              typeof summary.target.value === 'number'
                ? currencyKFormat(summary.target.value)
                : '--',
          })}
        </Body>
        <Box h={space[1]} />
        <Body color={colors.onBackground}>{t('fna:target')}</Body>
        <Box h={space[1] / 2} />
        <RichFemaleIncomeLogo />
      </Box>
    </Row>
  );
}
