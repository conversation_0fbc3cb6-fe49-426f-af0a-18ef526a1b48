import { useTheme } from '@emotion/react';
import { Body, Box, Icon, Row } from 'cube-ui-components';
import FemaleIncomeLogo from 'features/fna/components/illustrations/FemaleIncomeLogo';
import RichFemaleIncomeLogo from 'features/fna/components/illustrations/RichFemaleIncomeLogo';
import {
  currencyKFormat,
  getDisplayYear,
} from 'features/fna/utils/helper/fnaUtils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { IncomeProtectionSummaryProps } from '../IncomeProtectionSummary';

export function IBIncomeProtectionSummaryTablet({
  isGroupCompleted,
  onNextTab,
  onDone,
  summary,
  isCompleted,
  formData,
}: IncomeProtectionSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  const { space, colors, sizes } = useTheme();

  return (
    <Row px={space[2]} pt={space[2]} justifyContent="space-around">
      <Box alignSelf="stretch" alignItems="center" justifyContent="flex-end">
        <Body color={colors.secondaryVariant}>
          {t('common:withCurrency', {
            amount:
              typeof summary.coverage.value === 'number'
                ? currencyKFormat(summary.coverage.value)
                : '--',
          })}
        </Body>
        <Box h={space[1]} />
        <Body color={colors.secondaryVariant}>
          {t('fna:protectionGoals.invest')}
        </Body>
        <Box h={space[1] / 2} />
        <FemaleIncomeLogo />
      </Box>
      <Box pt={space[4]}>
        <Icon.ChevronRight
          size={sizes[4] + 2}
          fill={colors.palette.fwdGreyDark}
        />
      </Box>
      <Box flex={1} alignItems="center">
        <Body fontWeight="bold" color={colors.onBackground}>
          {getDisplayYear(formData.yearsToReplace, t)}
        </Body>
      </Box>
      <Box pt={space[4]}>
        <Icon.ChevronRight
          size={sizes[4] + 2}
          fill={colors.palette.fwdGreyDark}
        />
      </Box>
      <Box alignSelf="stretch" alignItems="center" justifyContent="flex-end">
        <Body color={colors.secondaryVariant}>
          {t('common:withCurrency', {
            amount:
              typeof summary.target.value === 'number'
                ? currencyKFormat(summary.target.value)
                : '--',
          })}
        </Body>
        <Box h={space[1]} />
        <Body color={colors.secondaryVariant}>
          {t('fna:protectionGoals.invest')}
        </Body>
        <Box h={space[1] / 2} />
        <RichFemaleIncomeLogo />
      </Box>
    </Row>
  );
}
