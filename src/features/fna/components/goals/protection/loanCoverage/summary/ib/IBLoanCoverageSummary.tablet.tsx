import { useTheme } from '@emotion/react';
import { Body, Box, Icon, Row } from 'cube-ui-components';
import {
  currencyKFormat,
  getDisplayYear,
} from 'features/fna/utils/helper/fnaUtils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import FemaleIncomeLogo from 'features/fna/components/illustrations/FemaleIncomeLogo';
import RichFemaleIncomeLogo from 'features/fna/components/illustrations/RichFemaleIncomeLogo';
import type { LoanCoverageSummaryProps } from '../LoanCoverageSummary';

export default function IBLoanCoverageSummary({
  summary,
  formData,
}: LoanCoverageSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  const { space, colors, sizes } = useTheme();
  return (
    <Row px={space[2]} pt={space[2]} justifyContent="space-around">
      <Box alignSelf="stretch" alignItems="center" justifyContent="flex-end">
        <Body color={colors.palette.fwdDarkGreen[50]}>
          {t('common:withCurrency', {
            amount:
              typeof summary.coverage.value === 'number'
                ? currencyKFormat(summary.coverage.value)
                : '--',
          })}
        </Body>
        <Box h={space[1]} />
        <Body color={colors.palette.fwdDarkGreen[50]}>
          {t('fna:protectionGoals.invest')}
        </Body>
        <Box h={space[1] / 2} />
        <FemaleIncomeLogo />
      </Box>
      <Box pt={space[4]}>
        <Icon.ChevronRight
          size={sizes[4] + 2}
          fill={colors.palette.fwdGreyDark}
        />
      </Box>
      <Box flex={1} alignItems="center">
        <Body fontWeight="bold" color={colors.palette.fwdDarkGreen[100]}>
          {getDisplayYear(formData.loanTerm, t)}
        </Body>
      </Box>
      <Box pt={space[4]}>
        <Icon.ChevronRight
          size={sizes[4] + 2}
          fill={colors.palette.fwdGreyDark}
        />
      </Box>
      <Box alignSelf="stretch" alignItems="center" justifyContent="flex-end">
        <Body color={colors.palette.fwdDarkGreen[50]}>
          {t('common:withCurrency', {
            amount:
              typeof summary.target.value === 'number'
                ? currencyKFormat(summary.target.value)
                : '--',
          })}
        </Body>
        <Box h={space[1]} />
        <Body color={colors.palette.fwdDarkGreen[50]}>
          {t('fna:protectionGoals.invest')}
        </Body>
        <Box h={space[1] / 2} />
        <RichFemaleIncomeLogo />
      </Box>
    </Row>
  );
}
