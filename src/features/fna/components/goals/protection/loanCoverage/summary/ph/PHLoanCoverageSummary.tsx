import React from 'react';
import { useTranslation } from 'react-i18next';
import GoalSummary from '../../../../goalSummary/GoalSummary';
import type { LoanCoverageSummaryProps } from '../LoanCoverageSummary';
import LoanCoverageSummaryPhone from './PHLoanCoverageSummary.phone';
import LoanCoverageSummaryTablet from './PHLoanCoverageSummary.tablet';
import DeviceBasedRendering from 'components/DeviceBasedRendering';

export default function PHLoanCoverageSummary({
  isGroupCompleted,
  onNextTab,
  onDone,
  summary,
  isCompleted,
  formData,
  setFormData,
}: LoanCoverageSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  return (
    <GoalSummary
      title={t('fna:protectionGoals.loan.totalFunds')}
      summary={summary}
      isCompleted={isCompleted}
      isGroupCompleted={isGroupCompleted}
      onNext={onNextTab}
      onDone={onDone}>
      <DeviceBasedRendering
        tablet={
          <LoanCoverageSummaryTablet
            isGroupCompleted={isGroupCompleted}
            onNextTab={onNextTab}
            onDone={onDone}
            summary={summary}
            isCompleted={isCompleted}
            formData={formData}
            setFormData={setFormData}
          />
        }
        phone={
          <LoanCoverageSummaryPhone
            isGroupCompleted={isGroupCompleted}
            onNextTab={onNextTab}
            onDone={onDone}
            summary={summary}
            isCompleted={isCompleted}
            formData={formData}
            setFormData={setFormData}
          />
        }
      />
    </GoalSummary>
  );
}
