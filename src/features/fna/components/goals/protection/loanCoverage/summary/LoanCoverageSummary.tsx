import { GoalSummaryProps, Summary } from 'features/fna/types/goal';
import { checkLoanCoverageCompletion } from 'features/fna/utils/helper/checkGoalCompletion';
import { FnaState } from 'features/fna/utils/store/fnaStore';
import React, { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { country } from 'utils/context';
import IBLoanCoverageSummary from './ib/IBLoanCoverageSummary';
import PHLoanCoverageSummary from './ph/PHLoanCoverageSummary';

export interface LoanCoverageSummaryProps
  extends GoalSummaryProps<FnaState['loanCoverageGoal']> {
  summary: Summary;
  isCompleted: boolean;
}

export default function LoanCoverageSummary({
  formData: loanGoal,
  setFormData: updateLoanGoal,
  isGroupCompleted,
  onNextTab,
  onDone,
}: GoalSummaryProps<FnaState['loanCoverageGoal']>) {
  const { t } = useTranslation(['fna']);

  const isCompleted = checkLoanCoverageCompletion(loanGoal);

  const targetAmount = useMemo(() => {
    if (!loanGoal.enabled) return 0;
    switch (country) {
      case 'my':
      case 'ib':
        if (typeof loanGoal.loanAmount !== 'number') return null;
        if (typeof loanGoal.loanTerm !== 'number') return null;

        return loanGoal.loanAmount * loanGoal.loanTerm;
      case 'ph':
        return loanGoal.targetAmount;
      default:
        return null;
    }
  }, [
    loanGoal.enabled,
    loanGoal.loanAmount,
    loanGoal.loanTerm,
    loanGoal.targetAmount,
  ]);

  const gap = useMemo(() => {
    if (!loanGoal.enabled) return 0;
    switch (country) {
      case 'my':
      case 'ib':
        if (typeof targetAmount !== 'number') return null;
        if (loanGoal.coverageAmount) {
          return Math.max(0, targetAmount - loanGoal.coverageAmount);
        } else {
          return targetAmount;
        }
      case 'ph':
        if (typeof loanGoal.targetAmount !== 'number') return null;
        if (loanGoal.coverageAmount) {
          return Math.max(0, loanGoal.targetAmount - loanGoal.coverageAmount);
        } else {
          return loanGoal.targetAmount;
        }
      default:
        return null;
    }
  }, [
    loanGoal.enabled,
    loanGoal.coverageAmount,
    loanGoal.targetAmount,
    targetAmount,
  ]);

  const summary = useMemo(
    () => ({
      enabled: loanGoal.enabled,
      target: {
        title: t('fna:protectionGoals.loan.totalNeeds'),
        value: targetAmount,
      },
      coverage: {
        title: t('fna:protectionGoals.totalCurrentCoverage'),
        value: loanGoal.coverageAmount,
      },
      gap: {
        title: t('fna:protectionGoals.loan.totalGap'),
        value: gap,
      },
    }),
    [loanGoal.enabled, loanGoal.coverageAmount, t, targetAmount, gap],
  );

  useEffect(() => {
    updateLoanGoal({ targetAmount: targetAmount });
  }, [targetAmount, updateLoanGoal]);

  useEffect(() => {
    updateLoanGoal({ gapAmount: gap });
  }, [gap, updateLoanGoal]);

  return (
    <>
      {Form && (
        <Form
          formData={loanGoal}
          setFormData={updateLoanGoal}
          isGroupCompleted={isGroupCompleted}
          onNextTab={onNextTab}
          onDone={onDone}
          summary={summary}
          isCompleted={isCompleted}
        />
      )}
    </>
  );
}

const Form = {
  ib: IBLoanCoverageSummary,
  ph: PHLoanCoverageSummary,
  my: IBLoanCoverageSummary,
  id: null,
}[country];
