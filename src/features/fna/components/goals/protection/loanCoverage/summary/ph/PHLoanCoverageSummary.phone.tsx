import { useTheme } from '@emotion/react';
import { View } from 'react-native';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { Body, Box, Column, Icon, Row } from 'cube-ui-components';
import {
  currencyKFormat,
} from 'features/fna/utils/helper/fnaUtils';
import FemaleCoin from 'features/fna/components/illustrations/FemaleCoin';
import TargetMoney from 'features/fna/components/illustrations/TargetMoney';
import type { LoanCoverageSummaryProps } from '../LoanCoverageSummary';

export default function PHLoanCoverageSummary({
  isGroupCompleted,
  onNextTab,
  onDone,
  summary,
  isCompleted,
  formData,
}: LoanCoverageSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  const { space, colors, sizes } = useTheme();
  
  return (
    <Box
      flex={1}
      px={space[2]}
      pt={space[2]}
      justifyContent="space-between"
      backgroundColor={colors.palette.white}>
      <Column>
        <Row alignItems="center" justifyContent="space-around">
          <Box alignItems="center">
            <Body color={colors.secondary} fontWeight="bold">
              {t('common:withCurrency', { amount: 0 })}
            </Body>
            <Body color={colors.secondary}>{t('fna:current')}</Body>
          </Box>
          <Icon.ChevronRight
            size={sizes[4]}
            fill={colors.palette.fwdGreyDark}
          />
          <Box alignItems="center">
            <Body color={colors.secondary} fontWeight="bold">
              {t('common:withCurrency', {
                amount:
                  typeof summary.target.value === 'number'
                    ? currencyKFormat(summary.target.value)
                    : '--',
              })}
            </Body>
            <Body color={colors.secondary}>{t('fna:target')}</Body>
          </Box>
        </Row>
        <Row alignItems="center" justifyContent="space-around">
          <FemaleCoin />
          <View />
          <TargetMoney />
        </Row>
      </Column>
    </Box>
  );
}
