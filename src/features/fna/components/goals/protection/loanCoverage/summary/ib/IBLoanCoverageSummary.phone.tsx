import { useTheme } from '@emotion/react';
import { Body, Box, Column, Icon, Row } from 'cube-ui-components';
import {
  currencyKFormat,
  getDisplayYear,
} from 'features/fna/utils/helper/fnaUtils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import FemaleIncomeLogo from 'features/fna/components/illustrations/FemaleIncomeLogo';
import RichFemaleIncomeLogo from 'features/fna/components/illustrations/RichFemaleIncomeLogo';
import type { LoanCoverageSummaryProps } from '../LoanCoverageSummary';

export default function IBLoanCoverageSummary({
  isGroupCompleted,
  onNextTab,
  onDone,
  summary,
  isCompleted,
  formData,
}: LoanCoverageSummaryProps) {
  const { t } = useTranslation(['common', 'fna']);
  const { space, colors, sizes } = useTheme();

  return (
    <Box
      flex={1}
      px={space[2]}
      pt={space[2]}
      justifyContent="space-between"
      backgroundColor={colors.palette.white}>
      <Row flex={1} justifyContent="space-between">
        <Box
          flex={1}
          alignSelf="stretch"
          justifyContent="flex-end"
          alignItems="center">
          <Body color={colors.palette.fwdDarkGreen[50]}>
            {t('common:withCurrency', {
              amount:
                typeof summary.coverage.value === 'number'
                  ? currencyKFormat(summary.coverage.value)
                  : '--',
            })}
          </Body>
          <Body color={colors.palette.fwdDarkGreen[50]}>
            {t('fna:protectionGoals.invest')}
          </Body>
          <FemaleIncomeLogo />
        </Box>
        <Box pt={space[4]}>
          <Icon.ChevronRight
            size={sizes[4] + 2}
            fill={colors.palette.fwdGreyDark}
          />
        </Box>
        <Box flex={1} alignItems="center" mt={space[3]}>
          <Body fontWeight="bold" color={colors.palette.fwdDarkGreen[100]}>
            {getDisplayYear(formData.loanTerm, t)}
          </Body>
        </Box>
        <Box pt={space[4]}>
          <Icon.ChevronRight
            size={sizes[4] + 2}
            fill={colors.palette.fwdGreyDark}
          />
        </Box>
        <Box
          flex={1}
          alignSelf="stretch"
          justifyContent="flex-end"
          alignItems="center">
          <Body color={colors.palette.fwdDarkGreen[50]}>
            {t('common:withCurrency', {
              amount:
                typeof summary.target.value === 'number'
                  ? currencyKFormat(summary.target.value)
                  : '--',
            })}
          </Body>
          <Body color={colors.palette.fwdDarkGreen[50]}>
            {t('fna:protectionGoals.invest')}
          </Body>
          <RichFemaleIncomeLogo />
        </Box>
      </Row>
    </Box>
  );
}
