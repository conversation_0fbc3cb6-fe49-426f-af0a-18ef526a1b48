import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import { Box, H6, LargeLabel, Row, TextField } from 'cube-ui-components';
import DecimalTextField from 'components/DecimalTextField';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import IllustrationLoanCoverageIcon from 'features/fna/components/illustrations/IllustrationLoanCoverageIcon';
import type { PHLoanCoverageFormProps } from './PHLoanCoverageForm';

export default function PHLoanCoverageForm({
  formData: loanGoal,
  updateLoanAmount,
  updateLoanYear,
}: PHLoanCoverageFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();

  return (
    <Box>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:protectionGoals.loan.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <IllustrationLoanCoverageIcon />
        </Box>
      </Row>
      <Content>
        <Row alignItems="center">
          <Row flex={1.8}>
            <FieldLabel>1.</FieldLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:protectionGoals.loan.question1')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            style={{ flex: 1 }}
            value={loanGoal.targetAmount}
            onChange={updateLoanAmount}
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
        <Row mt={space[3]} alignItems="center">
          <Row flex={1.8}>
            <FieldLabel>2.</FieldLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:protectionGoals.loan.question2')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <TextField
            label={t('fna:protectionGoals.loan.years')}
            value={String(loanGoal.loanTerm ?? '')}
            keyboardType="number-pad"
            returnKeyType="done"
            maxLength={2}
            style={{ flex: 1 }}
            onChange={updateLoanYear}
          />
        </Row>
      </Content>
    </Box>
  );
}

const Content = styled(View)(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
