import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  H6,
  LargeBody,
  LargeLabel,
  Row,
  TextField,
} from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import DecimalTextField from 'components/DecimalTextField';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import IllustrationLoanCoverageIcon from 'features/fna/components/illustrations/IllustrationLoanCoverageIcon';
import type { IBLoanCoverageFormProps } from './IBLoanCoverageForm';

export default function IBLoanCoverageForm({
  formData: loanGoal,
  updateLoanAmount,
  updateLoanYear,
  updateSkipThisGoal,
  updateCoverageAmount,
  updateHasCI,
  updateCIProtectionAmount,
}: IBLoanCoverageFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();

  return (
    <Box>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:protectionGoals.loan.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <IllustrationLoanCoverageIcon />
        </Box>
      </Row>
      <Box py={space[1]} mt={-32} mb={space[3]}>
        <Checkbox
          label={t('fna:goal.not.have.debt')}
          checked={!loanGoal.enabled}
          onChange={updateSkipThisGoal}
        />
      </Box>
      <Content>
        <FormContainer
          enabled={Boolean(loanGoal.enabled)}
          pointerEvents={loanGoal.enabled ? 'auto' : 'none'}>
          <Row alignItems="center">
            <Row flex={1.8}>
              <FieldLabel>1.</FieldLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:protectionGoals.debt.outstanding.loan')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={loanGoal.loanAmount}
              onChange={updateLoanAmount}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <Row mt={space[4]} alignItems="center">
            <Row flex={1.8}>
              <FieldLabel>2.</FieldLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:protectionGoals.debt.years.loan.settlement')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <TextField
              label={t('fna:protectionGoals.loan.years')}
              value={String(loanGoal.loanTerm ?? '')}
              keyboardType="number-pad"
              returnKeyType="done"
              maxLength={2}
              style={{ flex: 1 }}
              onChange={updateLoanYear}
            />
          </Row>
          <Row mt={space[4]} alignItems="center">
            <Row flex={1.8}>
              <FieldLabel>3.</FieldLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:protectionGoals.debt.existing.cancellation')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={loanGoal.coverageAmount}
              onChange={updateCoverageAmount}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <Box mt={space[6]} mx={space[2]}>
            <Checkbox
              label={t('fna:protectionGoals.debt.has.ci')}
              checked={!!loanGoal.hasCIProtection}
              onChange={updateHasCI}
            />
          </Box>
          {!!loanGoal.hasCIProtection && (
            <Row mt={space[4]} alignItems="center">
              <Row flex={1.8}>
                <FieldLabel>4.</FieldLabel>
                <LargeLabel style={{ flex: 1 }}>
                  {t('fna:protectionGoals.debt.ci.protection')}
                </LargeLabel>
              </Row>
              <Box w={space[3]} />
              <DecimalTextField
                label={t('fna:amount.label')}
                style={{ flex: 1 }}
                value={loanGoal.ciProtectionAmount}
                onChange={updateCIProtectionAmount}
                max={MAX_AMOUNT_VALUE}
              />
            </Row>
          )}
        </FormContainer>
      </Content>
      <Box mt={space[4]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </Box>
  );
}

const Content = styled(View)(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
