import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Button } from 'cube-ui-components';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { MainTabParamList, RootStackParamList } from 'types';
import { useCheckFnaCompletion } from '../utils/helper/checkFnaCompletion';
import { useFnaStore } from '../utils/store/fnaStore';

export default ({
  isLoading,
  isCalculatingGoal,
  onPressBeforeCalculation,
  onPressAfterCalculation,
}: {
  isLoading: boolean;
  isCalculatingGoal: boolean;
  onPressBeforeCalculation: () => void;
  onPressAfterCalculation: () => void;
}) => {
  const { t } = useTranslation(['fna']);

  const fnaState = useFnaStore();
  const { getFnaCompletion } = fnaState;
  const shouldShowRecommendation = useCheckFnaCompletion();

  const navigation =
    useNavigation<NavigationProp<RootStackParamList & MainTabParamList>>();

  const previousRouteName = useMemo(() => {
    const state = navigation.getState();

    if (state && state.routes && state.routes.length > 1) {
      const previousRoute = state.routes[state.routes.length - 2];
      return previousRoute.name;
    }

    return null; // No previous route
  }, [navigation]);

  const source = useMemo(() => {
    switch (previousRouteName) {
      case 'SalesIllustrationForm':
        return 'sales_illustration';
      case 'LeadProfile':
        return 'menu_leads_and_customers';
      case 'Main':
      case 'Home':
        return 'homepage_cta';
    }
  }, [previousRouteName]);

  if (!isCalculatingGoal) {
    return (
      <Button
        variant="primary"
        onPress={onPressBeforeCalculation}
        text={t('fna:continue')}
        style={{
          width: 200,
        }}
        loading={isLoading}
        disabled={!shouldShowRecommendation}
      />
    );
  }

  return (
    <Button
      variant="primary"
      onPress={onPressAfterCalculation}
      text={t('fna:lifeStage.recommend.plans')}
      style={{
        minWidth: 200,
      }}
      loading={isLoading}
      disabled={!shouldShowRecommendation || !getFnaCompletion()}
      gaParams={{
        eventType: 'fn_assessment',
        actionType: 'fna_view_plans',
        formSource: source || '',
      }}
    />
  );
};
