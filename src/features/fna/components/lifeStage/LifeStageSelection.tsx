import DeviceBasedRendering from 'components/DeviceBasedRendering';
import CoupleLogo from 'features/fna/components/illustrations/CoupleLogo';
import CoupleWithKidLogo from 'features/fna/components/illustrations/CoupleWithKidLogo';
import EmptyNesterCoupleLogo from 'features/fna/components/illustrations/EmptyNesterCoupleLogo';
import EmptyNesterFemaleLogo from 'features/fna/components/illustrations/EmptyNesterFemaleLogo';
import EmptyNesterMaleLogo from 'features/fna/components/illustrations/EmptyNesterMaleLogo';
import RetireLogo from 'features/fna/components/illustrations/RetireLogo';
import SingleFemaleLogo from 'features/fna/components/illustrations/SingleFemaleLogo';
import SingleMaleLogo from 'features/fna/components/illustrations/SingleMaleLogo';
import SingleWithDependFemaleLogo from 'features/fna/components/illustrations/SingleWithDependFemaleLogo';
import SingleWithDependMaleLogo from 'features/fna/components/illustrations/SingleWithDependMaleLogo';
import { LifeStage, LifeStageItemType } from 'features/fna/types/lifeJourney';
import {
  initialFnaState,
  useFnaStore,
} from 'features/fna/utils/store/fnaStore';
import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Gender } from 'types/person';
import { countryModuleFnaConfig } from 'utils/config/module';
import { shallow } from 'zustand/shallow';
import { ReadonlyProps } from '../lifeJourney/tablet/LifeJourneySummary.tablet';
import LifeStageSelectionPhone from './phone/LifeStageSelection.phone';
import LifeStageSelectionTablet from './tablet/LifeStageSelection.tablet';

export interface InternalLifeStageSelectionProps extends ReadonlyProps {
  onSelect: (item: LifeStageItemType | null) => void;
  data: LifeStageItemType[];
  titleVisible?: boolean;
  orderNumber?: number;
}

const LifeStageSelection = ({
  onSelect,
  readonly,
  titleVisible = true,
  orderNumber,
}: {
  onSelect?: () => void;
  titleVisible?: boolean;
  orderNumber?: number;
} & ReadonlyProps) => {
  const { t } = useTranslation(['fna']);
  const {
    lifeJourney,
    updateLifeStage,
    setRequiredBeforeSavingError,
    updateEducationGoal,
  } = useFnaStore(
    state => ({
      lifeJourney: state.lifeJourney,
      updateLifeStage: state.updateLifeStage,
      updateDependents: state.updateDependents,
      setRequiredBeforeSavingError: state.setRequiredBeforeSavingError,
      updateEducationGoal: state.updateEducationGoal,
    }),
    shallow,
  );

  const onLifeStageChosen = useCallback(
    (item: LifeStageItemType | null) => {
      if (item?.lifeStage) {
        if (item.lifeStage !== lifeJourney.lifeStage) {
          setRequiredBeforeSavingError(false);
          updateLifeStage(item.lifeStage);

          const questions = (
            item.lifeStage
              ? countryModuleFnaConfig.lifeStageItems[item.lifeStage] || []
              : []
          ).map(type => (typeof type === 'string' ? { type } : type));

          const dependentsIdx = questions.findIndex(
            question => question.type === 'dependents',
          );
          if (dependentsIdx === -1) {
            updateEducationGoal({
              ...initialFnaState.educationGoal,
            });
          }
        }
      } else {
        updateLifeStage(null);
      }
      onSelect?.();
    },
    [
      onSelect,
      lifeJourney.lifeStage,
      setRequiredBeforeSavingError,
      updateLifeStage,
      updateEducationGoal,
    ],
  );

  const items = useMemo(
    () => Object.keys(countryModuleFnaConfig.lifeStageItems) as LifeStage[],
    [],
  );

  const data: LifeStageItemType[] = useMemo(
    () =>
      items.map((lifeStage, id) => {
        const result: LifeStageItemType = {
          id,
          lifeStage,
          img: <></>,
          maxWidth: 80,
        };
        switch (lifeStage) {
          case 'SINGLE':
            result.title = t('fna:single');
            result.img =
              lifeJourney.gender === Gender.FEMALE ? (
                <SingleFemaleLogo />
              ) : (
                <SingleMaleLogo />
              );
            break;
          case 'SINGLE_WITH_DEPENDENT':
            result.title = t('fna:singleWithDepend');
            result.img =
              lifeJourney.gender === Gender.FEMALE ? (
                <SingleWithDependFemaleLogo />
              ) : (
                <SingleWithDependMaleLogo />
              );
            break;
          case 'COUPLE':
            result.title = t('fna:couple');
            result.img = <CoupleLogo />;
            break;
          case 'COUPLE_WITH_KIDS':
            result.title = t('fna:coupleWithKids');
            result.img = <CoupleWithKidLogo />;
            result.maxWidth = 130;
            break;
          case 'EMPTY_NESTER':
            result.title = t('fna:emptyNester');
            result.img = lifeJourney.havePartner ? (
              <EmptyNesterCoupleLogo />
            ) : lifeJourney.gender === Gender.FEMALE ? (
              <EmptyNesterFemaleLogo />
            ) : (
              <EmptyNesterMaleLogo />
            );
            result.maxWidth = 140;
            break;
          case 'RETIRED':
            result.title = t('fna:retired');
            result.img = <RetireLogo height={80} />;
            break;
        }
        return result;
      }),
    [items, t, lifeJourney.gender, lifeJourney.havePartner],
  );

  return (
    <DeviceBasedRendering
      phone={
        <LifeStageSelectionPhone
          onSelect={onLifeStageChosen}
          data={data}
          readonly={readonly}
          titleVisible={titleVisible}
          orderNumber={orderNumber}
        />
      }
      tablet={
        <LifeStageSelectionTablet
          onSelect={onLifeStageChosen}
          data={data}
          readonly={readonly}
        />
      }
    />
  );
};

export default LifeStageSelection;
