import styled from '@emotion/native';
import React from 'react';
import { View } from 'react-native';

import { useTranslation } from 'react-i18next';
import { useCheckFnaCompletion } from '../utils/helper/checkFnaCompletion';
import FnaHeader from './header/FnaHeader';
import FormFooter from './common/FormFooter';
import LifeJourneyPhone from './lifeJourney/phone/LifeJourney.phone';
import { InternalFnaProps } from './Fna';
import { useFnaStore } from '../utils/store/fnaStore';

export default function FnaPhone({ isLoading, onViewPlans }: InternalFnaProps) {
  const { t } = useTranslation(['fna']);

  const shouldShowRecommendation = useCheckFnaCompletion();
  const fnaState = useFnaStore();
  const { getFnaCompletion } = fnaState;

  return (
    <FnaScreenContainer>
      <FnaHeader />
      <LifeJourneyPhone shouldShowRecommendation={shouldShowRecommendation} />
      <FnaConfirmBtnContainer
        buttonTitle={t('fna:lifeStage.recommend.plans')}
        onPress={onViewPlans}
        visible={shouldShowRecommendation}
        disabled={!getFnaCompletion()}
        loading={isLoading}
      />
    </FnaScreenContainer>
  );
}

const FnaScreenContainer = styled(View)(({ theme: { colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[50],
  flex: 1,
}));

const FnaConfirmBtnContainer = styled(FormFooter)(({ theme: { sizes } }) => ({
  position: 'absolute',
  bottom: 0,
  left: sizes[4],
}));
