import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import MarkdownText, { useMarkdownStyle } from 'components/MarkdownText';
import { Box, Button, H6, H7, H8, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'cube-ui-components';
import { FNA_DISCLAIMER } from 'features/fna/constants/fnaDisclaimer';
import { useRootStackNavigation } from 'hooks/useRootStack';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, TouchableOpacity } from 'react-native';
import { useSafeAreaFrame } from 'react-native-safe-area-context';
import { LanguagesKeys } from 'utils/translation';
import TabletBottomSheet from 'components/TabletBottomSheet';
import { DisclaimerProps } from './Disclaimer';

export default function Disclaimer({
  visible,
  onDismiss,
  onAccept,
}: DisclaimerProps) {
  const { t } = useTranslation(['fna']);
  const { space, typography, colors, borderRadius } = useTheme();
  const [language, setLanguage] = useState<LanguagesKeys>('en');
  const mdStyle = useMarkdownStyle();
  const languages = useMemo(
    () =>
      FNA_DISCLAIMER
        ? Object.keys(FNA_DISCLAIMER).map(key => ({
            label: t(`fna:language.${key as LanguagesKeys}`),
            value: key,
          }))
        : [],
    [t],
  );
  const navigation = useRootStackNavigation();

  return (
    <TabletBottomSheet visible={visible} backdropColor="transparent">
      <Box
        p={space[6]}
        backgroundColor={colors.background}
        borderTopLeftRadius={borderRadius.large}
        borderTopRightRadius={borderRadius.large}>
        <Row mb={space[4]} justifyContent="space-between">
          <Row alignItems="center" gap={space[3]} flex={1}>
            <TouchableOpacity
              onPress={() => {
                onDismiss?.();
                if (navigation.canGoBack()) {
                  navigation.goBack();
                } else {
                  navigation.navigate('Main', {
                    screen: 'Home',
                  });
                }
              }}>
              <Icon.Close fill={colors.secondary} />
            </TouchableOpacity>
            <H6 fontWeight="bold">{t('fna:disclaimer.title')}</H6>
          </Row>
          {languages.length > 1 && (
            <Picker
              type="chip"
              items={languages}
              value={language}
              onChange={language => setLanguage(language as LanguagesKeys)}
            />
          )}
        </Row>
        <Content showsVerticalScrollIndicator={false}>
          <Box h={space[4]} />
          <MarkdownText
            style={{
              body: {
                ...mdStyle.body,
                fontSize: typography.largeBody.size,
                lineHeight: typography.largeBody.lineHeight,
              },
              strong: {
                ...mdStyle.strong,
                fontSize: typography.largeLabel.size,
                lineHeight: typography.largeLabel.lineHeight,
              },
              paragraph: {
                marginBottom: space[2],
                marginTop: 0,
              },
              ordered_list: {
                marginBottom: space[6],
              },
              em: {
                fontFamily: 'FWDCircularTT-Medium',
                fontSize: typography.largeLabel.size,
                lineHeight: typography.largeLabel.lineHeight,
              },
              hr: {
                backgroundColor: 'transparent',
                height: 4,
              },
            }}>
            {FNA_DISCLAIMER?.[language] || ''}
          </MarkdownText>
        </Content>
        <Box alignItems="flex-end" mt={space[6]} gap={space[4]}>
          <ActionButton
            text={t('fna:agree')}
            onPress={() => {
              onAccept?.();
              onDismiss?.();
            }}
          />
        </Box>
      </Box>
    </TabletBottomSheet>
  );
}

const Content = styled(ScrollView)(({ theme }) => {
  const { height } = useSafeAreaFrame();
  return {
    maxHeight: height - theme.space[65],
  };
});

const ActionButton = styled(Button)(() => {
  return {
    minWidth: 200,
  };
});
