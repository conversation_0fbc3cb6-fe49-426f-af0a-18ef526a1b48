import { country } from 'utils/context';
import IBPreFna from './ib/IBPreFna';
import MYPreFna from './my/MYPreFna';

export interface PreFnaProps {
  isFreshStart: boolean;
  hasFnaRecord: boolean;
  onDone: () => void;
}

export default function PreFna(props: PreFnaProps) {
  if (Component) {
    return <Component {...props} />;
  } else {
    props.onDone();
    return null;
  }
}

const Component = {
  ib: IBPreFna,
  ph: null,
  my: MYPreFna,
  id: null,
}[country];
