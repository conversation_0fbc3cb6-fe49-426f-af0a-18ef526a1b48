import { useTheme } from '@emotion/react';
import {
  Box,
  H6,
  Icon,
  Label,
  LargeLabel,
  H5,
  Center,
  LargeBody,
  Body,
  Fonts,
  PictogramIcon,
} from 'cube-ui-components';
import { AdviceType as AdviceTypeKey } from 'features/fna/utils/store/fnaStore';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';

import React, { createElement } from 'react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import MarkdownTextMore from 'components/MarkdownTextMore';
import TextMore from 'components/TextMore';
import UnTickIcon from '../../icons/UnTickIcon';

const ChoiceButton = ({
  type,
  label,
  description,
  instruction,
  selected,
  onPress,
  icon,
  disabled,
}: {
  type: string;
  label: string;
  instruction: string;
  description: string;
  selected: boolean;
  onPress: () => void;
  icon: React.ComponentType<{ width: number; height: number }>;
  disabled?: boolean;
}) => {
  const { space, colors, borderRadius, typography } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <Box
      flex={isTabletMode ? 1 : undefined}
      w="100%"
      borderRadius={borderRadius.large}
      borderWidth={selected ? 2 : 1}
      borderColor={selected ? colors.primary : colors.palette.fwdGrey[100]}
      backgroundColor={selected ? colors.primaryVariant3 : colors.background}>
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled}
        style={{
          opacity: disabled ? 0.3 : 1,
          marginVertical: selected ? 0 : 1,
        }}>
        <Box
          alignItems="center"
          py={isTabletMode ? 0 : space[4]}
          px={isTabletMode ? 0 : space[3]}
          my={isTabletMode ? space[8] : 0}
          gap={isTabletMode ? 0 : space[2]}>
          <Box mb={isTabletMode ? space[4] : 0}>
            {isTabletMode
              ? createElement(icon)
              : createElement(icon, {
                  width: 152,
                  height: 52,
                })}
          </Box>
          {isTabletMode ? (
            <H5 fontWeight="bold">{label}</H5>
          ) : (
            <H6 fontWeight="bold">{label}</H6>
          )}
          <Box
            h={isTabletMode ? 40 : undefined}
            justifyContent="center"
            alignItems="center"
            alignSelf="center"
            mt={isTabletMode ? space[3] : 0}
            mb={isTabletMode ? space[4] : 0}>
            <LargeLabel
              style={{ textAlign: 'center' }}
              color={colors.primary}
              fontWeight="medium">
              {instruction}
            </LargeLabel>
          </Box>

          <Box mx={isTabletMode ? space[6] : 0}>
            {isTabletMode ? (
              <LargeBody
                color={colors.palette.fwdGreyDarkest}
                style={{ textAlign: 'center' }}>
                {description}
              </LargeBody>
            ) : (
              <TextMore
                text={description}
                numLines={2}
                collapsable={false}
                style={{
                  textAlign: 'center',
                  fontSize: typography.body.size,
                  lineHeight: typography.body.lineHeight,
                  color: colors.palette.fwdGreyDarkest,
                }}
                buttonTextStyle={{
                  textTransform: 'lowercase',
                  fontFamily: Fonts.FWDCircularTT.Medium,
                  fontSize: typography.body.size,
                  lineHeight: typography.body.lineHeight,
                }}
              />
            )}
          </Box>
        </Box>

        <Box
          position="absolute"
          top={isTabletMode ? space[5] : space[4]}
          left={isTabletMode ? space[5] : space[3]}>
          {selected ? (
            <Icon.TickCircle
              fill={colors.palette.alertGreen}
              size={isTabletMode ? 35 : 32}
            />
          ) : (
            <UnTickIcon size={isTabletMode ? 35 : 32} />
          )}
        </Box>
      </TouchableOpacity>
    </Box>
  );
};

export default ChoiceButton;
