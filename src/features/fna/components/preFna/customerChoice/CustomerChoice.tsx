import React from 'react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import CustomerChoiceTablet from './CustomerChoice.tablet';
import CustomerChoicePhone from './CustomerChoice.phone';

export interface CustomerChoiceProps {
  visible?: boolean;
  onDismiss?: () => void;
  onAccept?: () => void;
  onBack: () => void;
}

export default function CustomerChoice({
  visible,
  onDismiss,
  onAccept,
  onBack,
}: CustomerChoiceProps) {
  return (
    <DeviceBasedRendering
      tablet={
        <CustomerChoiceTablet
          visible={visible}
          onDismiss={onDismiss}
          onAccept={onAccept}
          onBack={onBack}
        />
      }
      phone={
        <CustomerChoicePhone
          visible={visible}
          onDismiss={onDismiss}
          onAccept={onAccept}
          onBack={onBack}
        />
      }
    />
  );
}
