import React from 'react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import VulnerableCustomerCheckTablet from './VulnerableCustomerCheck.tablet';
import VulnerableCustomerCheckPhone from './VulnerableCustomerCheck.phone';

export interface VulnerableCustomerCheckProps {
  visible?: boolean;
  onDismiss?: () => void;
  onAccept?: () => void;
}

export default function VulnerableCustomerCheck({
  visible,
  onDismiss,
  onAccept,
}: VulnerableCustomerCheckProps) {
  return (
    <DeviceBasedRendering
      tablet={
        <VulnerableCustomerCheckTablet
          visible={visible}
          onDismiss={onDismiss}
          onAccept={onAccept}
        />
      }
      phone={
        <VulnerableCustomerCheckPhone
          visible={visible}
          onDismiss={onDismiss}
          onAccept={onAccept}
        />
      }
    />
  );
}
