import React, { useEffect } from 'react';
import CustomerChoice from '../customerChoice/CustomerChoice';
import Disclaimer from './disclaimer/Disclaimer';
import VulnerableCustomerCheck from '../vulnerableCustomerCheck/VulnerableCustomerCheck';
import { PreFnaProps } from '../PreFna';
import useToggle from 'hooks/useToggle';
import { Box } from 'cube-ui-components';
import { useWindowDimensions } from 'react-native';

export default function MYPreFna({
  isFreshStart,
  hasFnaRecord,
  onDone,
}: PreFnaProps) {
  const { height: screenHeight, width: screenWidth } = useWindowDimensions();
  const [disclaimerVisible, showDisclaimer, hideDisclaimer] = useToggle();
  const [vulnerableCheckVisible, showVulnerableCheck, hideVulnerableCheck] =
    useToggle();
  const [customerChoiceVisible, showCustomerChoice, hideCustomerChoice] =
    useToggle();

  useEffect(() => {
    const timeout = setTimeout(showDisclaimer, 1000);
    return () => clearTimeout(timeout);
  }, [showDisclaimer]);

  return (
    <>
      {(isFreshStart || !hasFnaRecord) && (
        <Box
          backgroundColor={
            disclaimerVisible || vulnerableCheckVisible || customerChoiceVisible
              ? 'rgba(0, 0, 0, 0.5)'
              : 'transparent'
          }
          flex={1}
          width={screenWidth}
          height={screenHeight}
          position="absolute">
          <Disclaimer
            visible={disclaimerVisible}
            onDismiss={hideDisclaimer}
            onAccept={showVulnerableCheck}
          />
          <VulnerableCustomerCheck
            visible={vulnerableCheckVisible}
            onDismiss={hideVulnerableCheck}
            onAccept={showCustomerChoice}
          />
          <CustomerChoice
            visible={customerChoiceVisible}
            onDismiss={hideCustomerChoice}
            onAccept={onDone}
            onBack={() => {
              hideCustomerChoice();
              showVulnerableCheck();
            }}
          />
        </Box>
      )}
    </>
  );
}
