import React from 'react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import DisclaimerTablet from './Disclaimer.tablet';
import DisclaimerPhone from './Disclaimer.phone';

export interface DisclaimerProps {
  visible?: boolean;
  onDismiss?: () => void;
  onAccept?: () => void;
}

export default function Disclaimer({
  visible,
  onDismiss,
  onAccept,
}: DisclaimerProps) {
  return (
    <DeviceBasedRendering
      tablet={
        <DisclaimerTablet
          visible={visible}
          onDismiss={onDismiss}
          onAccept={onAccept}
        />
      }
      phone={
        <DisclaimerPhone
          visible={visible}
          onDismiss={onDismiss}
          onAccept={onAccept}
        />
      }
    />
  );
}
