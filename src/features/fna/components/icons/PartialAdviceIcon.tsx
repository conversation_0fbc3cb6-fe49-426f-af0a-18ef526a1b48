import * as React from 'react';
import Svg, { Path } from 'react-native-svg';
const PartialAdviceIcon = ({width = 230, height = 120}: {width?: number; height?: number}) => (
  <Svg width={width} height={height} viewBox="0 0 230 120" fill="none">
    <Path
      d="M151.305 47.3836V47.4234C151.305 61.9547 142.833 74.4984 130.58 80.3367C128.484 81.3352 127.155 83.4656 127.155 85.8V91.0617H103.004V85.7977C103.004 83.4727 101.684 81.3398 99.5899 80.3438C87.3328 74.5055 78.8535 61.9617 78.8535 47.4258V47.3836C78.8745 27.2367 95.0331 11.0156 115.079 11.0156C135.125 11.0156 151.284 27.293 151.305 47.3836Z"
      fill="#E87722"
    />
    <Path
      d="M127.148 93.2812H102.851C101.061 93.2812 99.6084 91.7296 99.6084 89.8152V88.3098C99.6084 86.3954 101.061 84.8438 102.851 84.8438H127.148C128.938 84.8438 130.391 86.3954 130.391 88.3098V89.8152C130.399 91.7296 128.946 93.2812 127.148 93.2812Z"
      fill="#F3BB90"
    />
    <Path
      d="M127.148 101.484H102.851C101.061 101.484 99.6084 99.9758 99.6084 98.1146V96.6511C99.6084 94.7898 101.061 93.2812 102.851 93.2812H127.148C128.938 93.2812 130.391 94.7898 130.391 96.6511V98.1146C130.399 99.9758 128.938 101.484 127.148 101.484Z"
      fill="#E87722"
    />
    <Path
      d="M115.288 108.75H113.546C109.715 108.75 106.604 105.497 106.604 101.484H122.229C122.229 105.497 119.119 108.75 115.288 108.75Z"
      fill="#183028"
    />
    <Path
      d="M104.925 43.5938H94.0742C93.075 43.5938 92.2656 44.405 92.2656 45.4067C92.2656 46.4083 93.075 47.2196 94.0742 47.2196H104.925C105.925 47.2196 106.734 46.4083 106.734 45.4067C106.734 44.405 105.925 43.5938 104.925 43.5938Z"
      fill="#183028"
    />
    <Path
      d="M104.925 50.8457H90.457C89.4578 50.8457 88.6484 51.657 88.6484 52.6586C88.6484 53.6603 89.4578 54.4716 90.457 54.4716H104.925C105.925 54.4716 106.734 53.6603 106.734 52.6586C106.734 51.657 105.925 50.8457 104.925 50.8457Z"
      fill="#183028"
    />
    <Path
      d="M104.925 58.0977H94.0742C93.075 58.0977 92.2656 58.9089 92.2656 59.9106C92.2656 60.9122 93.075 61.7235 94.0742 61.7235H104.925C105.925 61.7235 106.734 60.9122 106.734 59.9106C106.734 58.9089 105.925 58.0977 104.925 58.0977Z"
      fill="#183028"
    />
    <Path
      d="M123.688 28.8683V25.992C123.688 23.1886 121.419 20.916 118.62 20.916H115.242C112.443 20.916 110.174 23.1886 110.174 25.992V28.8683C105.973 30.3488 102.375 33.1723 99.9374 36.9053C99.4243 37.6899 99.6439 38.7432 100.427 39.2571C101.211 39.771 102.262 39.5511 102.775 38.7664C107.862 30.9326 118.326 28.7129 126.146 33.8079C133.967 38.9028 136.183 49.3836 131.096 57.2164C126.528 64.2519 117.501 66.8692 109.887 63.3679C109.038 62.9798 108.036 63.3552 107.649 64.2054C107.261 65.0556 107.636 66.0592 108.485 66.4473C118.616 71.2197 130.691 66.8618 135.456 56.7152C140.22 46.5686 135.87 34.473 125.739 29.7006C125.071 29.3854 124.387 29.1084 123.689 28.8683H123.688Z"
      fill="#FAE4D3"
    />
    <Path
      d="M126.695 40.1697C126.114 39.4443 125.058 39.3227 124.33 39.899L119.583 43.689C118.792 43.1793 117.872 42.9086 116.931 42.9107C114.132 42.9107 111.863 45.1833 111.863 47.9867C111.863 50.7901 114.132 53.0626 116.931 53.0626C119.73 53.0626 121.999 50.7901 121.999 47.9867C121.997 47.4093 121.893 46.8361 121.695 46.2947L126.424 42.5047C127.128 41.9262 127.247 40.8941 126.695 40.1697Z"
      fill="#183028"
    />
  </Svg>
);
export default PartialAdviceIcon;
