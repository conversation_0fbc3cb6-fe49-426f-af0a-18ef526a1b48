import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const Health2InactiveIcon = (props: SvgPictogramProps) => (
  <Svg
    width={props.width || props.size || 40}
    height={props.height || props.size || 40}
    viewBox="0 0 40 40"
    fill="none">
    <Path
      fill="#DBDFE1"
      d="M31.721 9.068c-1.42-1.19-3.358-1.823-5.61-1.823-.08 0-.158 0-.233.003-2.278.043-4.245 1.241-5.683 3.462a9.256 9.256 0 0 0-.336.558c0-.004-.004-.004-.004-.007v21.746l.004.004.604-.376c.036-.021 3.48-2.178 6.856-5.321 2.006-1.867 3.605-3.72 4.753-5.51 1.494-2.329 2.238-4.568 2.213-6.663-.035-2.629-.897-4.67-2.564-6.073Z"
    />
    <Path
      fill="#EDEFF0"
      d="M19.518 10.71c-1.437-2.22-3.404-3.419-5.682-3.462-.079 0-.158-.003-.233-.003-2.249 0-4.19.629-5.61 1.823-1.667 1.399-2.529 3.444-2.565 6.076-.025 2.092.72 4.335 2.214 6.663 1.148 1.788 2.75 3.644 4.753 5.51 3.386 3.151 6.823 5.3 6.855 5.322l.597.372V11.264a9.05 9.05 0 0 0-.329-.554Z"
    />
    <Path
      fill="#8B8E8F"
      d="M30.78 15.04h-2.07a.412.412 0 0 1-.412-.414v-2.074a.415.415 0 0 0-.415-.415h-1.105a.415.415 0 0 0-.415.415v2.074a.415.415 0 0 1-.415.415h-2.07a.415.415 0 0 0-.415.415v1.105c0 .228.186.414.415.414h2.07c.23 0 .415.186.415.415v2.07c0 .23.186.416.415.416h1.105a.415.415 0 0 0 .415-.415v-2.07c0-.23.186-.416.411-.416h2.07a.415.415 0 0 0 .416-.415v-1.104a.42.42 0 0 0-.415-.415Z"
    />
  </Svg>
);

export default Health2InactiveIcon;
