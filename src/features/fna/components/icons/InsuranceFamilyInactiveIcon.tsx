import React from 'react';
import Svg, { Path } from 'react-native-svg';
const InsuranceFamilyInactiveIcon = (): JSX.Element => {
  return (
    <Svg width="40" height="40" viewBox="0 0 40 40" fill="none">
      <Path
        d="M24.0277 10.7476C24.202 13.9647 27.7363 16.2104 28.802 16.8161C28.9906 16.9218 29.2163 16.9218 29.4049 16.8161C30.4706 16.2104 34.0077 13.9647 34.1791 10.7476C34.262 9.24757 33.1334 7.91906 31.6306 7.88191C30.4677 7.85334 29.4706 8.58472 29.102 9.61329C28.7334 8.58472 27.7391 7.85334 26.5734 7.88191C25.0734 7.91906 23.9449 9.24757 24.0277 10.7476Z"
        fill="#EDEFF0"
      />
      <Path
        d="M25.2056 10.0505C25.4342 10.2533 25.9284 10.1734 26.3056 9.87051C26.6856 9.56766 26.8056 9.15624 26.577 8.95052C26.3484 8.74767 25.8542 8.83049 25.477 9.13335C25.097 9.43621 24.977 9.84762 25.2056 10.0505Z"
        fill="#EDEFF0"
      />
      <Path
        d="M28.0368 18.2532C28.2082 18.5161 28.8682 18.0418 29.1482 18.0418C29.4168 18.0418 30.0396 18.5018 30.2053 18.2532C30.4425 17.8961 29.5882 16.5933 29.5882 16.5933H28.6568C28.6568 16.5933 27.8025 17.8961 28.0368 18.2532Z"
        fill="#EDEFF0"
      />
      <Path
        d="M29.1101 17.0219C29.3215 17.0219 29.5301 16.9905 29.7387 16.9305C29.8044 16.9105 29.8444 16.8419 29.8244 16.7762C29.8044 16.7105 29.7358 16.6705 29.6701 16.6905C29.2987 16.799 28.9244 16.799 28.5501 16.6905C28.4844 16.6705 28.4129 16.7105 28.3958 16.7762C28.3758 16.8419 28.4158 16.9134 28.4815 16.9305C28.6901 16.9934 28.8987 17.0219 29.1101 17.0219Z"
        fill="#DBDFE1"
      />
      <Path
        d="M29.1104 17.2676C29.3904 17.2676 29.6733 17.2162 29.9647 17.1105C30.0304 17.0876 30.0647 17.0162 30.039 16.9505C30.0161 16.8848 29.9447 16.8505 29.879 16.8762C29.3561 17.0648 28.8533 17.0648 28.3847 16.8791C28.319 16.8534 28.2476 16.8848 28.2218 16.9505C28.1961 17.0162 28.2276 17.0876 28.2933 17.1133C28.5561 17.2133 28.8304 17.2676 29.1104 17.2676Z"
        fill="#DBDFE1"
      />
      <Path
        d="M28.3307 27.6419C29.2593 25.9105 29.3393 17.7419 29.2564 16.9476C29.4793 17.0905 29.8507 17.3733 30.2021 17.8476C30.7107 18.5248 30.4021 18.9076 30.3907 18.9219C30.3478 18.9733 30.3536 19.0533 30.405 19.099C30.4564 19.1447 30.5336 19.1391 30.5793 19.0876C30.5993 19.0648 31.0393 18.5448 30.4021 17.699C29.8079 16.9076 29.1793 16.6162 29.1536 16.6047L28.945 16.5105L28.9764 16.7362C28.9821 16.7677 29.1621 25.559 28.1078 27.5276C28.0764 27.5876 28.0993 27.6648 28.1593 27.6962C28.2221 27.7248 28.2964 27.7019 28.3307 27.6419Z"
        fill="#DBDFE1"
      />
      <Path
        d="M10.9939 13.6477C10.2054 13.6477 9.5625 14.4677 9.5625 15.4762C9.5625 16.4905 10.2054 17.3134 10.9939 17.3134C11.7911 17.3134 12.4311 16.4905 12.4311 15.4762C12.4311 14.4648 11.7825 13.6477 10.9939 13.6477Z"
        fill="#EDEFF0"
      />
      <Path
        d="M5.90176 23.362L8.47889 18.1334C8.51032 18.0705 8.55032 18.0162 8.59318 17.9648C8.57032 17.8905 9.71031 17.4363 10.6732 17.4363H11.0903C12.1332 17.4363 13.3675 17.8534 13.3646 17.882C13.3618 17.8992 13.3646 17.9134 13.3618 17.9306C13.4189 17.9906 13.4703 18.0563 13.5075 18.1334L16.0846 23.362C16.2875 23.7763 16.116 24.2791 15.7046 24.482C15.2903 24.6848 14.7875 24.5134 14.5846 24.1019L13.1789 21.2534L13.1017 23.0534L13.5103 31.4677C13.5389 31.802 13.196 32.0877 12.7332 32.1162H12.676C12.2389 32.1162 11.8703 31.8705 11.8303 31.5563L10.8789 24.6248L9.92176 31.5563C9.90176 31.8763 9.52461 32.1162 9.09604 32.1162H9.03031C8.5646 32.0877 8.22461 31.802 8.25032 31.4677L8.66747 23.0534H8.65889L8.60174 21.6677L7.40176 24.1019C7.1989 24.5162 6.69317 24.6877 6.28174 24.482C5.87032 24.2763 5.6989 23.7763 5.90176 23.362Z"
        fill="#EDEFF0"
      />
      <Path
        d="M18.1074 17.6335C18.1074 16.7763 18.7246 16.0935 19.4846 16.0935C20.2503 16.0935 20.8674 16.782 20.8674 17.6335C20.8674 18.482 20.2474 19.1734 19.4846 19.1734C18.7246 19.1706 18.1074 18.4792 18.1074 17.6335Z"
        fill="#DBDFE1"
      />
      <Path
        d="M15.0328 23.7305L17.4242 19.9191C17.4785 19.8334 17.5528 19.7705 17.6385 19.7248C17.6357 19.6705 18.51 19.2905 19.3128 19.2905H19.6528C20.4814 19.2905 21.3643 19.6991 21.3614 19.7363C21.4385 19.7791 21.5043 19.8391 21.5528 19.9162L23.9442 23.7277C24.1042 23.982 24.0157 24.3133 23.7442 24.4648C23.4757 24.6162 23.1243 24.5305 22.9643 24.2762L21.2614 21.5648L22.2728 26.2248C22.3414 26.5391 22.1014 26.8362 21.7785 26.8362H21.2442L20.8814 31.7962C20.8814 31.9677 20.69 32.1134 20.4643 32.1134C20.2357 32.1134 20.0471 31.9705 20.0471 31.7962L19.69 26.8362H19.2957L18.9071 31.7962C18.9071 31.9677 18.7185 32.1134 18.49 32.1134C18.2614 32.1134 18.0728 31.9705 18.0728 31.7962L17.7185 26.8362H17.1814C16.8585 26.8362 16.6185 26.5391 16.6871 26.2248L17.6814 21.6105L16.01 24.2734C15.85 24.5277 15.4985 24.6133 15.23 24.4619C14.9614 24.3162 14.8728 23.9848 15.0328 23.7305Z"
        fill="#DBDFE1"
      />
      <Path
        d="M26.1546 22.7192C25.5032 22.7192 24.9746 23.2792 24.9746 23.9792C24.9746 24.6735 25.5032 25.2393 26.1546 25.2393C26.8146 25.2393 27.3432 24.6735 27.3432 23.9792C27.3432 23.2792 26.8146 22.7192 26.1546 22.7192Z"
        fill="#8B8E8F"
      />
      <Path
        d="M23.0595 23.5822C23.2566 23.3822 23.5823 23.3765 23.7823 23.5736L24.9966 25.5937C24.9966 25.5822 24.9966 25.5679 24.9966 25.5565C25.2052 25.5251 25.4566 25.4565 26.0309 25.4565H26.2795C26.8766 25.4565 27.1309 25.5336 27.3509 25.5622L28.5337 23.7422C28.7337 23.545 29.0595 23.5479 29.2566 23.7507C29.4538 23.9507 29.4509 24.2765 29.248 24.4736L27.5909 26.7593C27.5909 26.7622 27.5909 26.7622 27.5909 26.7622L27.5166 28.0079L27.7623 31.6422C27.7709 31.6679 27.7623 31.8479 27.7423 31.8536C27.6766 32.0051 27.488 32.1193 27.248 32.1193C27.0195 32.1193 26.8195 32.0165 26.7452 31.8737C26.7537 31.8679 26.1566 28.9165 26.1566 28.9165C26.1566 28.9165 25.568 31.8679 25.568 31.8737C25.4823 32.0165 25.2938 32.1193 25.0652 32.1193C24.828 32.1193 24.6366 32.0051 24.5623 31.8536C24.5623 31.8479 24.5423 31.6679 24.5623 31.6422L24.7995 28.0079L24.728 26.8193C24.7337 26.8107 24.7395 26.8022 24.7452 26.7936C24.748 26.7908 24.748 26.7851 24.7509 26.7822L23.0709 24.3079C22.8652 24.1079 22.8623 23.7822 23.0595 23.5822Z"
        fill="#8B8E8F"
      />
    </Svg>
  );
};
export default InsuranceFamilyInactiveIcon;
