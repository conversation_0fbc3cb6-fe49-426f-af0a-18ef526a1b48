import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const MoneyInvestmentInactiveIcon = (props: SvgPictogramProps) => (
  <Svg
    width={props.width || props.size || 40}
    height={props.height || props.size || 40}
    fill="none"
    viewBox="0 0 40 40">
    <Path
      fill="#DBDFE1"
      d="m27.708 14.068-8.086-9.686a1.03 1.03 0 0 0-1.571 0l-8.086 9.686c-.743.886-.114 2.257 1.057 2.257h2.286v18.543c0 .486.4.886.886.886h9.257a.89.89 0 0 0 .886-.886V16.325h2.285c1.2-.028 1.829-1.371 1.086-2.257Z"
    />
    <Path
      fill="#8B8E8F"
      d="m16.48 24.24-4.286-5.143c-.228-.258-.628-.258-.828 0L7.08 24.238a.73.73 0 0 0 .571 1.2H8.88v9.829c0 .257.2.457.457.457h4.914c.258 0 .458-.2.458-.457v-9.829h1.228c.6 0 .943-.714.543-1.2Z"
    />
    <Path
      fill="#EDEFF0"
      d="M32.052 32.154v-4.629c0-1.571-.6-3.143-1.8-4.343a6.122 6.122 0 0 0-4.343-1.8c-1.571 0-3.143.6-4.343 1.8a6.123 6.123 0 0 0-1.8 4.343v4.629l-.971 2.4c-.286.685.228 1.428.971 1.428h12.286c.743 0 1.229-.743.971-1.428l-.97-2.4Z"
    />
    <Path
      fill="#8B8E8F"
      d="M27.397 20.382h-3.029a.44.44 0 0 0-.428.429v.485c0 .229.2.429.428.429h3.029a.44.44 0 0 0 .428-.429v-.485a.42.42 0 0 0-.428-.429Z"
    />
    <Path
      fill="#EDEFF0"
      d="m27.939 20.24 1.314-1.315c.171-.171.171-.457 0-.6l-.314-.314c-.172-.172-.457-.172-.6 0l-.315.314c-.171.171-.457.171-.6 0l-.314-.314c-.171-.172-.457-.172-.6 0l-.314.314c-.172.171-.457.171-.6 0l-.314-.314c-.172-.172-.458-.172-.6 0l-.315.314c-.171.171-.457.171-.6 0l-.314-.314c-.171-.172-.457-.172-.6 0l-.314.314c-.172.171-.172.457 0 .6l1.314 1.314a.43.43 0 0 0 .314.115h3.486c.114.028.2-.029.286-.115Z"
    />
    <Path
      fill="#fff"
      d="M22.223 28.296a3.646 3.646 0 0 1 3.657-3.657 3.646 3.646 0 0 1 3.657 3.657 3.646 3.646 0 0 1-3.657 3.658c-2 0-3.657-1.629-3.657-3.658Z"
    />
    <Path
      fill="#8B8E8F"
      d="M25.68 30.954v-.6a1.364 1.364 0 0 1-.514-.172c-.143-.086-.286-.171-.372-.286-.086-.114-.171-.228-.229-.342-.057-.115-.085-.258-.114-.372l.715-.171c0 .085.028.171.057.257.028.086.085.143.143.228a.621.621 0 0 0 .228.143c.086.029.2.057.343.057.2 0 .343-.028.428-.114.115-.086.143-.171.143-.314a.349.349 0 0 0-.114-.257.645.645 0 0 0-.314-.143l-.514-.114a1.525 1.525 0 0 1-.743-.372 1.024 1.024 0 0 1-.258-.686c0-.142.029-.285.086-.428.057-.143.143-.257.229-.343.085-.086.2-.171.343-.257.143-.057.285-.114.428-.143v-.6h.572v.629c.171.028.314.085.428.142.114.058.229.143.314.229.086.086.143.171.2.286.058.114.086.2.115.285l-.715.2a.406.406 0 0 0-.057-.171.594.594 0 0 0-.114-.171.512.512 0 0 0-.2-.143.844.844 0 0 0-.286-.057.702.702 0 0 0-.428.142.459.459 0 0 0-.143.315c0 .085.029.171.086.228a.67.67 0 0 0 .285.143l.515.114c.371.086.628.229.8.429.171.2.257.429.257.686 0 .143-.029.257-.057.371a2.13 2.13 0 0 1-.2.343.78.78 0 0 1-.343.257 2.141 2.141 0 0 1-.457.143v.629h-.543Z"
    />
  </Svg>
);
export default MoneyInvestmentInactiveIcon;
