import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';
const HumanBrainIcon = (props: SvgProps) => (
  <Svg width={41} height={40} viewBox="0 0 41 40" fill="none" {...props}>
    <Path
      d="M29.96 26.567l.077-.288.279-.17.085-.292.163-.242.22-.206.145-.255.135-.26.067-.305.152-.25.261-.18.142-.256.151-.25.125-.267.114-.273.239-.198.067-.301.199-.222.077-.305.232-.223.066-.308.182-.25.19-.252.094-.293.04-.312.034-.31.235-.247.109-.29-.032-.323.1-.291.108-.293-.012-.312-.053-.314.039-.302.192-.291-.088-.312.088-.304-.049-.308-.058-.298-.018-.299L34 15.62l.099-.315-.127-.285-.053-.296-.073-.291-.094-.285-.021-.306-.188-.256.054-.335-.12-.278-.239-.228-.014-.32-.218-.23-.138-.264-.082-.295-.186-.24-.161-.252-.143-.264-.155-.256-.145-.266-.245-.19-.107-.298-.163-.255-.206-.222-.301-.131-.098-.32-.203-.225-.32-.1-.154-.276-.218-.21-.234-.189-.248-.172-.313-.082-.165-.286-.305-.083-.19-.263-.313-.062-.275-.12-.193-.278-.3-.071-.314-.037-.22-.245-.287-.097-.274-.126-.3-.052-.312-.006-.267-.153-.301-.032-.273-.156-.299-.039-.312.05-.293-.055-.291-.096-.297-.044-.296-.072-.302.02-.3-.036-.298.11-.3-.018-.3.016-.3.007-.287.118-.316-.08-.291.082-.3.038-.29.082-.283.108-.258.181-.29.07-.26.158-.306.033-.304.051-.27.132-.268.137-.23.205-.29.095-.263.145-.234.191-.239.181-.274.129-.261.152-.186.25-.211.212-.297.113-.258.165-.147.281-.226.198-.174.247-.274.158-.174.247-.135.276-.203.222-.119.282-.254.19-.08.305-.196.229-.13.271-.218.223-.104.284-.191.24-.013.325-.207.237-.11.28-.131.271.005.321-.096.283-.114.277-.164.265.009.313-.177.266-.03.301.004.305.226.197.037.314.22.201.066.297.234.192.101.258-.086.273-.174.247-.175.247-.115.276-.06.302-.12.273-.149.26-.199.235-.079.294-.068.298-.117.274-.205.233-.146.262-.154.256-.13.269-.043.31-.116.275-.21.231-.057.304-.187.242-.062.3.008.304.122.267.255.153.306.024.306.007.304.019.291.084.306.017.348.077-.093.369.155.29.021.304.046.302.028.302-.1.315.08.299.028.302-.007.306.076.299-.023.308.084.297-.034.309-.022.308.198.287.036.302-.107.316.02.304.15.291.007.306.046.302-.034.287.152.24.244.141.224.165.278.031.291.016.292.005.29-.052.292-.052.29.02.292-.009.291.086.291-.096.292.059.291.093.291-.014.292-.135.279.068.01.298-.032.31-.048.309.057.31-.043.31.066.31-.07.31.063.306.096.294.201.233.105.308.248.194.289.123.318-.025.285.105.301-.005.302.014.302.012.301-.026.302.093.302-.099.301-.044.302.094.301-.025.302-.041.301.108.302-.031.302-.08.302-.023.302-.023.301.018.302.103.301-.096.302-.01.302.139.302-.155.302.021.302.031.301.063.302-.023.301.086.302-.075.302-.046.302.012.302.124.302-.061.302-.048.302.032.306-.018.309-.051.288-.132.178-.27.199-.226.134-.269.006-.302.029-.282.15-.294-.049-.294-.098-.294.093-.294.018-.294.06-.294-.102-.294-.046-.295.118-.294.014-.294.02-.294-.095-.294.075-.294-.056-.294-.031-.294.095-.294-.027-.295.01-.294-.148-.294.116-.294-.084-.297.078-.29z"
      fill="#B74701"
    />
    <Path
      d="M27.712 26.567l.067-.295.289-.164.05-.314.22-.207.133-.261.119-.271.247-.189.09-.29.12-.27.262-.18.14-.258.139-.258.105-.278.25-.19.129-.264.15-.252.069-.3.186-.244.137-.273.16-.262.12-.28.204-.247.027-.319.066-.301.117-.281.08-.295.129-.282.077-.296-.04-.32.211-.271-.096-.325.139-.289.006-.307-.035-.309-.009-.304.11-.302-.014-.306.024-.302-.12-.293.094-.31-.007-.303-.096-.291-.02-.303-.094-.29-.131-.276-.003-.311-.155-.268-.157-.263L31.16 13l-.076-.293-.069-.299-.157-.26-.233-.22-.068-.303-.195-.234-.135-.268-.17-.247-.157-.256-.198-.227-.148-.264-.2-.224-.14-.273-.285-.15-.088-.326-.289-.14-.143-.282-.287-.134-.21-.213-.241-.18-.208-.219-.271-.139-.165-.281-.305-.09-.248-.167-.186-.269-.253-.167-.291-.096-.25-.174-.338.005-.264-.141-.218-.25-.303-.054-.27-.137-.299-.058-.279-.116-.271-.147-.316.014-.304-.012-.288-.091-.289-.09-.301-.016-.299-.031-.296-.057-.301.032-.3.061-.299-.16-.299.066-.292.113-.299-.01-.301-.007-.29.08-.301.008-.308-.02-.302.033-.268.166-.307.014-.283.103-.264.157-.319-.002-.243.202-.253.167-.283.096-.252.161-.276.115-.33.02-.261.153-.232.197-.249.169-.267.142-.229.196-.265.15-.186.248-.224.199-.26.161-.172.254-.203.22-.24.186-.101.309-.245.184-.258.178-.2.227-.122.283-.138.268-.204.226-.133.27-.188.238-.178.247-.011.332-.231.22-.001.329-.174.25-.198.243-.023.313-.172.256-.059.297-.044.3-.11.28-.085.287-.07.29.007.306-.066.251.066.296.216.204.144.248.167.233.058.303.157.207-.029.287-.235.218-.06.302-.169.25-.117.274-.058.303-.106.28-.173.248-.134.267-.213.23-.06.302-.069.297-.273.2-.039.313-.175.247-.126.27-.13.269-.14.264-.119.274-.135.268.053.31-.13.296.258.15.137.267.324-.067.275.168.288.1.298.051.298.053.321-.027.03.31.11.295-.048.31-.018.308.101.296-.014.307.037.302.03.303.053.3-.084.314.065.3.058.3.094.296.012.305.055.3-.076.313.11.296.063.299-.075.313.043.302.11.296.056.245.1.244.19.19.23.16.279.056.291-.063.291.079.291.027.292-.182.29.083.292-.035.291.062.291-.109.291.104.292-.047.291-.006.292.074.228.02.007.25.127.31-.069.309.092.31-.11.31-.072.31.037.31.088.297.115.276.178.231.158.242.209.208.275.101.25.214.312-.086.3.095.302-.072.302-.063.301.19.302-.022.302-.142.301.014.302.148.302-.167.3.174.302-.017.302-.118.302.053.302-.014.301-.026.302-.023.302.032.302-.042.301.104.302.05.302-.057.302-.099.302.075.3.052.303-.13.302.103.301-.002.303-.098.301.099.302-.101.302.06.302-.028.302-.039h.288l.322.024.256-.177.21-.218.181-.236.221-.227.115-.294-.095-.317.008-.294-.047-.294.089-.294-.076-.294.118-.294.023-.294.002-.294-.064-.295-.08-.294.075-.294.02-.294-.095-.294.09-.294-.017-.294.012-.294.069-.294-.011-.295.02-.294-.12-.294-.006-.294-.014-.296.064-.291z"
      fill="#E87722"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.712 16.518l.344.036.297-.067.297.17.298-.125.297-.059.297.04.298.042.297.06.298.03.298-.154.297.113.297-.006.297-.092.298.152.297-.137.298.04.297.07.298-.049.298-.074.297.007.299.07.25-.13.24-.071.244-.143.063-.274.063-.272-.15-.286-.015-.293-.103-.274-.14-.256-.103-.294-.29-.133-.15-.295-.27-.147-.258-.075.001-.269.13-.282-.017-.303-.064-.3-.107-.284-.019-.303-.171-.251-.075-.295-.085-.309-.177-.256-.323-.108-.166-.275-.275-.14-.277-.125-.277-.126-.315.015-.275-.205-.311.152-.298-.054-.3.019-.305.037-.276.13-.236.101-.094-.242-.191-.238-.304-.136-.138-.279-.179-.255-.286-.124-.293-.095-.19-.277-.337.02-.255-.169-.272-.148-.323.102-.284-.115-.299-.041-.307-.032-.294.108-.277.138-.316-.018-.255.177-.281.101-.245.171-.238.178-.24.173-.236.181-.285.146-.189.24-.085.308-.119.273-.133.263-.263.221-.082.297.052.321-.148.284.003.279-.261.053-.289.076-.221.219-.295.077-.219.201-.224.191-.127.28-.188.215-.226.197-.133.262-.15.255-.01.305-.08.278-.045.287-.104.29.082.275.123.26-.094.317.166.264.24.198.29.104.298-.014.299-.107.298.057.298-.072.298.133.298-.086.299-.047.298.105.299-.044.297-.064.299.095.299.074.297-.128.299.075.298.01.299.017.297-.067.299.049.299.006.297.037.299-.163.299.109.298-.036.061.3.012.303.011.302-.088.301-.029.302.027.302-.044.302.01.302.018.302-.015.265-.267.097-.303-.154-.304.087-.305.01-.303-.012-.305-.078-.304.05-.322.098-.158-.319-.22-.207-.172-.307-.335-.032-.311-.11-.289.085-.247.155-.21.176-.284.115-.132.262-.078.277.02.283-.102.302.099.287.25.188.138.27.299.066.265.073.271.104.297-.104.267-.128.3-.102.233-.22.095-.373.325.067.305-.077.303.144.305-.065.303-.088.305.082.304.053.315-.051-.038.319.122.309-.09.309-.066.309.127.31.03.309-.099.313-.019.32-.174.272-.212.225-.185.284-.352-.033-.278.17-.315-.041-.297-.066-.296.003-.296.166-.296-.169-.296.057-.296.014-.296.092-.296-.111-.297-.01-.296.017-.296.033-.296.016-.296-.075-.297-.009-.3.082-.213-.242-.125-.296-.276-.152-.294-.091-.302-.149-.268.18-.3-.002-.218.193-.195.203-.129.248-.07.265-.142.27.066.287.074.283.179.23.194.215.242.163.27.122.297.003.302-.095.329-.026.296-.155.143-.309.17-.192.27-.114.296.07.296.013.296-.014.296.023.296-.145.297.086.296.074.296-.045.296-.107.296.055.296-.057.296.026.297.088.297-.006.296-.058.285-.086.293-.038.253-.154.299-.061.203-.22.233-.18.181-.234.098-.28.22-.236.088-.295-.04-.31-.076-.298-.011-.309.134-.309-.134-.309.138-.31.016-.309-.075-.322.317.08.304-.102.305-.06.303.136.305-.13.304.159.304-.011.307-.**************.342.295.16.34-.017.293.156.3-.01.282-.107.262-.151.19-.233.072-.293.066-.265.089-.267-.033-.28-.077-.274-.199-.206-.18-.208-.244-.129-.23-.205-.298.054-.327-.059-.25.226-.346.059-.225.251-.079.289-.29.04-.305.037-.303-.045-.305.034-.303-.05-.305.119-.304-.05-.284-.062-.02-.281.006-.302.086-.302-.127-.301.123-.302-.152-.303.099-.302-.012-.302.01-.305-.08-.346z"
      fill="#FED141"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.83 29.369l-.226.177-.231.204-.139.272-.105.277-.094.295.042.281.013.305.***************.226.186.268.139.301.014.293-.054.287-.078.23-.19.188-.218.248-.196-.016-.319-.026-.281.049-.313-.11-.296-.215-.229-.173-.286-.253-.108-.047-.277-.074-.295.118-.296-.145-.296.006-.296.007-.322.179-.285.139-.27.084-.331.322-.1.272-.143.294-.14.32.102.296-.098.295-.015.296.102.296-.076.32-.041.196.308.077.347.308.125.314.035.294.174.266-.************-.16.154-.255.23-.199.125-.279-.037-.304-.113-.27.058-.328-.194-.238-.233-.188-.242-.169-.286-.072-.288.061-.327-.105-.276.177-.2.242-.253.181-.165.247-.288.097-.295-.127-.296.105-.296-.146-.296.053-.297.052-.281.117-.318-.054-.228.235-.34-.008-.227.209-.147.273-.246.188-.145.266.017.322-.244.247.037.311.045.299.009.295-.002.296-.073.295.046.297-.06.283z"
      fill="#fff"
    />
  </Svg>
);
export default HumanBrainIcon;
