import { useTheme } from '@emotion/react';
import { SvgIconProps } from 'cube-ui-components';
import { Path, Svg } from 'react-native-svg';

export default function DropdownArrowIcon(props: SvgIconProps) {
  const theme = useTheme();
  return (
    <Svg
      width={props.size || props.width || 12}
      height={props.size || props.height || 13}
      viewBox="0 0 12 13"
      fill="none">
      <Path
        d="M6.7347 8.95295C6.33876 9.38172 5.6613 9.38172 5.26536 8.95295L1.54996 4.92954C0.958478 4.28902 1.41278 3.25112 2.28463 3.25112L9.71541 3.25112C10.5873 3.25112 11.0416 4.28902 10.4501 4.92954L6.7347 8.95295Z"
        fill={props.fill || theme.colors.primary}
      />
    </Svg>
  );
}
