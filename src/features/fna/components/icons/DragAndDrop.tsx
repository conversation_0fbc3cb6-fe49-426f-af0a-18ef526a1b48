import Svg, { Path } from 'react-native-svg';

export default function DragAndDrop() {
  return (
    <Svg width="25" height="24" viewBox="0 0 25 24" fill="none">
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.33325 6C7.22875 6 6.33325 5.1045 6.33325 4C6.33325 2.8955 7.22875 2 8.33325 2C9.43775 2 10.3333 2.8955 10.3333 4C10.3333 5.1045 9.43775 6 8.33325 6ZM8.33325 14C7.22875 14 6.33325 13.1045 6.33325 12C6.33325 10.8955 7.22875 10 8.33325 10C9.43775 10 10.3333 10.8955 10.3333 12C10.3333 13.1045 9.43775 14 8.33325 14ZM8.33325 22C7.22875 22 6.33325 21.1045 6.33325 20C6.33325 18.8955 7.22875 18 8.33325 18C9.43775 18 10.3333 18.8955 10.3333 20C10.3333 21.1045 9.43775 22 8.33325 22Z"
        fill="#B3B6B8"
      />
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M16.3333 6C15.2288 6 14.3333 5.1045 14.3333 4C14.3333 2.8955 15.2288 2 16.3333 2C17.4378 2 18.3333 2.8955 18.3333 4C18.3333 5.1045 17.4378 6 16.3333 6ZM16.3333 14C15.2288 14 14.3333 13.1045 14.3333 12C14.3333 10.8955 15.2288 10 16.3333 10C17.4378 10 18.3333 10.8955 18.3333 12C18.3333 13.1045 17.4378 14 16.3333 14ZM16.3333 22C15.2288 22 14.3333 21.1045 14.3333 20C14.3333 18.8955 15.2288 18 16.3333 18C17.4378 18 18.3333 18.8955 18.3333 20C18.3333 21.1045 17.4378 22 16.3333 22Z"
        fill="#B3B6B8"
      />
    </Svg>
  );
}
