import * as React from 'react';
import Svg, {
  <PERSON>,
  <PERSON>,
  De<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  SvgProps,
  Mask,
  LinearGradient,
  Stop,
} from 'react-native-svg';

export function MysFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <G clipPath="url(#clip0_2426_198559)">
        <Path d="M3 7h25v18H3V7z" fill="#C00" />
        <Path
          d="M3.02 8.496h24.96v1.44H3.02v-1.44zm0 2.951h24.96v1.44H3.02v-1.44zm0 2.955h24.96v1.44H3.02v-1.44z"
          fill="#fff"
        />
        <Path d="M3 7.02h12.5v10.5H3V7.02z" fill="#006" />
        <Path
          d="M11.105 9.768l.235 1.526.898-1.274-.484 1.47 1.387-.78-1.098 1.124 1.602-.12-1.497.556 1.497.555-1.602-.12 1.098 1.124-1.387-.78.48 1.474-.898-1.278-.235 1.526-.23-1.527-.898 1.276.484-1.47-1.387.78 1.094-1.126-1.602.12 1.5-.554-1.496-.556 1.602.12L9.07 10.71l1.387.78-.484-1.474.898 1.278.234-1.526zm-1.3.064a2.88 2.88 0 00-2.64.203 2.711 2.711 0 00-.927.963 2.582 2.582 0 000 2.543c.22.39.539.721.927.963a2.879 2.879 0 002.64.203 3.237 3.237 0 01-3.249.231 3.08 3.08 0 01-1.24-1.105 2.91 2.91 0 01-.458-1.563c0-.552.158-1.093.458-1.564.3-.471.729-.853 1.24-1.105a3.236 3.236 0 013.249.231z"
          fill="#FC0"
        />
        <Path
          d="M3.02 17.357h24.96v1.44H3.02v-1.44zm0 2.933h24.96v1.44H3.02v-1.44zM3 23.56h24.96V25H3v-1.44z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_2426_198559">
          <Path fill="#fff" transform="translate(3 7)" d="M0 0H25V18H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export function UsaFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Path
        d="M25.602 22.741c0 .253-.19.46-.422.46H6.023c-.232 0-.421-.207-.421-.46V9.261c0-.254.189-.46.42-.46h19.159c.231 0 .42.206.42.46v13.48z"
        fill="#FFF7EE"
      />
      <Path
        d="M25.18 8.8h-11.4v1.386h11.822V9.26c0-.253-.19-.46-.422-.46zM13.78 14.008v.864c0 .286-.214.52-.475.52h12.297v-1.384H13.78zM25.602 16.61h-20v1.384h20V16.61z"
        fill="#E02424"
      />
      <Path
        opacity={0.1}
        d="M6.886 16.61H5.602v1.384h1.284V16.61z"
        fill="#000"
      />
      <Path
        d="M25.602 19.213h-20v1.385h20v-1.385zM25.602 22.742v-.926h-20v.926c0 .*************.46h19.159c.231 0 .42-.207.42-.46zM25.601 11.404h-11.82v1.385H25.6v-1.385z"
        fill="#E02424"
      />
      <Path
        d="M6.023 8.8c-.232 0-.421.207-.421.46v6.132h8.153c.26 0 .474-.235.474-.521V8.8H6.023z"
        fill="#0853A4"
      />
      <Path
        opacity={0.1}
        d="M7.349 8.8H6.023c-.232 0-.421.207-.421.46v6.132h1.326V9.26c0-.253.19-.46.42-.46z"
        fill="#000"
      />
      <Path
        d="M7.465 10.636a.057.057 0 01-.028-.008l-.115-.067-.114.067a.056.056 0 01-.064-.005.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.128.021a.063.063 0 01.05.046.073.073 0 01-.016.07l-.093.1.022.143a.071.071 0 01-.024.066.052.052 0 01-.035.013zM7.465 11.804a.057.057 0 01-.028-.008l-.115-.067-.114.067a.056.056 0 01-.029.008.057.057 0 01-.035-.013.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.128.021a.064.064 0 01.05.046.073.073 0 01-.016.07l-.093.1.022.143a.072.072 0 01-.024.066.053.053 0 01-.035.013zM7.465 12.971a.057.057 0 01-.028-.008l-.115-.067-.114.067a.056.056 0 01-.064-.005.071.071 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.069.063.063 0 01.049-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.128.021a.064.064 0 01.05.046.072.072 0 01-.016.069l-.093.101.022.143a.071.071 0 01-.024.065.053.053 0 01-.035.013zM7.465 14.141a.057.057 0 01-.028-.008l-.115-.067-.114.067a.056.056 0 01-.029.008.057.057 0 01-.035-.013.071.071 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.07.063.063 0 01.049-.045l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.128.02a.064.064 0 01.05.047.073.073 0 01-.016.069l-.093.1.022.144a.071.071 0 01-.024.065.053.053 0 01-.035.013zM6.788 10.052a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.063-.005.07.07 0 01-.024-.066l.022-.142-.093-.102a.073.073 0 01-.015-.069.063.063 0 01.049-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.049.046.073.073 0 01-.015.07l-.093.1.021.143a.071.071 0 01-.023.065.054.054 0 01-.036.014zM6.787 11.219a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.07.07 0 01-.023-.066l.021-.142-.093-.101a.073.073 0 01-.015-.07.062.062 0 01.049-.045l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.053.037l.058.13.128.021a.063.063 0 01.05.046.073.073 0 01-.016.069l-.093.1.022.143a.071.071 0 01-.024.066.055.055 0 01-.035.013zM6.787 12.388a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.07.07 0 01-.023-.066l.021-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.053.037l.058.13.128.021a.063.063 0 01.05.046.073.073 0 01-.016.07l-.093.1.022.143a.072.072 0 01-.024.066.055.055 0 01-.035.013zM6.787 13.557a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.07.07 0 01-.023-.066l.021-.143-.093-.1a.073.073 0 01-.015-.07.062.062 0 01.049-.045l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.053.037l.058.13.128.021a.063.063 0 01.05.046.073.073 0 01-.016.069l-.093.1.022.143a.071.071 0 01-.024.066.055.055 0 01-.035.013zM6.787 14.725a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.07.07 0 01-.023-.065l.021-.143-.093-.101a.073.073 0 01-.015-.07.063.063 0 01.049-.045l.128-.021.058-.13a.06.06 0 01.054-.037.06.06 0 01.053.037l.058.13.128.02a.063.063 0 01.05.047.073.073 0 01-.016.069l-.093.1.022.144a.071.071 0 01-.024.065.055.055 0 01-.035.013zM8.782 10.636a.056.056 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.063-.005.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.048-.046l.129-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.021a.063.063 0 01.049.046.073.073 0 01-.015.07l-.093.1.022.143a.071.071 0 01-.024.066.056.056 0 01-.036.013zM8.782 11.804a.056.056 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.035-.013.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.064.064 0 01.048-.046l.129-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.021a.064.064 0 01.049.046.073.073 0 01-.015.07l-.093.1.022.143a.072.072 0 01-.024.066.057.057 0 01-.036.013zM8.782 12.971a.056.056 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.063-.005.071.071 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.069.063.063 0 01.048-.046l.129-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.021a.063.063 0 01.049.046.073.073 0 01-.015.069l-.093.101.022.143a.071.071 0 01-.024.065.056.056 0 01-.036.013zM8.782 14.141a.056.056 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.035-.013.071.071 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.07.064.064 0 01.048-.045l.129-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.02a.064.064 0 01.049.047.073.073 0 01-.015.069l-.093.1.022.144a.071.071 0 01-.024.065.057.057 0 01-.036.013zM8.104 10.051a.057.057 0 01-.029-.007l-.114-.068-.115.068a.056.056 0 01-.064-.006.071.071 0 01-.024-.065l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.048.046.073.073 0 01-.015.07l-.093.1.022.143a.071.071 0 01-.024.065.052.052 0 01-.034.013zM8.104 11.219a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.071.071 0 01-.024-.066l.022-.142-.093-.101a.073.073 0 01-.015-.07.062.062 0 01.049-.045l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.021a.063.063 0 01.049.046.073.073 0 01-.016.069l-.093.1.022.143a.071.071 0 01-.024.066.053.053 0 01-.034.013zM8.104 12.388a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.021a.063.063 0 01.049.046.073.073 0 01-.016.07l-.093.1.022.143a.072.072 0 01-.024.066.053.053 0 01-.034.013zM8.104 13.557a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.062.062 0 01.049-.045l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.021a.063.063 0 01.049.046.073.073 0 01-.016.069l-.093.1.022.143a.071.071 0 01-.024.066.053.053 0 01-.034.013zM8.104 14.725a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.071.071 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.07.063.063 0 01.049-.045l.128-.021.057-.13a.06.06 0 01.054-.037.06.06 0 01.054.037l.058.13.128.02a.063.063 0 01.049.047.073.073 0 01-.016.069l-.093.1.022.144a.071.071 0 01-.024.065.053.053 0 01-.034.013zM10.1 10.636a.057.057 0 01-.029-.008l-.115-.067-.114.067a.056.056 0 01-.064-.005.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.021a.063.063 0 01.05.046.073.073 0 01-.016.07l-.093.1.022.143a.07.07 0 01-.024.066.056.056 0 01-.036.013zM10.1 11.804a.058.058 0 01-.029-.008l-.115-.067-.114.067a.056.056 0 01-.029.008.057.057 0 01-.035-.013.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.021a.064.064 0 01.05.046.073.073 0 01-.016.07l-.093.1.022.143a.072.072 0 01-.024.066.056.056 0 01-.036.013zM10.1 12.971a.057.057 0 01-.029-.008l-.115-.067-.114.067a.056.056 0 01-.064-.005.071.071 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.069.063.063 0 01.049-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.021a.064.064 0 01.05.046.073.073 0 01-.016.069l-.093.101.022.143a.071.071 0 01-.024.065.056.056 0 01-.036.013zM10.1 14.141a.058.058 0 01-.029-.008l-.115-.067-.114.067a.056.056 0 01-.029.008.057.057 0 01-.035-.013.071.071 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.07.063.063 0 01.049-.045l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.058.13.128.02a.064.064 0 01.05.047.073.073 0 01-.016.069l-.093.1.022.144a.071.071 0 01-.024.065.056.056 0 01-.036.013zM9.421 10.051a.056.056 0 01-.028-.007l-.115-.068-.115.068a.056.056 0 01-.063-.006.071.071 0 01-.024-.065l.022-.143-.093-.1a.073.073 0 01-.016-.07.063.063 0 01.05-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.062.062 0 01.048.046.073.073 0 01-.015.07l-.093.1.022.143a.071.071 0 01-.*************** 0 01-.035.013zM9.42 11.219a.056.056 0 01-.027-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.071.071 0 01-.023-.066l.021-.142-.093-.101a.073.073 0 01-.015-.07.062.062 0 01.05-.045l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.062.062 0 01.048.046.073.073 0 01-.015.069l-.093.1.022.143a.071.071 0 01-.024.066.055.055 0 01-.035.013zM9.42 12.388a.056.056 0 01-.027-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.071.071 0 01-.023-.066l.021-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.05-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.062.062 0 01.048.046.073.073 0 01-.015.07l-.093.1.022.143a.072.072 0 01-.024.066.055.055 0 01-.035.013zM9.42 13.557a.056.056 0 01-.027-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.071.071 0 01-.023-.066l.021-.143-.093-.1a.073.073 0 01-.015-.07.062.062 0 01.05-.045l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.062.062 0 01.048.046.073.073 0 01-.015.069l-.093.1.022.143a.071.071 0 01-.024.066.055.055 0 01-.035.013zM9.42 14.725a.056.056 0 01-.027-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.071.071 0 01-.023-.065l.021-.143-.093-.101a.073.073 0 01-.015-.07.063.063 0 01.05-.045l.128-.021.057-.13a.06.06 0 01.054-.037.06.06 0 01.054.037l.057.13.129.02a.062.062 0 **************.073 0 01-.015.069l-.093.1.022.144a.071.071 0 01-.024.065.055.055 0 01-.035.013zM11.416 10.636a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.063-.005.07.07 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.048-.046l.129-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.062.062 0 01.048.046.073.073 0 01-.015.07l-.093.1.022.143a.07.07 0 01-.024.066.053.053 0 01-.035.013zM11.416 11.804a.058.058 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.056.056 0 01-.035-.013.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.048-.046l.129-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.048.046.073.073 0 01-.015.07l-.093.1.022.143a.072.072 0 01-.024.066.053.053 0 01-.035.013zM11.416 12.971a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.063-.005.071.071 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.069.063.063 0 01.048-.046l.129-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.048.046.073.073 0 01-.015.069l-.093.101.022.143a.071.071 0 01-.024.065.053.053 0 01-.035.013zM11.416 14.141a.058.058 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.056.056 0 01-.035-.013.071.071 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.07.063.063 0 01.048-.045l.129-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.02a.063.063 0 **************.073 0 01-.015.069l-.093.1.022.144a.071.071 0 01-.024.065.053.053 0 01-.035.013zM10.738 10.051a.057.057 0 01-.028-.007l-.115-.068-.115.068a.056.056 0 01-.064-.006.07.07 0 01-.024-.065l.022-.143-.093-.1a.073.073 0 01-.015-.07.064.064 0 01.049-.046l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.048.046.073.073 0 01-.015.07l-.093.1.022.143a.07.07 0 01-.024.065.056.056 0 01-.035.013zM10.738 11.219a.058.058 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.07.07 0 01-.024-.066l.022-.142-.093-.101a.073.073 0 01-.015-.07.062.062 0 01.049-.045l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.048.046.073.073 0 01-.015.069l-.093.1.022.143a.07.07 0 01-.024.066.056.056 0 01-.035.013zM10.738 12.388a.058.058 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.064.064 0 01.049-.046l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.048.046.073.073 0 01-.015.07l-.093.1.022.143a.072.072 0 01-.024.066.056.056 0 01-.035.013zM10.738 13.557a.058.058 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.07.07 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.062.062 0 01.049-.045l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.048.046.073.073 0 01-.015.069l-.093.1.022.143a.07.07 0 01-.024.066.056.056 0 01-.035.013zM10.738 14.725a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.07.07 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.07.064.064 0 01.049-.045l.128-.021.058-.13a.06.06 0 01.054-.037.06.06 0 01.054.037l.057.13.129.02a.063.063 0 **************.073 0 01-.015.069l-.093.1.022.144a.07.07 0 01-.024.065.056.056 0 01-.035.013zM12.733 10.636a.057.057 0 01-.028-.008l-.115-.067-.114.067a.056.056 0 01-.064-.005.07.07 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.058-.13a.06.06 0 01.053-.038.06.06 0 01.054.037l.058.13.128.021a.062.062 0 01.049.046.073.073 0 01-.015.07l-.094.1.022.143a.071.071 0 01-.023.066.054.054 0 01-.036.013zM12.733 11.804a.057.057 0 01-.028-.008l-.115-.067-.114.067a.056.056 0 01-.029.008.057.057 0 01-.035-.013.07.07 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.058-.13a.06.06 0 01.053-.038.06.06 0 01.054.037l.058.13.128.021a.063.063 0 01.049.046.073.073 0 01-.015.07l-.094.1.022.143a.072.072 0 01-.023.066.055.055 0 01-.036.013zM12.733 12.971a.057.057 0 01-.028-.008l-.115-.067-.114.067a.056.056 0 01-.064-.005.07.07 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.069.063.063 0 01.049-.046l.128-.02.058-.13a.06.06 0 01.053-.038.06.06 0 01.054.037l.058.13.128.021a.063.063 0 01.049.046.073.073 0 01-.015.069l-.094.101.022.143a.071.071 0 01-.023.065.055.055 0 01-.036.013zM12.733 14.141a.057.057 0 01-.028-.008l-.115-.067-.114.067a.056.056 0 01-.029.008.057.057 0 01-.035-.013.07.07 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.07.063.063 0 01.049-.045l.128-.02.058-.13a.06.06 0 01.053-.038.06.06 0 01.054.037l.058.13.128.02a.063.063 0 01.049.047.073.073 0 01-.015.069l-.094.1.022.144a.071.071 0 01-.023.065.055.055 0 01-.036.013zM12.055 10.051a.057.057 0 01-.028-.007l-.115-.068-.115.068a.056.056 0 01-.064-.006.071.071 0 01-.024-.065l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.062.062 0 01.048.046.073.073 0 01-.015.07l-.093.1.022.143a.071.071 0 01-.*************** 0 01-.035.013zM12.055 11.219a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.071.071 0 01-.024-.066l.022-.142-.093-.101a.073.073 0 01-.015-.07.062.062 0 01.049-.045l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.062.062 0 01.048.046.073.073 0 01-.015.069l-.093.1.022.143a.071.071 0 01-.024.066.055.055 0 01-.035.013zM12.055 12.388a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.072.072 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.063.063 0 01.049-.046l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.062.062 0 01.048.046.073.073 0 01-.015.07l-.093.1.022.143a.072.072 0 01-.024.066.055.055 0 01-.035.013zM12.055 13.557a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.028.008.057.057 0 01-.036-.013.071.071 0 01-.024-.066l.022-.143-.093-.1a.073.073 0 01-.015-.07.062.062 0 01.049-.045l.128-.02.058-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.062.062 0 01.048.046.073.073 0 01-.015.069l-.093.1.022.143a.071.071 0 01-.024.066.055.055 0 01-.035.013zM12.055 14.725a.057.057 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.071.071 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.015-.07.063.063 0 01.049-.045l.128-.021.058-.13a.06.06 0 01.054-.037.06.06 0 01.054.037l.057.13.129.02a.062.062 0 **************.073 0 01-.015.069l-.093.1.022.144a.071.071 0 01-.024.065.055.055 0 01-.035.013zM13.372 10.051a.056.056 0 01-.028-.007l-.115-.068-.115.068a.056.056 0 01-.064-.006.07.07 0 01-.023-.065l.022-.143-.093-.1a.073.073 0 01-.016-.07.063.063 0 01.05-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.128.021a.063.063 0 01.05.046.073.073 0 01-.016.07l-.093.1.022.143a.07.07 0 01-.*************** 0 01-.035.013zM13.372 11.219a.056.056 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.07.07 0 01-.023-.066l.022-.142-.093-.101a.073.073 0 01-.016-.07.062.062 0 01.05-.045l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.048.046.073.073 0 01-.015.069l-.093.1.022.143a.07.07 0 01-.024.066.054.054 0 01-.035.013zM13.372 12.388a.056.056 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.071.071 0 01-.023-.066l.022-.143-.093-.1a.073.073 0 01-.016-.07.063.063 0 01.05-.046l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.048.046.073.073 0 01-.015.07l-.093.1.022.143a.071.071 0 01-.024.066.054.054 0 01-.035.013zM13.372 13.557a.056.056 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.064-.005.07.07 0 01-.023-.066l.022-.143-.093-.1a.073.073 0 01-.016-.07.062.062 0 01.05-.045l.128-.02.057-.13a.06.06 0 01.054-.038.06.06 0 01.054.037l.057.13.129.021a.063.063 0 01.048.046.073.073 0 01-.015.069l-.093.1.022.143a.07.07 0 01-.024.066.054.054 0 01-.035.013zM13.372 14.725a.056.056 0 01-.028-.008l-.115-.067-.115.067a.056.056 0 01-.063-.005.07.07 0 01-.024-.065l.022-.143-.093-.101a.073.073 0 01-.016-.07.063.063 0 01.05-.045l.128-.021.057-.13a.06.06 0 01.054-.037.06.06 0 01.054.037l.057.13.129.02a.063.063 0 **************.073 0 01-.015.069l-.093.1.022.144a.07.07 0 01-.*************** 0 01-.035.013z"
        fill="#fff"
      />
      <Path
        opacity={0.1}
        d="M6.886 19.213H5.602v1.385h1.284v-1.385zM6.886 22.742v-.926H5.602v.926c0 .*************.46h1.285c-.232 0-.421-.207-.421-.46z"
        fill="#000"
      />
    </Svg>
  );
}

export function GbrFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <G clipPath="url(#clip0_2426_267906)">
        <Path d="M25.602 8.8h-20v14.4h20V8.8z" fill="#F0F0F0" />
        <Path
          d="M16.852 8.8h-2.5v5.85h-8.75v2.7h8.75v5.85h2.5v-5.85h8.75v-2.7h-8.75V8.8z"
          fill="#D80027"
        />
        <Path
          d="M20.984 18.506l4.617 2.77v-2.77h-4.617zM17.775 18.506l7.827 4.695v-1.327l-5.613-3.368h-2.214zM23.517 23.2l-5.742-3.444V23.2h5.742z"
          fill="#0052B4"
        />
        <Path
          d="M17.775 18.506l7.827 4.695v-1.327l-5.613-3.368h-2.214z"
          fill="#F0F0F0"
        />
        <Path
          d="M17.775 18.506l7.827 4.695v-1.327l-5.613-3.368h-2.214z"
          fill="#D80027"
        />
        <Path
          d="M9.13 18.506l-3.528 2.117v-2.117H9.13zM13.428 19.104v4.098h-6.83l6.83-4.099z"
          fill="#0052B4"
        />
        <Path
          d="M11.214 18.506l-5.612 3.368V23.2l7.826-4.695h-2.214z"
          fill="#D80027"
        />
        <Path
          d="M10.22 13.497l-4.618-2.77v2.77h4.617zM13.428 13.496L5.602 8.801v1.327l5.612 3.368h2.214zM7.686 8.8l5.742 3.446V8.8H7.686z"
          fill="#0052B4"
        />
        <Path
          d="M13.428 13.496L5.602 8.801v1.327l5.612 3.368h2.214z"
          fill="#F0F0F0"
        />
        <Path
          d="M13.428 13.496L5.602 8.801v1.327l5.612 3.368h2.214z"
          fill="#D80027"
        />
        <Path
          d="M22.073 13.496L25.6 11.38v2.117h-3.528zM17.775 12.9V8.8h6.83l-6.83 4.1z"
          fill="#0052B4"
        />
        <Path
          d="M19.989 13.496l5.613-3.368V8.801l-7.827 4.695h2.214z"
          fill="#D80027"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_2426_267906">
          <Path
            fill="#fff"
            transform="translate(5.602 8.8)"
            d="M0 0H20V14.4H0z"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export function SgpFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Path d="M25.602 8.8h-20v14.4h20V8.8z" fill="#F0F0F0" />
      <Path d="M25.602 8.8h-20V16h20V8.8z" fill="#D80027" />
      <Path
        d="M8.862 12.4c0-1.049.683-1.925 1.595-2.14a1.9 1.9 0 00-.435-.051c-1.12 0-2.03.98-2.03 2.191 0 1.21.91 2.191 2.03 2.191a1.9 1.9 0 00.435-.05c-.912-.215-1.595-1.092-1.595-2.14zM11.471 10.365l.144.479h.466l-.377.295.144.478-.377-.295-.377.295.144-.478-.376-.295h.465l.144-.479z"
        fill="#F0F0F0"
      />
      <Path
        d="M10.341 11.305l.145.478h.465l-.377.295.144.479-.377-.296-.376.296.143-.479-.376-.295h.466l.143-.478zM12.6 11.305l.145.478h.465l-.377.295.144.479-.376-.296-.377.296.144-.479-.377-.295h.466l.144-.478zM12.166 12.713l.144.478h.465l-.376.296.144.478-.377-.296-.377.296.144-.478-.377-.296h.466l.144-.478zM10.776 12.713l.144.478h.466l-.377.296.144.478-.377-.296-.376.296.143-.478-.376-.296h.465l.144-.478z"
        fill="#F0F0F0"
      />
    </Svg>
  );
}

export function JpnFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Path d="M5.602 23.201h20v-14.4h-20v14.4z" fill="#F7F7F7" />
      <Path
        d="M15.706 11.966c2.29 0 4.145 1.864 4.145 4.163 0 2.298-1.856 4.162-4.145 4.162-2.29 0-4.145-1.864-4.145-4.162 0-2.3 1.855-4.163 4.145-4.163z"
        fill="#D60A2E"
      />
    </Svg>
  );
}

export function KorFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <G clipPath="url(#clip0_2426_95881)">
        <Path fill="#F7F7F7" d="M5.60156 8.80078H30.60156V26.80078H5.60156z" />
        <Path
          d="M18.914 12.01l-.407.271.74 1.112.407-.271-.74-1.111zM19.777 13.33l-.401.268.741 1.11.402-.268-.742-1.11zM19.513 11.604l-.407.271 1.62 2.43.406-.271-1.62-2.43zM20.122 11.208l-.407.27.743 1.117.407-.27-.743-1.117zM20.997 12.523l.739 1.115-.405.268-.74-1.114.406-.269zM11.22 17.139l-.407.271 1.617 2.425.407-.271-1.617-2.425zM10.613 17.54l-.406.27.74 1.111.407-.27-.74-1.111zM11.493 18.858l-.407.27.74 1.112.407-.27-.74-1.112zM10.012 17.944l-.407.27 1.617 2.426.406-.271-1.616-2.425zM12.43 12.01l-1.616 2.425.402.268 1.616-2.425-.402-.268zM11.83 11.603l-1.619 2.43.407.271 1.62-2.43-.407-.27zM11.22 11.202l-1.618 2.43.406.272 1.619-2.431-.407-.271zM19.24 18.452l-.741 1.11.406.272.742-1.11-.407-.272zM20.122 17.136l-.742 1.116.402.267.742-1.116-.402-.267zM19.85 18.862l-.741 1.11.406.272.74-1.111-.406-.271zM19.984 18.655l.745-1.114.405.274-.745 1.109-.405-.269zM20.455 19.263l-.742 1.11.407.271.741-1.11-.406-.27zM21.333 17.953l-.74 1.111.402.268.74-1.111-.402-.268z"
          fill="#000"
        />
        <Path
          d="M13.244 14.305s-.024.03-.03.047c.012-.018.018-.035.03-.047z"
          fill="#B50037"
        />
        <Path
          d="M17.29 13.494a2.918 2.918 0 00-4.046.81 1.454 1.454 0 00.405 2.02 1.454 1.454 0 002.02-.405 1.454 1.454 0 012.02-.405c.674.447.853 1.353.406 2.02a2.922 2.922 0 00-.805-4.046"
          fill="#B50037"
        />
        <Path
          d="M13.244 14.305a1.453 1.453 0 00.405 2.02 1.454 1.454 0 002.02-.405 1.453 1.453 0 012.02-.405c.674.446.853 1.352.406 2.02a2.918 2.918 0 01-4.857-3.236"
          fill="#1E3476"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_2426_95881">
          <Path
            fill="#fff"
            transform="translate(5.602 8.8)"
            d="M0 0H20V14.4H0z"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export function ChnFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Path d="M5.602 8.8h20v14.4h-20V8.8z" fill="#D60A2E" />
      <Path
        d="M8.934 11.43l.701 1.467 1.568.235-1.135 1.142.268 1.612-1.402-.76-1.402.76.268-1.612-1.134-1.142 1.567-.235.701-1.467zM13.086 14.244l.28.586.627.094-.453.456.106.644-.56-.304-.56.304.107-.644-.453-.456.627-.094.28-.586zM12.754 11.682l.513.384.596-.218-.196.622.385.52-.633-.001-.36.538-.194-.622-.607-.186.512-.384-.016-.653zM11.934 9.913l-.12.642.444.466-.627.08-.292.58-.268-.592-.625-.107.462-.447-.094-.646.554.316.566-.292zM11.934 16.49l-.12.643.445.466-.628.08-.292.58-.268-.591-.624-.108.462-.447-.095-.646.555.316.565-.292z"
        fill="#FED953"
      />
    </Svg>
  );
}

export function TwnFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <G clipPath="url(#clip0_46_44)">
        <Path d="M27 9H6v15h21V9z" fill="url(#paint0_linear_46_44)" />
        <Path d="M27 9H6v15h21V9z" fill="url(#paint1_linear_46_44)" />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6 17h11V9H6v8z"
          fill="url(#paint2_linear_46_44)"
        />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.5 14.25l-.957 1.06.073-1.426-1.426.073L10.25 13l-1.06-.957 1.426.073-.073-1.426.957 1.06.957-1.06-.073 1.426 1.426-.073-1.06.957 1.06.957-1.426-.073.073 1.426-.957-1.06z"
          fill="url(#paint3_linear_46_44)"
        />
      </G>
      <Defs>
        <LinearGradient
          id="paint0_linear_46_44"
          x1={1056}
          y1={9}
          x2={1056}
          y2={1509}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#F0F0F0" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_46_44"
          x1={1056}
          y1={9}
          x2={1056}
          y2={1415.51}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#FE3030" />
          <Stop offset={1} stopColor="red" />
        </LinearGradient>
        <LinearGradient
          id="paint2_linear_46_44"
          x1={556}
          y1={9}
          x2={556}
          y2={809}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#0909B6" />
          <Stop offset={1} stopColor="#000096" />
        </LinearGradient>
        <LinearGradient
          id="paint3_linear_46_44"
          x1={240.16}
          y1={10.6903}
          x2={240.16}
          y2={472.63}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#F0F0F0" />
        </LinearGradient>
        <ClipPath id="clip0_46_44">
          <Path fill="#fff" transform="translate(6 9)" d="M0 0H21V15H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export function IndFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <G clipPath="url(#clip0_46_44)">
        <Path d="M27 9H6v15h21V9z" fill="url(#paint0_linear_46_44)" />
        <Path d="M27 9H6v5h21V9z" fill="url(#paint1_linear_46_44)" />
        <Path d="M27 19H6v5h21v-5z" fill="url(#paint2_linear_46_44)" />
        <Path d="M27 14H6v5h21v-5z" fill="url(#paint3_linear_46_44)" />
        <Path
          d="M16.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"
          fill="#181A93"
          fillOpacity={0.15}
        />
        <Path
          d="M16.5 18.5a2 2 0 110-4 2 2 0 010 4zm0-.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"
          fill="#181A93"
        />
        <Path d="M16.5 17.5a1 1 0 100-2 1 1 0 000 2z" fill="#181A93" />
      </G>
      <Defs>
        <LinearGradient
          id="paint0_linear_46_44"
          x1={1056}
          y1={9}
          x2={1056}
          y2={1509}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#F0F0F0" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_46_44"
          x1={1056}
          y1={9}
          x2={1056}
          y2={509}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#FFA44A" />
          <Stop offset={1} stopColor="#FF9934" />
        </LinearGradient>
        <LinearGradient
          id="paint2_linear_46_44"
          x1={1056}
          y1={19}
          x2={1056}
          y2={519}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#1A9F0B" />
          <Stop offset={1} stopColor="#138806" />
        </LinearGradient>
        <LinearGradient
          id="paint3_linear_46_44"
          x1={1056}
          y1={14}
          x2={1056}
          y2={514}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#F0F0F0" />
        </LinearGradient>
        <ClipPath id="clip0_46_44">
          <Path fill="#fff" transform="translate(6 9)" d="M0 0H21V15H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export function IdnFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Path d="M25.602 8.8h-20v14.4h20V8.8z" fill="#F0F0F0" />
      <Path d="M25.602 8.8h-20V16h20V8.8z" fill="#D80027" />
    </Svg>
  );
}

export function AusFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Path d="M25.602 8.8h-20v14.4h20V8.8z" fill="#0052B4" />
      <Path
        d="M14.328 16l.032-.04-.032.04zM12.993 17.77l.428.966.966-.24-.432.963.777.666-.968.236.003 1.07-.774-.67-.774.67.003-1.07-.968-.236.777-.666-.433-.964.967.241.428-.966zM20.722 18.97l.204.462.461-.115-.206.46.37.317-.461.112.001.511-.369-.32-.369.32.001-.51-.462-.113.371-.318-.206-.*************-.461zM18.822 14.08l.205.461.46-.115-.206.46.371.318-.462.112.002.511-.37-.32-.369.32.002-.511-.462-.113.37-.317-.206-.46.461.115.204-.46zM20.722 11.283l.204.461.461-.115-.206.46.37.318-.461.112.001.51-.369-.319-.369.32.001-.511-.462-.112.371-.318-.206-.46.46.115.205-.46zM22.381 13.38l.204.462.461-.115-.206.46.37.317-.461.113.001.51-.369-.32-.37.32.002-.51-.462-.113.371-.317-.206-.46.46.115.205-.461zM21.195 15.826l.16.534h.52l-.42.33.16.533-.42-.33-.421.33.161-.533-.42-.33h.52l.16-.534zM15.602 8.8v1.29l-1.765 1.059h1.765v2.504h-2.309l2.309 1.385v.963h-1.043l-2.87-1.723v1.723H9.514v-2.05L6.1 16.001h-.497v-1.29l1.764-1.058H5.602v-2.505H7.91L5.602 9.764v-.962h1.042l2.87 1.722V8.801h2.175v2.05l3.415-2.05h.498z"
        fill="#F0F0F0"
      />
      <Path
        d="M11.227 8.8h-1.25v2.926H5.602v1.35h4.375V16h1.25v-2.925h4.375v-1.35h-4.375V8.8z"
        fill="#D80027"
      />
      <Path
        d="M11.689 13.652L15.6 16v-.664l-2.806-1.684h-1.106z"
        fill="#0052B4"
      />
      <Path
        d="M11.689 13.652L15.6 16v-.664l-2.806-1.684h-1.106z"
        fill="#F0F0F0"
      />
      <Path
        d="M11.689 13.652L15.6 16v-.664l-2.806-1.684h-1.106zM8.408 13.652l-2.806 1.684V16l3.912-2.348H8.408zM9.514 11.149L5.602 8.8v.664l2.806 1.684h1.106zM12.795 11.149L15.6 9.465V8.8l-3.912 2.348h1.106z"
        fill="#D80027"
      />
    </Svg>
  );
}

export function NzlFlagIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <G clipPath="url(#clip0_46_44)">
        <Path d="M27 9H6v15h21V9z" fill="url(#paint0_linear_46_44)" />
        <Path d="M27 9H6v15h21V9z" fill="url(#paint1_linear_46_44)" />
        <Path
          d="M9 12.23L4.648 8.5H6.66l3.5 2.5h.697L15.5 8.098V9.25c0 .303-.167.627-.418.806L12 12.257v.513l3.137 2.69c.462.395.204 1.04-.387 1.04-.245 0-.545-.096-.749-.242L10.84 14h-.697L5.5 16.902v-1.66l3.5-2.5v-.512z"
          fill="url(#paint2_linear_46_44)"
        />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.5 12L6 9h.5l3.5 2.5h1L15 9v.25a.537.537 0 01-.209.399L11.5 12v1l3.312 2.839c.***************-.062.161a.898.898 0 01-.459-.149L11 13.5h-1L6 16v-.5L9.5 13v-1z"
          fill="url(#paint3_linear_46_44)"
        />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6 11.5v2h3.5v2.505c0 .273.214.495.505.495h.99a.496.496 0 00.505-.495V13.5h3.51a.49.49 0 00.49-.505v-.99a.495.495 0 00-.49-.505H11.5V9h-2v2.5H6z"
          fill="url(#paint4_linear_46_44)"
        />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6 12h4V9h1v3h4v1h-4v3h-1v-3H6v-1z"
          fill="url(#paint5_linear_46_44)"
        />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M21 22l-.707.207.207-.707-.207-.707L21 21l.707-.207-.207.707.207.707L21 22zM21 12.5l-.707.207L20.5 12l-.207-.707.707.207.707-.207L21.5 12l.207.707L21 12.5zM24 15.5l-.707.207L23.5 15l-.207-.707.707.207.707-.207L24.5 15l.207.707L24 15.5zM18 16.5l-.707.207L17.5 16l-.207-.707.707.207.707-.207L18.5 16l.207.707L18 16.5z"
          fill="#CA1931"
        />
      </G>
      <Defs>
        <LinearGradient
          id="paint0_linear_46_44"
          x1={1056}
          y1={9}
          x2={1056}
          y2={1509}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#F0F0F0" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_46_44"
          x1={1056}
          y1={9}
          x2={1056}
          y2={1509}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#0A17A7" />
          <Stop offset={1} stopColor="#030E88" />
        </LinearGradient>
        <LinearGradient
          id="paint2_linear_46_44"
          x1={547.23}
          y1={8.0979}
          x2={547.23}
          y2={888.523}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#F0F0F0" />
        </LinearGradient>
        <LinearGradient
          id="paint3_linear_46_44"
          x1={456}
          y1={9}
          x2={456}
          y2={709}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#DB1E36" />
          <Stop offset={1} stopColor="#D51931" />
        </LinearGradient>
        <LinearGradient
          id="paint4_linear_46_44"
          x1={481}
          y1={9}
          x2={481}
          y2={759}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#F0F0F0" />
        </LinearGradient>
        <LinearGradient
          id="paint5_linear_46_44"
          x1={456}
          y1={9}
          x2={456}
          y2={709}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#DB1E36" />
          <Stop offset={1} stopColor="#D51931" />
        </LinearGradient>
        <ClipPath id="clip0_46_44">
          <Path fill="#fff" transform="translate(6 9)" d="M0 0H21V15H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
