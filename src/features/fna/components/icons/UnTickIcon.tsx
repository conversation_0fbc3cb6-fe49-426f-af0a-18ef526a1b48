import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Circle, Path } from 'react-native-svg';

interface Props extends SvgIconProps {
  size?: number;
}

const UnTickIcon = (props: Props) => (
  <Svg
    width={props.size || 24}
    height={props.size || 24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}>
    <Circle
      cx={12}
      cy={12}
      r={11}
      fill="#F8F9F9"
      stroke="#E87722"
      strokeWidth={2}
    />
    <Path
      fill="#FAE4D3"
      fillRule="evenodd"
      stroke="#FAE4D3"
      d="m9.98 16-3.703-3.79.224-.21a.994.994 0 0 1 1.34 0l2.14 2.327 6.477-6.067a.994.994 0 0 1 1.34 0l.223.21L9.981 16Z"
      clipRule="evenodd"
    />
  </Svg>
);
export default UnTickIcon;
