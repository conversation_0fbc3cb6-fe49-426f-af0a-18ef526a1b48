import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, <PERSON>, Defs, <PERSON>lipPath, SvgProps } from 'react-native-svg';

function GraphIcon(props: SvgProps) {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      <G clipPath="url(#clip0_2120_333199)">
        <Mask
          id="a"
          style={{
            maskType: 'luminance',
          }}
          maskUnits="userSpaceOnUse"
          x={0}
          y={0}
          width={25}
          height={24}>
          <Path d="M0 0h24v24H0V0z" fill="#fff" />
        </Mask>
        <G mask="url(#a)" fill="#E87722">
          <Path d="M21.65 19.003H2.52a.842.842 0 100 1.684H21.65a.843.843 0 000-1.684zM22.076 3.07a.842.842 0 00-1.183-.145l-7.06 5.517-4.507-3.55a.842.842 0 00-1.1.053l-4.83 4.633a.843.843 0 001.166 1.215l4.3-4.125 4.45 3.505a.842.842 0 001.04 0l7.58-5.925a.842.842 0 00.146-1.182l-.002.004z" />
          <Path d="M22.05 3.067a.845.845 0 00-.577-.29l-4.717-.337a.837.837 0 00-.86 1.106.842.842 0 00.74.574l3.872.276-.272 3.536a.843.843 0 00.775.9h.065a.843.843 0 00.84-.777l.337-4.38a.843.843 0 00-.2-.615l-.002.007zM14.059 13.837a.841.841 0 00-.842.843v5.166a.842.842 0 001.684 0V14.68a.84.84 0 00-.842-.843zM18.158 11.45a.841.841 0 00-.842.843v7.553a.842.842 0 001.684 0v-7.553a.843.843 0 00-.842-.843zM9.988 10.44a.842.842 0 00-.842.842v8.564a.842.842 0 101.684 0v-8.564a.842.842 0 00-.842-.842zM5.888 14.174a.842.842 0 00-.842.842v4.83a.842.842 0 001.684 0v-4.83a.842.842 0 00-.842-.842z" />
        </G>
      </G>
      <Defs>
        <ClipPath id="clip0_2120_333199">
          <Path fill="#fff" d="M0 0H24.0003V24H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default GraphIcon;
