import * as React from 'react';
import Svg, { Circle, Path, SvgProps } from 'react-native-svg';

function NonGenderAvatar(props: SvgProps) {
  return (
    <Svg width={40} height={41} viewBox="0 0 40 41" fill="none" {...props}>
      <Circle cx={20} cy={20.5} r={20} fill="#FAE4D3" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20 15.054c-1.103 0-2 .897-2 2s.897 2 2 2 2-.897 2-2-.897-2-2-2zm0 5.333a3.337 3.337 0 01-3.333-3.333A3.337 3.337 0 0120 13.721a3.337 3.337 0 013.333 3.333A3.337 3.337 0 0120 20.387zm5 6h-.333v-1.416a2.584 2.584 0 00-2.584-2.584h-4.166a2.584 2.584 0 00-2.584 2.584v1.416H15a1 1 0 01-1-1v-.416a3.918 3.918 0 013.917-3.917h4.166A3.918 3.918 0 0126 24.971v.416a1 1 0 01-1 1z"
        fill="#E87722"
      />
    </Svg>
  );
}

export default NonGenderAvatar;
