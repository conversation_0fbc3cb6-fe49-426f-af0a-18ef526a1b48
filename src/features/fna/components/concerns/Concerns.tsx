import React, { useEffect, useMemo, useState } from 'react';
import { Concern, ConcernId } from 'features/fna/types/concern';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { shallow } from 'zustand/shallow';
import { useFnaConcerns } from 'features/fna/hooks/useFnaConcerns';
import DraggableFlatList from 'react-native-draggable-flatlist';
import { FlatList } from 'react-native-gesture-handler';
import { Box, Column, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ConcernItemPhone from './ConcernItem.phone';
import ConcernItemTablet from './ConcernItem.tablet';

export interface ConcernItemProps {
  selected: boolean;
  disabled: boolean;
  item: Concern;
  index?: number;
  onPress: (item: Concern) => void;
  drag?: () => void;
  highlight?: boolean;
}

interface ConcernItemRenderProps {
  item: ConcernDragItemProps;
  drag?: () => void;
}

interface ConcernDragItemProps {
  raw: Concern;
  selected: boolean;
  disabled: boolean;
  index?: number;
}

export function Concerns() {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { maxSelection, items: allConcerns } = useFnaConcerns();
  const { space } = useTheme();
  const [onDrag, setOnDrag] = useState(false);
  const { lifeJourney, updateConcerns, shouldHighlight, updateProductPremiumDevelopmentPotential } = useFnaStore(
    state => ({
      lifeJourney: state.lifeJourney,
      updateConcerns: state.updateConcerns,
      shouldHighlight: state.shouldHighlight,
      updateProductPremiumDevelopmentPotential: state.updateProductPremiumDevelopmentPotential,
    }),
    shallow,
  );

  const toggleConcern = (item: { id: ConcernId; title: string }) => {
    const updatedItems = lifeJourney.concerns.includes(item.id)
      ? lifeJourney.concerns?.filter(id => id !== item.id)
      : [...lifeJourney.concerns, item.id];
    const isPremiumProspect = updatedItems.slice(0, 3).includes('INVESTMENT') ? 'Y' : 'N';
    updateConcerns(updatedItems);
    updateProductPremiumDevelopmentPotential(isPremiumProspect);
  };

  const onDragEnd = ({
    data,
    from,
    to,
  }: {
    data: ConcernDragItemProps[];
    from: number;
    to: number;
  }) => {
    if (from !== to) {
      updateConcerns(data.map(({ raw }) => raw.id));
    }
    const isPremiumProspect = data?.map(({ raw }) => raw.id)?.slice(0, 3)?.includes('INVESTMENT') ? 'Y' : 'N';
    updateProductPremiumDevelopmentPotential(isPremiumProspect);
    setOnDrag(false);
  };

  const { minSelection } = useFnaConcerns();
  const highlight = useMemo(() => {
    return shouldHighlight && lifeJourney.concerns.length < minSelection;
  }, [shouldHighlight, lifeJourney.concerns.length, minSelection]);

  const renderItem = ({
    item: { raw: concern, selected, index, disabled },
    drag,
  }: ConcernItemRenderProps) => {
    const ConcernItem = isTabletMode ? ConcernItemTablet : ConcernItemPhone;

    return (
      <ConcernItem
        highlight={highlight && !selected}
        item={concern}
        index={index}
        selected={selected}
        disabled={disabled}
        onPress={toggleConcern}
        drag={drag}
      />
    );
  };

  const [draggable, setDraggable] = useState<ConcernDragItemProps[]>([]);

  const undraggable = useMemo<ConcernDragItemProps[]>(() => {
    return allConcerns
      .filter(({ id }) => !draggable.find(({ raw }) => raw.id === id))
      .map(raw => ({
        raw,
        selected: false,
        disabled: draggable.length >= maxSelection,
      }));
  }, [allConcerns, draggable, maxSelection]);

  useEffect(() => {
    const selectedConcerns = allConcerns
      .reduce<ConcernDragItemProps[]>((prev, concern) => {
        const selected = lifeJourney.concerns.includes(concern.id);
        if (!selected) return prev;
        const index = lifeJourney.concerns.indexOf(concern.id);
        return [...prev, { raw: concern, selected, index, disabled: false }];
      }, [])
      .sort((aConcern, bConcern) => {
        if (aConcern.index === undefined) return 1;
        if (bConcern.index === undefined) return -1;
        return aConcern.index - bConcern.index;
      });
    setDraggable(selectedConcerns);
  }, [allConcerns, lifeJourney.concerns, maxSelection]);

  const Container = isTabletMode ? Row : Box;

  return (
    <Container>
      <DraggableFlatList
        horizontal={isTabletMode}
        data={draggable}
        activationDistance={onDrag ? -100 : 100}
        renderItem={renderItem}
        keyExtractor={(item: ConcernDragItemProps) => item?.raw?.id}
        contentContainerStyle={{ gap: space[1] }}
        onDragEnd={onDragEnd}
        onDragBegin={() => {
          setOnDrag(true);
        }}
        scrollEnabled={false}
      />
      {draggable.length > 0 && <Column width={space[1]} height={space[1]} />}
      <FlatList
        horizontal={isTabletMode}
        data={undraggable}
        renderItem={renderItem}
        keyExtractor={item => item.raw.id}
        contentContainerStyle={{ gap: space[1] }}
        scrollEnabled={false}
      />
    </Container>
  );
}
