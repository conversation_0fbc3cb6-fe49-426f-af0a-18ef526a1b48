import { Box, Center, Column, H6, Icon, LargeLabel, HighlightShadowV2 } from 'cube-ui-components';
import React from 'react';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import UnTickIcon from 'features/fna/components/icons/UnTickIcon';
import DragAndDrop from '../icons/DragAndDrop';
import { ConcernItemProps } from './Concerns';

export default function ConcernItemTablet({
  item,
  selected,
  disabled,
  index,
  onPress,
  drag,
  highlight,
}: ConcernItemProps) {
  const { colors, space, sizes, borderRadius } = useTheme();

  return (
    <Column py={space[2]} px={space[1]}>
      <HighlightShadowV2
        disabled={!highlight}
        borderRadius={borderRadius.large}>
        <ConcernButton
          key={item.id}
          onPressIn={drag}
          onPress={() => onPress(item)}
          disabled={disabled}
          selected={selected}
          highlight={highlight}>
          <IconContainer>{item.icon}</IconContainer>
          <ConcernTitleContiner>
            <ConcernTitle fontWeight="bold">{item.title}</ConcernTitle>
          </ConcernTitleContiner>
          <Box pos="absolute" top={space[3]} right={space[3]} gap={space[1]}>
            {!selected ? (
              <UnTickIcon size={sizes[6]} />
            ) : (
              [
                <Icon.TickCircle
                  size={sizes[6]}
                  fill={colors.palette.alertGreen}
                />,
                <DragAndDrop />,
              ]
            )}
          </Box>
          {selected && (
            <Center
              pos="absolute"
              top={-space[1]}
              left={-space[1]}
              width={sizes[9]}
              height={sizes[9]}
              borderRadius={borderRadius.full}
              backgroundColor={colors.secondary}>
              <H6 fontWeight="bold" color={colors.onSecondary}>
                {(index || 0) + 1}
              </H6>
            </Center>
          )}
        </ConcernButton>
      </HighlightShadowV2>
    </Column>
  );
}

const ConcernButton = styled.TouchableOpacity<{
  selected?: boolean;
  disabled?: boolean;
  highlight?: boolean;
}>(({ theme, selected, disabled, highlight }) => ({
  borderRadius: theme.borderRadius.large,
  backgroundColor: selected
    ? theme.colors.primaryVariant3
    : theme.colors.background,
  height: 140,
  width: 123,
  padding: theme.space[3],
  borderWidth: 1,
  borderColor: selected
    ? theme.colors.primary
    : highlight
    ? theme.colors.primary
    : theme.colors.palette.fwdGrey[100],
  justifyContent: 'space-between',
  alignItems: 'center',
  overflow: 'visible',
  opacity: disabled ? 0.3 : 1,
}));

const IconContainer = styled.View({
  flexBasis: '44%',
  justifyContent: 'center',
});

const ConcernTitleContiner = styled.View({
  flexBasis: '56%',
  justifyContent: 'center',
});

const ConcernTitle = styled(LargeLabel)({
  textAlign: 'center',
});
