import { H7, Icon, Row, SmallLabel } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import React from 'react';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import UnTickIcon from 'features/fna/components/icons/UnTickIcon';
import DragAndDrop from 'features/fna/components/icons/DragAndDrop';
import { ConcernItemProps } from './Concerns';

export default function ConcernItemPhone({
  item,
  selected,
  disabled,
  index,
  onPress,
  drag,
}: ConcernItemProps) {
  const { t } = useTranslation(['fna']);
  const { colors, space, sizes } = useTheme();

  return (
    <TouchableOpacity onPress={() => onPress(item)} disabled={disabled}>
      <Container selected={selected} disabled={disabled}>
        {index !== undefined && (
          <Row
            backgroundColor={
              index < 3 ? colors.palette.fwdYellow[50] : colors.surface
            }
            borderTopLeftRadius={space[4]}
            py={4.5}
            px={space[2]}
            borderBottomRightRadius={space[2]}
            alignItems="center"
            alignSelf="flex-start">
            <SmallLabel fontWeight="medium">
              {t(`fna:lifeStage.concern.priority.${index as 0 | 1 | 2}`)}
            </SmallLabel>
          </Row>
        )}
        <Content
          key={`${item.id}`}
          alignItems="center"
          justifyContent="space-between">
          <Row style={{ gap: space[2], alignItems: 'center' }}>
            {selected && (
              <TouchableOpacity onPressIn={drag}>
                <DragAndDrop />
              </TouchableOpacity>
            )}
            <IconContainer>{item.icon}</IconContainer>
            <H7 fontWeight="bold">{item?.title.replace('\n', ' ')}</H7>
          </Row>
          <View>
            {!selected ? (
              <UnTickIcon size={24} />
            ) : (
              <Icon.TickCircle size={24} fill={colors.palette.alertGreen} />
            )}
          </View>
        </Content>
      </Container>
    </TouchableOpacity>
  );
}

const IconContainer = styled(Row)(() => ({
  width: 35,
  justifyContent: 'center',
  alignItems: 'center',
}));

const Content = styled(Row)(({ theme: { space, sizes } }) => ({
  paddingHorizontal: space[3],
  paddingVertical: space[2],
}));

const Container = styled(View)<{ selected?: boolean; disabled?: boolean }>(
  ({ selected, disabled, theme: { space, colors } }) => ({
    borderColor: selected
      ? colors.palette.fwdOrange[100]
      : colors.palette.fwdGrey[100],
    borderWidth: 1,
    borderRadius: space[4],
    overflow: 'hidden',
    marginBottom: space[3],
    backgroundColor: selected ? colors.palette.fwdOrange[5] : colors.background,
    opacity: disabled ? 0.3 : 1,
  }),
);
