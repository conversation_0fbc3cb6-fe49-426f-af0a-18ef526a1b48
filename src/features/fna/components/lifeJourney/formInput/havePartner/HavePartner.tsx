import { useTheme } from '@emotion/react';
import { Box, LargeLabel, Row, SmallLabel, Switch } from 'cube-ui-components';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import PartnerAge from '../partnerAge/PartnerAge';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import ReadonlyInput from '../readonly/ReadonlyInput';

const HavePartner = memo(
  ({ readonly, orderNumber }: { orderNumber?: number } & ReadonlyProps) => {
    const { t } = useTranslation(['fna']);
    const { colors, space } = useTheme();
    const { lifeJourney, updateHavePartner } = useFnaStore(
      state => ({
        lifeJourney: state.lifeJourney,
        updateHavePartner: state.updateHavePartner,
      }),
      shallow,
    );

    const { isTabletMode } = useLayoutAdoptionCheck();
    const switchLabel = lifeJourney.havePartner ? t('fna:yes') : t('fna:no');

    return (
      <>
        {readonly ? (
          <ReadonlyInput
            label={t('fna:lifeStage.detail.question.havePartner')}
            value={switchLabel}
          />
        ) : isTabletMode ? (
          <>
            <Box gap={14.5}>
              <SmallLabel
                fontWeight="medium"
                color={colors.palette.fwdGreyDarker}>
                {t('fna:lifeStage.detail.question.havePartner')}
              </SmallLabel>
              <Switch
                value={lifeJourney.havePartner}
                onChange={value => updateHavePartner(value)}
                label={switchLabel}
              />
            </Box>
            {lifeJourney.havePartner && <PartnerAge />}
          </>
        ) : (
          <>
            <Row alignItems="center">
              {orderNumber && (
                <LargeLabel style={{ minWidth: space[6] }}>
                  {`${orderNumber}. `}
                </LargeLabel>
              )}
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:lifeStage.detail.question.havePartner')}
              </LargeLabel>
              <LargeLabel fontWeight="bold">{switchLabel}</LargeLabel>
              <Box width={space[2]} />
              <Switch
                value={lifeJourney.havePartner}
                onChange={value => updateHavePartner(value)}
              />
            </Row>
            {lifeJourney.havePartner && (
              <Row justifyContent="space-between">
                <Row flex={1}>
                  <LargeLabel>{`${orderNumber}a. `}</LargeLabel>
                  <LargeLabel>
                    {t('fna:lifeStage.detail.question.partnerAge')}
                  </LargeLabel>
                </Row>
                <PartnerAge />
              </Row>
            )}
          </>
        )}
      </>
    );
  },
);

export default HavePartner;
