import { memo } from 'react';
import { InternalGenderProps } from './Gender';
import { <PERSON>umn, Picker, Row, SmallLabel } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { Gender } from 'types/person';
import FemaleIcon from '../../../icons/FemaleIcon';
import MaleIcon from '../../../icons/MaleIcon';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';

const GenderTablet = memo(({ gender, setGender }: InternalGenderProps) => {
  const { t } = useTranslation(['fna']);
  const { space, colors } = useTheme();
  const shouldHighlight = useFnaStore(state => state.shouldHighlight);

  return (
    <Column flex={1} gap={space[2]}>
      <SmallLabel fontWeight="medium" color={colors.placeholder}>
        {t('fna:gender')}
      </SmallLabel>
      <Row flex={1} gap={space[3]}>
        <Picker
          type="chip"
          size="large"
          highlight={shouldHighlight && !gender}
          items={[
            {
              value: Gender.MALE,
              label: t('fna:male'),
              icon: () => <MaleIcon />,
            },
            {
              value: Gender.FEMALE,
              label: t('fna:female'),
              icon: () => <FemaleIcon />,
            },
          ]}
          value={gender as Gender}
          onChange={type => {
            if (type) {
              setGender?.(type as Gender);
            }
          }}
          textPosition="right"
        />
      </Row>
    </Column>
  );
});

export default GenderTablet;
