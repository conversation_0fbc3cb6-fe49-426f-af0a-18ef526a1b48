import { memo } from 'react';
import { InternalGenderProps } from './Gender';
import { Column, Icon, SmallLabel } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { Gender } from 'types/person';
import HintText from '../../../common/HintText';
import { requiredInputMessage } from 'features/fna/utils/validation/fnaErrorMessages';

const GenderPhone = memo(
  ({
    gender,
    setFemaleGender,
    setMaleGender,
    shouldShowRequiredBeforeSavingError,
  }: InternalGenderProps) => {
    const { t } = useTranslation(['fna']);
    const { sizes, space, colors } = useTheme();

    return (
      <Column marginLeft={space[3]}>
        <GenderLabel
          color={colors.palette.fwdGreyDarkest}
          style={{ alignSelf: 'flex-start' }}>
          {t('fna:gender')}
        </GenderLabel>
        <GenderContainer>
          <GenderButtonContainer
            selected={gender === Gender.MALE}
            onPress={setMaleGender}>
            <Icon.Male2
              size={sizes[6]}
              fill={
                gender === Gender.MALE ? colors.primary : colors.placeholder
              }
            />
          </GenderButtonContainer>
          <GenderButtonContainer
            selected={gender === Gender.FEMALE}
            onPress={setFemaleGender}>
            <Icon.Female2
              size={sizes[6]}
              fill={
                gender === Gender.FEMALE ? colors.primary : colors.placeholder
              }
            />
          </GenderButtonContainer>
        </GenderContainer>
        {shouldShowRequiredBeforeSavingError && gender === null && (
          <HintText status="error">{t(requiredInputMessage)}</HintText>
        )}
      </Column>
    );
  },
);

export default GenderPhone;

const GenderContainer = styled.View(({ theme: { space } }) => ({
  alignSelf: 'flex-start',
  flexDirection: 'row',
  gap: space[2],
  marginTop: space[2],
}));

const GenderLabel = styled(SmallLabel)(() => ({
  alignSelf: 'flex-start',
}));

const GenderButtonContainer = styled.TouchableOpacity<{ selected?: boolean }>(
  ({ selected, theme }) => ({
    width: theme.sizes[10],
    height: theme.sizes[10],
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: theme.sizes[5],
    backgroundColor: selected
      ? theme.colors.primaryVariant2
      : theme.colors.background,
    borderWidth: selected ? 2 : 1,
    borderColor: selected
      ? theme.colors.primary
      : theme.colors.palette.fwdGrey[100],
  }),
);
