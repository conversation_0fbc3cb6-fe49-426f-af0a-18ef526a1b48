import { Box, DropdownPanel, Icon, LargeLabel, Row } from 'cube-ui-components';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { IncomeOptionType, InternalIncomeProps } from './Income';
import React, { useTheme } from '@emotion/react';

const IncomePhone = memo(
  ({
    value,
    selectedItem,
    visible,
    show,
    hide,
    data,
    onDone,
    orderNumber,
  }: InternalIncomeProps) => {
    const { colors, space } = useTheme();
    const { t } = useTranslation(['fna']);

    return (
      <>
        <TouchableOpacity onPress={show} style={{ flex: 1 }}>
          <Row>
            {orderNumber && (
              <LargeLabel style={{ minWidth: space[6] }}>
                {`${orderNumber}. `}
              </LargeLabel>
            )}
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:lifeStage.detail.question.income')}
            </LargeLabel>
            <Row flex={1} alignItems="center" justifyContent="flex-end">
              <LargeLabel
                style={{ textAlign: 'right', width: space[30] }}
                fontWeight="bold"
                color={
                  selectedItem?.label
                    ? colors.palette.black
                    : colors.palette.fwdGreyDark
                }>
                {value || t('fna:lifeStage.detail.question.income.placeholder')}
              </LargeLabel>
              <Box width={space[1]} />
              <Icon.Dropdown fill={colors.palette.fwdAlternativeOrange[100]} />
            </Row>
          </Row>
        </TouchableOpacity>
        <DropdownPanel<IncomeOptionType, string>
          title={t('fna:lifeStage.detail.question.income')}
          actionLabel={t('fna:done')}
          visible={visible}
          data={data}
          selectedItem={selectedItem}
          getItemLabel={item => item.label}
          getItemValue={item => item.label}
          onDismiss={hide}
          onDone={onDone}
        />
      </>
    );
  },
);

export default IncomePhone;
