import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { InternalIncomeProps } from './Income';
import FnaInput from '../../../common/FnaInput';
import Autocomplete from 'components/Autocomplete';
import ReadonlyInput from '../readonly/ReadonlyInput';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { Box } from 'cube-ui-components';
import IncomeTooltip from 'features/fna/components/common/IncomeTooltip';
import useToggle from 'hooks/useToggle';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { countryModuleFnaConfig } from 'utils/config/module';

const IncomeTablet = memo(
  ({ value, selectedItem, data, onDone, readonly }: InternalIncomeProps) => {
    const { t } = useTranslation(['fna']);
    const shouldHighlight = useFnaStore(state => state.shouldHighlight);
    const [tooltipVisible, showTooltip, hideTooltip] = useToggle(false);
    const channel = useGetCubeChannel();

    return (
      <>
        {readonly ? (
          <ReadonlyInput
            label={t('fna:lifeStage.detail.question.income')}
            value={selectedItem?.label}
          />
        ) : (
          <Box flex={1}>
            <IncomeTooltip visible={tooltipVisible} />
            <Autocomplete
              label={t('fna:lifeStage.detail.question.income')}
              renderInput={props => (
                <FnaInput
                  {...props}
                  value={value}
                  placeholder={t(
                    'fna:lifeStage.detail.question.income.placeholder',
                  )}
                  highlight={shouldHighlight && !value}
                />
              )}
              data={data}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              keyExtractor={item => item.label}
              value={selectedItem?.value}
              onChange={value => {
                const selectedItem = data.find(i => i.value === value);
                if (selectedItem) {
                  onDone?.(selectedItem);
                }
                hideTooltip();
              }}
              onFocus={() => {
                if (countryModuleFnaConfig.incomeTooltip.includes(channel)) {
                  showTooltip();
                }
              }}
              onBlur={hideTooltip}
            />
          </Box>
        )}
      </>
    );
  },
);

export default IncomeTablet;
