import { memo } from 'react';
import { InternalTitleProps } from './Title';
import Autocomplete from 'components/Autocomplete';
import { useTranslation } from 'react-i18next';
import FnaInput from '../../../common/FnaInput';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';

const TitleTablet = memo(
  ({ data, value, selectedItem, onDone }: InternalTitleProps) => {
    const { t } = useTranslation(['fna']);
    const shouldHighlight = useFnaStore(state => state.shouldHighlight);
    return (
      <Autocomplete
        label={t('fna:title')}
        renderInput={props => (
          <FnaInput
            {...props}
            value={value}
            placeholder={t('fna:title.placeholder')}
            highlight={shouldHighlight}
          />
        )}
        style={{ flex: 1 }}
        data={data}
        getItemLabel={item => item.label}
        getItemValue={item => item.value}
        value={selectedItem?.value}
        onChange={value => {
          const selectedItem = data.find(i => i.value === value);
          if (selectedItem) {
            onDone?.(selectedItem);
          }
        }}
      />
    );
  },
);

export default TitleTablet;
