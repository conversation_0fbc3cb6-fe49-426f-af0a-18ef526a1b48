import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { useCallback, useRef, useState } from 'react';
import {
  Box,
  <PERSON><PERSON>,
  Icon,
  Picker,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import Input from 'components/Input';
import styled from '@emotion/native';
import { DependentModalProps } from './DependentModal';
import { View, ScrollView, Animated, Modal } from 'react-native';
import { Gender } from 'types/person';
import MaleIcon from 'features/fna/components/icons/MaleIcon';
import FemaleIcon from 'features/fna/components/icons/FemaleIcon';
import { DeleteButton } from '../existingPolicies/ExistingPolicies.tablet';
import useDependentForm from 'features/fna/hooks/useDependentForm';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import { KeyboardAvoidingView } from 'react-native';
import { Portal } from '@gorhom/portal';

const DependentModalTablet = ({
  data,
  config,
  visible,
  onCancel,
  onDone,
}: DependentModalProps) => {
  const { t } = useTranslation(['fna']);
  const { space, colors, sizes } = useTheme();

  const {
    control,
    handleSubmit,
    formState: { isValid },
    relationshipItems,
    dependents,
    addDependent,
    remove,
  } = useDependentForm({
    data,
    visible,
    config,
    onCancel,
    onDone,
  });

  const onSubmit = () => {
    handleSubmit(({ dependents = [] }) => {
      onDone(dependents);
    })();
  };

  const scrollRef = useRef<ScrollView>(null);

  const onAdd = () => {
    addDependent();
    setTimeout(() => scrollRef.current?.scrollToEnd({ animated: true }));
  };

  const [itemHeight, setItemHeight] = useState(0);

  const positionAnim = useRef(new Animated.Value(0)).current;
  const height = useRef(new Animated.Value(0)).current;
  const maxHeight = height.interpolate({
    inputRange: [0, 1],
    outputRange: [itemHeight, 0],
  });
  const maxMarginHeight = height.interpolate({
    inputRange: [0, 1],
    outputRange: [space[2], 0],
  });
  const [removeAnimating, setRemove] = useState<number>();

  const removeItem = useCallback(
    (idx: number) => {
      setRemove(idx);
      Animated.sequence([
        Animated.timing(positionAnim, {
          toValue: -3000,
          duration: 125,
          useNativeDriver: false,
        }),
        Animated.timing(height, {
          toValue: 1,
          duration: 125,
          useNativeDriver: false,
        }),
      ]).start(({ finished }) => {
        if (finished) {
          remove(idx);
          setRemove(undefined);
          positionAnim.setValue(0);
          height.setValue(0);
        }
      });
    },
    [height, positionAnim, remove],
  );

  if (!visible) return null;

  return (
    <Portal>
      <Modal
        visible={visible}
        transparent
        statusBarTranslucent
        animationType="fade">
        <KeyboardAvoidingViewContainer behavior="padding">
          <Box justifyContent="center" alignItems="center" px={space[10]}>
            <ModalContainer>
              <Box alignItems="flex-start" gap={space[6]}>
                <Typography.H6 fontWeight="bold">
                  {t('fna:lifeStage.detail.question.dependents.modalTitle')}
                </Typography.H6>
                <ScrollContainer ref={scrollRef}>
                  <DependentContainer>
                    {dependents.map((field, idx, arr) => {
                      const deletionDisabled =
                        arr.length <= 1 || removeAnimating !== undefined;
                      return (
                        <Animated.View
                          key={field.id}
                          style={[
                            {
                              height: itemHeight,
                              maxHeight: itemHeight,
                              marginVertical: space[2],
                            },
                            removeAnimating === idx && {
                              transform: [{ translateX: positionAnim }],
                              height: itemHeight,
                              maxHeight: maxHeight,
                              marginVertical: maxMarginHeight,
                            },
                          ]}>
                          <Dependent
                            onLayout={e => {
                              if (itemHeight === undefined) return;
                              setItemHeight(e.nativeEvent.layout.height);
                            }}>
                            <Row alignItems="center">
                              <Typography.LargeLabel
                                fontWeight="bold"
                                style={{ flex: 1 }}>
                                {t(
                                  'fna:lifeStage.detail.question.dependents.title',
                                )}{' '}
                                {idx + 1}
                              </Typography.LargeLabel>

                              <DeleteButton
                                onPress={() => removeItem(idx)}
                                disabled={deletionDisabled}>
                                <Icon.Delete fill={colors.secondary} />
                              </DeleteButton>
                            </Row>
                            <Row alignItems="flex-start">
                              <Input
                                control={control}
                                as={Picker}
                                name={`dependents.${idx}.relationship`}
                                label={t(
                                  'fna:lifeStage.detail.question.dependentRelationship',
                                )}
                                style={{
                                  maxWidth: 420,
                                }}
                                containerStyle={{
                                  flexWrap: 'wrap',
                                  rowGap: space[1],
                                }}
                                type="chip"
                                size="large"
                                items={relationshipItems}
                                textPosition="right"
                                disabled={!!config.dependentType}
                                labelStyle={{ color: colors.placeholder }}
                              />
                              <Row
                                flexGrow={1}
                                flexShrink={1}
                                ml={space[10]}
                                gap={space[10]}
                                alignItems="flex-end">
                                {config.genderRequired && (
                                  <Input
                                    control={control}
                                    as={Picker}
                                    name={`dependents.${idx}.gender`}
                                    label={t('fna:gender')}
                                    type="chip"
                                    size="large"
                                    textPosition="right"
                                    items={[
                                      {
                                        value: Gender.MALE,
                                        text: t('fna:male'),
                                        icon: MaleIcon,
                                      },
                                      {
                                        value: Gender.FEMALE,
                                        text: t('fna:female'),
                                        icon: FemaleIcon,
                                      },
                                    ]}
                                    labelStyle={{ color: colors.placeholder }}
                                  />
                                )}
                                <Input
                                  control={control}
                                  as={TextField}
                                  name={`dependents.${idx}.age`}
                                  label={t('fna:review.age')}
                                  keyboardType="numeric"
                                  size="large"
                                  style={{ flex: 1, maxWidth: 159 }}
                                  maxLength={2}
                                />
                              </Row>
                            </Row>
                          </Dependent>
                        </Animated.View>
                      );
                    })}

                    <Button
                      size="medium"
                      variant="secondary"
                      icon={<Icon.Plus />}
                      text={t('fna:lifeStage.detail.question.dependents.add')}
                      onPress={() => {
                        onAdd();
                      }}
                      style={{
                        maxWidth: sizes[46],
                        justifyContent: 'center',
                        marginVertical: space[4],
                      }}
                      disabled={
                        config.max !== undefined &&
                        dependents.length >= config.max
                      }
                    />
                  </DependentContainer>
                </ScrollContainer>
                <View />
                <Row gap={space[4]} style={{ alignSelf: 'center' }}>
                  <BottomButton
                    size="medium"
                    variant="secondary"
                    text={t('fna:cancel')}
                    onPress={onCancel}
                  />
                  <BottomButton
                    disabled={!isValid}
                    size="medium"
                    text={t('fna:done')}
                    onPress={onSubmit}
                  />
                </Row>
              </Box>
            </ModalContainer>
          </Box>
        </KeyboardAvoidingViewContainer>
      </Modal>
    </Portal>
  );
};

const ScrollContainer = styled(ScrollView)(({ theme: { sizes } }) => ({
  maxHeight: sizes[90],
  width: '100%',
}));

const DependentContainer = styled.View(({ theme: { space } }) => ({}));

const Dependent = styled(Animated.View)(
  ({ theme: { space, sizes, colors } }) => ({
    borderRadius: sizes[3],
    borderColor: colors.palette.fwdGrey[100],
    borderWidth: 1,
    padding: space[4],
    gap: space[4],
  }),
);

const BottomButton = styled(Button)(({ theme: { sizes } }) => ({
  width: sizes[50],
}));

const ModalContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  width: '100%',
  borderRadius: theme.sizes[4],
  padding: theme.sizes[12],
}));

const KeyboardAvoidingViewContainer = styled(KeyboardAvoidingView)(() => ({
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  alignItems: 'center',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
}));

export default DependentModalTablet;
