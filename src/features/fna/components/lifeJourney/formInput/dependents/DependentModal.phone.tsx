import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import React, { useCallback, useRef, useState } from 'react';
import {
  Box,
  Icon,
  Picker,
  Row,
  TextField,
  Typography,
  H6,
  LargeLabel,
} from 'cube-ui-components';
import Input from 'components/Input';
import styled from '@emotion/native';
import { DependentModalProps } from './DependentModal';
import { Animated, TouchableOpacity, View } from 'react-native';
import { Gender } from 'types/person';
import MaleIcon from 'features/fna/components/icons/MaleIcon';
import FemaleIcon from 'features/fna/components/icons/FemaleIcon';
import { Portal } from '@gorhom/portal';
import { DeleteButton } from '../existingPolicies/ExistingPolicies.tablet';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollViewMethods,
} from '@gorhom/bottom-sheet';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FnaConfirmFooter from 'features/fna/components/common/FnaConfirmFooter';
import useDependentForm from 'features/fna/hooks/useDependentForm';
import useLatest from 'hooks/useLatest';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';

const DependentModalPhone = ({
  data,
  config,
  visible,
  onCancel,
  onDone,
}: DependentModalProps) => {
  const { t } = useTranslation(['fna']);
  const { space, colors, sizes } = useTheme();

  const {
    control,
    handleSubmit,
    formState: { isValid },
    relationshipItems,
    dependents,
    addDependent,
    remove,
  } = useDependentForm({
    data,
    visible,
    config,
    onCancel,
    onDone,
  });

  const onSubmit = useLatest(() => {
    handleSubmit(({ dependents = [] }) => {
      onDone(dependents);
    })();
  });

  const scrollRef = useRef<BottomSheetScrollViewMethods>(null);

  const onAdd = () => {
    setCollapseIndexes(dependents.map(({ dependentKey }) => dependentKey));
    addDependent();
    setTimeout(() => scrollRef.current?.scrollToEnd({ animated: true }));
  };

  const itemHeight = sizes[34];

  const positionAnim = useRef(new Animated.Value(0)).current;
  const height = useRef(new Animated.Value(0)).current;
  const maxHeight = height.interpolate({
    inputRange: [0, 1],
    outputRange: [itemHeight, 0],
  });
  const maxMarginHeight = height.interpolate({
    inputRange: [0, 1],
    outputRange: [space[2], 0],
  });
  const [removeAnimating, setRemove] = useState<number>();
  const [collapseIndexes, setCollapseIndexes] = useState<string[]>([]);

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints(false);

  const removeItem = useCallback(
    (idx: number) => {
      setRemove(idx);
      Animated.sequence([
        Animated.timing(positionAnim, {
          toValue: -3000,
          duration: 125,
          useNativeDriver: false,
        }),
        Animated.timing(height, {
          toValue: 1,
          duration: 125,
          useNativeDriver: false,
        }),
      ]).start(({ finished }) => {
        if (finished) {
          remove(idx);
          setRemove(undefined);
          positionAnim.setValue(0);
          height.setValue(0);
        }
      });
    },
    [height, positionAnim, remove],
  );

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FnaConfirmFooter
          {...props}
          buttonTitle={t('fna:confirm')}
          onPress={onSubmit.current}
          disabled={!isValid}
        />
      );
    },
    [isValid, onSubmit, t],
  );

  const toggleValue = (value: string) => {
    const index = collapseIndexes.indexOf(value);
    setCollapseIndexes(prev => {
      if (index === -1) {
        return [...prev, value];
      }
      return prev.filter(v => v !== value);
    });
  };

  return (
    <>
      {visible && (
        <Portal>
          <BottomSheetModalProvider>
            <BottomSheetModal
              onDismiss={onCancel}
              index={1}
              snapPoints={snapPoints}
              {...bottomSheetProps}
              style={{ padding: 0 }}
              footerComponent={renderFooter}>
              <Row px={space[4]}>
                <H6 fontWeight="bold" style={{ flex: 1 }}>
                  {t('fna:lifeStage.detail.question.dependents.modalTitle')}
                </H6>
                <AddButton
                  onPress={onAdd}
                  disabled={
                    config.max !== undefined && dependents.length >= config.max
                  }>
                  <Icon.Plus
                    size={space[4]}
                    fill={colors.palette.fwdAlternativeOrange[100]}
                  />
                  <LargeLabel
                    fontWeight="bold"
                    color={colors.palette.fwdAlternativeOrange[100]}>
                    {t('fna:lifeStage.detail.question.dependents.add')}
                  </LargeLabel>
                </AddButton>
              </Row>

              <BottomSheetKeyboardAwareScrollView
                ref={scrollRef}
                bottomOffset={space[8]}
                style={{
                  paddingHorizontal: space[5],
                }}>
                <DependentContainer>
                  {dependents.map((field, idx, arr) => {
                    const deletionDisabled =
                      arr.length <= 1 || removeAnimating !== undefined;
                    return (
                      <Animated.View
                        key={field.dependentKey}
                        style={[
                          removeAnimating === idx && {
                            transform: [{ translateX: positionAnim }],
                            height: itemHeight,
                            maxHeight: maxHeight,
                            marginVertical: maxMarginHeight,
                          },
                        ]}>
                        <Box>
                          <Row alignItems="center">
                            <Typography.LargeLabel
                              fontWeight="bold"
                              style={{ flex: 1 }}>
                              {t(
                                'fna:lifeStage.detail.question.dependents.title',
                              )}{' '}
                              {idx + 1}
                            </Typography.LargeLabel>

                            <DeleteButton
                              onPress={() => removeItem(idx)}
                              disabled={deletionDisabled}>
                              <Icon.Delete fill={colors.secondary} />
                            </DeleteButton>
                            <CollapseButton
                              onPress={() => toggleValue(field.dependentKey)}>
                              <Icon.Dropdown
                                fill={colors.palette.fwdAlternativeOrange[100]}
                              />
                            </CollapseButton>
                          </Row>
                          <Dependent
                            isLastItem={idx === arr.length - 1}
                            isCollapsed={collapseIndexes.includes(
                              field.dependentKey,
                            )}
                            gap={space[4]}>
                            <Input
                              control={control}
                              as={Picker}
                              name={`dependents.${idx}.relationship`}
                              label={t(
                                'fna:lifeStage.detail.question.dependentRelationship',
                              )}
                              type="chip"
                              size="large"
                              items={relationshipItems}
                              textPosition="right"
                              disabled={!!config.dependentType}
                              containerStyle={{
                                flexWrap: 'wrap',
                                rowGap: space[3],
                              }}
                              labelStyle={{
                                color: colors.placeholder,
                              }}
                            />
                            <Row alignItems="flex-end" gap={space[4]}>
                              {config.genderRequired && (
                                <Input
                                  control={control}
                                  as={Picker}
                                  name={`dependents.${idx}.gender`}
                                  label={t('fna:gender')}
                                  type="chip"
                                  size="large"
                                  textPosition="right"
                                  items={[
                                    {
                                      value: Gender.MALE,
                                      text: t('fna:male'),
                                      icon: MaleIcon,
                                    },
                                    {
                                      value: Gender.FEMALE,
                                      text: t('fna:female'),
                                      icon: FemaleIcon,
                                    },
                                  ]}
                                  labelStyle={{
                                    color: colors.placeholder,
                                  }}
                                />
                              )}
                              <Input
                                control={control}
                                as={TextField}
                                name={`dependents.${idx}.age`}
                                label={t('fna:review.age')}
                                keyboardType="numeric"
                                size="large"
                                style={{ flex: 1 }}
                                maxLength={2}
                              />
                            </Row>
                          </Dependent>
                          {idx + 1 < arr.length && <Divider />}
                        </Box>
                      </Animated.View>
                    );
                  })}
                </DependentContainer>
                <BottomSheetFooterSpace />
              </BottomSheetKeyboardAwareScrollView>
            </BottomSheetModal>
          </BottomSheetModalProvider>
        </Portal>
      )}
    </>
  );
};

const DependentContainer = styled.View(
  ({ theme: { space, sizes, colors } }) => ({
    marginTop: space[6],
    borderRadius: sizes[3],
    borderColor: colors.palette.fwdGrey[100],
    borderWidth: 1,
    padding: space[4],
  }),
);

const Dependent = styled(Box)<{ isCollapsed: boolean; isLastItem: boolean }>(
  ({ theme: { space }, isCollapsed, isLastItem }) => ({
    gap: space[4],
    height: isCollapsed ? 0 : 'auto',
    marginVertical: isCollapsed ? 0 : space[4],
    marginBottom: isLastItem ? 0 : space[4],
    overflow: 'hidden',
  }),
);

const AddButton = styled(TouchableOpacity)<{ disabled: boolean }>(
  ({ disabled }) => ({
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 5,
    opacity: disabled ? 0.5 : 1,
  }),
);

const Divider = styled(View)(({ theme }) => {
  return {
    marginBottom: theme.space[4],
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

const CollapseButton = styled(TouchableOpacity)(({ theme: { space } }) => ({
  paddingLeft: space[2],
}));

export default DependentModalPhone;
