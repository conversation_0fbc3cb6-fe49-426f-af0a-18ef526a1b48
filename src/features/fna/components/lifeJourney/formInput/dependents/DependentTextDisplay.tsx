import { Dependent } from 'types/case';
import React, { Fragment, memo, useEffect, useMemo, useState } from 'react';
import { DEPENDENT_ICONS } from './Dependents';
import { t } from 'i18next';
import { Gender } from 'types/person';
import { Icon, Row, Typography } from 'cube-ui-components';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';

const DependentTextDisplay = ({ data }: { data: Dependent[] }) => {
  const { colors, space } = useTheme();
  const [contentWidth, setContentWidth] = useState(0);
  const [hasOverflow, setHasOverflow] = useState(false);

  useEffect(() => {
    setHasOverflow(false);
  }, [data]);

  return (
    <Container>
      <Content
        onLayout={event => {
          const layout = event.nativeEvent.layout;
          setContentWidth(layout.width);
        }}>
        <DependentSummary
          data={data}
          contentWidth={contentWidth}
          setHasOverflow={setHasOverflow}
        />
      </Content>
      {hasOverflow && (
        <More>
          <Icon.More fill={colors.secondary} size={space[5]} />
        </More>
      )}
    </Container>
  );
};

export const DependentSummary = ({
  data,
  fontWeight = 'bold',
  setHasOverflow,
  contentWidth,
}: {
  data: Dependent[];
  fontWeight?: 'bold' | 'normal';
  setHasOverflow?: (overflow: boolean) => void;
  contentWidth?: number;
}) => {
  const { colors } = useTheme();
  const dependents = useMemo(
    () =>
      data.map(({ relationship, gender, age }) => {
        const relationshipKey =
          `fna:lifeStage.detail.question.dependentRelationship.${relationship}` as const;

        return {
          Icon: DEPENDENT_ICONS[relationship],
          str:
            t(relationshipKey) +
            '/ ' +
            (gender === Gender.MALE ? t('fna:male') + '/ ' : '') +
            (gender === Gender.FEMALE ? t('fna:female') + '/ ' : '') +
            age +
            ' ' +
            t('fna:shortYearsOld'),
        };
      }),
    [data],
  );
  return (
    <>
      {dependents.map(({ Icon, str }, idx) => (
        <Fragment key={idx}>
          <Icon fill={colors.secondary} />
          <Typography.LargeLabel fontWeight={fontWeight}>{str}</Typography.LargeLabel>
          <Space
            onLayout={event => {
              if (idx === dependents.length - 1) {
                const layout = event.nativeEvent.layout;
                setHasOverflow?.(layout.x > (contentWidth || 0));
              }
            }}
          />
        </Fragment>
      ))}
    </>
  );
};

const Container = styled(Row)(({ theme: { space } }) => ({
  gap: space[1],
}));

const Content = styled(Row)(({ theme: { space } }) => ({
  gap: space[1],
  overflow: 'hidden',
  flex: 1,
}));

const Space = styled.View(({ theme: { space } }) => ({
  width: space[2],
}));

const More = styled.View(({ theme: { space } }) => ({
  marginTop: space[2],
}));

export default memo(DependentTextDisplay);
