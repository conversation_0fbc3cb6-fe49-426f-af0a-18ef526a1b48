import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { DependentConfiguration } from 'features/fna/types/lifeJourney';
import { Dependent } from 'types/case';
import { memo } from 'react';
import DependentModalTablet from './DependentModal.tablet';
import DependentModalPhone from './DependentModal.phone';

export type DependentModalProps = {
  config: DependentConfiguration;
  data: Dependent[];
  visible: boolean;
  onCancel: () => void;
  onDone: (dependents: Dependent[]) => void;
};

const DependentModal = memo((props: DependentModalProps) => {
  return (
    <DeviceBasedRendering
      phone={<DependentModalPhone {...props} />}
      tablet={<DependentModalTablet {...props} />}
    />
  );
});

export default DependentModal;
