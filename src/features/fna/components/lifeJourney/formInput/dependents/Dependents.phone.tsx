import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon, Row, LargeLabel, Box } from 'cube-ui-components';
import useToggle from 'hooks/useToggle';
import React, { memo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { DependentProps } from './Dependents';
import DependentModal from './DependentModal';
import DependentsReadonly from './DependentsReadonly';
import useDependentHandlers from 'features/fna/hooks/useDependentHandlers';

const DependentsPhone = memo(
  ({
    config,
    onChange,
    onBack,
    data,
    readonly,
    orderNumber,
  }: Required<DependentProps>) => {
    const { t } = useTranslation(['fna']);
    const { space, sizes, colors } = useTheme();

    const [dialogVisible, showDialog, hideDialog] = useToggle();
    const { onCancel, onDone } = useDependentHandlers(
      config,
      onBack,
      onChange,
      hideDialog,
    );

    const hasDependent = data.length > 0;

    useEffect(() => {
      if (!hasDependent && config.required && !readonly) {
        showDialog();
      }
    }, []);

    return (
      <>
        {readonly ? (
          <DependentsReadonly data={data} />
        ) : (
          <Box gap={space[3]}>
            <Row maxW={'50%'}>
              {orderNumber && (
                <LargeLabel style={{ minWidth: space[6] }}>
                  {`${orderNumber}. `}
                </LargeLabel>
              )}
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:lifeStage.detail.question.dependents.phone')}
              </LargeLabel>
            </Row>
            <Container onPress={showDialog}>
              <Box flex={1} ml={orderNumber ? space[6] : 0}>
                <LargeLabel style={{ flex: 1 }}>
                  {t('fna:lifeStage.detail.question.added.dependents.phone', {
                    total: data.length,
                  })}
                </LargeLabel>
              </Box>

              <Row alignItems="center" gap={space[2]}>
                <LargeLabel fontWeight="bold">{t('fna:edit')}</LargeLabel>
                <Icon.Create
                  size={sizes[5]}
                  fill={colors.palette.fwdAlternativeOrange[100]}
                />
              </Row>
            </Container>
          </Box>
        )}
        <DependentModal
          config={config}
          data={data}
          visible={dialogVisible}
          onCancel={onCancel}
          onDone={onDone}
        />
      </>
    );
  },
);

const Container = styled.TouchableOpacity(() => ({
  flexDirection: 'row',
}));

export default DependentsPhone;
