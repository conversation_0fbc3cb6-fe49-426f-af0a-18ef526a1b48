import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon, SmallLabel } from 'cube-ui-components';
import FnaInput from 'features/fna/components/common/FnaInput';
import useToggle from 'hooks/useToggle';
import React, { memo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { DependentProps } from './Dependents';
import DependentModal from './DependentModal';
import DependentTextDisplay from './DependentTextDisplay';
import DependentsReadonly from './DependentsReadonly';
import { Keyboard, TouchableOpacity } from 'react-native';
import useDependentHandlers from 'features/fna/hooks/useDependentHandlers';

const DependentsTablet = memo(
  ({ config, onChange, onBack, data, readonly }: Required<DependentProps>) => {
    const { t } = useTranslation(['fna']);
    const { sizes } = useTheme();
    const [dialogVisible, showDialog, hideDialog] = useToggle();
    const { onCancel, onDone } = useDependentHandlers(config, onBack, onChange, hideDialog);

    const hasDependent = data.length > 0;

    useEffect(() => {
      if (!hasDependent && config.required && !readonly) {
        showDialog();
      }
    }, []);

    return (
      <>
        {readonly ? (
          <DependentsReadonly data={data} />
        ) : (
          <Container>
            <FnaInput
              label={t('fna:lifeStage.detail.question.dependents')}
              labelRight={
                <Total>
                  <SmallLabel
                    fontWeight="medium"
                    style={{ transform: 'scale(0.8)' }}>
                    {t('fna:lifeStage.detail.question.dependents.total', {
                      total: data.length,
                    })}
                  </SmallLabel>
                </Total>
              }
              right={
                <TouchableOpacity onPress={showDialog}>
                  <Icon.Create size={sizes[6]} />
                </TouchableOpacity>
              }
              placeholder={t('fna:lifeStage.detail.question.dependents')}
              value={hasDependent ? ' ' : undefined}
              onFocus={() => {
                Keyboard.dismiss();
                showDialog();
              }}
            />

            {hasDependent && (
              <DependentContent onPress={showDialog}>
                <DependentTextDisplay data={data} />
              </DependentContent>
            )}
          </Container>
        )}
        <DependentModal
          config={config}
          data={data}
          visible={dialogVisible}
          onCancel={onCancel}
          onDone={onDone}
        />
      </>
    );
  },
);

const Container = styled.View(() => ({
  flex: 1,
}));

const Total = styled.View(({ theme: { colors, space } }) => ({
  backgroundColor: colors.primaryVariant2,
  borderRadius: space[1],
  transform: 'scale(1.25)',
}));

const DependentContent = styled.Pressable(({ theme: { space } }) => ({
  position: 'absolute',
  top: space[6],
  left: 0,
  right: space[7],
}));

export default DependentsTablet;
