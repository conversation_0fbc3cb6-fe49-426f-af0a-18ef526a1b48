import { useTheme } from '@emotion/react';
import { Box, Column, Icon, Label } from 'cube-ui-components';
import React, { memo } from 'react';
import { InternalTotalAssetsProps } from './TotalAssets';
import { useTranslation } from 'react-i18next';
import FnaInput from '../../../common/FnaInput';
import useToggle from 'hooks/useToggle';
import { formatCurrency } from 'utils';
import InfoToolTip from './InfoToolTip';
import ReadonlyInput from '../readonly/ReadonlyInput';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';

const TotalAssetsTablet = memo(
  ({ label, value, onChange, readonly }: InternalTotalAssetsProps) => {
    const { t } = useTranslation(['fna']);
    const { sizes, space } = useTheme();
    const [isFocusing, setFocusing, setBlur] = useToggle(false);
    const shouldHighlight = useFnaStore(state => state.shouldHighlight);

    const displayValue = value
      ? t('fna:currency') + ` ${formatCurrency(value)}`
      : '';

    return (
      <>
        {readonly ? (
          <ReadonlyInput
            label={label}
            value={displayValue}
            tooltip={t('fna:totalAssets.reminder')}
          />
        ) : (
          <FnaInput
            labelRight={
              <Box>
                <Box pos="absolute" top={-space[2]}>
                  <InfoToolTip
                    children={<InfoBtn />}
                    childrenContent={<InfoContent />}
                    isDefaultCloseButtonShown={false}
                  />
                </Box>
              </Box>
            }
            label={label}
            value={isFocusing ? value : displayValue}
            onChange={onChange}
            right={<Icon.Create size={sizes[5]} />}
            onFocus={setFocusing}
            onBlur={setBlur}
            keyboardType="number-pad"
            returnKeyType="done"
            style={{ flex: 0.8 }}
            placeholder={t('fna:currency')}
            highlight={shouldHighlight && !value}
          />
        )}
      </>
    );
  },
);

const InfoBtn = () => {
  const { sizes } = useTheme();
  return <Icon.InfoCircle size={sizes[4]} />;
};

const InfoContent = () => {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();
  return (
    <Column width={320}>
      <Label fontWeight="bold">{t('fna:totalAssets')}</Label>
      <Label style={{ lineHeight: space[5] }}>
        {t('fna:totalAssets.reminder')}
      </Label>
    </Column>
  );
};

export default TotalAssetsTablet;
