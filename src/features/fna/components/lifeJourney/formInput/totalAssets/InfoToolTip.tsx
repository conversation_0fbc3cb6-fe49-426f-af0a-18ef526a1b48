import {
  Dimensions,
  Platform,
  StatusBar,
  StyleProp,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import React, { FC, useState } from 'react';
import Tooltip from 'react-native-walkthrough-tooltip';
import { useTheme } from '@emotion/react';
import { Column, Icon, Row } from 'cube-ui-components';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

const HIT_SLOP = { top: 8, left: 8, right: 8, bottom: 8 };

export default function InfoToolTip({
  children,
  childrenContent,
  isDefaultCloseButtonShown = true,
  innerContentStyle,
  style,
}: {
  children: React.ReactNode;
  childrenContent: React.ReactNode;
  isDefaultCloseButtonShown?: boolean;
  innerContentStyle?: StyleProp<ViewStyle>;
  style?: StyleProp<ViewStyle>;
}) {
  const [isVisible, setIsVisible] = useState(false);

  const theme = useTheme();
  const { sizes, elevation, borderRadius, space, colors } = theme;

  const isTooltipOnHandler = () => {
    setIsVisible(!isVisible);
  };

  return (
    <>
      <Tooltip
        disableShadow
        arrowSize={{ width: sizes[6], height: sizes[3] }}
        arrowStyle={{ zIndex: 999 }}
        useInteractionManager={true}
        backgroundColor="rgba(0,0,0,0)"
        topAdjustment={
          Platform.OS === 'android' ? -(StatusBar.currentHeight ?? 0) : 0
        }
        placement="bottom"
        isVisible={isVisible}
        tooltipStyle={[
          { paddingTop: space[2] },
          Platform.OS === 'ios' && elevation[5], // Jessica: IOS elevation
        ]}
        contentStyle={[
          {
            borderRadius: borderRadius['x-small'],
            backgroundColor: colors.palette.fwdOrange[5],
            padding: 0,
            height: 'auto',
          },
          Platform.OS === 'android' && { elevation: 5 }, // Jessica: Android elevation
        ]}
        onClose={() => {
          isTooltipOnHandler();
        }}
        content={
          <TooltipWindow
            isVisibleHandler={isTooltipOnHandler}
            children={children}
            childrenContent={childrenContent}
            innerContentStyle={innerContentStyle}
            isDefaultCloseButtonShown={isDefaultCloseButtonShown}
          />
        }>
        <TouchableOpacity onPress={isTooltipOnHandler} style={style}>
          {children}
        </TouchableOpacity>
      </Tooltip>
    </>
  );
}

const TooltipWindow: FC<{
  children: React.ReactNode;
  childrenContent: React.ReactNode;
  isVisibleHandler: () => void;
  innerContentStyle?: StyleProp<ViewStyle>;
  isDefaultCloseButtonShown?: boolean;
}> = ({
  childrenContent,
  isVisibleHandler,
  innerContentStyle,
  isDefaultCloseButtonShown,
}) => {
  const theme = useTheme();
  const { colors, sizes, borderRadius } = theme;

  return (
    <Animated.View
      style={[
        {
          borderRadius: borderRadius['x-small'],
          padding: sizes[4],
          backgroundColor: colors.palette.fwdOrange[5],
        },
        innerContentStyle,
      ]}
      entering={FadeIn}
      exiting={FadeOut}>
      <Column>
        {isDefaultCloseButtonShown && (
          <TouchableOpacity
            style={{
              width: sizes[6],
              height: sizes[6],
            }}
            onPress={isVisibleHandler}
            hitSlop={HIT_SLOP}>
            <Icon.Close
              fill={colors.primary}
              height={sizes[6]}
              width={sizes[6]}
            />
          </TouchableOpacity>
        )}
        {childrenContent}
      </Column>
    </Animated.View>
  );
};
