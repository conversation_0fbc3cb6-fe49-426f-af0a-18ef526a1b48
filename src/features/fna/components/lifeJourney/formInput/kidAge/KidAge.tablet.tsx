import { useTheme } from '@emotion/react';
import { Icon } from 'cube-ui-components';
import { memo } from 'react';
import { InternalKidAgeProps } from './KidAge';
import { useTranslation } from 'react-i18next';
import FnaInput from '../../../common/FnaInput';
import useToggle from 'hooks/useToggle';

const KidAgeTablet = memo(({ label, value, onChange }: InternalKidAgeProps) => {
  const { t } = useTranslation(['fna']);
  const { sizes } = useTheme();
  const [isFocusing, setFocusing, setBlur] = useToggle(false);

  const displayedValue = t('fna:yo', { age: value });

  return (
    <FnaInput
      label={label}
      value={isFocusing ? value : displayedValue}
      onChange={onChange}
      right={<Icon.Create size={sizes[5]} />}
      onFocus={setFocusing}
      onBlur={setBlur}
      keyboardType="number-pad"
      returnKeyType="done"
      style={{ width: 104 }}
    />
  );
});

export default KidAgeTablet;
