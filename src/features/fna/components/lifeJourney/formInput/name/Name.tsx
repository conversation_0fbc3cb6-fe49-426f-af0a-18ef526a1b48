import React, { memo, useMemo } from 'react';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { shallow } from 'zustand/shallow';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import NamePhone from './Name.phone';
import NameTablet from './Name.tablet';
import { MAX_NAME_LENGHT } from 'features/coverageDetails/validation/common/constant';
import { getNameRegex } from 'utils/validation/customValidation';
import { useTranslation } from 'react-i18next';

export interface InternalNameProps {
  firstName?: string;
  middleName?: string;
  lastName?: string;
  onChange?: (
    firstName: string,
    middleName?: string,
    lastName?: string,
  ) => void;
  error?: string;
}

const Name = memo(() => {
  const { t } = useTranslation(['fna']);
  const { firstName, middleName, lastName, updateProfileName } = useFnaStore(
    state => ({
      firstName: state.lifeJourney.firstName,
      middleName: state.lifeJourney.middleName,
      lastName: state.lifeJourney.lastName,
      updateProfileName: state.updateProfileName,
    }),
    shallow,
  );

  const nameError = useMemo(() => {
    if (!firstName && !middleName && !lastName) return '';

    const fullName = [firstName?.trim(), middleName?.trim(), lastName?.trim()]
      .filter(Boolean)
      .join(' ');
    if (fullName.length > MAX_NAME_LENGHT)
      return t('fna:validation.maxLength60');
    if (!getNameRegex().test(fullName)) {
      return t('fna:validation.invalidFormat');
    }

    return '';
  }, [firstName, middleName, lastName, t]);

  return (
    <DeviceBasedRendering
      phone={
        <NamePhone
          firstName={firstName}
          middleName={middleName}
          lastName={lastName}
          onChange={updateProfileName}
          error={nameError}
        />
      }
      tablet={
        <NameTablet
          firstName={firstName}
          middleName={middleName}
          lastName={lastName}
          onChange={updateProfileName}
          error={nameError}
        />
      }
    />
  );
});

export default Name;
