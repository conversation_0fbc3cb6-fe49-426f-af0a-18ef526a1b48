import React, { useMemo } from 'react';
import { Pressable, View } from 'react-native';
import { Box, Icon, SmallLabel, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import useToggle from 'hooks/useToggle';
import isEmpty from 'lodash/isEmpty';
import HintText from '../../../common/HintText';
import { useTranslation } from 'react-i18next';
import { InternalNameProps } from './Name';
import { NameModalInput } from 'features/fna/components/lifeJourney/formInput/name/inputs/NameModalInput';
import FnaInput from 'features/fna/components/common/FnaInput';
import { countryModuleFnaConfig } from 'utils/config/module';
import { useNameFieldBlur } from 'components/NameField';

const NamePhone = ({
  firstName,
  middleName,
  lastName,
  onChange,
  error,
}: InternalNameProps) => {
  const { t } = useTranslation(['fna']);
  const { colors, sizes, space } = useTheme();
  const [visible, show, hide] = useToggle(false);
  const fullName = useMemo(
    () =>
      [firstName?.trim(), middleName?.trim(), lastName?.trim()]
        .filter(Boolean)
        .join(' '),
    [firstName, lastName, middleName],
  );

  const { onBlur } = useNameFieldBlur({
    value: fullName,
    onChange,
  });

  if (countryModuleFnaConfig.nameInputMode === 'text') {
    return (
      <FnaInput
        label={firstName && t('fna:customer')}
        value={firstName}
        onChange={onChange}
        placeholder={t('fna:name')}
        right={<Icon.Create size={sizes[5]} />}
        returnKeyType="done"
        style={{ flex: 1 }}
        error={error}
        onBlur={onBlur}
      />
    );
  }

  if (countryModuleFnaConfig.nameInputMode === 'modal') {
    return (
      <>
        <Box flex={1}>
          <Container onPress={show}>
            <Box flex={1}>
              <SmallLabel color={colors.palette.fwdGreyDarkest}>
                {fullName ? t('fna:name') : ' '}
              </SmallLabel>
              <Box py={space[3]}>
                <Typography.LargeBody
                  numberOfLines={1}
                  fontWeight={'bold'}
                  color={!fullName ? colors.palette.fwdGreyDarkest : undefined}>
                  {fullName ? fullName : t('fna:name')}
                </Typography.LargeBody>
              </Box>
            </Box>
            <IconContainer>
              <Icon.Create size={sizes[6]} />
            </IconContainer>
          </Container>
          {!isEmpty(error) && <HintText status="error">{error}</HintText>}
        </Box>
        {visible && (
          <NameModalInput
            firstName={firstName ?? ''}
            middleName={middleName ?? ''}
            lastName={lastName ?? ''}
            onDismiss={hide}
            onDone={onChange}
          />
        )}
      </>
    );
  }

  return null;
};

export default NamePhone;

const Container = styled(Pressable)(({ theme: { colors } }) => ({
  flexDirection: 'row',
  alignItems: 'flex-end',
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.fwdGrey[100],
}));

const IconContainer = styled(View)(({ theme: { space } }) => ({
  alignItems: 'flex-start',
  marginBottom: space[3],
}));
