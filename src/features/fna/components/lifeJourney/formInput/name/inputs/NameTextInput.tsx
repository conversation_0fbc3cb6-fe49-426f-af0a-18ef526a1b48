import React, { useState, useRef, useImperativeHandle } from 'react';
import {
  Pressable,
  StyleProp,
  ViewStyle,
  View,
  TextInput,
} from 'react-native';
import {
  Box,
  Column,
  Row,
  SmallLabel,
  TextFieldProps,
  TextFieldRef,
  Typography,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import useToggle from 'hooks/useToggle';
import isEmpty from 'lodash/isEmpty';
import HintText from 'features/fna/components/common/HintText';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useTranslation } from 'react-i18next';

const NameTextInput = React.forwardRef(
  (
    {
      label,
      hint,
      error,
      left,
      right,
      onChange,
      onFocus: onFocusProp,
      onBlur: onBlurProp,
      style,
      ...props
    }: {
      value?: string;
      onChange?: (value: string) => void;
    } & TextFieldProps,
    ref: React.Ref<TextFieldRef>,
  ) => {
    const { t } = useTranslation(['fna']);
    const { colors, space } = useTheme();
    const inputRef = useRef<TextInput>(null);
    const [isFocused, setFocused, setBlur] = useToggle(false);
    const isControlled = props.value !== undefined;
    const validInputValue = isControlled ? props.value : props.defaultValue;
    const [uncontrolledValue, setUncontrolledValue] = useState(validInputValue);
    const value = isControlled ? props.value : uncontrolledValue;
    const { isTabletMode } = useLayoutAdoptionCheck();

    const TextComponent = isTabletMode ? Typography.H6 : Typography.H7;

    useImperativeHandle(ref, () => inputRef.current as TextInput);

    const onChangeText = (text: string) => {
      onChange?.(text);
      if (props.value === undefined) {
        setUncontrolledValue(text);
      }
      props?.onChangeText?.(text);
    };

    return (
      <View style={style as StyleProp<ViewStyle>}>
        <Container
          error={!!error}
          isFocused={isFocused}
          onPress={() => inputRef?.current?.focus()}>
          <Column flex={1}>
            <SmallLabel
              style={{ opacity: isFocused || !!value ? 1 : 0 }}
              fontWeight="medium"
              color={colors.palette.fwdGreyDarker}>
              {label ? label : ' '}
            </SmallLabel>
            <Row alignItems="flex-end">
              {left && (
                <IconContainer>
                  {React.isValidElement(left)
                    ? left
                    : React.createElement(left)}
                </IconContainer>
              )}
              <CustomInput
                ref={inputRef}
                selectionColor={colors.palette.fwdOrange[100]}
                autoCorrect={false}
                isTabletMode={isTabletMode}
                {...props}
                value={value}
                onChangeText={onChangeText}
                onFocus={() => {
                  setFocused();
                  onFocusProp?.();
                }}
                onBlur={() => {
                  setBlur();
                  onBlurProp?.();
                }}
                placeholder={isFocused ? '' : props.placeholder}
                placeholderTextColor={colors.placeholder}
                style={props.inputStyle}
              />
              <Box
                position={'absolute'}
                bottom={space[2]}
                right={space[6]}
                left={0}
                top={0}
                display={isFocused || !value ? 'none' : 'flex'}
                justifyContent={'flex-end'}
                bgColor={colors.background}>
                <TextComponent fontWeight={'bold'} numberOfLines={1}>
                  {value}
                </TextComponent>
              </Box>
              {!isFocused && right && (
                <IconContainer>
                  {React.isValidElement(right)
                    ? right
                    : React.createElement(right)}
                </IconContainer>
              )}
            </Row>
          </Column>
        </Container>
        {(!isEmpty(hint) || !isEmpty(error)) && (
          <HintText status={error ? 'error' : 'default'}>
            {error || hint}
          </HintText>
        )}
      </View>
    );
  },
);

const Container = styled(Pressable)<{ isFocused?: boolean; error?: boolean }>(
  ({ theme: { colors }, isFocused, error }) => ({
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderBottomWidth: 1,
    borderBottomColor: error
      ? colors.error
      : isFocused
      ? colors.primary
      : colors.palette.fwdGrey[100],
  }),
);

const CustomInput = styled(TextInput)<{ isTabletMode: boolean }>(
  ({ theme: { typography }, isTabletMode }) => ({
    paddingTop: 10,
    paddingBottom: 9,
    fontSize: isTabletMode ? typography.h6.size : typography.h7.size,
    lineHeight: isTabletMode
      ? typography.h6.lineHeight
      : typography.h7.lineHeight,
    fontFamily: 'FWDCircularTT-Bold',
    flex: 1,
  }),
);

const IconContainer = styled(View)(() => ({
  alignItems: 'flex-start',
  marginBottom: 9,
}));

export default NameTextInput;
