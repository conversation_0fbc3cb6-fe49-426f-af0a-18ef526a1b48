import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import React, { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import {
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import { Box, Button, Icon, Row, Typography } from 'cube-ui-components';
import {
  SharedValue,
  useDerivedValue,
  useSharedValue,
} from 'react-native-reanimated';
import { useValidationYupResolver } from 'utils/validation';
import Input from 'components/Input';
import { firstNameAndLastNameSchema } from 'features/fna/utils/validation/fnaValidation';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import DialogTablet from 'components/Dialog.tablet';
import NameTextInput from './NameTextInput';
import { TFuncKey } from 'i18next';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import styled from '@emotion/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LayoutChangeEvent } from 'react-native';
import { useNameFieldBlur } from 'components/NameField';
import { Portal } from '@gorhom/portal';

const HANDLE_HEIGHT = 29;

export const NameModalInput = ({
  firstName,
  middleName,
  lastName,
  onDismiss,
  onDone,
}: {
  firstName: string;
  middleName: string;
  lastName: string;
  onDismiss: () => void;
  onDone?: (firstName: string, middleName: string, lastName: string) => void;
}) => {
  const { t } = useTranslation(['fna']);
  const { space, sizes } = useTheme();
  const contentHeight = useSharedValue(0);
  const keyboardShown = useKeyboardShown();
  const { bottom: bottomInset } = useSafeAreaInsets();

  const resolver = useValidationYupResolver(firstNameAndLastNameSchema);

  const {
    control,
    handleSubmit,
    formState: { isValid, errors },
    watch,
    setValue,
  } = useForm({
    mode: 'onBlur',
    defaultValues: {
      firstName,
      middleName,
      lastName,
    },
    resolver,
  });

  const { onBlur: onFirstNameBlur } = useNameFieldBlur({
    value: watch('firstName'),
    onChange: value => setValue('firstName', value),
  });

  const { onBlur: onLastNameBlur } = useNameFieldBlur({
    value: watch('lastName'),
    onChange: value => setValue('lastName', value),
  });

  const bottomSheetProps = useBottomSheet();

  const onSubmit = () => {
    handleSubmit(data => {
      onDone?.(data.firstName, data.middleName ?? '', data.lastName);
      onDismiss();
    })();
  };

  const onLayout = useCallback((e: LayoutChangeEvent) => {
    contentHeight.value = e.nativeEvent.layout.height;
  }, []);

  const dynamicSnapPoints = useDerivedValue(() => {
    return [
      contentHeight.value + HANDLE_HEIGHT - (keyboardShown ? bottomInset : 0),
    ];
  }, [bottomInset, contentHeight.value, keyboardShown]);

  const { isTabletMode } = useLayoutAdoptionCheck();

  if (isTabletMode) {
    return (
      <SDialog
        keyboardShown={keyboardShown}
        dismissable
        onDismiss={onDismiss}
        visible={true}>
        <Box alignItems="center" gap={space[6]}>
          <Row gap={space[8]}>
            <Input
              control={control}
              as={TabletNameInput}
              name="firstName"
              label={t('fna:firstName')}
              placeholder={t('fna:firstName')}
              error={t(errors.firstName?.message as TFuncKey)}
              right={<Icon.Create size={sizes[6]} />}
              autoFocus={!firstName}
              onBlur={onFirstNameBlur}
            />
            <Input
              control={control}
              as={TabletNameInput}
              name="lastName"
              label={t('fna:lastName')}
              placeholder={t('fna:lastName')}
              error={t(errors.lastName?.message as TFuncKey)}
              right={<Icon.Create size={sizes[6]} />}
              onBlur={onLastNameBlur}
            />
          </Row>
          <SButton
            disabled={!isValid}
            size="medium"
            text={t('fna:save')}
            onPress={onSubmit}
          />
        </Box>
      </SDialog>
    );
  }
  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={dynamicSnapPoints as SharedValue<(string | number)[]>}
          contentHeight={contentHeight}
          index={0}
          {...bottomSheetProps}
          keyboardBehavior={'interactive'}
          style={{ padding: 0 }}>
          <Box onLayout={onLayout} px={space[4]}>
            <Box py={16}>
              <Typography.ExtraLargeBody fontWeight={'bold'}>
                {t('fna:name')}
              </Typography.ExtraLargeBody>
            </Box>
            <Input
              control={control}
              name={'firstName'}
              as={MobileNameInput}
              label={t('fna:firstName')}
              placeholder={t('fna:firstName')}
              right={<Icon.Create size={sizes[6]} />}
              autoFocus={!firstName}
              onBlur={onFirstNameBlur}
            />
            <Input
              control={control}
              name={'lastName'}
              as={MobileNameInput}
              label={t('fna:lastName')}
              placeholder={t('fna:lastName')}
              right={<Icon.Create size={sizes[6]} />}
              onBlur={onLastNameBlur}
            />
            <Box pt={space[4]} pb={space[4] + bottomInset}>
              <Button
                text={t('fna:save')}
                disabled={!isValid}
                onPress={onSubmit}
              />
            </Box>
          </Box>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
};

const TabletNameInput = styled(NameTextInput)(() => ({ flex: 1 }));

const MobileNameInput = styled(NameTextInput)(({ theme: { space } }) => ({
  marginBottom: space[4],
}));

const SDialog = styled(DialogTablet)<{ keyboardShown: boolean }>(
  ({ theme: { space }, keyboardShown }) => ({
    width: 794,
    padding: space[12],
    paddingBottom: space[9],
    marginBottom: keyboardShown ? space[24] : 0,
  }),
);

const SButton = styled(Button)(() => ({
  width: 200,
}));
