import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { memo, useCallback } from 'react';
import { shallow } from 'zustand/shallow';
import { useTranslation } from 'react-i18next';
import TotalLiabilitiesPhone from './TotalLiabilities.phone';
import TotalLiabilitiesTablet from './TotalLiabilities.tablet';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import { onChangeNumericValue } from 'features/fna/utils/helper/fnaUtils';
import { MAX_INPUT_AMOUNT } from 'features/fna/constants/lifeJourney';

export interface InternalTotalLiabilitiesProps extends ReadonlyProps {
  label?: string;
  value: string;
  onChange: (value: string) => void;
  orderNumber?: number;
}

const TotalLiabilities = memo(
  ({ readonly, orderNumber }: { orderNumber?: number } & ReadonlyProps) => {
    const { t } = useTranslation(['fna']);
    const { totalLiabilities, updateTotalLiabilities } = useFnaStore(
      state => ({
        totalLiabilities: state.lifeJourney.totalLiabilities,
        updateTotalLiabilities: state.updateTotalLiabilities,
      }),
      shallow,
    );

    const label = t('fna:totalLiabilities');

    const onChange = useCallback(
      (value: string) => {
        onChangeNumericValue(
          value,
          updateTotalLiabilities,
          amount => amount <= MAX_INPUT_AMOUNT,
        );
      },
      [updateTotalLiabilities],
    );

    return (
      <DeviceBasedRendering
        phone={
          <TotalLiabilitiesPhone
            label={label}
            value={String(totalLiabilities ?? '')}
            onChange={onChange}
            readonly={readonly}
            orderNumber={orderNumber}
          />
        }
        tablet={
          <TotalLiabilitiesTablet
            label={label}
            value={String(totalLiabilities ?? '')}
            onChange={onChange}
            readonly={readonly}
          />
        }
      />
    );
  },
);

export default TotalLiabilities;
