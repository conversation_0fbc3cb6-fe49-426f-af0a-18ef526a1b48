import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { Box, DropdownPanel, Icon, LargeLabel, Row } from 'cube-ui-components';
import {
  InternalInsuranceProtectionPeriodProps,
  InsuranceProtectionPeriodOption,
} from './InsuranceProtectionPeriod';
import { useTheme } from '@emotion/react';

const InsuranceProtectionPeriodPhone = memo(
  ({
    value,
    selectedItem,
    visible,
    show,
    hide,
    data,
    onDone,
    readonly,
    orderNumber,
  }: InternalInsuranceProtectionPeriodProps) => {
    const { colors, space } = useTheme();
    const { t } = useTranslation(['fna']);

    if (readonly) {
      return (
        <Row>
          {orderNumber && (
            <LargeLabel style={{ minWidth: space[6] }}>
              {`${orderNumber}. `}
            </LargeLabel>
          )}
          <LargeLabel style={{ flex: 1 }}>
            {t('fna:lifeStage.detail.question.insuranceProtectionPeriod')}
          </LargeLabel>
          <LargeLabel
            style={{ textAlign: 'right', flex: 1 }}
            fontWeight="bold"
            color={colors.palette.black}>
            {selectedItem?.label || '-'}
          </LargeLabel>
        </Row>
      );
    }

    return (
      <>
        <TouchableOpacity onPress={show} style={{ flex: 1 }}>
          <Row>
            {orderNumber && (
              <LargeLabel style={{ minWidth: space[6] }}>
                {`${orderNumber}. `}
              </LargeLabel>
            )}
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:lifeStage.detail.question.insuranceProtectionPeriod')}
            </LargeLabel>
            <Row flex={1} alignItems="center" justifyContent="flex-end">
              <LargeLabel
                style={{ textAlign: 'right', width: space[30] }}
                fontWeight="bold"
                color={
                  selectedItem?.label
                    ? colors.palette.black
                    : colors.palette.fwdGreyDark
                }>
                {value ||
                  t(
                    'fna:lifeStage.detail.question.insuranceProtectionPeriod.placeholder',
                  )}
              </LargeLabel>
              <Box width={space[1]} />
              <Icon.Dropdown fill={colors.palette.fwdAlternativeOrange[100]} />
            </Row>
          </Row>
        </TouchableOpacity>
        <DropdownPanel<InsuranceProtectionPeriodOption, string>
          title={t('fna:lifeStage.detail.question.insuranceProtectionPeriod')}
          actionLabel={t('fna:done')}
          visible={visible}
          data={data}
          selectedItem={selectedItem}
          getItemLabel={item => item.label}
          getItemValue={item => item.label}
          onDismiss={hide}
          onDone={onDone}
        />
      </>
    );
  },
);

export default InsuranceProtectionPeriodPhone;
