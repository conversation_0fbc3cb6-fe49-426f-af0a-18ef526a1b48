import { useTheme } from '@emotion/react';
import { Icon } from 'cube-ui-components';
import React, { memo } from 'react';
import { InternalProps } from './ExpectedIncomeIncrement';
import FnaInput from '../../../common/FnaInput';
import useToggle from 'hooks/useToggle';
import ReadonlyInput from '../readonly/ReadonlyInput';
import { useTranslation } from 'react-i18next';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';

const ExpectedIncomeIncrementTablet = memo(
  ({ label, value, onChange, readonly }: InternalProps) => {
    const { t } = useTranslation(['fna']);
    const { sizes } = useTheme();
    const [isFocusing, setFocusing, setBlur] = useToggle(false);
    const displayValue = typeof value === 'number' ? `${value} %` : '';
    const shouldHighlight = useFnaStore(state => state.shouldHighlight);

    return (
      <>
        {readonly ? (
          <ReadonlyInput label={label} value={displayValue} />
        ) : (
          <FnaInput
            label={label}
            value={isFocusing ? value?.toString() ?? '' : displayValue}
            onChange={onChange}
            right={<Icon.Create size={sizes[5]} />}
            onFocus={setFocusing}
            onBlur={setBlur}
            keyboardType="number-pad"
            returnKeyType="done"
            style={{ flex: 1 }}
            placeholder={t('fna:amount')}
            highlight={shouldHighlight && !value}
          />
        )}
      </>
    );
  },
);

export default ExpectedIncomeIncrementTablet;
