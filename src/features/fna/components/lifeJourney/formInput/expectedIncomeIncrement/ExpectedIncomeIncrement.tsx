import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { memo, useCallback } from 'react';
import { shallow } from 'zustand/shallow';
import ExpectedIncomeIncrementPhone from './ExpectedIncomeIncrement.phone';
import ExpectedIncomeIncrementTablet from './ExpectedIncomeIncrement.tablet';
import { useTranslation } from 'react-i18next';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import { onChangeNumericValue } from 'features/fna/utils/helper/fnaUtils';

export interface InternalProps extends ReadonlyProps {
  label?: string;
  value: number | null;
  onChange: (value: string) => void;
  orderNumber?: number;
}

const ExpectedIncomeIncrement = memo(
  ({
    readonly,
    orderNumber,
  }: {
    orderNumber?: number;
  } & ReadonlyProps) => {
    const { t } = useTranslation(['fna']);
    const { expectedIncomeIncrement, updateExpectedIncomeIncrement } =
      useFnaStore(
        state => ({
          expectedIncomeIncrement: state.lifeJourney.expectedIncomeIncrement,
          updateExpectedIncomeIncrement: state.updateExpectedIncomeIncrement,
        }),
        shallow,
      );

    const label = t('fna:expectedIncomeIncrement');

    const onChange = useCallback(
      (value: string) => {
        onChangeNumericValue(
          value,
          updateExpectedIncomeIncrement,
          percent =>
            Number.isInteger(percent) && percent >= 0 && percent <= 10000,
        );
      },
      [updateExpectedIncomeIncrement],
    );

    return (
      <DeviceBasedRendering
        phone={
          <ExpectedIncomeIncrementPhone
            value={expectedIncomeIncrement}
            onChange={onChange}
            readonly={readonly}
            orderNumber={orderNumber}
          />
        }
        tablet={
          <ExpectedIncomeIncrementTablet
            label={label}
            value={expectedIncomeIncrement}
            onChange={onChange}
            readonly={readonly}
          />
        }
      />
    );
  },
);

export default ExpectedIncomeIncrement;
