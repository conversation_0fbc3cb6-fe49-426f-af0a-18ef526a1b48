import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { memo, useCallback } from 'react';
import { shallow } from 'zustand/shallow';
import ProductCurrencyTablet from './ProductCurrency.tablet';
import { ProductCurrencyEnum } from 'types/products';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import ProductCurrencyPhone from './ProductCurrency.phone';
export interface InternalProductCurrencyProps {
  productCurrency: ProductCurrencyEnum | null;
  setProductCurrency?: (type: ProductCurrencyEnum) => void;
  resetProductCurrency?: () => void;
  shouldShowRequiredBeforeSavingError?: boolean;
  orderNumber?: number;
}

const ProductCurrency = memo(
  ({ readonly, orderNumber }: { orderNumber?: number } & ReadonlyProps) => {
    const {
      productCurrency,
      updateProductCurrency,
      shouldShowRequiredBeforeSavingError,
      setRequiredBeforeSavingError,
    } = useFnaStore(
      state => ({
        productCurrency: state.lifeJourney.productCurrency,
        updateProductCurrency: state.updateProductCurrency,
        shouldShowRequiredBeforeSavingError:
          state.shouldShowRequiredBeforeSavingError,
        setRequiredBeforeSavingError: state.setRequiredBeforeSavingError,
      }),
      shallow,
    );

    const setProductCurrency = useCallback(
      (type: ProductCurrencyEnum) => {
        setRequiredBeforeSavingError(false);
        updateProductCurrency(type);
      },
      [updateProductCurrency, setRequiredBeforeSavingError],
    );

    const resetProductCurrency = useCallback(() => {
      setRequiredBeforeSavingError(false);
      updateProductCurrency(null);
    }, [updateProductCurrency, setRequiredBeforeSavingError]);

    return (
      <DeviceBasedRendering
        phone={
          <ProductCurrencyPhone
            productCurrency={productCurrency}
            setProductCurrency={setProductCurrency}
            resetProductCurrency={resetProductCurrency}
            shouldShowRequiredBeforeSavingError={
              shouldShowRequiredBeforeSavingError
            }
            readonly={readonly}
            orderNumber={orderNumber}
          />
        }
        tablet={
          <ProductCurrencyTablet
            productCurrency={productCurrency}
            setProductCurrency={setProductCurrency}
            resetProductCurrency={resetProductCurrency}
            shouldShowRequiredBeforeSavingError={
              shouldShowRequiredBeforeSavingError
            }
            readonly={readonly}
          />
        }
      />
    );
  },
);

export default ProductCurrency;
