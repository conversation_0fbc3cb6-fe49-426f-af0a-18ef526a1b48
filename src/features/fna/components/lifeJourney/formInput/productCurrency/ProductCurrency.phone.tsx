import { useTheme } from '@emotion/react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'cube-ui-components';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { ProductCurrencyEnum } from 'types/products';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import { InternalProductCurrencyProps } from './ProductCurrency';

const ProductCurrencyPhone = memo(
  ({
    productCurrency,
    setProductCurrency,
    readonly,
    orderNumber,
  }: InternalProductCurrencyProps & ReadonlyProps) => {
    const { t } = useTranslation(['fna']);
    const { space, colors } = useTheme();
    const shouldHighlight = useFnaStore(state => state.shouldHighlight);

    console.log('orderNumber', orderNumber);

    if (readonly) {
      return (
        <Row>
          {orderNumber && (
            <LargeLabel style={{ minWidth: space[6] }}>
              {`${orderNumber}. `}
            </LargeLabel>
          )}
          <LargeLabel style={{ flex: 1 }}>
            {t('fna:productCurrency')}
          </LargeLabel>
          <LargeLabel
            style={{ textAlign: 'right', flex: 1 }}
            fontWeight="bold"
            color={colors.palette.black}>
            {productCurrency === ProductCurrencyEnum.RUPIAH
              ? t('fna:productCurrencyRupiah')
              : t('fna:productCurrencyForeignCurrency')}
          </LargeLabel>
        </Row>
      );
    }
    return (
      <Row flex={1} alignItems="center">
        {orderNumber && (
          <LargeLabel style={{ minWidth: space[6] }}>
            {`${orderNumber}. `}
          </LargeLabel>
        )}
        <LargeLabel style={{ flex: 1 }}>{t('fna:productCurrency')}</LargeLabel>

        <Picker
          type="chip"
          size="large"
          highlight={shouldHighlight && !productCurrency}
          items={[
            {
              value: ProductCurrencyEnum.RUPIAH,
              label: t('fna:productCurrencyRupiah'),
            },
            {
              value: ProductCurrencyEnum.FOREIGN_CURRENCY,
              label: t('fna:productCurrencyForeignCurrency'),
            },
          ]}
          value={productCurrency as ProductCurrencyEnum}
          onChange={type => {
            if (type) {
              setProductCurrency?.(type as ProductCurrencyEnum);
            }
          }}
          textPosition="right"
        />
      </Row>
    );
  },
);

export default ProductCurrencyPhone;
