import { Box, DropdownPanel, Icon, LargeLabel, Row } from 'cube-ui-components';
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { InternalAgeToRetireProps, RetireAgeOptionType } from './AgeToRetire';
import { useTheme } from '@emotion/react';

const AgeToRetirePhone = memo(
  ({
    visible,
    show,
    hide,
    data,
    value,
    selectedItem,
    onDone,
    orderNumber,
  }: InternalAgeToRetireProps) => {
    const { colors, space } = useTheme();
    const { t } = useTranslation(['fna']);

    return (
      <>
        <TouchableOpacity onPress={show} style={{ flex: 1 }}>
          <Row flex={1} justifyContent="flex-end">
            {orderNumber && (
              <LargeLabel style={{ minWidth: space[6] }}>
                {`${orderNumber}. `}
              </LargeLabel>
            )}
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.retirement.ageToRetire')}
            </LargeLabel>
            <Row flex={1} alignItems="center" justifyContent="flex-end">
              <LargeLabel fontWeight="bold">{value}</LargeLabel>
              <Icon.Dropdown fill={colors.palette.fwdAlternativeOrange[100]} />
            </Row>
          </Row>
        </TouchableOpacity>
        <DropdownPanel<RetireAgeOptionType, string>
          title={t('fna:savingsGoals.retirement.ageToRetire')}
          actionLabel={t('fna:done')}
          visible={visible}
          data={data}
          selectedItem={selectedItem}
          getItemLabel={item => item.label}
          getItemValue={item => item.label}
          onDismiss={hide}
          onDone={onDone}
        />
      </>
    );
  },
);

export default AgeToRetirePhone;
