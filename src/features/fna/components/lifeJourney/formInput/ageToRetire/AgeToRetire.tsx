import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import useToggle from 'hooks/useToggle';
import { memo, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import isEqual from 'lodash/isEqual';
import { calculateAge } from 'utils/helper/calculateAge';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import AgeToRetirePhone from './AgeToRetire.phone';
import AgeToRetireTablet from './AgeToRetire.tablet';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';

export interface InternalAgeToRetireProps extends ReadonlyProps {
  visible?: boolean;
  show?: () => void;
  hide?: () => void;
  data: RetireAgeOptionType[];
  value?: string;
  selectedItem?: RetireAgeOptionType;
  onDone?: (item: RetireAgeOptionType) => void;
  orderNumber?: number;
}

export type RetireAgeOptionType = {
  label: string;
  value: number;
};

export const RETIRE_AGES = [
  { label: '50', value: 50 },
  { label: '55', value: 55 },
  { label: '60', value: 60 },
  { label: '65', value: 65 },
];

const AgeToRetire = memo(
  ({ readonly, orderNumber }: { orderNumber?: number } & ReadonlyProps) => {
    const { lifeJourney, updateAgeRetire, updateRetirementGoal } = useFnaStore(
      state => ({
        lifeJourney: state.lifeJourney,
        updateAgeRetire: state.updateAgeRetire,
        updateRetirementGoal: state.updateRetirementGoal,
      }),
      shallow,
    );

    const { t } = useTranslation(['fna']);
    const [visible, show, hide] = useToggle(false);
    const retireAgeOptions = useMemo<RetireAgeOptionType[]>(() => {
      const dataRetireAge = RETIRE_AGES.filter(item =>
        lifeJourney.dob ? item.value > calculateAge(lifeJourney.dob) : true,
      );

      return dataRetireAge;
    }, [lifeJourney.dob]);

    const onDone = useCallback(
      (item: RetireAgeOptionType) => {
        updateAgeRetire(item.value);
        updateRetirementGoal({ ageToRetire: item.value });
        hide();
      },
      [hide, updateAgeRetire, updateRetirementGoal],
    );

    const selectedItem = useMemo(
      () =>
        retireAgeOptions.find(option =>
          isEqual(option.value, lifeJourney?.ageToRetire),
        ),
      [retireAgeOptions, lifeJourney?.ageToRetire],
    );

    const value = lifeJourney?.ageToRetire
      ? t('fna:yo', { age: lifeJourney?.ageToRetire })
      : t('fna:select');

    return (
      <DeviceBasedRendering
        phone={
          <AgeToRetirePhone
            visible={visible}
            show={show}
            hide={hide}
            data={retireAgeOptions}
            value={value}
            selectedItem={selectedItem}
            onDone={onDone}
            readonly={readonly}
            orderNumber={orderNumber}
          />
        }
        tablet={
          <AgeToRetireTablet
            visible={visible}
            show={show}
            hide={hide}
            data={retireAgeOptions}
            value={value}
            selectedItem={selectedItem}
            onDone={onDone}
            readonly={readonly}
          />
        }
      />
    );
  },
);

export default AgeToRetire;
