import styled from '@emotion/native';
import {
  getDefaultPolicy,
  InternalExistingPoliciesProps,
} from './ExistingPolicies';
import { useSafeAreaFrame } from 'react-native-safe-area-context';
import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  H6,
  Icon,
  Label,
  LargeLabel,
  Row,
  SvgIconProps,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import {
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { useGetOptionList } from 'hooks/useGetOptionList';
import Autocomplete from 'components/Autocomplete';
import { getOptionListLabel, getOptionListValue } from 'constants/optionList';
import Input from 'components/Input';
import { PaymentMode4CFF, PlanType } from 'types/optionList';
import DecimalTextField from 'components/DecimalTextField';
import DatePickerCalendar from 'components/DatePickerCalendar';
import React, { useRef } from 'react';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import ReadonlyInput from '../readonly/ReadonlyInput';
import { formatCurrency } from 'utils';
import { dateFormatWithSlashUtil } from 'utils/helper/formatUtil';
import NameField from 'components/NameField';
import * as Crypto from 'expo-crypto';
import { BenefitCoverage } from 'types/case';
import { ExistingPolicy } from 'features/fna/utils/store/fnaStore';
import useExistingPoliciesForm from 'features/fna/hooks/useExistingPoliciesForm';
import { MAX_INPUT_AMOUNT } from 'features/fna/constants/lifeJourney';

const MAX_POLICY = 5;
const BENEFIT_ICON_BY_NAME: Record<
  BenefitCoverage,
  React.ComponentType<SvgIconProps>
> = {
  death: Icon.Health,
  disability: Icon.Injury,
  criticalIllness: Icon.Medical,
  others: Icon.LoanApplication,
};
const POLICY_KEYS = [
  'productLine',
  'insuredName',
  'totalPremium',
  'premiumBenefits',
  'paymentMode',
  'maturityDate',
] as const;

export default function ExistingPoliciesTablet({
  visible,
  existingPolicies,
  onCancel,
  onDone,
  readonly,
}: InternalExistingPoliciesProps) {
  const { t } = useTranslation(['fna', 'common']);
  const { space, colors, borderRadius } = useTheme();
  const scrollRef = useRef<ScrollView>(null);
  const { height, width } = useSafeAreaFrame();
  const { data: optionList } = useGetOptionList();

  const {
    control,
    formState: { isValid },
    onDelete,
    setValue,
    watch,
    onSubmit,
    onAdd,
  } = useExistingPoliciesForm({
    visible,
    existingPolicies,
    onCancel,
    onDone,
    readonly,
    scrollRef,
  });

  const policies = watch('policies');

  return (
    <CFFModal visible={visible}>
      <KeyboardAvoidingViewContainer behavior="padding">
        <Box
          w={width - space[40]}
          maxH={height - space[40]}
          backgroundColor={colors.background}
          p={space[12]}
          pr={space[8]}
          borderRadius={borderRadius.large}>
          <H6 fontWeight="bold">{t('fna:existingPolicies.title')}</H6>
          <ScrollViewContainer ref={scrollRef}>
            {policies.map((policy, idx, arr) => {
              const deletionDisabled = arr.length <= 1;
              return (
                <Box
                  key={policy.id}
                  p={space[readonly ? 6 : 4]}
                  mb={space[4]}
                  borderWidth={1}
                  borderColor={colors.palette.fwdGrey[100]}
                  borderRadius={borderRadius.large}>
                  <Row justifyContent="space-between" alignItems="center">
                    <LargeLabel fontWeight="bold">
                      {t('fna:existingPolicies.policyPosition', {
                        position: idx + 1,
                      })}
                    </LargeLabel>
                    {!readonly && (
                      <DeleteButton
                        disabled={deletionDisabled}
                        hitSlop={ICON_HIT_SLOP}
                        onPress={() => onDelete(policy.id)}>
                        <Icon.Delete fill={colors.secondary} />
                      </DeleteButton>
                    )}
                  </Row>
                  {readonly ? (
                    <Row mt={space[4]} flexWrap="wrap" rowGap={space[5]}>
                      {POLICY_KEYS.map(key => {
                        switch (key) {
                          case 'premiumBenefits':
                            return policy[key].map(benefit => (
                              <ReadonlyInput
                                key={benefit.coverage}
                                label={t(
                                  `fna:existingPolicies.form.premiumOfBenefits.${benefit.coverage}`,
                                )}
                                value={
                                  benefit.totalPremium
                                    ? t('common:withCurrency', {
                                        amount: formatCurrency(
                                          benefit.totalPremium,
                                          2,
                                        ),
                                      })
                                    : '--'
                                }
                                style={styles.summaryField}
                              />
                            ));
                          case 'productLine':
                            return (
                              <ReadonlyInput
                                key={key}
                                label={t(
                                  'fna:existingPolicies.form.typeOfPlan',
                                )}
                                value={policy[key]}
                                style={styles.summaryField}
                              />
                            );
                          case 'insuredName':
                            return (
                              <ReadonlyInput
                                key={key}
                                label={t('fna:existingPolicies.form.insurer')}
                                value={policy[key]}
                                style={styles.summaryField}
                              />
                            );
                          case 'paymentMode':
                            return (
                              <ReadonlyInput
                                key={key}
                                label={t(
                                  'fna:existingPolicies.form.freqOfPayment',
                                )}
                                value={policy[key]}
                                style={styles.summaryField}
                              />
                            );
                          case 'maturityDate':
                            return (
                              <ReadonlyInput
                                key={key}
                                label={t(
                                  'fna:existingPolicies.form.maturityDate',
                                )}
                                value={dateFormatWithSlashUtil(policy[key])}
                                style={styles.summaryField}
                              />
                            );
                          case 'totalPremium':
                            return (
                              <ReadonlyInput
                                key={key}
                                label={t('fna:existingPolicies.form.premium')}
                                value={formatCurrency(policy[key])}
                                style={styles.summaryField}
                              />
                            );
                        }
                      }).flat()}
                    </Row>
                  ) : (
                    <>
                      <Row gap={space[5]} mt={23}>
                        <Input
                          key={policy.id}
                          control={control}
                          as={Autocomplete<PlanType, string>}
                          name={`policies.${idx}.productLine`}
                          label={t('fna:existingPolicies.form.typeOfPlan')}
                          data={optionList?.PLAN_TYPE.options || []}
                          getItemLabel={getOptionListLabel}
                          getItemValue={getOptionListValue}
                          style={styles.flex1}
                        />
                        <Input
                          key={policy.id}
                          control={control}
                          as={NameField}
                          name={`policies.${idx}.insuredName`}
                          label={t('fna:existingPolicies.form.insurer')}
                          style={styles.flex1}
                        />
                      </Row>
                      <Row gap={space[5]} mt={28}>
                        <Input
                          key={policy.id}
                          control={control}
                          as={DecimalTextField}
                          name={`policies.${idx}.totalPremium`}
                          label={t('fna:existingPolicies.form.premium')}
                          style={styles.flex1}
                          max={MAX_INPUT_AMOUNT}
                        />
                        <Input
                          key={policy.id}
                          control={control}
                          as={Autocomplete<PaymentMode4CFF, string>}
                          name={`policies.${idx}.paymentMode`}
                          label={t('fna:existingPolicies.form.freqOfPayment')}
                          data={optionList?.PAYMENTMODE4_CFF.options || []}
                          getItemLabel={getOptionListLabel}
                          getItemValue={getOptionListValue}
                          style={styles.flex1}
                        />
                        <Input
                          key={policy.id}
                          control={control}
                          as={DatePickerCalendar}
                          name={`policies.${idx}.maturityDate`}
                          label={t('fna:existingPolicies.form.maturityDate')}
                          hint={t('common:dateHint')}
                          style={styles.flex1}
                        />
                      </Row>
                      <Box gap={space[4]} mt={space[5]}>
                        <LargeLabel fontWeight="medium">
                          {t('fna:existingPolicies.form.premiumOfBenefits')}
                        </LargeLabel>
                        <Row gap={space[5]}>
                          {policy.premiumBenefits?.map(
                            (benefit, benefitIdx) => {
                              const Icon =
                                BENEFIT_ICON_BY_NAME[benefit.coverage];
                              return (
                                <Box
                                  key={benefit.coverage}
                                  gap={space[4]}
                                  flex={1}>
                                  <Row gap={space[1]}>
                                    {Boolean(Icon) && (
                                      <Icon size={20} fill={colors.primary} />
                                    )}
                                    <Label fontWeight="medium">
                                      {t(
                                        `fna:existingPolicies.form.premiumOfBenefits.${benefit.coverage}`,
                                      )}
                                    </Label>
                                  </Row>
                                  <Input
                                    control={control}
                                    as={DecimalTextField}
                                    name={`policies.${idx}.premiumBenefits.${benefitIdx}.totalPremium`}
                                    label={t('fna:amount.label.optional')}
                                    max={MAX_INPUT_AMOUNT}
                                  />
                                </Box>
                              );
                            },
                          )}
                        </Row>
                      </Box>
                    </>
                  )}
                </Box>
              );
            })}
            {!readonly && policies.length < MAX_POLICY && (
              <Button
                variant="secondary"
                size="small"
                mini
                icon={<Icon.Plus size={18} />}
                text={t('fna:existingPolicies.add')}
                onPress={onAdd}
              />
            )}
          </ScrollViewContainer>
          <Row
            justifyContent="center"
            alignSelf="center"
            gap={space[4]}
            mr={space[4]}
            mt={space[12]}>
            {readonly ? (
              <ActionButton
                size="medium"
                text={t('fna:ok')}
                onPress={onCancel}
              />
            ) : (
              <>
                <ActionButton
                  size="medium"
                  variant="secondary"
                  text={t('fna:cancel')}
                  onPress={onCancel}
                />
                <ActionButton
                  size="medium"
                  text={t('fna:done')}
                  disabled={!isValid}
                  onPress={onSubmit}
                />
              </>
            )}
          </Row>
        </Box>
      </KeyboardAvoidingViewContainer>
    </CFFModal>
  );
}

const ScrollViewContainer = styled(ScrollView)(({ theme }) => ({
  marginTop: theme.space[4],
  paddingRight: theme.space[4],
}));

const ActionButton = styled(Button)({
  width: 200,
});

export const DeleteButton = styled(TouchableOpacity)(({ disabled }) => ({
  opacity: disabled ? 0.3 : 1,
}));

const KeyboardAvoidingViewContainer = styled(KeyboardAvoidingView)(() => ({
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  alignItems: 'center',
}));

const styles = StyleSheet.create({
  flex1: {
    flex: 1,
  },
  summaryField: {
    flexBasis: '33%',
  },
});
