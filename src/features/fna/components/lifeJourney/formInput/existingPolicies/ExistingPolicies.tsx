import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { ExistingPolicy, useFnaStore } from 'features/fna/utils/store/fnaStore';
import useToggle from 'hooks/useToggle';
import { useTranslation } from 'react-i18next';
import ExistingPoliciesTablet from './ExistingPolicies.tablet';
import { shallow } from 'zustand/shallow';
import cloneDeep from 'lodash/cloneDeep';
import { DefaultValues } from 'react-hook-form';
import { ExistingPoliciesFormType } from 'features/fna/utils/validation/existingPoliciesValidation';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import ReadonlyInput from '../readonly/ReadonlyInput';
import { BenefitCoverage } from 'types/case';
import ExistingPoliciesPhone from './ExistingPolicies.phone';
import ExistingPoliciesFieldPhone from './ExistingPoliciesField.phone';
import ExistingPoliciesFieldTablet from './ExistingPoliciesField.tablet';
import React from 'react';

export interface InternalExistingPoliciesProps extends ReadonlyProps {
  visible: boolean;
  existingPolicies: ExistingPolicy[] | null;
  onCancel: () => void;
  onDone: (existingPolicies: ExistingPolicy[] | null) => void;
  orderNumber?: number;
}

export default function ExistingPolicies({ readonly, orderNumber }: { orderNumber?: number } & ReadonlyProps) {
  const { t } = useTranslation(['fna']);
  const [modalVisible, showModal, hideModal] = useToggle();
  const { existingPolicies, setExistingPolicies } = useFnaStore(
    state => ({
      existingPolicies: state.lifeJourney.existingPolicies,
      setExistingPolicies: state.setExistingPolicies,
    }),
    shallow,
  );
  const hasExistingPolicies = Boolean(
    existingPolicies && existingPolicies.length > 0,
  );

  return (
    <>
      {readonly ? (
        <>
          <ReadonlyInput
            label={t('fna:existingPolicies')}
            value={hasExistingPolicies ? t('fna:yes') : t('fna:no')}
            hasDetails={hasExistingPolicies}
            onViewDetails={showModal}
          />
        </>
      ) : (
        <DeviceBasedRendering
          phone={<ExistingPoliciesFieldPhone showModal={showModal} orderNumber={orderNumber} />}
          tablet={<ExistingPoliciesFieldTablet showModal={showModal} />}
        />
      )}

      <DeviceBasedRendering
        phone={
          <ExistingPoliciesPhone
            visible={modalVisible}
            existingPolicies={existingPolicies}
            onCancel={hideModal}
            onDone={existingPolicies => {
              setExistingPolicies(cloneDeep(existingPolicies));
              hideModal();
            }}
            readonly={readonly}
          />
        }
        tablet={
          <ExistingPoliciesTablet
            visible={modalVisible}
            existingPolicies={existingPolicies}
            onCancel={hideModal}
            onDone={existingPolicies => {
              setExistingPolicies(cloneDeep(existingPolicies));
              hideModal();
            }}
            readonly={readonly}
          />
        }
      />
    </>
  );
}

export const getDefaultPolicy = () => ({
  productLine: '',
  insuredName: '',
  paymentMode: '',
  totalPremium: undefined,
  maturityDate: undefined,
  premiumBenefits: [
    {
      coverage: BenefitCoverage.DEATH,
      totalPremium: null,
    },
    {
      coverage: BenefitCoverage.DISABILITY,
      totalPremium: null,
    },
    {
      coverage: BenefitCoverage.CRITICAL_ILLNESS,
      totalPremium: null,
    },
    {
      coverage: BenefitCoverage.OTHERS,
      totalPremium: null,
    },
  ],
});

export const defaultPolicies: DefaultValues<ExistingPoliciesFormType> = {
  policies: [getDefaultPolicy()],
};
