import { useTheme } from '@emotion/react';
import { DefaultValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import styled from '@emotion/native';

import { Icon, Row, Switch, LargeLabel, Box } from 'cube-ui-components';
import { ExistingPolicy, useFnaStore } from 'features/fna/utils/store/fnaStore';
import { ExistingPoliciesFormType } from 'features/fna/utils/validation/existingPoliciesValidation';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import { BenefitCoverage } from 'types/case';

export interface InternalExistingPoliciesProps extends ReadonlyProps {
  visible: boolean;
  existingPolicies: ExistingPolicy[] | null;
  onCancel: () => void;
  onDone: (existingPolicies: ExistingPolicy[] | null) => void;
}

export default function ExistingPoliciesField({
  showModal,
  orderNumber,
}: {
  showModal: () => void;
  orderNumber?: number;
}) {
  const { t } = useTranslation(['fna']);
  const { space, sizes, colors } = useTheme();
  const { existingPolicies, setExistingPolicies } = useFnaStore(
    state => ({
      existingPolicies: state.lifeJourney.existingPolicies,
      setExistingPolicies: state.setExistingPolicies,
    }),
    shallow,
  );
  const hasExistingPolicies = Boolean(
    existingPolicies && existingPolicies.length > 0,
  );

  return (
    <Box flex={1} gap={space[3]}>
      <Row>
        {orderNumber && (
          <LargeLabel style={{ minWidth: space[6] }}>
            {`${orderNumber}. `}
          </LargeLabel>
        )}
        <Row flex={1}>
          <LargeLabel>{t('fna:existingPolicies')}</LargeLabel>
        </Row>
        <Row flex={1} alignItems="center" justifyContent="flex-end">
          <Switch
            checked={hasExistingPolicies}
            label={hasExistingPolicies ? t('fna:yes') : t('fna:no')}
            onChange={value => {
              if (value) {
                showModal();
              } else {
                setExistingPolicies(null);
              }
            }}
          />
        </Row>
      </Row>
      {hasExistingPolicies && (
        <EditButton onPress={showModal}>
          <Row flex={1} ml={orderNumber ? space[6] : 0}>
            <LargeLabel>
              {t('fna:lifeStage.detail.question.added.policies.phone', {
                total: (existingPolicies || []).length,
              })}
            </LargeLabel>
          </Row>
          <Row alignItems="center" gap={space[2]}>
            <LargeLabel fontWeight="medium">{t('fna:edit')}</LargeLabel>
            <Icon.Create
              size={sizes[6]}
              fill={colors.palette.fwdAlternativeOrange[100]}
            />
          </Row>
        </EditButton>
      )}
    </Box>
  );
}

export const getDefaultPolicy = () => ({
  productLine: '',
  insuredName: '',
  paymentMode: '',
  totalPremium: undefined,
  maturityDate: undefined,
  premiumBenefits: [
    {
      coverage: BenefitCoverage.DEATH,
      totalPremium: null,
    },
    {
      coverage: BenefitCoverage.DISABILITY,
      totalPremium: null,
    },
    {
      coverage: BenefitCoverage.CRITICAL_ILLNESS,
      totalPremium: null,
    },
    {
      coverage: BenefitCoverage.OTHERS,
      totalPremium: null,
    },
  ],
});

const EditButton = styled.TouchableOpacity(() => ({
  flexDirection: 'row',
  alignItems: 'center',
}));

export const defaultPolicies: DefaultValues<ExistingPoliciesFormType> = {
  policies: [getDefaultPolicy()],
};
