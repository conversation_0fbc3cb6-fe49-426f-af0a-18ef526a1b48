import styled from '@emotion/native';
import { InternalExistingPoliciesProps } from './ExistingPolicies';
import { useTheme } from '@emotion/react';
import {
  Box,
  H6,
  Icon,
  Label,
  LargeLabel,
  Row,
  SvgIconProps,
  DatePicker,
  TextField,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { getOptionListLabel, getOptionListValue } from 'constants/optionList';
import Input from 'components/Input';
import { PaymentMode4CFF, PlanType } from 'types/optionList';
import DecimalTextField from 'components/DecimalTextField';
import React, { useCallback, useRef, useState } from 'react';
import ReadonlyInput from '../readonly/ReadonlyInput';
import { formatCurrency } from 'utils';
import { dateFormatWithSlashUtil } from 'utils/helper/formatUtil';
import NameField from 'components/NameField';
import { BenefitCoverage } from 'types/case';
import { Portal } from '@gorhom/portal';
import {
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  BottomSheetFooterProps,
} from '@gorhom/bottom-sheet';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import SearchableDropdown from 'components/SearchableDropdown';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FnaConfirmFooter from 'features/fna/components/common/FnaConfirmFooter';
import useExistingPoliciesForm from 'features/fna/hooks/useExistingPoliciesForm';
import { MAX_INPUT_AMOUNT } from 'features/fna/constants/lifeJourney';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';

const MAX_POLICY = 5;
const BENEFIT_ICON_BY_NAME: Record<
  BenefitCoverage,
  React.ComponentType<SvgIconProps>
> = {
  death: Icon.Health,
  disability: Icon.Injury,
  criticalIllness: Icon.Medical,
  others: Icon.LoanApplication,
};
const POLICY_KEYS = [
  'productLine',
  'insuredName',
  'totalPremium',
  'premiumBenefits',
  'paymentMode',
  'maturityDate',
] as const;

export default function ExistingPoliciesPhone({
  visible,
  existingPolicies,
  onCancel,
  onDone,
  readonly,
}: InternalExistingPoliciesProps) {
  const { t } = useTranslation(['fna', 'common']);
  const { space, colors, borderRadius } = useTheme();
  const scrollRef = useRef<ScrollView>(null);
  const { data: optionList } = useGetOptionList<'my'>();

  const {
    control,
    formState: { isValid },
    handleSubmit,
    onAdd,
    watch,
    trigger,
    onSubmit,
    onDelete,
  } = useExistingPoliciesForm({
    visible,
    existingPolicies,
    onCancel,
    onDone,
    readonly,
    scrollRef,
  });

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints(false);

  const policies = watch('policies');

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FnaConfirmFooter
          {...props}
          buttonTitle={readonly ? t('fna:ok') : t('fna:done')}
          onPress={() => {
            if (readonly) {
              onCancel();
            } else {
              onSubmit();
            }
          }}
          disabled={!isValid}
        />
      );
    },
    [readonly, t, isValid, onCancel, onSubmit],
  );

  const [collapseIndexes, setCollapseIndexes] = useState<string[]>([]);

  const toggleValue = (value: string) => {
    const index = collapseIndexes.indexOf(value);
    setCollapseIndexes(prev => {
      if (index === -1) {
        return [...prev, value];
      }
      return prev.filter(v => v !== value);
    });
  };

  const handleAdd = () => {
    setCollapseIndexes(policies.map(policy => policy.id || ''));
    onAdd();
  };

  return (
    <>
      {visible && (
        <Portal>
          <BottomSheetModalProvider>
            <BottomSheetModal
              onDismiss={onCancel}
              index={1}
              snapPoints={snapPoints}
              {...bottomSheetProps}
              style={{ padding: 0 }}
              footerComponent={renderFooter}>
              <Row
                backgroundColor={colors.background}
                p={space[5]}
                justifyContent="space-between"
                alignItems="center"
                gap={space[2]}
                borderRadius={borderRadius.large}>
                <H6 style={{ flex: 1 }} fontWeight="bold">
                  {t('fna:existingPolicies.title')}
                </H6>
                {!readonly && policies.length < MAX_POLICY && (
                  <AddButton onPress={handleAdd}>
                    <Icon.Plus
                      size={space[4]}
                      fill={colors.palette.fwdAlternativeOrange[100]}
                    />
                    <LargeLabel
                      fontWeight="bold"
                      color={colors.palette.fwdAlternativeOrange[100]}>
                      {t('fna:existingPolicies.add')}
                    </LargeLabel>
                  </AddButton>
                )}
              </Row>

              <BottomSheetKeyboardAwareScrollView
                ref={scrollRef}
                bottomOffset={space[8]}
                style={{
                  paddingHorizontal: space[5],
                }}>
                {Boolean(policies?.length) && (
                  <Container>
                    {policies.map((policy, idx, arr) => {
                      const deletionDisabled = arr.length <= 1;
                      return (
                        <Box key={policy.id}>
                          <Row alignItems="center">
                            <LargeLabel fontWeight="bold" style={{ flex: 1 }}>
                              {t('fna:existingPolicies.policyPosition', {
                                position: idx + 1,
                              })}
                            </LargeLabel>
                            {!readonly && (
                              <DeleteButton
                                disabled={deletionDisabled}
                                hitSlop={ICON_HIT_SLOP}
                                onPress={() => onDelete(policy.id)}>
                                <Icon.Delete fill={colors.secondary} />
                              </DeleteButton>
                            )}
                            <CollapseButton
                              onPress={() => toggleValue(policy.id || '')}>
                              <Icon.Dropdown
                                fill={colors.palette.fwdAlternativeOrange[100]}
                              />
                            </CollapseButton>
                          </Row>
                          <CollapseItem
                            isLastItem={idx === arr.length - 1}
                            isCollapsed={collapseIndexes.includes(
                              policy.id || '',
                            )}
                            gap={space[4]}>
                            {readonly ? (
                              <Row
                                mt={space[4]}
                                flexWrap="wrap"
                                rowGap={space[5]}>
                                {POLICY_KEYS.map(key => {
                                  switch (key) {
                                    case 'premiumBenefits':
                                      return policy[key].map(benefit => (
                                        <ReadonlyInput
                                          key={benefit.coverage}
                                          label={t(
                                            `fna:existingPolicies.form.premiumOfBenefits.${benefit.coverage}`,
                                          )}
                                          value={
                                            benefit.totalPremium
                                              ? t('common:withCurrency', {
                                                  amount: formatCurrency(
                                                    benefit.totalPremium,
                                                    2,
                                                  ),
                                                })
                                              : '--'
                                          }
                                          style={styles.summaryField}
                                        />
                                      ));
                                    case 'productLine':
                                      return (
                                        <ReadonlyInput
                                          key={key}
                                          label={t(
                                            'fna:existingPolicies.form.typeOfPlan',
                                          )}
                                          value={policy[key]}
                                          style={styles.summaryField}
                                        />
                                      );
                                    case 'insuredName':
                                      return (
                                        <ReadonlyInput
                                          key={key}
                                          label={t(
                                            'fna:existingPolicies.form.insurer',
                                          )}
                                          value={policy[key]}
                                          style={styles.summaryField}
                                        />
                                      );
                                    case 'paymentMode':
                                      return (
                                        <ReadonlyInput
                                          key={key}
                                          label={t(
                                            'fna:existingPolicies.form.freqOfPayment',
                                          )}
                                          value={policy[key]}
                                          style={styles.summaryField}
                                        />
                                      );
                                    case 'maturityDate':
                                      return (
                                        <ReadonlyInput
                                          key={key}
                                          label={t(
                                            'fna:existingPolicies.form.maturityDate',
                                          )}
                                          value={dateFormatWithSlashUtil(
                                            policy[key],
                                          )}
                                          style={styles.summaryField}
                                        />
                                      );
                                    case 'totalPremium':
                                      return (
                                        <ReadonlyInput
                                          key={key}
                                          label={t(
                                            'fna:existingPolicies.form.premium',
                                          )}
                                          value={formatCurrency(policy[key])}
                                          style={styles.summaryField}
                                        />
                                      );
                                  }
                                }).flat()}
                              </Row>
                            ) : (
                              <Box gap={space[4]} mt={space[5]}>
                                <Input
                                  key={policy.id}
                                  control={control}
                                  as={SearchableDropdown<PlanType, string>}
                                  name={`policies.${idx}.productLine`}
                                  label={t(
                                    'fna:existingPolicies.form.typeOfPlan',
                                  )}
                                  modalTitle={t(
                                    'fna:existingPolicies.form.typeOfPlan',
                                  )}
                                  data={optionList?.PLAN_TYPE.options || []}
                                  getItemLabel={getOptionListLabel}
                                  getItemValue={getOptionListValue}
                                  style={styles.flex1}
                                />
                                <Input
                                  key={policy.id}
                                  control={control}
                                  as={NameField}
                                  name={`policies.${idx}.insuredName`}
                                  label={t('fna:existingPolicies.form.insurer')}
                                  style={styles.flex1}
                                />
                                <Input
                                  key={policy.id}
                                  control={control}
                                  as={DecimalTextField}
                                  name={`policies.${idx}.totalPremium`}
                                  label={t('fna:existingPolicies.form.premium')}
                                  style={styles.flex1}
                                  max={MAX_INPUT_AMOUNT}
                                />
                                <Input
                                  key={policy.id}
                                  control={control}
                                  as={
                                    SearchableDropdown<PaymentMode4CFF, string>
                                  }
                                  name={`policies.${idx}.paymentMode`}
                                  label={t(
                                    'fna:existingPolicies.form.freqOfPayment',
                                  )}
                                  data={
                                    optionList?.PAYMENTMODE4_CFF.options || []
                                  }
                                  getItemLabel={getOptionListLabel}
                                  getItemValue={getOptionListValue}
                                  style={styles.flex1}
                                />
                                <Input
                                  key={policy.id}
                                  control={control}
                                  as={DatePicker}
                                  name={`policies.${idx}.maturityDate`}
                                  label={t(
                                    'fna:existingPolicies.form.maturityDate',
                                  )}
                                  hint={t('common:dateHint')}
                                  onChange={() =>
                                    setTimeout(
                                      () =>
                                        trigger(`policies.${idx}.maturityDate`),
                                      100,
                                    )
                                  }
                                  style={styles.flex1}
                                />
                                <Box gap={space[4]} mt={space[5]}>
                                  <LargeLabel fontWeight="medium">
                                    {t(
                                      'fna:existingPolicies.form.premiumOfBenefits',
                                    )}
                                  </LargeLabel>
                                  <Row
                                    width="100%"
                                    flexWrap="wrap"
                                    justifyContent="space-between">
                                    {policy.premiumBenefits?.map(
                                      (benefit, benefitIdx) => {
                                        const Icon =
                                          BENEFIT_ICON_BY_NAME[
                                            benefit.coverage
                                          ];
                                        return (
                                          <Box
                                            key={benefit.coverage}
                                            gap={space[4]}
                                            mb={space[4]}
                                            width="48%">
                                            <Row gap={space[1]}>
                                              {Boolean(Icon) && (
                                                <Icon
                                                  size={20}
                                                  fill={colors.primary}
                                                />
                                              )}
                                              <Label fontWeight="medium">
                                                {t(
                                                  `fna:existingPolicies.form.premiumOfBenefits.${benefit.coverage}`,
                                                )}
                                              </Label>
                                            </Row>
                                            <Input
                                              control={control}
                                              as={DecimalTextField}
                                              name={`policies.${idx}.premiumBenefits.${benefitIdx}.totalPremium`}
                                              label={t(
                                                'fna:amount.label.optional',
                                              )}
                                              max={MAX_INPUT_AMOUNT}
                                            />
                                          </Box>
                                        );
                                      },
                                    )}
                                  </Row>
                                </Box>
                              </Box>
                            )}
                          </CollapseItem>
                          {idx + 1 < arr.length && <Divider />}
                        </Box>
                      );
                    })}
                  </Container>
                )}
                <BottomSheetFooterSpace />
              </BottomSheetKeyboardAwareScrollView>
            </BottomSheetModal>
          </BottomSheetModalProvider>
        </Portal>
      )}
    </>
  );
}

export const DeleteButton = styled(TouchableOpacity)(({ disabled }) => ({
  opacity: disabled ? 0.3 : 1,
}));

const AddButton = styled(TouchableOpacity)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
  gap: 5,
}));

const CollapseButton = styled(TouchableOpacity)(({ theme: { space } }) => ({
  paddingLeft: space[2],
}));

const CollapseItem = styled(Box)<{ isCollapsed: boolean; isLastItem: boolean }>(
  ({ theme: { space }, isCollapsed, isLastItem }) => ({
    gap: space[4],
    height: isCollapsed ? 0 : 'auto',
    marginVertical: isCollapsed ? 0 : space[4],
    marginBottom: isLastItem ? 0 : space[4],
    overflow: 'hidden',
  }),
);

const Container = styled.View(({ theme: { space, sizes, colors } }) => ({
  borderRadius: sizes[3],
  borderColor: colors.palette.fwdGrey[100],
  borderWidth: 1,
  padding: space[4],
}));

const Divider = styled(Box)(({ theme }) => {
  return {
    marginBottom: theme.space[4],
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

const styles = StyleSheet.create({
  flex1: {
    flex: 1,
  },
  summaryField: {
    flexBasis: '33%',
  },
});
