import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { InternalDateOfBirthProps } from './DateOfBirth';
import DatePickerCalendar from 'components/DatePickerCalendar';
import FnaInput from '../../../common/FnaInput';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { country } from 'utils/context';
import {
  DEFAULT_LOCALIZATION,
  ID_LOCALIZATION,
} from 'components/DatePickerCalendar/constants';

const { minDate, maxDate, defaultDate } = getDateOfBirthDropdownProps();

const DateOfBirthTablet = memo(({ onDone }: InternalDateOfBirthProps) => {
  const { t } = useTranslation(['fna', 'common']);
  const dob = useFnaStore(state => state.lifeJourney.dob);
  const shouldHighlight = useFnaStore(state => state.shouldHighlight);

  return (
    <DatePickerCalendar
      label={t('fna:yourBirthdate')}
      placeholder={t('common:dateHint')}
      renderInput={props => (
        <FnaInput {...props} highlight={shouldHighlight && !dob} />
      )}
      style={{ flex: 0.8 }}
      formatDate={date => `${dateFormatUtil(date)}`}
      value={dob ?? undefined}
      onChange={onDone}
      minDate={minDate}
      maxDate={maxDate}
      defaultDate={dob ?? defaultDate}
      localization={country === 'id' ? ID_LOCALIZATION : DEFAULT_LOCALIZATION}
    />
  );
});

export default DateOfBirthTablet;
