import { useTheme } from '@emotion/react';
import { Box, LargeLabel, Row, SmallLabel, Switch } from 'cube-ui-components';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import ReadonlyInput from '../readonly/ReadonlyInput';

const PlanToTravel = memo(
  ({ readonly, orderNumber }: { orderNumber?: number } & ReadonlyProps) => {
    const { t } = useTranslation(['fna']);
    const { colors, space } = useTheme();
    const { lifeJourney, updatePlanToTravel } = useFnaStore(
      state => ({
        lifeJourney: state.lifeJourney,
        updatePlanToTravel: state.updatePlanToTravel,
      }),
      shallow,
    );

    const { isTabletMode } = useLayoutAdoptionCheck();
    const switchLabel = lifeJourney.planToTravel ? t('fna:yes') : t('fna:no');

    return (
      <>
        {readonly ? (
          <ReadonlyInput
            label={t('fna:lifeStage.detail.question.planToTravel')}
            value={switchLabel}
          />
        ) : isTabletMode ? (
          <Box gap={14.5}>
            <SmallLabel
              fontWeight="medium"
              color={colors.palette.fwdGreyDarker}>
              {t('fna:lifeStage.detail.question.planToTravel')}
            </SmallLabel>
            <Switch
              value={lifeJourney.planToTravel}
              onChange={value => updatePlanToTravel(value)}
              label={switchLabel}
            />
          </Box>
        ) : (
          <Row alignItems="center">
            {orderNumber && (
              <LargeLabel style={{ minWidth: space[6] }}>
                {`${orderNumber}. `}
              </LargeLabel>
            )}
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:lifeStage.detail.question.planToTravel')}
            </LargeLabel>
            <LargeLabel fontWeight="bold">{switchLabel}</LargeLabel>
            <Box width={space[2]} />
            <Switch
              value={lifeJourney.planToTravel}
              onChange={value => updatePlanToTravel(value)}
            />
          </Row>
        )}
      </>
    );
  },
);

export default PlanToTravel;
