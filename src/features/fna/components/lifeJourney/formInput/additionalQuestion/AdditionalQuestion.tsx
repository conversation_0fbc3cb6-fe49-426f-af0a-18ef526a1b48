import React, { Fragment } from 'react';
import AgeToRetire from '../ageToRetire/AgeToRetire';
import PlanToTravel from '../planToTravel/PlanToTravel';
import Dependents from '../dependents/Dependents';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import NumberOfKids from '../numberOfKids/NumberOfKids';
import HavePartner from '../havePartner/HavePartner';
import { Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useAdditionalQuestions } from 'features/fna/hooks/useAdditionalQuestions';

const AdditionalQuestion = ({
  readonly,
  orderNumber = 0,
}: {
  orderNumber?: number;
} & ReadonlyProps) => {
  const { space } = useTheme();

  const additionalQuestions = useAdditionalQuestions(readonly);

  const { isTabletMode } = useLayoutAdoptionCheck();

  if (additionalQuestions.length === 0) return null;

  const content = additionalQuestions.map((type, index) => {
    const question = typeof type === 'string' ? { type } : type;
    return (
      <React.Fragment key={question.type}>
        {question.type === 'ageToRetire' && (
          <AgeToRetire readonly={readonly} orderNumber={orderNumber + index} />
        )}
        {question.type === 'planToTravel' && (
          <PlanToTravel readonly={readonly} orderNumber={orderNumber + index} />
        )}
        {question.type === 'dependents' && (
          <Dependents
            config={question}
            readonly={readonly}
            orderNumber={orderNumber + index}
          />
        )}
        {question.type === 'numberOfKids' && (
          <NumberOfKids readonly={readonly} orderNumber={orderNumber + index} />
        )}
        {question.type === 'havePartner' && (
          <HavePartner readonly={readonly} orderNumber={orderNumber + index} />
        )}
      </React.Fragment>
    );
  });

  if (isTabletMode) {
    return content;
  }

  return <Box flex={1} gap={space[6]}>{content}</Box>;
};

export default AdditionalQuestion;
