import LifeStageSelection from 'features/fna/components/lifeStage/LifeStageSelection';
import DateOfBirth from '../../../formInput/dateOfBirth/DateOfBirth';
import ExpectedIncomeIncrement from '../../../formInput/expectedIncomeIncrement/ExpectedIncomeIncrement';
import FinancialBudget from '../../../formInput/financialBudget/FinancialBudget';
import Income from '../../../formInput/income/Income';
import Title from '../../../formInput/title/Title';
import TotalAssets from '../../../formInput/totalAssets/TotalAssets';
import TotalLiabilities from '../../../formInput/totalLiabilities/TotalLiabilities';
import AdditionalQuestion from '../../../formInput/additionalQuestion/AdditionalQuestion';
import ExistingPolicies from '../../../formInput/existingPolicies/ExistingPolicies';

export const phoneFormConfig = [
  LifeStageSelection,
  Title,
  DateOfBirth,
  Income,
  ExpectedIncomeIncrement,
  FinancialBudget,
  TotalAssets,
  TotalLiabilities,
  ExistingPolicies,
  AdditionalQuestion,
];
