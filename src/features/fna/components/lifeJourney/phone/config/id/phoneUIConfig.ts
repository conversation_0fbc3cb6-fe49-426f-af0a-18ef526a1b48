import LifeStageSelection from 'features/fna/components/lifeStage/LifeStageSelection';
import DateOfBirth from '../../../formInput/dateOfBirth/DateOfBirth';
import ProductCurrency from '../../../formInput/productCurrency/ProductCurrency';
import Income from '../../../formInput/income/Income';
import InsuranceProtectionPeriod from '../../../formInput/insuranceProtectionPeriod/InsuranceProtectionPeriod';
import AdditionalQuestion from '../../../formInput/additionalQuestion/AdditionalQuestion';

export const phoneFormConfig = [
  LifeStageSelection,
  DateOfBirth,
  InsuranceProtectionPeriod,
  Income,
  ProductCurrency,
  AdditionalQuestion,
];
