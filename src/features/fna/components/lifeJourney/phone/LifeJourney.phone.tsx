import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';
import { Box, H6, Label } from 'cube-ui-components';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import useToggle from 'hooks/useToggle';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';
import { countryModuleFnaConfig } from 'utils/config/module';
import FnaConfirmFooter from '../../common/FnaConfirmFooter';
import { ExploreNeeds } from '../../exploreNeeds/ExploreNeeds';
import GoalsPlanning from '../../goals/GoalsPlanning';
import LifeStageSelection from '../../lifeStage/LifeStageSelection';
import { AboutMe } from './aboutMe/AboutMe';
import { BasicInformation } from './basicInformation/BasicInformation';
import { LifeJourneyForm } from './form/LifeJourneyForm';

export default function LifeJourneyPhone({
  shouldShowRecommendation,
}: {
  shouldShowRecommendation: boolean;
}) {
  const { space } = useTheme();
  const { t } = useTranslation(['fna']);
  const [visible, show, hide] = useToggle();

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints(false);
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const [planningGoalVisible, showPlanningGoal, hidePlanningGoal] = useToggle();
  const fnaState = useFnaStore();
  const resetConcernsPlanning = useFnaStore(
    state => state.resetConcernsPlanning,
  );

  const onConfirm = useCallback(async () => {
    if (countryModuleFnaConfig.hasGoalPlan) {
      resetConcernsPlanning();
      showPlanningGoal();
    } else {
      bottomSheetProps.bottomSheetRef.current?.close();
    }
  }, [
    bottomSheetProps.bottomSheetRef,
    resetConcernsPlanning,
    showPlanningGoal,
  ]);

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FnaConfirmFooter
          {...props}
          buttonTitle={t('fna:lifeStage.confirm')}
          onPress={onConfirm}
          disabled={!shouldShowRecommendation}
        />
      );
    },
    [onConfirm, shouldShowRecommendation, t],
  );

  return (
    <>
      <FnaScreenFormContainer>
        {shouldShowRecommendation ? (
          <AboutMe onPress={show} />
        ) : (
          <Box>
            <BasicInformation />
            <Box height={space[6]} />
            <Box ml={space[2]}>
              <Label fontWeight="bold">{t('fna:lifeStage.detail.1')}</Label>
            </Box>
            <LifeStageSelection onSelect={show} titleVisible={false} />
          </Box>
        )}
        <ExploreNeeds isFnaValid={shouldShowRecommendation} />
      </FnaScreenFormContainer>
      {visible && (
        <Portal>
          <BottomSheetModalProvider>
            <BottomSheetModal
              onDismiss={hide}
              index={1}
              snapPoints={snapPoints}
              footerComponent={renderFooter}
              {...bottomSheetProps}
              style={{ padding: 0 }}>
              <Box paddingX={space[isNarrowScreen ? 3 : 4]}>
                <H6 fontWeight="bold">
                  {t('fna:hi', { userName: fnaState.getCustomerName() ?? '' })}
                </H6>
                <Box height={space[4]} />
              </Box>
              <BottomSheetKeyboardAwareScrollView
                keyboardDismissMode="on-drag"
                bottomOffset={space[4]}
                style={{
                  paddingHorizontal: space[isNarrowScreen ? 3 : 4],
                }}>
                <LifeJourneyForm />
                <BottomSheetFooterSpace />
              </BottomSheetKeyboardAwareScrollView>
            </BottomSheetModal>
          </BottomSheetModalProvider>
        </Portal>
      )}

      {countryModuleFnaConfig.hasGoalPlan && (
        <GoalsPlanning
          isModalVisible={planningGoalVisible}
          onDone={() => {
            hidePlanningGoal();
            bottomSheetProps.bottomSheetRef.current?.close();
          }}
          onDismiss={hidePlanningGoal}
        />
      )}
    </>
  );
}

const FnaScreenFormContainer = styled(ScrollView)(({ theme: { space } }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    paddingHorizontal: isNarrowScreen ? space[3] : space[4],
    paddingVertical: space[3],
    flex: 1,
  };
});
