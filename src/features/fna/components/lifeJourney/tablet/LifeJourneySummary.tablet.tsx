import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box, LargeLabel,
  Row,
  SmallBody
} from 'cube-ui-components';
import { getCheckLifeJourneyCompletion } from 'features/fna/utils/helper/checkFnaCompletion';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from 'react-native';
import { Path, Svg } from 'react-native-svg';
import { shallow } from 'zustand/shallow';
import { Gender } from 'types/person';
import DropShadow from 'react-native-drop-shadow';
import {
  tabletSummaryConfig
} from './config/lifeJourneyTabletConfig';
import BasicInfoMaleLogo from '../../illustrations/BasicInfoMaleLogo';
import BasicInfoFemaleLogo from '../../illustrations/BasicInfoFemaleLogo';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { calculateAge } from 'utils/helper/calculateAge';
import { getIllustrationsByLifeStage } from './LifeJourney.tablet';

export interface ReadonlyProps {
  readonly?: boolean;
}

export default function LifeJourneySummaryTablet() {
  const theme = useTheme();
  const { t } = useTranslation(['fna']);
  const { data: optionList } = useGetOptionList();

  const { lifeJourney, getCustomerName } = useFnaStore(
    state => ({
      getCustomerName: state.getCustomerName,
      lifeJourney: state.lifeJourney,
      lifeStage: state.lifeJourney.lifeStage,
    }),
    shallow,
  );

  const [containerLayout, setContainerLayout] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });

  const isLifeJourneyCompleted = useMemo(
    () => getCheckLifeJourneyCompletion(lifeJourney),
    [lifeJourney],
  );

  const lifeStageIllustrations = useMemo(
    () => getIllustrationsByLifeStage(lifeJourney),
    [lifeJourney],
  );

  return (
    <DropShadowContainer>
      <View
        onLayout={e =>
          setContainerLayout(e.nativeEvent?.layout ?? containerLayout)
        }>
        <Box mx={theme.space[16]}>
          <Row gap={theme.space[6]}>
            <Box
              w={140}
              minHeight={130}
              borderRadius={theme.borderRadius.large}
              overflow="hidden"
              alignSelf="stretch">
              <Box style={StyleSheet.absoluteFill}>
                {lifeJourney.gender === Gender.MALE ? (
                  <BasicInfoMaleLogo />
                ) : (
                  <BasicInfoFemaleLogo />
                )}
              </Box>
              <Box
                pos="absolute"
                top={0}
                left={0}
                right={0}
                p={theme.space[3]}
                pr={0}>
                {Boolean(lifeJourney?.title) && (
                  <SmallBody>
                    {
                      optionList?.CUBE_TITLE.options.find(
                        option => option.value === lifeJourney?.title,
                      )?.label
                    }
                  </SmallBody>
                )}
                <LargeLabel numberOfLines={3} fontWeight="bold">
                  {getCustomerName()}
                </LargeLabel>
                <Row mt={theme.space[1]}>
                  <SmallBody color={theme.colors.placeholder}>{`${
                    lifeJourney.gender === Gender.MALE
                      ? t('fna:male')
                      : t('fna:female')
                  }・${
                    lifeJourney.dob
                      ? calculateAge(lifeJourney.dob)
                      : lifeJourney.dob
                  } ${t('fna:yearsOld')}`}</SmallBody>
                </Row>
              </Box>
            </Box>
            <Box flex={1} justifyContent="center">
              <Row
                mb={theme.space[tabletSummaryConfig.rowTwo.length > 0 ? 6 : 0]}
                gap={theme.space[6]}
                flexWrap="wrap">
                {tabletSummaryConfig.rowOne.map((Component, idx) => (
                  <Component key={idx} readonly />
                ))}
              </Row>
              <Row gap={theme.space[6]} flexWrap="wrap">
                {tabletSummaryConfig.rowTwo.map((Component, idx) => (
                  <Component key={idx} readonly />
                ))}
              </Row>
            </Box>
          </Row>
          <Box h={20} />
          {isLifeJourneyCompleted && (
            <Row mb={16}>
              <Box flex={1} />
              <Row flex={1}>
                <Row
                  justifyContent="flex-start"
                  borderRadius={theme.borderRadius['x-small']}
                  overflow="hidden"
                  backgroundColor={theme.colors.background}>
                  {lifeStageIllustrations.map((Stage, idx, arr) => (
                    <Box key={idx} flexShrink={1} ml={idx === 0 ? 0 : 1}>
                      <Stage mode={arr.length === 2 ? 'half' : 'one-third'} />
                      <Box
                        borderRadius={theme.borderRadius['x-small']}
                        pos="absolute"
                        overflow="hidden"
                        bottom={theme.space[2]}
                        left={theme.space[2]}
                        py={1}
                        px={3}>
                        <Box
                          style={StyleSheet.absoluteFill}
                          backgroundColor={'rgba(255,255,255,0.9)'}
                        />
                        <SmallBody
                          fontWeight="medium"
                          color={theme.colors.primary}>
                          {idx === 0
                            ? t('fna:currentStage')
                            : t('fna:futureStage')}
                        </SmallBody>
                      </Box>
                    </Box>
                  ))}
                </Row>
                <Box flex={1} />
              </Row>
            </Row>
          )}
        </Box>
        <SvgContainer
          theme={theme}
          height={containerLayout.height}
          width={containerLayout.width}>
          <Path
            d={
              isLifeJourneyCompleted
                ? `M.742.64 H${containerLayout.width} v${
                    containerLayout.height
                  } H${
                    containerLayout.width / 2
                  } a50 50 0 0 1 -45.679 -29.668 l -10.089 -37.665 a49.999 49.999 0 0 0 -45.679 -29.667 H80.742 c-44.183 0-80-35.818-80-80v-155z`
                : `M0 0 h${containerLayout.width} v${containerLayout.height} H80.563 c-44.183 0-80-35.817-80-80v-257z`
            }
            fill="#fff"
          />
        </SvgContainer>
      </View>
    </DropShadowContainer>
  );
}

const DropShadowContainer = styled(DropShadow)(({ theme }) => {
  return {
    ...theme.getElevation(3),
  };
});

const SvgContainer = styled(Svg)(({ theme }) => {
  return {
    ...StyleSheet.absoluteFillObject,
    zIndex: -1,
    marginTop: -1,
  };
});
