import LifeStageSelection from 'features/fna/components/lifeStage/LifeStageSelection';
import DateOfBirth from '../../../formInput/dateOfBirth/DateOfBirth';
import FinancialBudget from '../../../formInput/financialBudget/FinancialBudget';
import Gender from '../../../formInput/gender/Gender';
import Income from '../../../formInput/income/Income';
import Name from '../../../formInput/name/Name';
import AdditionalQuestion from '../../../formInput/additionalQuestion/AdditionalQuestion';

export const tabletFormConfig = {
  rowOne: [Gender, Name, DateOfBirth, Income, FinancialBudget],
  rowTwo: [LifeStageSelection, AdditionalQuestion],
  rowThree: [],
};

export const tabletSummaryConfig = {
  rowOne: [Income, FinancialBudget, LifeStageSelection, AdditionalQuestion],
  rowTwo: [],
};
