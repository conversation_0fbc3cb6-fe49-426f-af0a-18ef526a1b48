import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import {
  checkEducationCompletion,
  checkHealthProtectionCompletion,
  checkIncomeProtectionCompletion,
  checkInvestmentCompletion,
  checkLegacyPlanningCompletion,
  checkLoanCoverageCompletion,
  checkRetirementCompletion,
  checkSavingsCompletion,
} from 'features/fna/utils/helper/checkGoalCompletion';
import { calculateAge } from 'utils/helper/calculateAge';
import { MAX_AGE_RETIREMENT } from 'features/fna/constants/goalCalculation';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import GoalChartPhone from './GoalChart.phone';
import GoalChartTablet from './GoalChart.tablet';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { country } from 'utils/context';
import { countryModuleFnaConfig } from 'utils/config/module';
import MoneyBagIncrementIcon from 'features/fna/components/icons/MoneyBagIncrementIcon';
import MoneyBagIncrementInactiveIcon from 'features/fna/components/icons/MoneyBagIncrementInactiveIcon';
import HumanBrainIcon from 'features/fna/components/icons/HumanBrainIcon';
import HumanBrainInactiveIcon from 'features/fna/components/icons/HumanBrainInactiveIcon';
import FamilyHeartInactiveIcon from 'features/fna/components/icons/FamilyHeartInactiveIcon';
import FamilyHeartIcon from 'features/fna/components/icons/FamilyHeartIcon';
import FinancialGrowthInactiveIcon from 'features/fna/components/icons/FinancialGrowthInactiveIcon';
import FinancialGrowthIcon from 'features/fna/components/icons/FinancialGrowthIcon';
import MoneyBagInactiveIcon from 'features/fna/components/icons/MoneyBagInactiveIcon';
import MoneyBagIcon from 'features/fna/components/icons/MoneyBagIcon';
import MedicineInactiveIcon from 'features/fna/components/icons/MedicineInactiveIcon';
import MedicineIcon from 'features/fna/components/icons/MedicineIcon';
import NuturingFinanceInactiveIcon from 'features/fna/components/icons/NuturingFinanceInactiveIcon';
import NuturingFinanceIcon from 'features/fna/components/icons/NuturingFinanceIcon';
import MoneyUnderUmbrellaInactiveIcon from 'features/fna/components/icons/MoneyUnderUmbrellaInactiveIcon';
import MoneyUnderUmbrellaIcon from 'features/fna/components/icons/MoneyUnderUmbrellaIcon';
import ShieldIcon from 'features/fna/components/icons/ShieldIcon';
import { PictogramIcon } from 'cube-ui-components';
import { shallow } from 'zustand/shallow';

export type GoalType = 'SAVINGS' | 'PROTECTION';

export interface GoalChartData {
  id: string;
  title: string;
  isCompleted: boolean;
  enabled: boolean;
  mandatory: boolean;
  icon: JSX.Element;
  value: number | null;
  target: number | null;
  coverage: number | null;
  yearsToAchieve?: number | null;
  investmentDuration?: string;
}

export interface GoalChartConfig {
  title: string;
  icon: JSX.Element;
  navigationScreen: 'SavingsGoal' | 'ProtectionGoal';
  totalGapLabel: string;
  needsLabel: string;
  flexRatio: number;
  breakdownNeedType: 'SAVINGS' | 'PROTECTION';
}

export interface GoalChartProps {
  isFnaValid: boolean;
  data: GoalChartData[];
  hasIllustration: boolean;
  hasSecondaryIllustration: boolean;
  totalGap: number;
  config: GoalChartConfig;
  onShowSummary?: () => void;
}

export const GoalChart = ({
  isFnaValid,
  onShowSummary,
  goalType,
}: {
  isFnaValid: boolean;
  onShowSummary?: () => void;
  goalType: GoalType;
}) => {
  const { t } = useTranslation(['fna']);
  const { sizes } = useTheme();

  const {
    compulsoryFna,
    educationGoal,
    retirementGoal,
    investmentGoal,
    savingsGoal,
    incomeProtectionGoal,
    healthProtectionGoal,
    legacyPlanningGoal,
    loanCoverageGoal,
    lifeJourney: { dob },
    adviceType,
    concerns,
  } = useFnaStore(
    state => ({
      compulsoryFna: state.compulsoryFna,
      educationGoal: state.educationGoal,
      retirementGoal: state.retirementGoal,
      investmentGoal: state.investmentGoal,
      savingsGoal: state.savingsGoal,
      incomeProtectionGoal: state.incomeProtectionGoal,
      healthProtectionGoal: state.healthProtectionGoal,
      legacyPlanningGoal: state.legacyPlanningGoal,
      loanCoverageGoal: state.loanCoverageGoal,
      lifeJourney: state.lifeJourney,
      adviceType: state.adviceType,
      concerns: state.lifeJourney.concerns,
    }),
    shallow,
  );

  const { isTabletMode } = useLayoutAdoptionCheck();

  const { data, config } = useMemo(() => {
    const concernsList = Array.from(
      new Set([
        ...countryModuleFnaConfig.concerns.items,
        ...countryModuleFnaConfig.concerns.additionalItems,
      ]),
    );

    let goalData: GoalChartData[] = [];
    let chartConfig: GoalChartConfig;

    if (goalType === 'SAVINGS') {
      chartConfig = {
        title: t('fna:saving'),
        icon: <PictogramIcon.Cash2 size={sizes[9]} />,
        navigationScreen: 'SavingsGoal',
        totalGapLabel: t('fna:savingsGoals.totalSavingNeeds'),
        needsLabel: t('fna:savingNeeds'),
        flexRatio: countryModuleFnaConfig.goalGroupUIRatio.saving,
        breakdownNeedType: 'SAVINGS',
      };

      goalData = concernsList
        .map(concern => {
          switch (concern) {
            case 'EDUCATION':
              return {
                id: 'EDUCATION',
                title: isTabletMode
                  ? t('fna:lifeStage.concern.EDUCATION.tablet')
                  : t('fna:lifeStage.concern.EDUCATION.mobile'),
                isCompleted: checkEducationCompletion(
                  educationGoal,
                  adviceType,
                  concerns,
                ),
                enabled: educationGoal.enabled,
                mandatory: compulsoryFna.includes('EDUCATION'),
                icon: checkEducationCompletion(
                  educationGoal,
                  adviceType,
                  concerns,
                ) ? (
                  <HumanBrainIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ) : (
                  <HumanBrainInactiveIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ),
                value: educationGoal.gapAmount,
                target: educationGoal.targetAmount,
                coverage: educationGoal.coverageAmount,
                yearsToAchieve: educationGoal.yearsToAchieve,
              };
            case 'RETIREMENT':
              return {
                id: 'RETIREMENT',
                title: isTabletMode
                  ? t('fna:lifeStage.concern.RETIREMENT.tablet')
                  : t('fna:lifeStage.concern.RETIREMENT.mobile'),
                isCompleted: checkRetirementCompletion(retirementGoal),
                enabled: retirementGoal.enabled,
                mandatory: compulsoryFna.includes('RETIREMENT'),
                icon: checkRetirementCompletion(retirementGoal) ? (
                  <FamilyHeartIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ) : (
                  <FamilyHeartInactiveIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ),
                value: retirementGoal.gapAmount,
                target: retirementGoal.targetAmount,
                coverage: retirementGoal.coverageAmount,
                yearsToAchieve: retirementGoal.yearsToAchieve,
              };
            case 'INVESTMENT':
              return {
                id: 'INVESTMENT',
                title: isTabletMode
                  ? t('fna:lifeStage.concern.INVESTMENT.tablet')
                  : t('fna:lifeStage.concern.INVESTMENT.mobile'),
                isCompleted: checkInvestmentCompletion(investmentGoal),
                enabled: investmentGoal.enabled,
                mandatory: compulsoryFna.includes('INVESTMENT'),
                icon: checkInvestmentCompletion(investmentGoal) ? (
                  <FinancialGrowthIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ) : (
                  <FinancialGrowthInactiveIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ),
                value: investmentGoal.gapAmount,
                target: investmentGoal.targetAmount,
                coverage: investmentGoal.coverageAmount,
                yearsToAchieve: investmentGoal.yearsToAchieve,
                investmentDuration: investmentGoal.investmentDuration,
              };
            case 'SAVINGS':
              return {
                id: 'SAVINGS',
                title: isTabletMode
                  ? t('fna:lifeStage.concern.SAVINGS.tablet')
                  : t('fna:lifeStage.concern.SAVINGS.mobile'),
                isCompleted: checkSavingsCompletion(savingsGoal),
                enabled: savingsGoal.enabled,
                mandatory: compulsoryFna.includes('SAVINGS'),
                icon: checkSavingsCompletion(savingsGoal) ? (
                  <MoneyBagIncrementIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ) : (
                  <MoneyBagIncrementInactiveIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ),
                value: savingsGoal.gapAmount,
                target: savingsGoal.targetAmount,
                coverage: savingsGoal.coverageAmount,
                yearsToAchieve: savingsGoal.yearsToAchieve,
              };
          }
        })
        .filter(Boolean) as GoalChartData[];

      // Remove retirement goal for Philippines if user is over retirement age
      if (country === 'ph') {
        if (dob && calculateAge(dob) >= MAX_AGE_RETIREMENT) {
          goalData.splice(
            goalData.findIndex(tab => tab.id === 'RETIREMENT'),
            1,
          );
        }
      }
    } else {
      // PROTECTION
      chartConfig = {
        title: t('fna:protect'),
        icon: <ShieldIcon width={sizes[9]} height={sizes[9]} />,
        navigationScreen: 'ProtectionGoal',
        totalGapLabel: t('fna:protectionGoals.totalCoverNeeds'),
        needsLabel: t('fna:coverNeeds'),
        flexRatio: countryModuleFnaConfig.goalGroupUIRatio.protection,
        breakdownNeedType: 'PROTECTION',
      };

      goalData = concernsList
        .map(concern => {
          switch (concern) {
            case 'INCOME_PROTECTION':
              return {
                id: 'INCOME_PROTECTION',
                title: isTabletMode
                  ? t('fna:lifeStage.concern.INCOME_PROTECTION.tablet')
                  : t('fna:lifeStage.concern.INCOME_PROTECTION.mobile'),
                isCompleted:
                  checkIncomeProtectionCompletion(incomeProtectionGoal),
                enabled: incomeProtectionGoal.enabled,
                mandatory: compulsoryFna.includes('INCOME_PROTECTION'),
                icon: checkIncomeProtectionCompletion(incomeProtectionGoal) ? (
                  <MoneyBagIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ) : (
                  <MoneyBagInactiveIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ),
                value: incomeProtectionGoal.gapAmount,
                target: incomeProtectionGoal.targetAmount,
                coverage: incomeProtectionGoal.coverageAmount,
              };
            case 'HEALTH_PROTECTION':
              return {
                id: 'HEALTH_PROTECTION',
                title: isTabletMode
                  ? t('fna:lifeStage.concern.HEALTH_PROTECTION.tablet')
                  : t('fna:lifeStage.concern.HEALTH_PROTECTION.mobile'),
                isCompleted:
                  checkHealthProtectionCompletion(healthProtectionGoal),
                enabled: healthProtectionGoal.enabled,
                mandatory: compulsoryFna.includes('HEALTH_PROTECTION'),
                icon: checkHealthProtectionCompletion(healthProtectionGoal) ? (
                  <MedicineIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ) : (
                  <MedicineInactiveIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ),
                value: healthProtectionGoal.gapAmount,
                target: healthProtectionGoal.targetAmount,
                coverage: healthProtectionGoal.coverageAmount,
              };
            case 'LEGACY_PLANNING':
              return {
                id: 'LEGACY_PLANNING',
                title: isTabletMode
                  ? t('fna:lifeStage.concern.LEGACY_PLANNING.tablet')
                  : t('fna:lifeStage.concern.LEGACY_PLANNING.mobile'),
                isCompleted: checkLegacyPlanningCompletion(legacyPlanningGoal),
                enabled: legacyPlanningGoal.enabled,
                mandatory: compulsoryFna.includes('LEGACY_PLANNING'),
                icon: checkLegacyPlanningCompletion(legacyPlanningGoal) ? (
                  <NuturingFinanceIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ) : (
                  <NuturingFinanceInactiveIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ),
                value: legacyPlanningGoal.gapAmount,
                target: legacyPlanningGoal.targetAmount,
                coverage: legacyPlanningGoal.coverageAmount,
              };
            case 'LOAN_PROTECTION':
              return {
                id: 'LOAN_PROTECTION',
                title: isTabletMode
                  ? t('fna:lifeStage.concern.LOAN_PROTECTION.tablet')
                  : t('fna:lifeStage.concern.LOAN_PROTECTION.mobile'),
                isCompleted: checkLoanCoverageCompletion(loanCoverageGoal),
                enabled: loanCoverageGoal.enabled,
                mandatory: compulsoryFna.includes('LOAN_PROTECTION'),
                icon: checkLoanCoverageCompletion(loanCoverageGoal) ? (
                  <MoneyUnderUmbrellaIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ) : (
                  <MoneyUnderUmbrellaInactiveIcon
                    width={isTabletMode ? 58 : sizes[10]}
                    height={isTabletMode ? 58 : sizes[10]}
                  />
                ),
                value: loanCoverageGoal.targetAmount,
                target: loanCoverageGoal.targetAmount,
                coverage: loanCoverageGoal.coverageAmount,
              };
          }
        })
        .filter(Boolean) as GoalChartData[];
    }

    return { data: goalData, config: chartConfig };
  }, [
    goalType,
    isTabletMode,
    t,
    sizes,
    educationGoal,
    adviceType,
    concerns,
    compulsoryFna,
    retirementGoal,
    investmentGoal,
    savingsGoal,
    dob,
    incomeProtectionGoal,
    healthProtectionGoal,
    legacyPlanningGoal,
    loanCoverageGoal,
  ]);

  const hasIllustration = useMemo(() => {
    if (goalType === 'SAVINGS') {
      return (
        (checkEducationCompletion(educationGoal, adviceType, concerns) &&
          educationGoal.gapAmount !== null) ||
        (checkRetirementCompletion(retirementGoal) &&
          retirementGoal.gapAmount !== null) ||
        (checkInvestmentCompletion(investmentGoal) &&
          investmentGoal.gapAmount !== null) ||
        (checkSavingsCompletion(savingsGoal) && savingsGoal.gapAmount !== null)
      );
    } else {
      return (
        (checkIncomeProtectionCompletion(incomeProtectionGoal) &&
          incomeProtectionGoal.gapAmount !== null) ||
        (checkHealthProtectionCompletion(healthProtectionGoal) &&
          healthProtectionGoal.gapAmount !== null) ||
        (checkLegacyPlanningCompletion(legacyPlanningGoal) &&
          legacyPlanningGoal.gapAmount !== null) ||
        (checkLoanCoverageCompletion(loanCoverageGoal) &&
          loanCoverageGoal.targetAmount !== null)
      );
    }
  }, [
    goalType,
    educationGoal,
    adviceType,
    concerns,
    retirementGoal,
    investmentGoal,
    savingsGoal,
    incomeProtectionGoal,
    healthProtectionGoal,
    legacyPlanningGoal,
    loanCoverageGoal,
  ]);

  const hasSecondaryIllustration = useMemo(() => {
    if (goalType === 'SAVINGS') {
      return (
        checkIncomeProtectionCompletion(incomeProtectionGoal) ||
        checkHealthProtectionCompletion(healthProtectionGoal) ||
        checkLegacyPlanningCompletion(legacyPlanningGoal) ||
        checkLoanCoverageCompletion(loanCoverageGoal)
      );
    } else {
      return (
        checkEducationCompletion(educationGoal, adviceType, concerns) ||
        checkRetirementCompletion(retirementGoal) ||
        checkInvestmentCompletion(investmentGoal)
      );
    }
  }, [
    goalType,
    incomeProtectionGoal,
    healthProtectionGoal,
    legacyPlanningGoal,
    loanCoverageGoal,
    educationGoal,
    adviceType,
    concerns,
    retirementGoal,
    investmentGoal,
  ]);

  const totalGap = useMemo(() => {
    if (goalType === 'SAVINGS') {
      return (
        (retirementGoal.gapAmount || 0) +
        (educationGoal.gapAmount || 0) +
        (savingsGoal.gapAmount || 0) +
        (investmentGoal.gapAmount || 0)
      );
    } else {
      return (
        (incomeProtectionGoal.gapAmount || 0) +
        (healthProtectionGoal.gapAmount || 0) +
        (legacyPlanningGoal.gapAmount || 0) +
        (loanCoverageGoal.gapAmount || 0)
      );
    }
  }, [
    goalType,
    retirementGoal.gapAmount,
    educationGoal.gapAmount,
    savingsGoal.gapAmount,
    investmentGoal.gapAmount,
    incomeProtectionGoal.gapAmount,
    healthProtectionGoal.gapAmount,
    legacyPlanningGoal.gapAmount,
    loanCoverageGoal.gapAmount,
  ]);

  return (
    <DeviceBasedRendering
      phone={
        <GoalChartPhone
          isFnaValid={isFnaValid}
          data={data}
          hasIllustration={hasIllustration}
          hasSecondaryIllustration={hasSecondaryIllustration}
          totalGap={totalGap}
          config={config}
        />
      }
      tablet={
        <GoalChartTablet
          isFnaValid={isFnaValid}
          data={data}
          hasIllustration={hasIllustration}
          hasSecondaryIllustration={hasSecondaryIllustration}
          totalGap={totalGap}
          config={config}
          onShowSummary={onShowSummary}
        />
      }
    />
  );
};
