import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useTheme } from '@emotion/react';
import { Box, Column, Row } from 'cube-ui-components';
import React, { useEffect, useMemo, useState } from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { G, Mask, Path, Rect, Svg, SvgProps } from 'react-native-svg';
import PolygonIcon from '../../icons/PolygonIcon';
import { View } from 'react-native';

const AnimatedSvg = Animated.createAnimatedComponent(Svg);

interface Props {
  hasTotalLegion?: boolean;
  hasCoverageLine?: boolean;
  current: number;
  total: number;
}

export default function GoalProgress({
  hasTotalLegion,
  hasCoverageLine,
  current,
  total,
}: Props) {
  const { borderRadius, colors, space, sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const [barMaxWidth, setBarMaxWidth] = useState(0);
  const coverageWidth = useSharedValue(0);
  const dashSpacing = space[1];

  const percentage = useMemo(() => {
    if (!current || !total) return 0;
    return current / total;
  }, [current, total]);

  useEffect(() => {
    coverageWidth.value = withTiming(barMaxWidth * percentage, {
      duration: 500,
    });
  }, [barMaxWidth, coverageWidth, percentage]);

  const yDashes = new Array(Math.floor(space[4] / dashSpacing)).fill(null);

  const coverageDash = useMemo(() => {
    return new Array(Math.round((barMaxWidth * percentage) / dashSpacing)).fill(
      null,
    );
  }, [barMaxWidth, dashSpacing, percentage]);

  const remainingGapDash = useMemo(() => {
    return new Array(Math.floor(barMaxWidth / dashSpacing)).fill(null);
  }, [barMaxWidth, dashSpacing]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: coverageWidth.value,
      height: '100%',
      backgroundColor: colors.palette.fwdLightGreen[100],
      borderRadius: isTabletMode ? borderRadius.full : borderRadius['small'],
      borderWidth: coverageWidth.value === 0 ? 0 : 1,
      borderLeftWidth: 0,
      borderColor: colors.background,
    };
  });

  const currentAssetAnimatedStyle = useAnimatedStyle(() => {
    return {
      width: coverageWidth.value,
    };
  });

  const remainingAnimatedStyle = useAnimatedStyle(() => {
    return {
      width: barMaxWidth - coverageWidth.value - space[1],
    };
  });

  return (
    <Column
      alignSelf="stretch"
      overflow="hidden"
      pt={space[hasTotalLegion ? 1 : 0]}>
      {hasTotalLegion && (
        <Box pos="absolute" right={1}>
          <PolygonIcon />
        </Box>
      )}
      <Row mb={space[1]} alignItems="center" justifyContent="flex-start">
        {hasCoverageLine && (
          <AnimatedSvg height={5} style={currentAssetAnimatedStyle}>
            {yDashes.map((_, index) => (
              <Rect
                key={index}
                x={0}
                y={0}
                width={dashSpacing / 4}
                height={dashSpacing / 2}
                fill={colors.palette.fwdLightGreen[100]}
                translateY={dashSpacing * index}
              />
            ))}
            {coverageDash.map((_, index) => (
              <Rect
                key={index}
                x={0}
                y={0}
                width={dashSpacing / 2}
                height={dashSpacing / 4}
                fill={colors.palette.fwdLightGreen[100]}
                translateX={dashSpacing * index}
              />
            ))}
            {yDashes.map((_, index) => (
              <Rect
                key={index}
                x={barMaxWidth * percentage - space[1]}
                y={0}
                width={dashSpacing / 4}
                height={dashSpacing / 2}
                fill={colors.palette.fwdLightGreen[100]}
                translateY={dashSpacing * index}
              />
            ))}
          </AnimatedSvg>
        )}
      </Row>
      <View
        onLayout={e => {
          setBarMaxWidth(e.nativeEvent.layout.width);
        }}>
        <DashedBackgroundView height={sizes[3] + 2} width={barMaxWidth}>
          <Animated.View style={animatedStyle} />
        </DashedBackgroundView>
      </View>
      <Row mt={space[1]} alignItems="center" justifyContent="flex-end">
        <AnimatedSvg height={5} style={remainingAnimatedStyle}>
          <G>
            {yDashes.map((_, index) => (
              <Rect
                key={index}
                x={0}
                y={0}
                width={dashSpacing / 4}
                height={dashSpacing / 2}
                fill={colors.primary}
                translateY={dashSpacing * index}
              />
            ))}
            {remainingGapDash.map((_, index) => (
              <Rect
                key={index}
                x={0}
                y={4}
                width={dashSpacing / 2}
                height={dashSpacing / 4}
                fill={colors.primary}
                translateX={dashSpacing * index}
              />
            ))}
          </G>
        </AnimatedSvg>
        <Svg height={5} width={sizes[1] / 2}>
          <G>
            {yDashes.map((_, index) => (
              <Rect
                key={index}
                x={0}
                y={0}
                width={dashSpacing / 4}
                height={dashSpacing / 2}
                fill={colors.primary}
                translateY={dashSpacing * index}
              />
            ))}
          </G>
        </Svg>
      </Row>
    </Column>
  );
}

const DashedBackgroundView = ({
  width,
  height,
  children,
  ...props
}: { height: number; width: number } & SvgProps) => {
  return (
    <Svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height - 2}`}
      fill="none"
      {...props}>
      <Rect
        x={0.320312}
        y={0.160156}
        width={width}
        height={height - 2}
        rx={height / 2}
        fill="#E87722"
      />
      <G opacity={0.4}>
        <Mask
          id="a"
          maskUnits="userSpaceOnUse"
          x={0}
          y={0}
          width={width}
          height={height - 2}>
          <Rect
            x={0.320312}
            y={0.160156}
            width={width}
            height={height - 2}
            rx={height / 2}
            fill="#E87722"
          />
        </Mask>
        <G mask="url(#a)" stroke="#fff" strokeWidth={1.5}>
          <Path d="M5.963-46.406L-118.145 77.7M13.07-46.406L-111.037 77.7M20.178-46.406L-103.93 77.7M27.287-46.406L-96.82 77.7M34.395-46.406L-89.714 77.7M41.502-46.406L-82.606 77.7M48.61-46.406L-75.499 77.7M55.717-46.406L-68.391 77.7M62.826-46.406L-61.282 77.7M69.934-46.406L-54.174 77.7M77.041-46.406L-47.067 77.7M84.148-46.406L-39.959 77.7M91.256-46.406L-32.852 77.7M98.365-46.406L-25.742 77.7M105.473-46.406L-18.635 77.7M112.58-46.406L-11.528 77.7M119.688-46.406L-4.42 77.7M126.796-46.406L2.688 77.7M133.903-46.406L9.796 77.7M141.011-46.406L16.903 77.7M148.119-46.406L24.011 77.7M155.227-46.406L31.119 77.7M162.334-46.406L38.226 77.7M169.442-46.406L45.335 77.7M176.55-46.406L52.442 77.7M183.658-46.406L59.55 77.7M190.766-46.406L66.658 77.7M197.873-46.406L73.765 77.7M204.981-46.406L80.874 77.7M212.089-46.406L87.981 77.7M219.196-46.406L95.088 77.7M226.305-46.406L102.197 77.7M233.412-46.406L109.304 77.7M240.521-46.406L116.413 77.7M247.628-46.406L123.52 77.7M254.735-46.406L130.628 77.7M261.844-46.406L137.736 77.7M268.951-46.406L144.843 77.7M276.059-46.406L151.951 77.7M283.167-46.406L159.059 77.7M290.274-46.406L166.167 77.7M297.383-46.406L173.275 77.7M304.49-46.406L180.382 77.7M311.598-46.406L187.49 77.7M318.706-46.406L194.598 77.7M325.813-46.406L201.706 77.7M332.921-46.406L208.813 77.7M340.029-46.406L215.922 77.7M347.137-46.406L223.029 77.7M354.245-46.406L230.137 77.7M361.353-46.406L237.245 77.7M368.46-46.406L244.352 77.7M375.568-46.406L251.461 77.7M382.676-46.406L258.568 77.7M389.783-46.406L265.675 77.7M396.892-46.406L272.784 77.7M403.999-46.406L279.891 77.7M411.107-46.406L287 77.7M418.215-46.406L294.107 77.7" />
        </G>
      </G>
      {children}
    </Svg>
  );
};
