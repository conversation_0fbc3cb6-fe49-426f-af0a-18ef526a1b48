import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DialogPhone from 'components/Dialog.phone';
import {
  Body,
  Box,
  ExtraSmallLabel,
  Fonts,
  H5,
  H6,
  Icon,
  Label,
  LargeLabel,
  PictogramIcon,
  Row,
  SmallBody,
  SmallLabel,
  SvgIconProps,
} from 'cube-ui-components';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, useWindowDimensions } from 'react-native';
import { Pressable } from 'react-native';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import useToggle from 'hooks/useToggle';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import {
  currencyKFormat,
  getDisplayInYear,
  getPriorityColor,
} from 'features/fna/utils/helper/fnaUtils';
import {
  GoalBreakdownSummaryItem,
  InternalGoalBreakdownProps,
} from './GoalBreakdown';
import GoalProgress from '../GoalProgress';
import { NumberSequence } from 'types';
import { ConcernId } from 'features/fna/types/concern';
import ShieldIcon from 'features/fna/components/icons/ShieldIcon';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { countryModuleFnaConfig } from 'utils/config/module';
import PolygonIcon from 'features/fna/components/icons/PolygonIcon';
import { Svg, Rect } from 'react-native-svg';
import { country } from 'utils/context';

export default function GoalBreakdownPhone({
  savings,
  protection,
  needType,
  hasTooltip,
  fromLeadProfile = false,
  concerns = [],
}: InternalGoalBreakdownProps) {
  const { t } = useTranslation(['fna']);
  const { colors, space, sizes } = useTheme();
  const { height } = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const [tooltipVisible, showTooltip, hideTooltip] = useToggle();

  let summary: {
    totalTarget: number;
    totalCoverage: number;
    totalGap: number;
  } | null = null;
  let goals: GoalBreakdownSummaryItem[] | null = null;
  switch (needType) {
    case 'SAVINGS':
      goals = savings.items;
      summary = savings;
      break;
    case 'PROTECTION':
      goals = protection.items;
      summary = protection;
      break;
  }

  const IS_ID = country === 'id';

  const LabelText = IS_ID ? SmallLabelMediumFont : Label;

  return (
    <>
      <Row alignItems="center" mb={space[IS_ID ? 2 : 4]}>
        {needType === 'PROTECTION' ? (
          <ShieldIcon width={sizes[9]} height={sizes[9]} />
        ) : (
          <PictogramIcon.Cash size={sizes[10]} />
        )}
        <Box ml={space[1]}>
          <H6 fontWeight="bold">
            {t(needType === 'PROTECTION' ? 'fna:protect' : 'fna:saving')}
          </H6>
        </Box>
      </Row>
      <ScrollView
        style={
          !fromLeadProfile && {
            maxHeight: height - space[55] - insets.top,
            paddingRight: space[isNarrowScreen ? 3 : 4],
          }
        }>
        <Box
          padding={space[3]}
          borderRadius={space[4]}
          bgColor={colors.palette.fwdOrange[5]}>
          <Row alignItems="center" justifyContent="space-between">
            <LabelText>{t('fna:savingsGoals.retirement.totalNeeds')}</LabelText>
            <Row>
              <SmallBody color={colors.palette.fwdBlue[100]}>
                {t('fna:currency')}
              </SmallBody>
              <H6
                fontWeight="bold"
                color={colors.palette.fwdBlue[100]}
                style={{ marginLeft: space[1] }}>
                {currencyKFormat(summary?.totalTarget || 0)}
              </H6>
            </Row>
          </Row>
          <Row mt={space[2]} alignItems="center" justifyContent="space-between">
            <LabelText>
              {needType === 'SAVINGS'
                ? t('fna:savingsGoals.totalCurrentAssets')
                : t('fna:protectionGoals.totalCurrentCoverage')}
            </LabelText>
            <Row>
              <SmallBody color={colors.palette.fwdDarkGreen[100]}>
                {t('fna:currency')}
              </SmallBody>
              <H6
                fontWeight="bold"
                color={colors.palette.fwdDarkGreen[100]}
                style={{ marginLeft: space[1] }}>
                {currencyKFormat(summary?.totalCoverage || 0)}
              </H6>
            </Row>
          </Row>
          <Row h={1} my={space[2]} bgColor={colors.palette.fwdGrey[100]} />
          <Row mt={space[2]} alignItems="center" justifyContent="space-between">
            <Row alignItems="center" flex={1} pr={space[2]}>
              <LabelText style={{ flexShrink: 1 }}>
                {t('fna:savingsGoals.retirement.totalGap')}
              </LabelText>
              {hasTooltip && IS_ID && (
                <Box ml={space[1]}>
                  <Pressable hitSlop={ICON_HIT_SLOP} onPress={showTooltip}>
                    <Icon.InfoCircle
                      size={sizes[5]}
                      fill={colors.palette.fwdAlternativeOrange[100]}
                    />
                  </Pressable>
                </Box>
              )}
            </Row>
            <Row alignItems="center">
              {hasTooltip && !IS_ID && (
                <Box mr={space[4]}>
                  <Pressable hitSlop={ICON_HIT_SLOP} onPress={showTooltip}>
                    <Icon.InfoCircle
                      fill={colors.palette.fwdAlternativeOrange[100]}
                    />
                  </Pressable>
                </Box>
              )}
              <Row>
                <Body color={colors.palette.fwdAlternativeOrange[100]}>
                  {t('fna:currency')}
                </Body>
                <H5
                  fontWeight="bold"
                  color={colors.palette.fwdAlternativeOrange[100]}
                  style={{ marginLeft: space[1] }}>
                  {currencyKFormat(summary?.totalGap || 0)}
                </H5>
              </Row>
            </Row>
          </Row>
        </Box>
        {goals?.some(goal => goal.isCompleted) && (
          <Box mt={space[4]}>
            <SmallBody color={colors.palette.fwdGreyDarkest}>
              {t('fna:breakdown')}
            </SmallBody>
          </Box>
        )}
        <>
          {goals
            ?.filter(goal => goal.isCompleted)
            .map(goal => {
              return (
                <GoalItem
                  key={goal.id}
                  goal={goal}
                  needType={needType}
                  priority={concerns.indexOf(goal.id as ConcernId)}
                />
              );
            })}
        </>
      </ScrollView>
      {hasTooltip && (
        <DialogPhone visible={tooltipVisible}>
          <Box>
            <CloseButtonDialog onPress={hideTooltip}>
              <Icon.Close fill={colors.onBackground} />
            </CloseButtonDialog>
            <H6 fontWeight="bold">{t('fna:inflationRate')}</H6>
            <Box mt={space[4]}>
              <LargeLabel>{t('fna:inflationRate.desc')}</LargeLabel>
            </Box>
          </Box>
        </DialogPhone>
      )}
    </>
  );
}

const CloseButtonDialog = styled.TouchableOpacity(({ theme }) => ({
  flexDirection: 'row',
  justifyContent: 'flex-end',
  paddingRight: theme.space[2],
}));

interface GoalItemProps {
  needType?: 'SAVINGS' | 'PROTECTION';
  goal: GoalBreakdownSummaryItem;
  priority: number;
}

const GoalItem = ({ priority, goal, needType }: GoalItemProps) => {
  const { t } = useTranslation(['fna']);
  const { colors, space } = useTheme();
  const priorityColor = useMemo(() => getPriorityColor(colors), [colors]);

  return (
    <Box mt={space[4]}>
      <Row alignItems="center">
        {goal.icon}
        <Box flex={1} ml={space[2]}>
          <Row justifyContent="space-between" alignItems="baseline">
            <LargeLabel fontWeight="bold">{goal.title}</LargeLabel>
            {goal.isCompleted && typeof goal.yearsToAchieve === 'number' && (
              <SmallBody color={colors.palette.fwdGreyDarkest}>
                ({getDisplayInYear(goal, t)})
              </SmallBody>
            )}
          </Row>
          {priority >= 0 ? (
            <Box
              mt={space[1]}
              alignSelf="flex-start"
              bgColor={priorityColor[priority as NumberSequence<0, 2>]}
              borderRadius={space[3]}
              paddingX={space[2]}
              paddingY={space[1] / 2}>
              <ExtraSmallLabel>
                {t(`fna:lifeStage.concern.priority.${priority as 0 | 1 | 2}`)}
              </ExtraSmallLabel>
            </Box>
          ) : null}
          <Row mt={space[1]} alignItems="center">
            <PolygonIcon />
            <Row ml={space[1]}>
              <SmallBody color={colors.palette.fwdBlue[100]}>
                {t('fna:goals.totalNeedsTarget')}
              </SmallBody>
              <SmallBody fontWeight="bold" color={colors.palette.fwdBlue[100]}>
                {t('fna:currency')} {currencyKFormat(goal?.target || 0)}
              </SmallBody>
            </Row>
          </Row>
        </Box>
      </Row>
      {(!countryModuleFnaConfig.shouldHideProgressBarFor0Needs ||
        Boolean(goal?.target && goal?.target > 0)) && (
        <>
          <Box mb={space[1]} mt={space[2]}>
            <Row alignItems="center" gap={space[1]}>
              <Circle fill={colors.palette.fwdLightGreen[100]} />
              <SmallBody>
                {needType === 'SAVINGS'
                  ? t('fna:goals.currentAssets')
                  : t('fna:goals.currentCoverage')}
                {t('fna:currency')} {currencyKFormat(goal?.coverage || 0)}
              </SmallBody>
            </Row>
          </Box>
          <GoalProgress
            hasTotalLegion
            hasCoverageLine
            total={goal?.target || 0}
            current={goal?.coverage || 0}
          />
          <Row mt={space[1]} alignItems="center" justifyContent="flex-end">
            <SmallBody color={colors.palette.fwdAlternativeOrange[100]}>
              {t('fna:goals.gapToTarget')}
            </SmallBody>
            <Row>
              <SmallBody
                fontWeight="bold"
                color={colors.palette.fwdAlternativeOrange[100]}>
                {t('fna:currency')}{' '}
              </SmallBody>
              <SmallBody
                fontWeight="bold"
                color={colors.palette.fwdAlternativeOrange[100]}>
                {currencyKFormat(goal?.gap || 0)}
              </SmallBody>
            </Row>
          </Row>
        </>
      )}
    </Box>
  );
};

const Circle = (props?: SvgIconProps) => {
  const { colors } = useTheme();
  return (
    <Svg width={12} height={12} viewBox="0 0 12 12" fill="none" {...props}>
      <Rect
        y={0}
        width={12}
        height={12}
        rx={6}
        fill={props?.fill || colors.primary}
      />
    </Svg>
  );
};

const SmallLabelMediumFont = styled(SmallLabel)(() => ({
  fontFamily: Fonts.FWDCircularTT.Medium,
}));
