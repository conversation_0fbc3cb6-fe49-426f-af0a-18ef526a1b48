import React, { useMemo } from 'react';
import { shallow } from 'zustand/shallow';

import { AdviceType, FnaState } from 'features/fna/utils/store/fnaStore';
import { Fna } from 'types/case';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import GoalBreakdownPhone from './GoalBreakdown.phone';
import { ConcernId } from 'features/fna/types/concern';
import GoalBreakdownTablet from './GoalBreakdown.tablet';
import { useTranslation } from 'react-i18next';
import { countryModuleFnaConfig } from 'utils/config/module';
import { country } from 'utils/context';
import {
  checkEducationCompletion,
  checkHealthProtectionCompletion,
  checkIncomeProtectionCompletion,
  checkInvestmentCompletion,
  checkLegacyPlanningCompletion,
  checkLoanCoverageCompletion,
  checkRetirementCompletion,
  checkSavingsCompletion,
} from 'features/fna/utils/helper/checkGoalCompletion';
import HumanBrainIcon from 'features/fna/components/icons/HumanBrainIcon';
import FamilyHeartIcon from 'features/fna/components/icons/FamilyHeartIcon';
import FinancialGrowthIcon from 'features/fna/components/icons/FinancialGrowthIcon';
import MoneyBagIncrementIcon from 'features/fna/components/icons/MoneyBagIncrementIcon';
import MoneyBagIcon from 'features/fna/components/icons/MoneyBagIcon';
import MedicineIcon from 'features/fna/components/icons/MedicineIcon';
import MoneyUnderUmbrellaIcon from 'features/fna/components/icons/MoneyUnderUmbrellaIcon';
import NuturingFinanceIcon from 'features/fna/components/icons/NuturingFinanceIcon';
import HumanBrainInactiveIcon from 'features/fna/components/icons/HumanBrainInactiveIcon';
import FamilyHeartInactiveIcon from 'features/fna/components/icons/FamilyHeartInactiveIcon';
import FinancialGrowthInactiveIcon from 'features/fna/components/icons/FinancialGrowthInactiveIcon';
import MoneyBagIncrementInactiveIcon from 'features/fna/components/icons/MoneyBagIncrementInactiveIcon';
import MoneyBagInactiveIcon from 'features/fna/components/icons/MoneyBagInactiveIcon';
import MedicineInactiveIcon from 'features/fna/components/icons/MedicineInactiveIcon';
import MoneyUnderUmbrellaInactiveIcon from 'features/fna/components/icons/MoneyUnderUmbrellaInactiveIcon';
import NuturingFinanceInactiveIcon from 'features/fna/components/icons/NuturingFinanceInactiveIcon';
import { calculateAge } from 'utils/helper/calculateAge';
import { MAX_AGE_RETIREMENT } from 'features/fna/constants/goalCalculation';
import { goalsByNeed } from 'features/fna/constants/lifeJourney';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

interface Props {
  needType?: 'SAVINGS' | 'PROTECTION';
  data: Pick<
    Fna & FnaState,
    | 'educationGoal'
    | 'retirementGoal'
    | 'investmentGoal'
    | 'savingsGoal'
    | 'incomeProtectionGoal'
    | 'healthProtectionGoal'
    | 'legacyPlanningGoal'
    | 'loanCoverageGoal'
  > & {
    name?: string;
    dob?: Date | null;
  };
  concerns: ConcernId[];
  adviceType: AdviceType;
  hasTooltip?: boolean;
  fromLeadProfile?: boolean;
  onDismiss?: () => void;
}

export interface GoalBreakdownSummaryItem {
  id: string;
  title: string;
  icon: JSX.Element;
  isCompleted?: boolean;
  enabled?: boolean | null;
  gap: number | null;
  target: number | null;
  coverage: number | null;
  yearsToAchieve?: number | null;
  investmentDuration?: string | null;
  priority?: number;
}

export type InternalGoalBreakdownProps = Props &
  Record<
    'savings' | 'protection',
    {
      totalTarget: number;
      totalCoverage: number;
      totalGap: number;
      items: GoalBreakdownSummaryItem[];
    }
  >;

export const GoalBreakdown = (props: Props) => {
  const { t } = useTranslation(['fna']);
  const data = props.data;
  const { isTabletMode } = useLayoutAdoptionCheck();
  const iconSize = isTabletMode ? 58 : 40;

  const { savings, protection } = useMemo(() => {
    const concerns = Array.from(
      new Set([
        ...countryModuleFnaConfig.concerns.items,
        ...countryModuleFnaConfig.concerns.additionalItems,
      ]),
    );
    const savingsItems: GoalBreakdownSummaryItem[] = [];
    const protectionItems: GoalBreakdownSummaryItem[] = [];

    concerns.forEach(concern => {
      switch (concern) {
        case 'EDUCATION':
          savingsItems.push({
            id: 'EDUCATION',
            title: isTabletMode
              ? t('fna:lifeStage.concern.EDUCATION.tablet')
              : t('fna:lifeStage.concern.EDUCATION.mobile'),
            isCompleted: checkEducationCompletion(
              data.educationGoal,
              props.adviceType,
              props.concerns,
            ),
            enabled: data.educationGoal.enabled,
            icon: checkEducationCompletion(
              data.educationGoal,
              props.adviceType,
              props.concerns,
            ) ? (
              <HumanBrainIcon width={iconSize} height={iconSize} />
            ) : (
              <HumanBrainInactiveIcon width={iconSize} height={iconSize} />
            ),
            gap: data.educationGoal.gapAmount,
            target: data.educationGoal.targetAmount,
            coverage: data.educationGoal.coverageAmount,
            yearsToAchieve: data.educationGoal.yearsToAchieve,
            priority: data.educationGoal.priority,
          });
          break;
        case 'RETIREMENT':
          savingsItems.push({
            id: 'RETIREMENT',
            title: isTabletMode
              ? t('fna:lifeStage.concern.RETIREMENT.tablet')
              : t('fna:lifeStage.concern.RETIREMENT.mobile'),
            isCompleted: checkRetirementCompletion(data.retirementGoal),
            enabled: data.retirementGoal.enabled,
            icon: checkRetirementCompletion(data.retirementGoal) ? (
              <FamilyHeartIcon width={iconSize} height={iconSize} />
            ) : (
              <FamilyHeartInactiveIcon width={iconSize} height={iconSize} />
            ),
            gap: data.retirementGoal.gapAmount,
            target: data.retirementGoal.targetAmount,
            coverage: data.retirementGoal.coverageAmount,
            yearsToAchieve: data.retirementGoal.yearsToAchieve,
            priority: data.retirementGoal.priority,
          });
          break;
        case 'INVESTMENT':
          savingsItems.push({
            id: 'INVESTMENT',
            title: isTabletMode
              ? t('fna:lifeStage.concern.INVESTMENT.tablet')
              : t('fna:lifeStage.concern.INVESTMENT.mobile'),
            isCompleted: checkInvestmentCompletion(data.investmentGoal),
            enabled: data.investmentGoal.enabled,
            icon: checkInvestmentCompletion(data.investmentGoal) ? (
              <FinancialGrowthIcon width={iconSize} height={iconSize} />
            ) : (
              <FinancialGrowthInactiveIcon width={iconSize} height={iconSize} />
            ),
            gap: data.investmentGoal.gapAmount,
            target: data.investmentGoal.targetAmount,
            coverage: data.investmentGoal.coverageAmount,
            yearsToAchieve: data.investmentGoal.yearsToAchieve,
            investmentDuration: data.investmentGoal.investmentDuration,
            priority: data.investmentGoal.priority,
          });
          break;
        case 'SAVINGS':
          savingsItems.push({
            id: 'SAVINGS',
            title: isTabletMode
              ? t('fna:lifeStage.concern.SAVINGS.tablet')
              : t('fna:lifeStage.concern.SAVINGS.mobile'),
            isCompleted: checkSavingsCompletion(data.savingsGoal),
            enabled: data.savingsGoal?.enabled,
            icon: checkSavingsCompletion(data.savingsGoal) ? (
              <MoneyBagIncrementIcon width={iconSize} height={iconSize} />
            ) : (
              <MoneyBagIncrementInactiveIcon
                width={iconSize}
                height={iconSize}
              />
            ),
            gap: data.savingsGoal?.gapAmount,
            target: data.savingsGoal?.targetAmount,
            coverage: data.savingsGoal?.coverageAmount,
            yearsToAchieve: data.savingsGoal?.yearsToAchieve,
            priority: data.savingsGoal?.priority,
          });
          break;
        case 'INCOME_PROTECTION':
          protectionItems.push({
            id: 'INCOME_PROTECTION',
            title: isTabletMode
              ? t('fna:lifeStage.concern.INCOME_PROTECTION.tablet')
              : t('fna:lifeStage.concern.INCOME_PROTECTION.mobile'),
            isCompleted: checkIncomeProtectionCompletion(
              data.incomeProtectionGoal,
            ),
            enabled: data.incomeProtectionGoal.enabled,
            icon: checkIncomeProtectionCompletion(data.incomeProtectionGoal) ? (
              <MoneyBagIcon width={iconSize} height={iconSize} />
            ) : (
              <MoneyBagInactiveIcon width={iconSize} height={iconSize} />
            ),
            gap: data.incomeProtectionGoal.gapAmount,
            target: data.incomeProtectionGoal.targetAmount,
            coverage: data.incomeProtectionGoal.coverageAmount,
            priority: data.incomeProtectionGoal.priority,
          });
          break;
        case 'HEALTH_PROTECTION':
          protectionItems.push({
            id: 'HEALTH_PROTECTION',
            title: isTabletMode
              ? t('fna:lifeStage.concern.HEALTH_PROTECTION.tablet')
              : t('fna:lifeStage.concern.HEALTH_PROTECTION.mobile.2'),
            isCompleted: checkHealthProtectionCompletion(
              data.healthProtectionGoal,
            ),
            enabled: data.healthProtectionGoal.enabled,
            icon: checkHealthProtectionCompletion(data.healthProtectionGoal) ? (
              <MedicineIcon width={iconSize} height={iconSize} />
            ) : (
              <MedicineInactiveIcon width={iconSize} height={iconSize} />
            ),
            gap: data.healthProtectionGoal.gapAmount,
            target: data.healthProtectionGoal.targetAmount,
            coverage: data.healthProtectionGoal.coverageAmount,
            priority: data.healthProtectionGoal.priority,
          });
          break;
        case 'LOAN_PROTECTION':
          protectionItems.push({
            id: 'LOAN_PROTECTION',
            title: isTabletMode
              ? t('fna:lifeStage.concern.LOAN_PROTECTION.tablet')
              : t('fna:lifeStage.concern.LOAN_PROTECTION.mobile'),
            isCompleted: checkLoanCoverageCompletion(data.loanCoverageGoal),
            enabled: data.loanCoverageGoal.enabled,
            icon: checkLoanCoverageCompletion(data.loanCoverageGoal) ? (
              <MoneyUnderUmbrellaIcon width={iconSize} height={iconSize} />
            ) : (
              <MoneyUnderUmbrellaInactiveIcon
                width={iconSize}
                height={iconSize}
              />
            ),
            gap: data.loanCoverageGoal.targetAmount,
            target: data.loanCoverageGoal.targetAmount,
            coverage: data.loanCoverageGoal.coverageAmount,
            priority: data.loanCoverageGoal.priority,
          });
          break;
        case 'LEGACY_PLANNING':
          protectionItems.push({
            id: 'LEGACY_PLANNING',
            title: isTabletMode
              ? t('fna:lifeStage.concern.LEGACY_PLANNING.tablet')
              : t('fna:lifeStage.concern.LEGACY_PLANNING.mobile'),
            isCompleted: checkLegacyPlanningCompletion(data.legacyPlanningGoal),
            enabled: data.legacyPlanningGoal.enabled,
            icon: checkLegacyPlanningCompletion(data.legacyPlanningGoal) ? (
              <NuturingFinanceIcon width={iconSize} height={iconSize} />
            ) : (
              <NuturingFinanceInactiveIcon width={iconSize} height={iconSize} />
            ),
            gap: data.legacyPlanningGoal.gapAmount,
            target: data.legacyPlanningGoal.targetAmount,
            coverage: data.legacyPlanningGoal.coverageAmount,
            priority: data.legacyPlanningGoal.priority,
          });
          break;
      }
    });
    const savings = {
      totalTarget: 0,
      totalCoverage: 0,
      totalGap: 0,
      items: [...savingsItems]
        .filter(item =>
          country === 'ph' && item.id === 'RETIREMENT'
            ? data.dob && calculateAge(data.dob) < MAX_AGE_RETIREMENT
            : true,
        )
        .sort((a, b) => (a.priority ?? 0) - (b.priority ?? 0)),
    };
    goalsByNeed['SAVINGS'].forEach(goalType => {
      const goal = data[goalType];
      if (goalType) {
        savings.totalTarget += goal?.targetAmount || 0;
        savings.totalCoverage += goal?.coverageAmount || 0;
        savings.totalGap += goal?.gapAmount || 0;
      }
    });

    const protection = {
      totalTarget: 0,
      totalCoverage: 0,
      totalGap: 0,
      items: [...protectionItems].sort(
        (a, b) => (a.priority ?? 0) - (b.priority ?? 0),
      ),
    };
    goalsByNeed['PROTECTION'].forEach(goalType => {
      const goal = data[goalType];
      if (goalType) {
        protection.totalTarget += goal.targetAmount || 0;
        protection.totalCoverage += goal.coverageAmount || 0;
        protection.totalGap += goal.gapAmount || 0;
      }
    });

    return {
      savings,
      protection,
    };
  }, [data, iconSize, isTabletMode, props.adviceType, props.concerns, t]);

  return (
    <DeviceBasedRendering
      phone={
        <GoalBreakdownPhone
          {...props}
          savings={savings}
          protection={protection}
          hasTooltip={countryModuleFnaConfig.hasGoalTooltip}
        />
      }
      tablet={
        <GoalBreakdownTablet
          {...props}
          savings={savings}
          protection={protection}
        />
      }
    />
  );
};
