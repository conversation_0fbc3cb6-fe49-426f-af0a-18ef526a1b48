import {
  Body,
  Box,
  Center,
  ExtraSmallLabel,
  H6,
  LargeLabel,
  PictogramIcon,
  Row,
  SmallBody,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import React, { useEffect, useMemo } from 'react';
import styled from '@emotion/native';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { ConcernId } from 'features/fna/types/concern';
import {
  currencyKFormat,
  getPriorityColor,
} from 'features/fna/utils/helper/fnaUtils';
import { NumberSequence } from 'types';
import Animated, {
  measure,
  runOnUI,
  useAnimatedRef,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import ShieldIcon from '../../icons/ShieldIcon';
import type { InternalNeedsSummary, SummaryItem } from './NeedsSummary';

export default function NeedsSummaryTablet({
  savings,
  protection,
}: InternalNeedsSummary) {
  const { t } = useTranslation(['fna']);
  const { space, animation } = useTheme();

  const fnaState = useFnaStore();

  const height = useSharedValue(0);
  const containerRef = useAnimatedRef<Animated.View>();
  useEffect(() => {
    const timeout = setTimeout(() => {
      runOnUI(() => {
        'worklet';
        height.value = withTiming(measure(containerRef)?.height || 0, {
          duration: animation.duration,
        });
      })();
    }, 2000);
    return () => clearTimeout(timeout);
  }, [animation.duration, containerRef, height]);

  const animatedContainerStyle = useAnimatedStyle(() => {
    return {
      height: height.value,
    };
  }, [height.value]);

  return (
    <Animated.View style={animatedContainerStyle}>
      <Container ref={containerRef}>
        <LargeLabel fontWeight="bold">
          {t('fna:summary', {
            name: fnaState.getCustomerName(),
          })}
        </LargeLabel>
        <Row gap={space[4]}>
          <NeedSummary
            type="SAVINGS"
            concerns={fnaState.lifeJourney.concerns}
            totalGap={savings.totalGap}
            data={savings.items}
          />
          <Box w={1} backgroundColor="#C4C4C4" mx={space[5]} />
          <NeedSummary
            type="PROTECTION"
            concerns={fnaState.lifeJourney.concerns}
            totalGap={protection.totalGap}
            data={protection.items}
          />
        </Row>
      </Container>
    </Animated.View>
  );
}

const Container = styled(Animated.View)(({ theme }) => {
  return {
    position: 'absolute',
    width: '100%',
    top: 0,
    paddingHorizontal: theme.space[8],
    paddingVertical: theme.space[4],
    backgroundColor: theme.colors.background,
    gap: theme.space[3],
  };
});

const NeedSummary = ({
  type,
  concerns,
  totalGap,
  data,
}: {
  type: 'SAVINGS' | 'PROTECTION';
  totalGap: number;
  concerns: ConcernId[];
  data: SummaryItem[];
}) => {
  const { t } = useTranslation(['common', 'fna']);
  const { borderRadius, space, sizes, colors } = useTheme();
  const priorityColor = useMemo(() => getPriorityColor(colors), [colors]);
  return (
    <Row gap={space[5]} flex={1}>
      <Box gap={space[1]}>
        <Body>
          {type === 'SAVINGS'
            ? t('fna:savingsGoals.totalSavingNeeds')
            : t('fna:protectionGoals.totalCoverNeeds')}
        </Body>
        <Row alignItems="center" gap={space[1]}>
          {type === 'SAVINGS' && <PictogramIcon.Cash2 size={sizes[10]} />}
          {type === 'PROTECTION' && (
            <ShieldIcon width={sizes[10]} height={sizes[10]} />
          )}
          <SmallBody color={colors.palette.alertRed}>
            {t('fna:currency')}
          </SmallBody>
          <H6 fontWeight="bold" color={colors.palette.alertRed}>
            {currencyKFormat(totalGap).toUpperCase()}
          </H6>
        </Row>
      </Box>
      <Box gap={6} flex={1}>
        {data.map(item => {
          const priority = concerns.indexOf(item.id as ConcernId);
          return (
            <Row key={item.id} alignItems="center">
              <Center w={67} h={17} mr={space[1]}>
                {priority >= 0 && (
                  <Center
                    alignSelf="stretch"
                    flex={1}
                    bgColor={priorityColor[priority as NumberSequence<0, 2>]}
                    px={7}
                    py={2}
                    borderRadius={borderRadius.full}>
                    <ExtraSmallLabel>
                      {t(
                        `fna:lifeStage.concern.priority.${
                          priority as 0 | 1 | 2
                        }`,
                      )}
                    </ExtraSmallLabel>
                  </Center>
                )}
              </Center>
              {item.icon}
              <Box flex={1}>
                <LargeLabel>{item.title}</LargeLabel>
              </Box>
              <LargeLabel fontWeight="bold">
                {t('common:withCurrency', {
                  amount: currencyKFormat(item.value || 0).toUpperCase(),
                })}
              </LargeLabel>
            </Row>
          );
        })}
      </Box>
    </Row>
  );
};
