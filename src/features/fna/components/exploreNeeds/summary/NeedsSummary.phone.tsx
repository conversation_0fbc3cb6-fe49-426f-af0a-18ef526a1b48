import {
  Box,
  Center,
  ExtraSmallLabel,
  H6,
  H8,
  LargeLabel,
  PictogramIcon,
  Row,
  SmallBody,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import React, { useEffect, useMemo, useState } from 'react';
import styled from '@emotion/native';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { ConcernId } from 'features/fna/types/concern';
import {
  currencyKFormat,
  getPriorityColor,
} from 'features/fna/utils/helper/fnaUtils';
import { NumberSequence } from 'types';
import Animated, {
  measure,
  runOnJS,
  runOnUI,
  SharedValue,
  useAnimatedRef,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import ShieldIcon from '../../icons/ShieldIcon';
import type { InternalNeedsSummary, SummaryItem } from './NeedsSummary';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import useToggle from 'hooks/useToggle';
import DropShadow from 'react-native-drop-shadow';

export default function NeedsSummaryPhone({
  savings,
  protection,
  handlerBackgroundColor,
}: InternalNeedsSummary & { handlerBackgroundColor?: string }) {
  const { animation, space, colors, borderRadius } = useTheme();
  const { t } = useTranslation(['product']);
  const fnaState = useFnaStore();
  const [draggable, enableDrag] = useToggle();
  const dragA = useSharedValue(0);
  const dragB = useSharedValue(0);

  const height = useSharedValue(0);
  const extraHeight = useSharedValue(0);
  const changeY = useSharedValue(0);
  const [initialHeight, setInitialHeight] = useState(0);
  const [extraHeightA, setExtraHeightA] = useState(0);
  const [extraHeightB, setExtraHeightB] = useState(0);
  const containerRef = useAnimatedRef<Animated.View>();
  useEffect(() => {
    runOnUI(() => {
      'worklet';
      const defaultHeight = measure(containerRef)?.height || 0;
      runOnJS(setInitialHeight)(defaultHeight);
      height.value = withTiming(
        defaultHeight,
        {
          duration: animation.duration,
        },
        () => {
          runOnJS(enableDrag)();
          extraHeight.value = defaultHeight;
        },
      );
    })();
  }, [
    animation.duration,
    containerRef,
    enableDrag,
    extraHeight,
    height,
    setInitialHeight,
  ]);

  const animatedContainerStyle = useAnimatedStyle(() => {
    return {
      height: height.value + 21,
      zIndex: 99,
      overflow: draggable ? 'visible' : 'hidden',
    };
  }, [draggable, height.value]);

  const animatedContentStyle = useAnimatedStyle(() => {
    return {
      height: extraHeight.value || undefined,
    };
  }, [extraHeight.value]);

  const animatedHandlerContainerStyle = useAnimatedStyle(() => {
    return {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor:
        handlerBackgroundColor || (draggable ? 'transparent' : colors.primary),
      transform: [
        {
          translateY: changeY.value,
        },
      ],
    };
  }, [changeY.value, colors.primary, draggable, handlerBackgroundColor]);

  const maxHeight = initialHeight + extraHeightA + extraHeightB;
  const gesture = Gesture.Pan()
    .onChange(e => {
      const aChangeY =
        (e.changeY * extraHeightA) / (extraHeightA + extraHeightB);
      const bChangeY = e.changeY - aChangeY;
      const changeYValue = aChangeY + bChangeY;
      const exceededMaxHeight = extraHeight.value + changeYValue > maxHeight;
      const exceededMinHeight =
        extraHeight.value + changeYValue < initialHeight;
      if (exceededMaxHeight || exceededMinHeight) return;
      dragA.value = Math.max(0, Math.min(dragA.value + aChangeY, extraHeightA));
      dragB.value = Math.max(0, Math.min(dragB.value + bChangeY, extraHeightB));
      extraHeight.value = Math.max(
        initialHeight,
        extraHeight.value + changeYValue,
      );
      changeY.value = Math.max(0, changeY.value + changeYValue);
    })
    .onEnd(e => {
      const duration =
        (animation.duration * Math.min(e.y, maxHeight)) / maxHeight;
      if (extraHeight.value > maxHeight / 1.5) {
        dragA.value = withTiming(extraHeightA, {
          duration: (duration * extraHeightA) / (extraHeightA + extraHeightB),
        });
        dragB.value = withTiming(extraHeightB, {
          duration: (duration * extraHeightB) / (extraHeightA + extraHeightB),
        });
        extraHeight.value = withTiming(maxHeight, {
          duration: duration,
        });
        changeY.value = withTiming(extraHeightA + extraHeightB, {
          duration: duration,
        });
      } else {
        dragA.value = withTiming(0, { duration: duration });
        dragB.value = withTiming(0, { duration: duration });
        extraHeight.value = withTiming(initialHeight, {
          duration: duration,
        });
        changeY.value = withTiming(0, { duration: duration });
      }
    })
    .enabled(draggable);

  return (
    <GestureDetector gesture={gesture}>
      <Animated.View style={animatedContainerStyle}>
        <Container ref={containerRef} style={animatedContentStyle}>
          <Box bgColor={colors.background} pt={space[4]} px={space[4]}>
            <LargeLabel fontWeight="bold">
              {t('product:yourGoalSummary')}
            </LargeLabel>
          </Box>
          <Content>
            <NeedSummary
              key="SAVINGS"
              drag={dragA}
              type="SAVINGS"
              concerns={fnaState.lifeJourney.concerns}
              totalGap={savings.totalGap}
              data={savings.items}
              onMeasureExtraHeight={setExtraHeightA}
            />
            <NeedSummary
              key="PROTECTION"
              drag={dragB}
              type="PROTECTION"
              concerns={fnaState.lifeJourney.concerns}
              totalGap={protection.totalGap}
              data={protection.items}
              onMeasureExtraHeight={setExtraHeightB}
            />
          </Content>
        </Container>
        <Animated.View style={animatedHandlerContainerStyle}>
          <DropShadowContainer>
            <Center
              backgroundColor={colors.background}
              borderBottomLeftRadius={borderRadius.large}
              borderBottomRightRadius={borderRadius.large}>
              <Handle />
            </Center>
          </DropShadowContainer>
        </Animated.View>
      </Animated.View>
    </GestureDetector>
  );
}

const DropShadowContainer = styled(DropShadow)(() => ({
  shadowColor: '#000',
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.23,
  shadowRadius: 1,
}));

const Container = styled(Animated.View)(() => {
  return {
    position: 'absolute',
    width: '100%',
    top: 0,
  };
});

const Content = styled(Animated.View)(({ theme }) => {
  return {
    paddingHorizontal: theme.space[4],
    paddingVertical: theme.space[2],
    backgroundColor: theme.colors.background,
    gap: theme.space[2],
  };
});

const NeedSummary = ({
  type,
  concerns,
  totalGap,
  data,
  drag,
  onMeasureExtraHeight,
}: {
  type: 'SAVINGS' | 'PROTECTION';
  totalGap: number;
  concerns: ConcernId[];
  data: SummaryItem[];
  drag: SharedValue<number>;
  onMeasureExtraHeight: (height: number) => void;
}) => {
  const { t } = useTranslation(['common', 'fna']);
  const { borderRadius, space, colors } = useTheme();
  const priorityColor = useMemo(() => getPriorityColor(colors), [colors]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      height: drag.value,
      overflow: 'hidden',
    };
  }, [drag.value]);

  const extraContentContainerRef = useAnimatedRef();
  useEffect(() => {
    runOnUI(() => {
      'worklet';
      const height = measure(extraContentContainerRef)?.height || 0;
      runOnJS(onMeasureExtraHeight)(height);
    })();
  }, [extraContentContainerRef, onMeasureExtraHeight]);

  return (
    <Box>
      <Row gap={space[1]} alignItems="center" mt={3.5}>
        {type === 'SAVINGS' && <PictogramIcon.Cash2 size={24} />}
        {type === 'PROTECTION' && <ShieldIcon width={24} height={24} />}
        <H8 fontWeight="bold">
          {type === 'SAVINGS'
            ? t('fna:savingsGoals.totalSavingNeeds')
            : t('fna:protectionGoals.totalCoverNeeds')}
        </H8>
        <Row
          flex={1}
          justifyContent="flex-end"
          alignItems="center"
          gap={space[1]}>
          <SmallBody color={colors.palette.alertRed}>
            {t('fna:currency')}
          </SmallBody>
          <H6 fontWeight="bold" color={colors.palette.alertRed}>
            {currencyKFormat(totalGap).toUpperCase()}
          </H6>
        </Row>
      </Row>
      <Animated.View style={animatedStyle}>
        <ExtraContentContainer ref={extraContentContainerRef}>
          {data.map(item => {
            const priority = concerns.indexOf(item.id as ConcernId);
            return (
              <Row key={item.id} alignItems="center" gap={space[1]}>
                <Center w={67} h={17}>
                  {priority >= 0 && (
                    <Center
                      alignSelf="stretch"
                      flex={1}
                      bgColor={priorityColor[priority as NumberSequence<0, 2>]}
                      px={7}
                      py={2}
                      borderRadius={borderRadius.full}>
                      <ExtraSmallLabel>
                        {t(
                          `fna:lifeStage.concern.priority.${
                            priority as 0 | 1 | 2
                          }`,
                        )}
                      </ExtraSmallLabel>
                    </Center>
                  )}
                </Center>
                {item.icon}
                <Box flex={1}>
                  <LargeLabel>{item.title}</LargeLabel>
                </Box>
                <LargeLabel fontWeight="bold">
                  {t('common:withCurrency', {
                    amount: currencyKFormat(item.value || 0).toUpperCase(),
                  })}
                </LargeLabel>
              </Row>
            );
          })}
        </ExtraContentContainer>
      </Animated.View>
    </Box>
  );
};

const ExtraContentContainer = styled(Animated.View)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  width: '100%',
  gap: theme.space[3],
  paddingTop: 23.5,
}));

const Handle = styled.View(({ theme }) => ({
  marginVertical: theme.space[2],
  backgroundColor: theme.colors.palette.fwdGrey[100],
  width: 40,
  height: 5,
  borderRadius: 6,
}));
