import { useTheme } from '@emotion/react';
import { Box, Column, H6, Row, SmallBody } from 'cube-ui-components';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import useToggle from 'hooks/useToggle';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';
import { GoalBreakdown } from './chart/breakdown/GoalBreakdown';
import { GoalChart } from './chart/GoalChart';

export const ExploreNeeds = ({ isFnaValid }: { isFnaValid: boolean }) => {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();
  const [showingSummary, showSummary, hideSummary] = useToggle();
  const fnaState = useFnaStore();
  const breakdownData = useMemo(
    () => ({
      ...fnaState,
      name: fnaState.getCustomerName(),
      dob: fnaState.lifeJourney.dob,
    }),
    [fnaState],
  );
  const compulsoryFna = fnaState.compulsoryFna;
  const hasCompulsoryFna = compulsoryFna.length > 0;

  return (
    <>
      {isTabletMode ? (
        <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
          <Row mb={space[4]} alignItems="center" gap={space[3]} px={space[16]}>
            <H6 fontWeight="bold">{t('fna:calculateNeed')}</H6>
          </Row>
          <Box mb={10} ml={space[16]}>
            {hasCompulsoryFna && (
              <SmallBody>{t('fna:goal.mandatoryWarning')}</SmallBody>
            )}
          </Box>
          <Row mb={space[10]} px={space[16]} gap={space[4]}>
            <GoalChart
              isFnaValid={isFnaValid}
              onShowSummary={showSummary}
              goalType="SAVINGS"
            />
            <GoalChart
              isFnaValid={isFnaValid}
              onShowSummary={showSummary}
              goalType="PROTECTION"
            />
          </Row>
          <CFFModal visible={showingSummary}>
            <GoalBreakdown
              data={breakdownData}
              concerns={fnaState.lifeJourney.concerns}
              onDismiss={hideSummary}
              adviceType={fnaState.adviceType}
            />
          </CFFModal>
        </ScrollView>
      ) : (
        <Column flex={1} pt={space[4]}>
          <Box mb={space[4]} ml={space[2]}>
            <H6 fontWeight="bold">{t('fna:explore')}</H6>
          </Box>
          {hasCompulsoryFna && (
            <Box mb={space[1]} ml={space[2]}>
              <SmallBody>{t('fna:goal.mandatoryWarning')}</SmallBody>
            </Box>
          )}
          <Box mb={space[7]}>
            <GoalChart isFnaValid={isFnaValid} goalType="SAVINGS" />
            <GoalChart isFnaValid={isFnaValid} goalType="PROTECTION" />
          </Box>
        </Column>
      )}
    </>
  );
};
