import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import SaveModal from 'components/SaveModal';
import { addToast, Icon } from 'cube-ui-components';
import { useSaveFna } from 'features/fna/hooks/useSaveFna';
import {
  FnaState,
  parseFna,
  useFnaStore,
} from 'features/fna/utils/store/fnaStore';
import { nameSchema } from 'features/fna/utils/validation/fnaValidation';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import useToggle from 'hooks/useToggle';
import isEqual from 'lodash/isEqual';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { CaseStatus } from 'types/case';
import { PartyRole } from 'types/party';
import GATracking from 'utils/helper/gaTracking';
import CreateLead from '../common/createLead/CreateLead';
import FnaHeaderPhone from './FnaHeader.phone';
import FnaHeaderTablet from './FnaHeader.tablet';

export interface FnaHeaderViewProps {
  onPressClose?: () => void;
  onPressHome?: () => void;
  title?: string;
}

export default function FnaHeader({
  title,
  onClose,
}: {
  title?: string;
  onClose?: () => void;
}) {
  const { t } = useTranslation(['fna']);
  const { colors } = useTheme();
  const [createLeadVisible, showCreateLeadVisible, hideCreateLeadVisible] =
    useToggle();
  const [showSaveModal, setShowSaveModal] = useState(false);
  const hasExistingLead = useFnaStore(state => Boolean(state.existingLead));
  const isBackToHome = useRef(false);
  const { saveFna, isLoading: isSavingFna } = useSaveFna();

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId || '');
  const lifeJourney = useFnaStore(state => state.lifeJourney);
  const setRequiredBeforeSavingError = useFnaStore(
    state => state.setRequiredBeforeSavingError,
  );
  const isFnaChanged = useMemo(() => {
    if (caseObj && caseObj.latestStatus === CaseStatus.FNA && caseObj.fna) {
      const convertedLifeJourney: FnaState['lifeJourney'] = parseFna(
        caseObj.fna,
      ).lifeJourney;
      if (!isEqual(convertedLifeJourney, lifeJourney)) {
        return true;
      }
    }

    return true;
  }, [caseObj, lifeJourney]);

  const backToHome = useCallback(() => {
    navigation.navigate('Main', {
      screen: 'Home',
    });
  }, [navigation]);

  const backToMain = useCallback(() => {
    navigation.navigate('Main');
  }, [navigation]);

  const onFinish = useCallback(() => {
    GATracking.logCustomEvent('fn_assessment', {
      action_type: 'fna_save',
    });
    addToast([
      {
        message: t('fna:savedSuccessfully'),
        IconLeft: <Icon.Tick size={18} fill={colors.onSecondary} />,
      },
    ]);
    if (isBackToHome.current) {
      backToHome();
    } else {
      backToMain();
    }
  }, [backToHome, backToMain, colors.onSecondary, t]);

  const onFinishCreateLead = useCallback(() => {
    addToast([
      {
        message: t('fna:savedLead'),
        IconLeft: <Icon.Tick size={18} fill={colors.onSecondary} />,
      },
    ]);
    if (isBackToHome.current) {
      backToHome();
    } else {
      backToMain();
    }
  }, [backToHome, backToMain, colors.onSecondary, t]);

  const onConfirmSaveModal = useCallback(async () => {
    try {
      nameSchema.validateSync(lifeJourney.firstName);
      if (lifeJourney.gender === null) throw new Error('Missing gender');
      if (lifeJourney.lifeStage === null) throw new Error('Missing life stage');
      const party = caseObj?.parties?.find(e =>
        e.roles.includes(PartyRole.PROPOSER),
      );
      if (caseObj && (caseObj.status?.includes(CaseStatus.COVERAGE) || party)) {
        await saveFna({
          isCompleted: false,
          party,
        });
        onFinish();
        return;
      }
      if (hasExistingLead) {
        await saveFna({ isCompleted: false });
        addToast([
          {
            message: t('fna:savedSuccessfully'),
            IconLeft: <Icon.Tick size={18} fill={colors.onSecondary} />,
          },
        ]);
        onFinish();
      } else {
        showCreateLeadVisible();
      }
    } catch (e) {
      setRequiredBeforeSavingError(true);
    } finally {
      setShowSaveModal(false);
    }
  }, [
    lifeJourney.firstName,
    lifeJourney.gender,
    lifeJourney.lifeStage,
    hasExistingLead,
    caseObj,
    saveFna,
    t,
    colors.onSecondary,
    onFinish,
    showCreateLeadVisible,
    setRequiredBeforeSavingError,
  ]);

  const onDenySaveModal = useCallback(() => {
    GATracking.logCustomEvent('fn_assessment', {
      action_type: 'fna_exit',
    });

    setShowSaveModal(false);
    backToMain?.();
  }, [backToMain]);

  const onPressClose = useCallback(() => {
    if (isFnaChanged) {
      isBackToHome.current = false;
      setShowSaveModal(true);
    } else {
      backToMain?.();
    }
  }, [isFnaChanged, backToMain]);

  const onPressHome = useCallback(() => {
    if (isFnaChanged) {
      isBackToHome.current = true;
      setShowSaveModal(true);
    } else {
      backToHome?.();
    }
  }, [isFnaChanged, backToHome]);

  const onCancelSaveModal = useCallback(() => {
    setShowSaveModal(false);
  }, []);

  return (
    <>
      <DeviceBasedRendering
        tablet={
          <FnaHeaderTablet
            onPressClose={onClose || onPressClose}
            onPressHome={onPressHome}
            title={title}
          />
        }
        phone={
          <FnaHeaderPhone
            onPressClose={onClose || onPressClose}
            onPressHome={onPressHome}
            title={title}
          />
        }
      />
      <SaveModal
        dialogVisible={showSaveModal}
        onCancel={onCancelSaveModal}
        onConfirm={onConfirmSaveModal}
        onDeny={onDenySaveModal}
        isLoading={isSavingFna}
        title={t('fna:exitFna')}
        denyLabel={t('fna:dontSave')}
        saveLabel={t('fna:save')}
        subTitle={t('fna:exitFnaDesc')}
      />
      <CreateLead
        visible={createLeadVisible}
        onDismiss={hideCreateLeadVisible}
        onFinish={onFinishCreateLead}
      />
    </>
  );
}
