import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Icon, LargeLabel, Row } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { FnaHeaderViewProps } from './FnaHeader';

export default function FnaHeader({
  onPressClose,
}: FnaHeaderViewProps) {
  const { t } = useTranslation(['fna']);
  const { colors, sizes } = useTheme();

  const { top: topInset } = useSafeAreaInsets();

  return (
    <Container style={{ paddingTop: topInset }}>
      <Row alignItems="center">
        <Title fontWeight="bold">{t('fna:fna')}</Title>
        <CloseButton onPress={onPressClose}>
          <Icon.Close size={sizes[6]} fill={colors.secondary} />
        </CloseButton>
        <Box flex={1} />
        {/* <Column alignItems="center" mr={space[4]}>
          <Switch
            label=""
            checked={isFullScreen}
            onChange={() => {
              updateFullScreen(!isFullScreen);
            }}
          />
          <ExtraSmallLabel>{t('fna:fullScreen')}</ExtraSmallLabel>
        </Column> */}
      </Row>
    </Container>
  );
}

const Container = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  borderBottomColor: theme.colors.palette.fwdGrey[100],
  borderBottomWidth: 1,
}));

const CloseButton = styled.TouchableOpacity(({ theme: { sizes } }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    width: sizes[isNarrowScreen ? 10 : 11],
    height: sizes[11],
    justifyContent: 'center',
    alignItems: 'center',
  };
});

const Title = styled(LargeLabel)(() => ({
  position: 'absolute',
  left: 0,
  right: 0,
  alignSelf: 'center',
  textAlign: 'center',
}));
