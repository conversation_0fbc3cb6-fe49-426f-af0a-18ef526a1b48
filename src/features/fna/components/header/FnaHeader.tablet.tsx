import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { Box, H6, Icon, LargeLabel, Row } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FnaHeaderViewProps } from './FnaHeader';

export default function FnaHeader({
  title,
  onPressClose,
  onPressHome,
}: FnaHeaderViewProps) {
  const theme = useTheme();
  const { top: topInset } = useSafeAreaInsets();
  const { t } = useTranslation(['fna']);

  return (
    <Row
      zIndex={10}
      h={theme.sizes[14] + topInset}
      pt={topInset}
      gap={theme.space[5]}
      px={theme.space[4]}
      backgroundColor={theme.colors.background}
      alignItems="center">
      <BackButton hitSlop={ICON_HIT_SLOP} onPress={onPressClose}>
        <Icon.ArrowLeft fill={theme.colors.secondary} size={theme.sizes[6]} />
      </BackButton>
      {Boolean(title) && <H6 fontWeight="bold">{title}</H6>}
      <Box flex={1} />
      <IconButton onPress={onPressHome}>
        <Icon.Home fill={theme.colors.secondary} size={theme.sizes[6]} />
        <LargeLabel fontWeight="bold">{t('fna:home')}</LargeLabel>
      </IconButton>
    </Row>
  );
}

const BackButton = styled(TouchableOpacity)({
  justifyContent: 'center',
  alignItems: 'center',
});

const IconButton = styled(TouchableOpacity)(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  paddingHorizontal: theme.space[2],
  paddingVertical: 10,
  gap: theme.space[1],
}));
