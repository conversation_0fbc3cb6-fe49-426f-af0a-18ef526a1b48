import { MAX_NAME_LENGHT } from 'features/coverageDetails/validation/common/constant';
import { FnaState } from '../../store/fnaStore';

export const checkLifeJourneyCompletion = (
  data: FnaState['lifeJourney'],
): { isComplete: boolean; missingFields: number } => {
  const conditions = [
    data.gender,
    data.firstName.length &&
      data.firstName.length <= MAX_NAME_LENGHT &&
      data.lastName?.length &&
      data.lastName.length <= MAX_NAME_LENGHT,
    data.dob,
    data.annuallyIncome.from || data.annuallyIncome.to,
    data.goalPercentage.from || data.goalPercentage.to,
    data.lifeStage,
  ];

  return {
    isComplete: conditions.every(condition => <PERSON><PERSON><PERSON>(condition)),
    missingFields: conditions.filter(condition => !condition).length,
  };
};
