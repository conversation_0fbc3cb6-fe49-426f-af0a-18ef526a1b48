import { MAX_NAME_LENGHT } from 'features/coverageDetails/validation/common/constant';
import { FnaState } from '../../store/fnaStore';
import { getNameRegex } from 'utils/validation/customValidation';

export const checkLifeJourneyCompletion = (
  data: FnaState['lifeJourney'],
): { isComplete: boolean; missingFields: number } => {
  const conditions = [
    data.gender,
    data.title,
    data.firstName.length &&
      data.firstName.length <= MAX_NAME_LENGHT &&
      getNameRegex().test(data.firstName ?? ''),
    data.dob,
    data.annuallyIncome.from || data.annuallyIncome.to,
    data.expectedIncomeIncrement !== null,
    data.goalPercentage.from || data.goalPercentage.to,
    data.totalAssets !== null,
    data.totalLiabilities !== null,
    data.lifeStage,
  ];

  return {
    isComplete: conditions.every(condition => <PERSON><PERSON>an(condition)),
    missingFields: conditions.filter(condition => !condition).length,
  };
};
