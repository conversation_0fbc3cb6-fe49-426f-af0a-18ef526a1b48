import { ConcernId } from 'features/fna/types/concern';
import _ from 'lodash';
import { countryModuleFnaConfig } from 'utils/config/module';
import { country } from 'utils/context';
import { getNameRegex } from 'utils/validation/customValidation';
import { AdviceType, FnaState } from '../store/fnaStore';

export const getCompulsoryFna = (
  concerns: ConcernId[],
  adviceType: AdviceType,
): ConcernId[] => {
  const config = countryModuleFnaConfig.compulsory;
  if (!config) return [];
  const { byConcerns, byPriorities } = config[adviceType];
  return _.union(concerns.slice(0, byPriorities ?? 0), byConcerns);
};

export const checkEducationCompletion = (
  educationGoal: Omit<
    FnaState['educationGoal'],
    'yearsToCollege' | 'yearsInCollege' | 'yearsToAchieve' | 'goals'
  > & {
    goals: Omit<FnaState['educationGoal']['goals'][0], 'dateOfBirth'>[];
  },
  adviceType: AdviceType,
  concerns: ConcernId[],
) => {
  if (!educationGoal.enabled) return true;
  switch (country) {
    case 'ph':
      for (const goal of educationGoal.goals) {
        if (goal.firstName === '') return false;
        if (!getNameRegex().test(goal.firstName)) return false;
        if (goal.childAge === null) return false;
        if (goal.hasSavings === null) return false;
        if (goal.hasSavings && goal.coverageAmount === null) {
          return false;
        }
        if (goal.targetAmount === null) return false;
      }

      return true;
    case 'my':
    case 'ib':
    case 'id':
      if (
        adviceType === AdviceType.PARTIAL &&
        concerns.indexOf('EDUCATION') > 2
      ) {
        return educationGoal.gapAmount !== null;
      }
      for (const goal of educationGoal.goals) {
        if (goal.firstName === '') return false;
        if (!getNameRegex().test(goal.firstName)) return false;
        if (goal.yearsToAchieve === null) return false;
        if (goal.childAge === null) return false;
        if (goal.gender === null) return false;
        if (goal.targetAmount === null) return false;
      }
      if (educationGoal.coverageAmount === null) return false;

      return true;
  }
  return false;
};

export const checkRetirementCompletion = (
  retirementGoal: FnaState['retirementGoal'],
) => {
  if (!retirementGoal.enabled) return true;
  switch (country) {
    case 'my':
    case 'ib':
    case 'id':
      if (
        retirementGoal.hasSavings &&
        retirementGoal.otherCoverageAmount === null
      ) {
        return false;
      }
      if (
        retirementGoal.targetAmount === null ||
        retirementGoal.coverageAmount === null
      ) {
        return false;
      }
      return true;

    case 'ph':
      if (retirementGoal.hasSavings === null) return false;
      if (retirementGoal.hasSavings && retirementGoal.coverageAmount === null) {
        return false;
      }
      if (retirementGoal.targetAmount === null) return false;
      return true;
  }
  return false;
};

export const checkSavingsCompletion = (
  savingsGoal: FnaState['savingsGoal'],
) => {
  if (!savingsGoal?.enabled) return true;
  switch (country) {
    case 'my':
    case 'ib':
    case 'id':
      if (
        !savingsGoal.purpose ||
        savingsGoal.targetAmount === null ||
        savingsGoal.coverageAmount === null ||
        savingsGoal.yearsToAchieve === null
      ) {
        return false;
      }
      return true;
  }
  return false;
};

export const checkInvestmentCompletion = (
  investmentGoal: FnaState['investmentGoal'],
) => {
  if (!investmentGoal.enabled) return true;
  switch (country) {
    case 'my':
    case 'ib':
    case 'id':
      if (
        investmentGoal.initialInvestmentAmount === null ||
        investmentGoal.regularInvestmentAmount === null ||
        investmentGoal.investmentDuration === null ||
        investmentGoal.monthlyPayout === null ||
        investmentGoal.payoutPeriod === null
      ) {
        return false;
      }
      return true;

    case 'ph':
      if (
        investmentGoal.purpose === null ||
        investmentGoal.yearsToAchieve === null ||
        investmentGoal.targetAmount === null
      ) {
        return false;
      }

      if (investmentGoal.hasSavings === null) return false;

      if (investmentGoal.hasSavings && investmentGoal.coverageAmount === null) {
        return false;
      }

      if (
        Number(investmentGoal?.yearsToAchieve) < 1 ||
        Number(investmentGoal?.yearsToAchieve) > 99
      ) {
        return false;
      }
      return true;
  }
  return false;
};

export const checkIncomeProtectionCompletion = (
  incomeGoal: FnaState['incomeProtectionGoal'],
) => {
  if (!incomeGoal.enabled) return true;
  switch (country) {
    case 'my':
    case 'ib':
    case 'id':
      if (incomeGoal.annualIncome === null) return false;
      if (incomeGoal.hasSavings && incomeGoal.coverageAmount === null) {
        return false;
      }

      if (incomeGoal.yearsToReplace === null) return false;
      if (incomeGoal.annualIncome > 0 && incomeGoal.yearsToReplace <= 0) {
        return false;
      }

      return true;

    case 'ph':
      if (incomeGoal.monthlyExpenses === null) return false;
      if (incomeGoal.hasSavings === null) return false;
      if (incomeGoal.hasSavings && incomeGoal.coverageAmount === null) {
        return false;
      }

      if (incomeGoal.targetAmount === null) return false;

      return true;
  }
  return false;
};

export const checkHealthProtectionCompletion = (
  healthGoal: FnaState['healthProtectionGoal'],
) => {
  if (!healthGoal.enabled) return true;
  switch (country) {
    case 'my':
    case 'ib':
    case 'id':
      if (typeof healthGoal.hospitalisation?.targetAmount !== 'number') {
        return false;
      }
      if (typeof healthGoal.criticalIllness?.targetAmount !== 'number') {
        return false;
      }

      return true;
    case 'ph':
      if (healthGoal.hasSavings === null) return false;
      if (
        healthGoal.hasSavings &&
        healthGoal.insuranceCoverageAmount === null
      ) {
        return false;
      }

      if (healthGoal.targetAmount === null) return false;
      if (healthGoal.healthEmergenciesAmount === null) return false;

      return true;
  }
  return false;
};

export const checkLegacyPlanningCompletion = (
  legacyGoal: FnaState['legacyPlanningGoal'],
) => {
  if (!legacyGoal.enabled) return true;

  switch (country) {
    case 'ph':
    case 'id':
      if (legacyGoal.netTaxableEstateAmount === null) return false;
      if (legacyGoal.targetAmount === null) return false;
      if (legacyGoal.coverageAmount === null) return false;

      return true;
  }
  return false;
};

export const checkLoanCoverageCompletion = (
  loanGoal: FnaState['loanCoverageGoal'],
) => {
  if (!loanGoal.enabled) return true;
  switch (country) {
    case 'my':
    case 'ib':
      if (loanGoal.loanAmount === null) return false;
      if (loanGoal.loanTerm === null) return false;
      if (loanGoal.coverageAmount === null) return false;

      return true;
    case 'ph':
      if (loanGoal.targetAmount === null) return false;
      if (loanGoal.loanTerm === null) return false;

      return true;
  }
  return false;
};
