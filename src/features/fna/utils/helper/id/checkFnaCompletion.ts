import { getNameRegex } from 'utils/validation/customValidation';
import { FnaState } from '../../store/fnaStore';

export const checkLifeJourneyCompletion = (
  data: FnaState['lifeJourney'],
): { isComplete: boolean; missingFields: number } => {
  const conditions = [
    data.gender,
    data.dob,
    data.lifeStage,
    data.firstName.length && getNameRegex().test(data.firstName ?? ''),
    data.insuranceProtectionPeriod.from || data.insuranceProtectionPeriod.to,
    data.monthlyIncome.from || data.monthlyIncome.to,
    data.productCurrency,
  ];

  return {
    isComplete: conditions.every(condition => <PERSON><PERSON><PERSON>(condition)),
    missingFields: conditions.filter(condition => !condition).length,
  };
};
