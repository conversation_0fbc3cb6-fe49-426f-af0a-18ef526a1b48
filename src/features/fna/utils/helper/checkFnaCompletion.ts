import { country } from 'utils/context';
import { FnaState, useFnaStore } from '../store/fnaStore';
import { checkLifeJourneyCompletion as checkLifeJourneyCompletionIb } from './ib/checkFnaCompletion';
import { checkLifeJourneyCompletion as checkLifeJourneyCompletionId } from './id/checkFnaCompletion';
import { checkLifeJourneyCompletion as checkLifeJourneyCompletionPh } from './ph/checkFnaCompletion';

import { useFnaConcerns } from 'features/fna/hooks/useFnaConcerns';
import { useMemo } from 'react';

export const useCheckFnaCompletion = (): boolean => {
  const data = useFnaStore(state => state.lifeJourney);
  const { minSelection } = useFnaConcerns();
  return useMemo(() => {
    if (
      !getCheckLifeJourneyCompletion(data) ||
      data.concerns.length < minSelection
    ) {
      return false;
    } else {
      return true;
    }
  }, [data, minSelection]);
};

export const getCheckLifeJourneyCompletion = (
  data: FnaState['lifeJourney'],
): boolean => {
  switch (country) {
    case 'my':
    case 'ib':
      return checkLifeJourneyCompletionIb(data).isComplete;
    case 'ph':
      return checkLifeJourneyCompletionPh(data).isComplete;
    case 'id':
      return checkLifeJourneyCompletionId(data).isComplete;
    default:
      return checkLifeJourneyCompletionPh(data).isComplete;
  }
};

export const useGetLifeJourneyIncompleteFields = (): number => {
  const data = useFnaStore(state => state.lifeJourney);
  const { minSelection } = useFnaConcerns();
  return useMemo(
    () =>
      getLifeJourneyIncompleteFields(data) +
      (data.concerns.length < minSelection ? 1 : 0),
    [data, minSelection],
  );
};

export const getLifeJourneyIncompleteFields = (
  data: FnaState['lifeJourney'],
): number => {
  switch (country) {
    case 'my':
    case 'ib':
      return checkLifeJourneyCompletionIb(data).missingFields;
    case 'ph':
      return checkLifeJourneyCompletionPh(data).missingFields;
    case 'id':
      return checkLifeJourneyCompletionId(data).missingFields;
    default:
      return checkLifeJourneyCompletionPh(data).missingFields;
  }
};
