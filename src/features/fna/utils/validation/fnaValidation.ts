import { DependentConfiguration } from 'features/fna/types/lifeJourney';
import { DependentRelationship } from 'types/case';
import { Gender } from 'types/person';
import { object, string, array, number, InferType } from 'yup';
import { invalidFormatMessage, requiredInputMessage } from './fnaErrorMessages';

export const maxNameLength = 60;
export const maxEmailLength = 50;
export const maxMobileLength = 16;
export const mobileWithout0PrefixRegex = /^[1-9][0-9]*$/;
export const emailRegex = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+.[a-zA-Z0-9-.]+$/;

export const nameSchema = string()
  .required(requiredInputMessage)
  .max(maxNameLength, invalidFormatMessage)
  .validateName(invalidFormatMessage);

export const firstNameAndLastNameSchema = object({
  firstName: nameSchema,
  lastName: nameSchema,
  middleName: string()
    .max(maxNameLength, invalidFormatMessage)
    .validateName(invalidFormatMessage),
});

export const dependentsSchema = (config: DependentConfiguration) => {
  let gender = string().oneOf(Object.values(Gender));
  if (config.genderRequired) gender = gender.required(requiredInputMessage);

  return object({
    dependents: array()
      .of(
        object().shape({
          id: string(),
          relationship: string()
            .oneOf(Object.values(DependentRelationship))
            .required(),
          gender,
          age: number().integer().positive().required(),
        }),
      )
      .test({
        test: arr => !config.required || (arr && arr.length > 0),
      }),
  });
};

export type DependentForm = InferType<ReturnType<typeof dependentsSchema>>;
