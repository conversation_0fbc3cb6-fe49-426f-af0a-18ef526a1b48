import { array, date, InferType, number, object, string } from 'yup';
import { requiredInputMessage } from './fnaErrorMessages';
import { BenefitCoverage } from 'types/case';

export const existingPoliciesSchema = object({
  policies: array()
    .of(
      object({
        id: string(),
        productLine: string().required(requiredInputMessage),
        insuredName: string().required(requiredInputMessage),
        totalPremium: number().required(requiredInputMessage),
        paymentMode: string().required(requiredInputMessage),
        maturityDate: date().required(requiredInputMessage),
        premiumBenefits: array()
          .of(
            object({
              coverage: string()
                .oneOf(Object.values(BenefitCoverage))
                .required(requiredInputMessage),
              totalPremium: number().nullable(),
            }),
          )
          .defined(),
      }),
    )
    .required()
    .defined(),
});

export type ExistingPoliciesFormType = InferType<typeof existingPoliciesSchema>;
