import { useMemo } from 'react';
import { DependentRelationship } from 'types/case';
import { country } from 'utils/context';
import {
  ID_MAX_DEPENDENTS,
  MY_MAX_DEPENDENTS,
  PH_MAX_KIDS,
} from '../constants/educationConstants';
import { useFnaStore } from '../utils/store/fnaStore';

export const useGetMaxKids = () => {
  const dependents = useFnaStore(state => state.lifeJourney.dependents);

  const totalNonKidDependents = useMemo(
    () =>
      dependents.filter(d => ![DependentRelationship.kid, DependentRelationship.grandchild].includes(d.relationship))
        .length,
    [dependents],
  );
  switch (country) {
    case 'ph':
      return PH_MAX_KIDS;
    case 'id':
      return ID_MAX_DEPENDENTS;
    case 'my':
    case 'ib':
      return MY_MAX_DEPENDENTS - totalNonKidDependents;
  }
};
