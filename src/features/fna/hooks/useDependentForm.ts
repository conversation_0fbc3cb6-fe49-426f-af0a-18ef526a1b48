import { useEffect, useMemo } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import * as Crypto from 'expo-crypto';
import { DependentModalProps } from '../components/lifeJourney/formInput/dependents/DependentModal';
import { useValidationYupResolver } from 'utils/validation';
import {
  DependentForm,
  dependentsSchema,
} from 'features/fna/utils/validation/fnaValidation';
import { DependentRelationship } from 'types/case';
import { DEPENDENT_ICONS } from '../components/lifeJourney/formInput/dependents/Dependents';
import { useTranslation } from 'react-i18next';
import { countryModuleFnaConfig } from 'utils/config/module';

type UseDependentFormProps = DependentModalProps;

const useDependentForm = ({ data, visible, config }: UseDependentFormProps) => {
  const resolver = useValidationYupResolver(dependentsSchema(config));
  const { t } = useTranslation(['fna']);

  const emptyDependentForm = useMemo(
    () => ({
      relationship: config.dependentType ?? ('' as DependentRelationship),
      age: '' as unknown as number,
    }),
    [config],
  );

  const {
    control,
    handleSubmit,
    formState: { isValid },
    reset,
  } = useForm<DependentForm>({
    mode: 'onBlur',
    defaultValues: {
      dependents: data,
    },
    resolver,
  });

  const relationshipItems = useMemo(
    () =>
      countryModuleFnaConfig.dependentRelationships.map(value => {
        return {
          value,
          text: t(
            `fna:lifeStage.detail.question.dependentRelationship.${value}` as const,
          ),
          icon: DEPENDENT_ICONS[value],
        };
      }),
    [t],
  );

  useEffect(() => {
    if (visible) {
      reset(
        data.length === 0
          ? { dependents: [{ id: Crypto.randomUUID(), ...emptyDependentForm }] }
          : { dependents: data },
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, data]);

  const {
    fields: dependents,
    append,
    remove,
  } = useFieldArray({
    name: 'dependents',
    control,
    keyName: 'dependentKey',
  });

  const addDependent = () => {
    append({
      id: Crypto.randomUUID(),
      ...emptyDependentForm,
    });
  };

  return {
    control,
    handleSubmit,
    formState: { isValid },
    reset,
    relationshipItems,
    dependents,
    addDependent,
    remove,
  };
};

export default useDependentForm;
