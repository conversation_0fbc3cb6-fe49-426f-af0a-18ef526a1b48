import { shallow } from 'zustand/shallow';
import { useFnaStore } from '../utils/store/fnaStore';
import { useMemo } from 'react';
import { countryModuleFnaConfig } from 'utils/config/module';
import { calculateAge } from 'utils/helper/calculateAge';
import { MAX_AGE_RETIREMENT } from '../constants/goalCalculation';

export const useAdditionalQuestions = (readonly?: boolean) => {
  const { lifeStage, dob } = useFnaStore(
    state => ({
      lifeStage: state.lifeJourney.lifeStage,
      dob: state.lifeJourney.dob,
    }),
    shallow,
  );

  return useMemo(() => {
    const questions = (
      lifeStage
        ? countryModuleFnaConfig.lifeStageItems[lifeStage] || []
        : []
    ).map(type => (typeof type === 'string' ? { type } : type));
    if (readonly) {
      // move dependents question to last position in readonly mode
      const dependentsIdx = questions.findIndex(
        question => question.type === 'dependents',
      );
      if (dependentsIdx > -1) {
        const dependents = questions.splice(dependentsIdx, 1)[0];
        questions.push(dependents);
      }
    }
    return questions.filter(q => {
      if (q.type === 'ageToRetire') {
        return dob && calculateAge(dob) < MAX_AGE_RETIREMENT;
      }
      return true;
    });
  }, [dob, lifeStage, readonly]);
};
