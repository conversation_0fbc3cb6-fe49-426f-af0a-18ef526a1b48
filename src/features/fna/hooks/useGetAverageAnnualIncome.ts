import { useMemo } from 'react';
import { useFnaStore } from '../utils/store/fnaStore';

export const useGetAverageAnnualIncome = () => {
  const annuallyIncome = useFnaStore(state => state.lifeJourney.annuallyIncome);
  return useMemo(() => {
    if (annuallyIncome.from === null && annuallyIncome.to === null) return 0;
    if (annuallyIncome.from === null && annuallyIncome.to !== null)
      return annuallyIncome.to;
    if (annuallyIncome.from !== null && annuallyIncome.to === null)
      return annuallyIncome.from;
    return Math.round(
      ((annuallyIncome.from || 0) + (annuallyIncome.to || 0)) / 2,
    );
  }, [annuallyIncome.from, annuallyIncome.to]);
};
