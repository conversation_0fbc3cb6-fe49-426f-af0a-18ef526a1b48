import useBoundStore from 'hooks/useBoundStore';
import { useGetCaseManually } from 'hooks/useGetCase';
import { parseFna, useFnaStore } from '../utils/store/fnaStore';
import { shallow } from 'zustand/shallow';
import { useEffect, useRef, useState } from 'react';
import { Case, CaseStatus } from 'types/case';
import { Party, PartyRole } from 'types/party';
import { parse } from 'date-fns';
import { Gender } from 'types/person';
import { useGetLeadByLeadIdManually } from 'hooks/useGetLeads';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { useGetFnaByLeadId } from 'hooks/useGetLeadFna';
import { Lead } from 'types';

export const usePopulateFna = () => {
  const { caseId, inquiringLeadId } = useBoundStore(
    state => ({
      caseId: state.case.caseId,
      inquiringLeadId: state.lead.inquiringLeadId,
    }),
    shallow,
  );
  const { mutateAsync: getLeadById, isLoading: isGettingLead } =
    useGetLeadByLeadIdManually();
  const { mutateAsync: getCase, isLoading: isGettingCase } =
    useGetCaseManually();
  const setExistingLead = useFnaStore(state => state.setExistingLead);
  const [isFreshStart, setIsFreshStart] = useState(false);
  const {
    updateProfileName,
    updateGender,
    updateDob,
    updateFna,
    existingLead,
  } = useFnaStore(
    state => ({
      updateProfileName: state.updateProfileName,
      updateGender: state.updateGender,
      updateDob: state.updateDob,
      updateFna: state.updateFna,
      setExistingLead: state.setExistingLead,
      existingLead: state.existingLead,
    }),
    shallow,
  );

  const [activeCase, setCase] = useState<Case>();
  const [proposer, setProposer] = useState<Party>();
  const [lead, setLead] = useState<Lead>();
  const [hasFnaRecord, setHasFnaRecord] = useState(false);
  const finalLeadId = proposer?.leadId || lead?.id;

  const { data: lastFna } = useGetFnaByLeadId(finalLeadId);

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const loadingTimeout = useRef<NodeJS.Timeout>();
  useEffect(() => {
    clearTimeout(loadingTimeout.current);
    if (isGettingCase || isGettingLead) {
      loadingTimeout.current = setTimeout(setAppLoading, 1000);
    } else {
      setAppIdle();
    }
  }, [isGettingCase, setAppLoading, setAppIdle, isGettingLead]);

  const populated = useRef<boolean>(false);

  // fetch case and determine fresh start
  useEffect(() => {
    if (caseId) {
      getCase(caseId).then(caseObj => {
        setCase(caseObj);
        setIsFreshStart(false);
      });
    } else {
      setIsFreshStart(!inquiringLeadId);
    }
  }, [caseId, getCase, inquiringLeadId]);

  // populate from saved fna or prepare lead for last fna
  useEffect(() => {
    if (activeCase) {
      if (
        activeCase.latestStatus === CaseStatus.FNA &&
        activeCase.fna &&
        !populated.current
      ) {
        //
        updateFna(parseFna(activeCase.fna));
        setHasFnaRecord(true);
        populated.current = true;
      } else if (activeCase.status?.includes(CaseStatus.COVERAGE)) {
        const activeProposer = activeCase.parties?.find(p =>
          p.roles.includes(PartyRole.PROPOSER),
        );
        if (activeProposer && !proposer) {
          setProposer(activeProposer);
        }
      }
    } else if (!lead && !proposer) {
      if (inquiringLeadId) {
        getLeadById(inquiringLeadId).then(l => {
          setExistingLead(l);
          setLead(l);
        });
      } else {
        setLead(existingLead ?? undefined);
      }
    }
  }, [
    activeCase,
    proposer,
    lead,
    inquiringLeadId,
    existingLead,
    updateFna,
    getLeadById,
  ]);

  // fetch and populate last fna if not already
  useEffect(() => {
    if (lastFna && !populated.current) {
      updateFna(parseFna(lastFna));
      setHasFnaRecord(true);
      populated.current = true;
    }

    if (proposer) {
      updateProfileName(
        proposer?.person?.name?.firstName || '',
        proposer?.person?.name?.middleName || '',
        proposer?.person?.name?.lastName || '',
      );
      updateGender(proposer.person.gender);
      updateDob(
        parse(proposer.person.dateOfBirth.date, 'yyyy-MM-dd', new Date()),
      );
    } else if (lead) {
      console.debug('populate from lead profile');
      updateProfileName(lead.firstName, lead.middleName, lead.lastName);
      updateGender(lead.genderCode as Gender);
      if (lead.birthDate) {
        updateDob(parse(lead.birthDate, 'yyyy-MM-dd', new Date()));
      }
    }
  }, [
    lastFna,
    proposer,
    lead,
    updateFna,
    updateProfileName,
    updateGender,
    updateDob,
  ]);

  return { isFreshStart, hasFnaRecord };
};
