import { format, parse } from 'date-fns';
import { useFormatLeadToIndividualParty } from 'features/coverageDetails/hooks/common/partyUtils';
import { useCreateLead } from 'features/lead/hooks/useCreateLead';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateCase } from 'hooks/useCreateCase';
import { useCreateCFF } from 'hooks/useCreateCFF';
import { useCreateFna } from 'hooks/useCreateFna';
import { useGetLeadByLeadIdManually } from 'hooks/useGetLeads';
import { useSaveParty } from 'hooks/useParty';
import { useCallback } from 'react';
import { CreateLeadRequest, Lead } from 'types';
import { Party } from 'types/party';
import { Gender } from 'types/person';
import { country } from 'utils/context';
import { calculateAge } from 'utils/helper/calculateAge';
import { shallow } from 'zustand/shallow';
import { useFnaStore } from '../utils/store/fnaStore';

export interface SaveFnaParams {
  isCompleted: boolean;
  leadRequest?: CreateLeadRequest;
  party?: Party;
}

export const useSaveFna = () => {
  const formatLead = useFormatLeadToIndividualParty();
  const { saveParty } = useSaveParty();
  const { mutateAsync: getLeadById, isLoading: isGettingLead } =
    useGetLeadByLeadIdManually();
  const { mutateAsync: createCase, isLoading: isCreatingCase } =
    useCreateCase();
  const { mutateAsync: createFna, isLoading: isCreatingFna } = useCreateFna();
  const { mutateAsync: createLead, isLoading: isCreatingLead } =
    useCreateLead();
  const { setActiveCase, caseId, agentCode } = useBoundStore(
    state => ({
      setActiveCase: state.caseActions.setActiveCase,
      caseId: state.case.caseId,
      agentCode: state.auth.agentCode,
    }),
    shallow,
  );
  const {
    adviceType,
    vulnerable,
    existingLead,
    lifeJourney,
    educationGoal,
    retirementGoal,
    investmentGoal,
    incomeProtectionGoal,
    healthProtectionGoal,
    legacyPlanningGoal,
    loanCoverageGoal,
    savingsGoal,
  } = useFnaStore(
    state => ({
      adviceType: state.adviceType,
      vulnerable: state.vulnerable,
      existingLead: state.existingLead,
      lifeJourney: state.lifeJourney,
      educationGoal: state.educationGoal,
      retirementGoal: state.retirementGoal,
      investmentGoal: state.investmentGoal,
      incomeProtectionGoal: state.incomeProtectionGoal,
      healthProtectionGoal: state.healthProtectionGoal,
      legacyPlanningGoal: state.legacyPlanningGoal,
      loanCoverageGoal: state.loanCoverageGoal,
      savingsGoal: state.savingsGoal,
    }),
    shallow,
  );
  const saveFna = useCallback(
    async ({ isCompleted, leadRequest, party }: SaveFnaParams) => {
      if (!lifeJourney.lifeStage || !lifeJourney.gender) {
        throw new Error('invalid data: lifeStage or gender');
      }
      let validCaseId = caseId;
      if (!validCaseId) {
        const newCaseId = await createCase({ agent: { id: agentCode ?? '' } });
        validCaseId = newCaseId;
        setActiveCase(newCaseId);
      }
      await createFna({
        caseId: validCaseId,
        fna: {
          ...lifeJourney,
          expectedIncomeIncrement: lifeJourney.expectedIncomeIncrement ?? 0,
          totalAssets: lifeJourney.totalAssets ?? 0,
          totalLiabilities: lifeJourney.totalLiabilities ?? 0,
          existingPolicies: (lifeJourney.existingPolicies || []).map(p => ({
            ...p,
            maturityDate: p.maturityDate.toISOString(),
          })),
          concerns: lifeJourney.concerns,
          isCompleted,
          lifeStage: lifeJourney.lifeStage,
          dateOfBirth: {
            date: lifeJourney.dob ? format(lifeJourney.dob, 'yyyy-MM-dd') : '',
          },
          gender: lifeJourney.gender,
          goalPercentage: lifeJourney.goalPercentage,
          dependents: lifeJourney.dependents || [],
          educationGoal: {
            ...educationGoal,
            goals: educationGoal.goals.map(goal => ({
              ...goal,
              dateOfBirth: goal.dateOfBirth
                ? {
                    date: format(goal.dateOfBirth, 'yyyy-MM-dd'),
                  }
                : null,
            })),
          },
          retirementGoal,
          investmentGoal,
          incomeProtectionGoal,
          healthProtectionGoal,
          legacyPlanningGoal,
          loanCoverageGoal,
          savingsGoal,
          vulnerable,
          acknowledge: false,
          adviceType: adviceType,
          productWithPremiumDevelopmentPotential: Boolean(
            lifeJourney.productWithPremiumDevelopmentPotential,
          ),
          productCurrency: lifeJourney.productCurrency ?? '',
        },
      });
      if (party) {
        const newParty: Party = {
          ...party,
          person: {
            ...party.person,
            name: {
              ...party.person?.name,
              title: lifeJourney?.title ?? party.person?.name?.title,
              firstName: lifeJourney.firstName,
              lastName: lifeJourney.lastName,
            },
            dateOfBirth: {
              ...party.person?.dateOfBirth,
              date: format(lifeJourney.dob ?? new Date(), 'yyyy-MM-dd'),
            },
            age: lifeJourney.dob
              ? calculateAge(lifeJourney.dob)
              : party.person?.age,
            gender: lifeJourney.gender ?? Gender.MALE,
          },
        };
        await saveParty(newParty, { caseId: validCaseId });
      }
      let lead: Lead | null = null;
      if (!party && existingLead) {
        lead = existingLead;
      }
      if (leadRequest) {
        const leadId = await createLead({
          caseId: validCaseId,
          birthDate: format(lifeJourney.dob ?? new Date(), 'yyyy-MM-dd'),
          genderCode: lifeJourney.gender ?? Gender.MALE,
          ...leadRequest,
        });
        lead = await getLeadById(String(leadId));
      }
      if (lead) {
        const formattedParty = formatLead(lead);
        const owner: Party = {
          ...formattedParty,
          person: {
            ...formattedParty.person,
            name: {
              ...formattedParty.person?.name,
              title: lifeJourney?.title ?? formattedParty.person?.name?.title,
              firstName:
                lifeJourney?.firstName ??
                formattedParty.person?.name?.firstName,
              lastName:
                lifeJourney?.lastName ?? formattedParty.person?.name?.lastName,
            },
            dateOfBirth: {
              date: lifeJourney.dob
                ? format(lifeJourney.dob, 'yyyy-MM-dd')
                : lead.birthDate,
            },
            gender: lifeJourney.gender ?? (lead.genderCode as Gender),
            age: lifeJourney.dob
              ? calculateAge(lifeJourney.dob)
              : calculateAge(parse(lead.birthDate, 'yyyy-MM-dd', new Date())),
          },
        };

        await saveParty(owner, { caseId: validCaseId });
      }

      return validCaseId;
    },
    [
      lifeJourney,
      caseId,
      createFna,
      educationGoal,
      retirementGoal,
      investmentGoal,
      incomeProtectionGoal,
      healthProtectionGoal,
      legacyPlanningGoal,
      loanCoverageGoal,
      savingsGoal,
      vulnerable,
      adviceType,
      existingLead,
      createCase,
      agentCode,
      setActiveCase,
      saveParty,
      createLead,
      getLeadById,
      formatLead,
    ],
  );

  return {
    saveFna: saveFna,
    isLoading:
      isGettingLead || isCreatingCase || isCreatingFna || isCreatingLead,
  };
};
