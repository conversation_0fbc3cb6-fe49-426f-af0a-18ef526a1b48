import { DependentRelationship } from 'types/case';
import {
  MY_MAX_DEPENDENTS,
  PH_MAX_KIDS,
  PH_MAX_PARTNER,
} from '../constants/educationConstants';

export type LifeStage =
  | 'SINGLE'
  | 'SINGLE_WITH_DEPENDENT'
  | 'COUPLE'
  | 'COUPLE_WITH_KIDS'
  | 'EMPTY_NESTER'
  | 'RETIRED';

export type DependentConfiguration = {
  type: 'dependents';
  required?: true;
  max?: number;
  dependentType?: DependentRelationship;
  genderRequired: boolean;
};

export type LifeJourneyQuestion =
  | 'numberOfKids'
  | 'havePartner'
  | 'planToTravel'
  | 'ageToRetire'
  | DependentConfiguration;

export const myNumberOfDependents: DependentConfiguration = {
  type: 'dependents',
  max: MY_MAX_DEPENDENTS,
  required: true,
  genderRequired: true,
};

export const phNumberOfKids: DependentConfiguration = {
  type: 'dependents',
  required: true,
  max: PH_MAX_KIDS,
  genderRequired: false,
  dependentType: DependentRelationship.kid,
};

export const phHavePartner: DependentConfiguration = {
  type: 'dependents',
  max: PH_MAX_PARTNER,
  genderRequired: false,
  dependentType: DependentRelationship.spouse,
};

export type FinancialBudgetType = '<10' | '10-14' | '15-19' | '20-24' | '>=25';

export interface LifeStageItemType {
  id?: number;
  title?: string;
  img: React.ReactElement;
  maxWidth?: number;
  lifeStage: LifeStage;
}
