import React, { ReactNode, useEffect, useMemo, useState } from 'react';
import {
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { useTheme } from '@emotion/react';
import OverviewScreen from 'screens/HomeScreen/OverviewScreen';
import MyTasksScreen from 'screens/HomeScreen/MyTasksScreen';
import WelcomeSection, {
  HOME_SCREEN_HORIZONTAL_PADDING_SPACE,
  ShowComponentHandle,
} from 'features/home/<USER>/WelcomeSection/phone';
import useBoundStore from 'hooks/useBoundStore';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useGetOptionList } from 'hooks/useGetOptionList';
import AppTopTabBar from 'components/AppTopTabBar';
import { useTranslation } from 'react-i18next';
import { useBottomBar } from 'hooks/useBottomBar';

import { DateUtil } from 'utils';
import ApplicationRatingModal from 'features/home/<USER>/ApplicationRatingModal';
import { country } from 'utils/context';
import useEcoachConfiguration from 'features/ecoach/hooks/useEcoachConfiguration';
import { popBiometricDialog } from 'features/login/biometric';
import AgentRewardModal from 'features/agentRewardModal';
import ScrollViewPanGestureWrapper from 'features/home/<USER>/components/ScrollViewPanGestureWrapper';
import { checkSurveySetting } from 'hooks/useCheckSurveySetting';
import isToday from 'date-fns/esm/isToday/index';
import { useTermsConditionsUrl } from '../hooks/useTermsConditionsUrl';
import PdfViewer from 'features/pdfViewer/components/PdfViewer';
import { PdfProps } from 'react-native-pdf';
import { useGetProductExtraConfig } from 'hooks/useGetProductExtraConfig';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetFeatureFlags } from 'hooks/useGetFeatureFlags';
import { ScriptTextPhone } from 'features/voicePrint/VoiceModal';
import reactStringReplace from 'react-string-replace';
import VoiceModal from 'features/voicePrint/VoiceModal';
import { cancelUploadVoicePrint } from 'api/authApi';
import { useUploadAgentVoice } from 'hooks/useUploadAgentVoice';
import { Label } from 'features/policy/components/POSScreen/tablet/POSStyle';
import { ExtraLargeBody, H7 } from 'cube-ui-components';

const { wideScreen, defaultV, narrowScreen } =
  HOME_SCREEN_HORIZONTAL_PADDING_SPACE;

export default function HomeScreenPhone() {
  const { t: ct } = useTranslation('common');
  // const { t: tHome } = useTranslation('home');
  const { t } = useTranslation(['navigation', 'common', 'home', 'voicePrint']);

  const { colors, sizes } = useTheme();
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();

  const { refetch: refetchEcoachConfiguration } = useEcoachConfiguration();
  const { data: agentProfile } = useGetAgentProfile();
  const { data: featureFlags } = useGetFeatureFlags();
  const isVoicePrintFeatureOn =
    featureFlags?.voice_recording.available ?? false;
  const {
    mutate: uploadAgentVoiceSync,
    isLoading,
    isSuccess,
    reset,
    error: uploadVoiceErr,
  } = useUploadAgentVoice();

  const { showBottomBar, hideBottomBar } = useBottomBar();
  const [isVoiceModalShown, setIsVoiceModalShown] = useState(false);
  const hasVoiceRecorded = agentProfile?.hasVoiceRecorded ?? true;
  const HomeTab = createMaterialTopTabNavigator();
  const windowHeight = Dimensions.get('window').height;

  useGetOptionList();
  useGetProductExtraConfig();

  const showComponentRef = React.useRef<ShowComponentHandle>(null);

  const [isWebViewShow, setIsWebViewShow] = useState<boolean>(false);

  const scrollToShow = () => {
    showComponentRef.current?.showComponent();
    showComponentRef.current?.allowPress();
    // showBottomBar();
  };

  const scrollToHide = () => {
    showComponentRef.current?.hideComponent();
    showComponentRef.current?.disablePress();
    // hideBottomBar();
  };

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    if (currentOffset > windowHeight * 0.2) {
      scrollToHide();
    } else if (currentOffset <= 0) {
      scrollToShow();
    }
  };

  const firstLoginTime = useBoundStore(store => store.firstLoginTime);
  const lastSurveyTriggerTime = useBoundStore(
    store => store.lastSurveyTriggerTime,
  );

  const isMorningMode = useBoundStore(state => state.home.isMorningMode);

  const checkIsMorningMode = useBoundStore(
    store => store.homeActions.checkIsMorningMode,
  );

  const setFirstLoginTime = useBoundStore(
    store => store.appActions.setFirstLoginTime,
  );
  const setLastSurveyTriggerTime = useBoundStore(
    store => store.appActions.setLastSurveyTriggerTime,
  );

  useFocusEffect(
    React.useCallback(() => {
      refetchEcoachConfiguration();
    }, []),
  );

  useFocusEffect(
    React.useCallback(() => checkIsMorningMode(), [isMorningMode]),
  );

  const isFocused = useIsFocused();

  useEffect(() => {
    if (!isFocused) {
      return;
    }
    const currentTime = new Date();

    const surveySettingHandler = async () => {
      const { day_of_month } = await checkSurveySetting();
      if (day_of_month == undefined) {
        console.log(
          '🔴🔴🔴HomeScreenPhone.tsx:116 ~ surveySettingHandler NO day_of_month',
        );
      }
      const isTodayTheDayToCheck = day_of_month
        ? currentTime.getDate() === day_of_month
        : false;
      const hasShownToday = lastSurveyTriggerTime
        ? isToday(new Date(Date.parse(lastSurveyTriggerTime)))
        : false;

      if (isTodayTheDayToCheck && !hasShownToday) {
        setIsWebViewShow(true);
        setLastSurveyTriggerTime(currentTime.toISOString());
      }
    };

    surveySettingHandler();
  }, [isFocused, lastSurveyTriggerTime, setLastSurveyTriggerTime]);

  const spaceCheckedNarrow = isNarrowScreen ? narrowScreen : defaultV;

  const commonStyle = {
    paddingVertical: sizes[3],
    paddingHorizontal: sizes[isWideScreen ? wideScreen : spaceCheckedNarrow],
  };

  const isBiometricEnabled = useBoundStore(
    state => state.auth.bioMetricEnabled,
  );
  const enableBiometric = useBoundStore(
    state => state.authActions.enableBiometric,
  );
  const disableBiometric = useBoundStore(
    state => state.authActions.disableBiometric,
  );

  const bioMetricInfo = useBoundStore(state => state.auth.bioMetricInfo);

  const showTermsAndConditions = useBoundStore(
    state => state.home.showTermsAndConditions,
  );

  const onTermsAndConditionsAgree = useBoundStore(
    state => state.homeActions.onTermsAndConditionsAgree,
  );

  useEffect(() => {
    if (
      !showTermsAndConditions &&
      isBiometricEnabled === null &&
      bioMetricInfo.supported.length > 0
    ) {
      popBiometricDialog(
        ct,
        bioMetricInfo.supported,
        () => {
          enableBiometric();
        },
        { onCancel: () => disableBiometric() },
      );
    }
  }, [
    isBiometricEnabled,
    showTermsAndConditions,
    bioMetricInfo.supported.length,
  ]);

  const [pdfUrl, pdfGenerator] = useTermsConditionsUrl();

  const pdfOption = useMemo<Partial<PdfProps>>(() => {
    return {
      fitPolicy: 0,
      scale: 1,
      minScale: 1,
    };
  }, []);

  useEffect(() => {
    if (isVoicePrintFeatureOn && !hasVoiceRecorded) {
      setIsVoiceModalShown(true);
    }
  }, [isVoicePrintFeatureOn, hasVoiceRecorded]);

  const voicePrintScript = useMemo(() => {
    let voicePrintText: string | ReactNode[] | undefined = t(
      'voicePrint:script.home',
    );
    voicePrintText = reactStringReplace(
      voicePrintText,
      t('voicePrint:script.agentName'),
      () => (
        <ExtraLargeBody
          key={'agentName'}
          fontWeight="bold"
          color={colors.primary}>
          {
            //eslint-disable-next-line react-native/no-raw-text
            `[${agentProfile?.person.fullName ?? ''}]`
          }
        </ExtraLargeBody>
      ),
    );

    voicePrintText = reactStringReplace(
      voicePrintText,
      t('voicePrint:script.agentTitle'),
      () => (
        <ExtraLargeBody
          key={'agentTitle'}
          fontWeight="bold"
          color={colors.primary}>
          {
            //eslint-disable-next-line react-native/no-raw-text
            `[${agentProfile?.designation ?? ''}]`
          }
        </ExtraLargeBody>
      ),
    );

    return voicePrintText;
  }, [
    agentProfile?.designation,
    agentProfile?.person.fullName,
    colors.primary,
    t,
  ]);

  return (
    <>
      {isVoicePrintFeatureOn && isVoiceModalShown && (
        <VoiceModal
          uiMode="phone"
          visible={isVoiceModalShown}
          instruction={t('voicePrint:instruction.home')}
          script={voicePrintScript}
          onPressDismiss={() => {
            setIsVoiceModalShown(false);
          }}
          cancelUpload={cancelUploadVoicePrint}
          onSubmit={fileUri => {
            uploadAgentVoiceSync(fileUri);
          }}
          isUploading={isLoading}
          isSuccessUpload={isSuccess}
          errorWhenUpload={uploadVoiceErr}
          resetUpload={reset}
        />
      )}
      {!showTermsAndConditions && (
        <ApplicationRatingModal
          visible={isWebViewShow}
          setIsVisible={setIsWebViewShow}
        />
      )}
      <SafeAreaView
        edges={['top']}
        style={{
          flex: 1,
          backgroundColor: isMorningMode ? colors.primary : colors.secondary,
        }}>
        <WelcomeSection ref={showComponentRef} />
        <HomeTab.Navigator
          tabBar={props => <AppTopTabBar variant="scrollable" {...props} />}
          screenOptions={{ swipeEnabled: false }}>
          <HomeTab.Screen
            name="Overview"
            options={{ tabBarLabel: t('navigation:tabScreen.Overview') }}
            children={() => (
              <ScrollViewPanGestureWrapper
                onShow={scrollToShow}
                onHide={scrollToHide}
                handleScroll={handleScroll}
                style={[
                  commonStyle,
                  {
                    backgroundColor: colors.background,
                  },
                ]}
                contentContainerStyle={{ paddingBottom: sizes[30] }}
                showsVerticalScrollIndicator={false}
                scrollEventThrottle={16}>
                <OverviewScreen />
              </ScrollViewPanGestureWrapper>
            )}
          />
          <HomeTab.Screen
            name="MyTasks"
            options={{ tabBarLabel: t('navigation:tabScreen.MyTasks') }}
            children={() => (
              <ScrollViewPanGestureWrapper
                onShow={scrollToShow}
                onHide={scrollToHide}
                handleScroll={handleScroll}
                style={[
                  commonStyle,
                  {
                    backgroundColor: colors.primaryVariant2,
                  },
                ]}
                contentContainerStyle={{ paddingBottom: sizes[30] }}
                showsVerticalScrollIndicator={false}
                scrollEventThrottle={16}>
                <MyTasksScreen />
              </ScrollViewPanGestureWrapper>
            )}
          />
        </HomeTab.Navigator>
      </SafeAreaView>
      <AgentRewardModal />
      {pdfUrl && (
        <PdfViewer
          title={''}
          onClose={() => {
            onTermsAndConditionsAgree();
          }}
          visible={showTermsAndConditions}
          pdfGenerator={pdfGenerator}
          actionOption={{
            activeMode: 'end-of-file',
            disabledMode: 'exclude-zero',
            actionMode: 'default',
            text: t('home:home.agree'),
            onPress: () => {
              onTermsAndConditionsAgree();
            },
          }}
          closable={false}
          pdfOption={pdfOption}
        />
      )}
    </>
  );
}
