import { ecoachClient } from 'features/ecoach/api/ecoachClient';
import useBoundStore from 'hooks/useBoundStore';

export type Config = {
  updated_at: string;
  value: any;
  created_at: string;
  key: string;
  uid: string;
  country?: string;
};

export type ConfigurationData = {
  config: Config[];
};

export const getConfigurationData = async (): Promise<Config[]> => {
  const state = useBoundStore.getState();
  const token = state.auth.authInfo?.accessToken;
  // console.log('getConfigurationData token', token);
  const response = await ecoachClient.get<Config[]>(
    `/configuration/list`,
    {
      headers: { access_token: token },
    },
  );
  // console.log('getConfigurationData response', JSON.stringify(response.data));
  // return response.data;

  return [{
    "updated_at": "2025-09-10T03:10:06.334242+00:00",
    "value": 1,
    "created_at": "2025-07-04T03:36:36.009604+00:00",
    "key": "quickfire_completed_hearts",
    "country": "id",
    "uid": "18cb35d6-287b-450f-9e7f-2123041f6eda"
  }, {
    "updated_at": "2025-09-10T03:09:01.802509+00:00",
    "value": [{
      "elevenlabs_product_code": "busy_bestie",
      "product_group_description": "these are your inner circle.You can speak to them casually,",
      "clickable": true,
      "channel": "agency",
      "use_elevenlabs": true,
      "language": "en-US",
      "personaSelection": "ib_young_female_voice_only",
      "product_code": "busy_bestie",
      "product_name": "Busy bestie",
      "personas": {
        "persona_age": 32,
        "persona_gender": "F",
        "persona_name": "Nadia"
      },
      "cai_metadata": {
        "mode": "full-experience",
        "context": {
          "customer_name": "Nadia",
          "customer_relationship": "Best Friend",
          "gender": "female",
          "product_name": "Set For Life",
          "conversation_scenario": "Busy bestie",
          "age_group": "young"
        },
        "subcomponents": {
          "country": "my",
          "scenario": "appointment-setting-warm",
          "resistance_type": "passive",
          "communication_style": "busy_impatient",
          "language": "en",
          "personas": "custom",
          "customer_segment": "young-professional",
          "products": "busy_bestie"
        }
      },
      "product_group": "Friends & Family",
      "product_description": "Your busy friend, highlight efficiency and offer brief, convenient options.",
      "order": 0
    }, {
      "elevenlabs_product_code": "skeptical_uncle",
      "product_group_description": "these are your inner circle.You can speak to them casually,",
      "clickable": true,
      "channel": "agency",
      "use_elevenlabs": true,
      "language": "en-US",
      "personaSelection": "ib_old_male_voice_only",
      "product_code": "skeptical_uncle",
      "product_name": "Skeptical uncle",
      "personas": {
        "persona_age": 52,
        "persona_gender": "M",
        "persona_name": "Bryan"
      },
      "cai_metadata": {
        "mode": "full-experience",
        "context": {
          "customer_name": "Bryan",
          "customer_relationship": "Uncle",
          "gender": "male",
          "product_name": "Set For Life",
          "conversation_scenario": "Skeptical uncle",
          "age_group": "old"
        },
        "subcomponents": {
          "country": "my",
          "scenario": "appointment-setting-warm",
          "resistance_type": "passive",
          "communication_style": "skeptical_cautious",
          "language": "en",
          "personas": "custom",
          "customer_segment": "young-professional",
          "products": "skeptical_uncle"
        }
      },
      "product_group": "Friends & Family",
      "product_description": "Cautious about insurance; assure them it's a no-pressure, informative chat.",
      "order": 1
    }, {
      "elevenlabs_product_code": "busy_bee_ex_schoolmate",
      "product_group_description": "People you are not close with. Speak in semi formal or formal way.",
      "clickable": true,
      "channel": "agency",
      "use_elevenlabs": true,
      "language": "en-US",
      "personaSelection": "ib_young_male_voice_only",
      "product_code": "busy_bee_ex_schoolmate",
      "product_name": "Busy bee ex-schoolmate",
      "personas": {
        "persona_age": 28,
        "persona_gender": "M",
        "persona_name": "Adam"
      },
      "cai_metadata": {
        "mode": "full-experience",
        "context": {
          "customer_name": "Adam",
          "customer_relationship": "Busy bee ex-schoolmate",
          "gender": "male",
          "product_name": "Set For Life",
          "conversation_scenario": "Busy bee ex-schoolmate",
          "age_group": "young"
        },
        "subcomponents": {
          "country": "my",
          "scenario": "appointment-setting-acquaintance",
          "resistance_type": "passive",
          "communication_style": "busy_impatient",
          "language": "en",
          "personas": "custom",
          "customer_segment": "young-professional",
          "products": "busy_bee_ex_schoolmate"
        }
      },
      "product_group": "Acquaintance",
      "product_description": "Build rapport and emphasize on main points and flexibility",
      "order": 2
    }],
    "created_at": "2025-09-10T03:09:01.802470+00:00",
    "key": "appointment_setting_product_selection",
    "country": "id",
    "uid": "4d997c45-cd5d-4dac-98cf-e74887815725"
  }, {
    "updated_at": "2025-09-10T03:10:19.952640+00:00",
    "value": [{
      "policy_type": "GoSecure",
      "product_image": "https://trainer-guru-sandbox-product-images.s3.amazonaws.com/IB-GoSecure.jpg?AWSAccessKeyId=AKIA2JOXVZZTWVTTOKGP&Signature=9xO1mdh2d1cWwZRoqWXAkndqhZM%3D&Expires=**********",
      "clickable": true,
      "channel": "agency",
      "language": "en-US",
      "product_description": "",
      "product_code": "ib-go-secure-quickfire",
      "product_name": "(EN) GoSecure & \nGoSecure+",
      "order": 0
    }, {
      "policy_type": "Max Wealth",
      "product_image": "https://trainer-guru-sandbox-product-images.s3.amazonaws.com/ib_max_wealth.png?AWSAccessKeyId=AKIA2JOXVZZTWVTTOKGP&Signature=%2FkTY35tpTK7smjwi9YhtwwP%2FNGo%3D&Expires=**********",
      "clickable": true,
      "channel": "agency",
      "language": "en-US",
      "product_description": "",
      "product_code": "ib-max-wealth-quickfire",
      "product_name": "(EN) Max Wealth",
      "order": 1
    }],
    "created_at": "2025-07-04T03:38:59.222916+00:00",
    "key": "quickfire_product_selection",
    "country": "id",
    "uid": "99c55b81-d0c9-441a-bee2-d03092c5ef6b"
  }, {
    "updated_at": "2025-09-10T03:10:50.346151+00:00",
    "value": "https://youtu.be/XqZsoesa55w?si=F2Qfs5mdY4hG-DtA",
    "created_at": "2025-09-10T03:06:01.992321+00:00",
    "key": "trainer_guru_tutorial_video",
    "country": "id",
    "uid": "21843ce8-c272-4fa0-9068-7c863a010384"
  }, {
    "updated_at": "2025-09-10T03:09:47.402797+00:00",
    "value": [{
      "policy_type": "Smart Start",
      "product_image": "https://trainer-guru-sandbox-product-images.s3.amazonaws.com/objection-handling-no-money.png?AWSAccessKeyId=AKIA2JOXVZZTWVTTOKGP&Signature=jq7YJW93i4xb%2Bc3laZT5pu4vP1A%3D&Expires=**********",
      "clickable": true,
      "channel": "agency",
      "language": "en-US",
      "product_description": "This customer’s objections are about budget concerns. Show empathy and help them see how insurance can fit their finances!",
      "product_code": "ph-smart-start-en-objection-handling-no-money",
      "product_name": "No Money",
      "order": 0
    }, {
      "policy_type": "Smart Start",
      "product_image": "https://trainer-guru-sandbox-product-images.s3.amazonaws.com/objection-handling-no-trust.png?AWSAccessKeyId=AKIA2JOXVZZTWVTTOKGP&Signature=Ag%2F9qhOX6%2FIhO1boZqQ8P0zbCls%3D&Expires=**********",
      "clickable": true,
      "channel": "agency",
      "language": "en-US",
      "product_description": "This customer's objections show a lack of trust, identify the objection and use examples and logic to handle them!",
      "product_code": "ph-smart-start-en-objection-handling-no-trust",
      "product_name": "No Trust",
      "order": 1
    }, {
      "policy_type": "Smart Start",
      "product_image": "https://trainer-guru-sandbox-product-images.s3.amazonaws.com/objection-handling-no-hurry.png?AWSAccessKeyId=AKIA2JOXVZZTWVTTOKGP&Signature=C7yMHUWAtXGHrYn1J%2B8E6PAuF7c%3D&Expires=**********",
      "clickable": true,
      "channel": "agency",
      "language": "en-US",
      "product_description": "This customer's objections show a lack of urgency towards buying insurance. Convince them of the value that insurance can bring!",
      "product_code": "ph-smart-start-en-objection-handling-no-hurry",
      "product_name": "No Hurry",
      "order": 2
    }],
    "created_at": "2025-09-10T03:09:47.402759+00:00",
    "key": "objection_handling_product_selection",
    "country": "id",
    "uid": "13e9808f-fe82-46d4-829a-9cc3f808efba"
  }, {
    "updated_at": "2025-09-10T03:09:56.909527+00:00",
    "value": [{
      "product_image": "https://trainer-guru-sandbox-product-images.s3.amazonaws.com/IB-GoSecure.jpg?AWSAccessKeyId=AKIA2JOXVZZTWVTTOKGP&Signature=9xO1mdh2d1cWwZRoqWXAkndqhZM%3D&Expires=**********",
      "clickable": true,
      "channel": "agency",
      "language": "en-US",
      "product_description": "Sell this plan to a customer with the need to protect his income and business",
      "product_code": "GoSecure",
      "product_name": "(EN) GoSecure & \nGoSecure+",
      "order": 0
    }, {
      "policy_type": "WealthLink",
      "product_image": "https://trainer-guru-sandbox-product-images.s3.amazonaws.com/ib_wealthlink.png?AWSAccessKeyId=AKIA2JOXVZZTWVTTOKGP&Signature=bC1clN4WQ%2FPCoRIe%2FrDZ%2FGZLqpc%3D&Expires=**********",
      "clickable": true,
      "channel": "agency",
      "language": "en-US",
      "product_description": "Sell this plan to a customer with the need for comprehensive protection",
      "product_code": "ib-wealth-link",
      "product_name": "(EN) WealthLink",
      "order": 1
    }],
    "created_at": "2025-07-04T03:38:07.841914+00:00",
    "key": "product_selection",
    "country": "id",
    "uid": "629d07b7-adeb-4b9d-bc7b-d9e538e0d350"
  }, {
    "updated_at": "2025-07-04T03:39:39.813323+00:00",
    "value": 10,
    "created_at": "2025-07-04T03:39:39.813287+00:00",
    "key": "video_to_audio_speed",
    "country": "id",
    "uid": "3d9df340-7568-4a50-8a5c-b10328319bbc"
  }, {
    "updated_at": "2025-09-10T03:10:33.681107+00:00",
    "value": {
      "agency": true,
      "banca": true
    },
    "created_at": "2025-09-10T03:05:10.678739+00:00",
    "key": "trainer_guru_availability",
    "country": "id",
    "uid": "f4816387-851a-4f96-b601-61a8159269a8"
  }, {
    "updated_at": "2025-09-10T03:09:14.501369+00:00",
    "value": [{
      "background_image_tablet": "https://trainer-guru-sandbox-product-images.s3.amazonaws.com/id_homepage_background_tablet.png?AWSAccessKeyId=AKIA2JOXVZZTWVTTOKGP&Signature=Sj06sSCGJKkSRfqSav2zDWJ2zag%3D&Expires=**********",
      "background_image_mobile": "https://trainer-guru-sandbox-product-images.s3.amazonaws.com/id_homepage_background_mobile.png?AWSAccessKeyId=AKIA2JOXVZZTWVTTOKGP&Signature=pPUDlgAICP6fdLgnD5axhKIgrek%3D&Expires=**********"
    }],
    "created_at": "2025-07-04T03:35:40.605534+00:00",
    "key": "homepage_background",
    "country": "id",
    "uid": "a6ec8f34-5f94-4c3e-89ed-215e110bc3e8"
  }, {
    "updated_at": "2025-09-10T03:11:18.147453+00:00",
    "value": {
      "agency": {
        "appointment_setting": true,
        "product_knowledge": true,
        "face_to_face_meeting": true,
        "objection_handling": true
      },
      "banca": {
        "appointment_setting": false,
        "product_knowledge": false,
        "face_to_face_meeting": false,
        "objection_handling": false
      }
    },
    "created_at": "2025-09-10T03:09:28.043917+00:00",
    "key": "module_availability",
    "country": "id",
    "uid": "25b16ade-7271-4fdc-bff8-3cd1f013d20b"
  }];
};
