import useBoundStore from 'hooks/useBoundStore';
import { country } from 'utils/context';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { moduleConfigs } from 'utils/config/module';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { CubeIdToken } from 'types';
import { jwtDecode } from 'jwt-decode';
import { CHANNELS_BY_COUNTRY } from 'types/channel';

export const useEcoachEnable = () => {
  // Checking logic from agent profile api
  const { isTabletMode } = useLayoutAdoptionCheck();
  const channel = useGetCubeChannel();
  const idToken = useBoundStore.getState().auth.authInfo?.idToken;
  const productConfig = useBoundStore(state => state.ecoach.productConfig);
  const trainerGuruAvailability = useBoundStore(state => state.ecoach.trainerGuruAvailability);
  const config = moduleConfigs[country]?.ecoachConfig[channel];
  // Checking logic for token and FE config
  if (!idToken) return false;

  // Checking logic from token (controlled by IDP)

  // console.log("useEcoachEnable String(idToken)", jwtDecode(String(idToken)) as CubeIdToken);

  // const { trainer_guru_enabled } = jwtDecode(String(idToken)) as CubeIdToken;
  // if (!trainer_guru_enabled) return false;

  // Only show TG card if configuration data is loaded
  if (!productConfig) return false;


  console.log("useEcoachEnable trainerGuruAvailability", trainerGuruAvailability);
  console.log("useEcoachEnable CHANNELS_BY_COUNTRY[country]", CHANNELS_BY_COUNTRY[country]);
  console.log("useEcoachEnable channel", channel);
  // Check trainer_guru_availability based on current user channel
  if (trainerGuruAvailability) {
    // Map channel values to trainer_guru_availability keys using CHANNELS_BY_COUNTRY
    const countryChannels = CHANNELS_BY_COUNTRY[country];
    if (channel === countryChannels.AGENCY) {
      return trainerGuruAvailability.agency;
    } else if (channel === countryChannels.BANCA) {
      return trainerGuruAvailability.banca;
    } else {
      return false;
    }
  }

  if (isTabletMode) {
    return config?.tablet;
  } else {
    return config?.mobile;
  }
};
