import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { TextProps, useWindowDimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MOBILE_WIDTH_SCALE, TABLET_HEIGHT_SCALE } from '.';

const Tablet = styled(Typography.H6)<{ heightOffset: number }>(
  ({ theme: { space, colors }, heightOffset }) => {
    const { width: screenWidth, height: screenHeight } = useWindowDimensions();
    const { top: topSafeArea } = useSafeAreaInsets();
    const useableHeight = screenHeight - topSafeArea;
    const heightScale = TABLET_HEIGHT_SCALE;
    const horizontalBlockHeight =
      (useableHeight - useableHeight * heightScale) / 2;
    const offset = heightOffset + space[7];
    return {
      width: screenWidth,
      position: 'absolute',
      top: horizontalBlockHeight - offset,
      color: colors.palette.white,
      textAlign: 'center',
      fontWeight: 'bold',
    };
  },
);

const Mobile = styled(Typography.LargeLabel)<{
  heightOffset: number;
}>(({ theme: { space, colors }, heightOffset }) => {
  const { width: screenWidth, height: screenHeight } = useWindowDimensions();
  const ocrCaptureAreaWidth = screenWidth * MOBILE_WIDTH_SCALE;
  const ocrBorderBorderWidth = space[1];
  const verticalBlockWidth =
    (screenWidth - ocrCaptureAreaWidth) / 2 + ocrBorderBorderWidth;
  const offset = heightOffset + space[3];
  return {
    width: screenHeight,
    position: 'absolute',
    left: offset + ocrCaptureAreaWidth + verticalBlockWidth,
    color: colors.palette.white,
    textAlign: 'center',
    transformOrigin: 'top left',
    transform: [{ rotate: '90deg' }],
  };
});

export function CaptureReminder(props: { heightOffset: number } & TextProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const Component = isTabletMode ? Tablet : Mobile;
  return <Component {...props} />;
}
