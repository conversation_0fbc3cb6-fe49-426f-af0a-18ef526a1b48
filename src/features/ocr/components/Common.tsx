import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { TouchableOpacityProps, ViewStyle } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export type OrientationProps = {
  orientation: number;
};

export const Container = styled.View(({ theme }) => {
  return {
    width: '100%',
    height: '100%',
    backgroundColor: theme.colors.palette.black,
    flex: 1,
  };
});

const ShutterButtonComponent = styled.TouchableOpacity(
  ({ theme: { space, colors } }) => {
    const { isTabletMode } = useLayoutAdoptionCheck();
    const { bottom } = useSafeAreaInsets();
    const size = isTabletMode ? space[17] : space[14];
    const startPoint: ViewStyle = isTabletMode
      ? {
          top: '50%',
          right: space[7],
        }
      : {
          left: '50%',
          bottom: space[5] + bottom,
          transform: [{ translateX: -size / 2 }],
        };

    return {
      position: 'absolute',
      width: size,
      height: size,
      borderRadius: 50,
      borderWidth: space[1],
      borderColor: colors.palette.white,
      justifyContent: 'center',
      alignItems: 'center',
      ...startPoint,
    };
  },
);

export const ShutterButtonInner = styled.View(
  ({ theme: { space, colors } }) => {
    const { isTabletMode } = useLayoutAdoptionCheck();
    const size = isTabletMode ? space[13] : space[11];
    return {
      width: size,
      height: size,
      backgroundColor: colors.palette.white,
      borderRadius: 50,
    };
  },
);

const BackButtonComponent = styled.TouchableOpacity<OrientationProps>(
  ({ theme: { space }, orientation }) => {
    const { isTabletMode } = useLayoutAdoptionCheck();
    const { bottom } = useSafeAreaInsets();
    if (isTabletMode) {
      return {
        position: 'absolute',
        top: space[10],
        right: space[7],
      };
    }

    let startPoint: ViewStyle = {};
    if (orientation === -90) {
      startPoint = {
        left: space[5],
        transform: [{ rotate: '270deg' }],
      };
    } else {
      startPoint = {
        right: space[5],
        transform: [{ rotate: '90deg' }],
      };
    }
    return {
      position: 'absolute',
      bottom: bottom + space[3],
      ...startPoint,
    };
  },
);

export function ShutterButton(props: TouchableOpacityProps & OrientationProps) {
  return (
    <ShutterButtonComponent {...props}>
      <ShutterButtonInner />
    </ShutterButtonComponent>
  );
}

export function BackButton(props: TouchableOpacityProps & OrientationProps) {
  const { colors } = useTheme();
  return (
    <BackButtonComponent {...props}>
      <Icon.Close size={30} fill={colors.palette.white} />
    </BackButtonComponent>
  );
}
