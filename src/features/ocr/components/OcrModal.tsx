import React from 'react';
import { Modal, useWindowDimensions } from 'react-native';
import { useGetFeatureFlags } from 'hooks/useGetFeatureFlags';
import { Ocr } from './Ocr';
import { OcrEdgeDetectionComponent } from './OcrEdgeDetectionComponent';
import { OcrFile } from 'types/ocr';

export type OcrProps = {
  onShutterPress: (file: OcrFile) => void;
  onBackPress?: () => void;
};

export type OcrModalProps = OcrProps & {
  isVisible: boolean;
};

export function OcrModal({ isVisible, ...props }: OcrModalProps) {
  const { width, height } = useWindowDimensions();

  const featureFlags = useGetFeatureFlags();
  const isUseEdgeDetectionCamera =
    featureFlags.data?.ocr_edge_detection.available ?? false;

  const OcrComponent = isUseEdgeDetectionCamera
    ? OcrEdgeDetectionComponent
    : Ocr;

  return (
    <Modal visible={isVisible} style={{ width, height }}>
      <OcrComponent {...props} />
    </Modal>
  );
}
