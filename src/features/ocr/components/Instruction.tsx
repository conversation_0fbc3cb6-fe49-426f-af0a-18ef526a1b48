import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';

const Container = styled.View(({ theme: { space } }) => {
  return {
    flexDirection: 'row',
    gap: space[2],
    alignItems: 'center',
  };
});

const Circle = styled.View(({ theme }) => {
  return {
    width: 28,
    height: 28,
    borderRadius: 25,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  };
});

type Props = {
  step: number;
  instruction: string;
};

export function Instruction({ step, instruction }: Props) {
  const { colors } = useTheme();
  return (
    <Container>
      <Circle>
        <Typography.H7_2 style={{ color: colors.palette.white }}>
          {step}
        </Typography.H7_2>
      </Circle>
      <Typography.LargeBody style={{ color: colors.palette.black }}>
        {instruction}
      </Typography.LargeBody>
    </Container>
  );
}
