import { SkImage, Skia, useImage } from '@shopify/react-native-skia';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSharedValue, Worklets } from 'react-native-worklets-core';
import {
  DrawableFrame,
  runAtTargetFps,
  useSkiaFrameProcessor,
} from 'react-native-vision-camera';
import { useResizePlugin } from 'vision-camera-resize-plugin';
import {
  OcrDocIndicationTagMobile,
  OcrDocIndicationTagTablet,
} from 'features/ocr/assets';
import { OCR_BACKDROP_ALPHA } from 'features/ocr/components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import {
  drawImageOnFrame,
  drawObjectOnFrame,
  getPathsForRoundedRect,
  getRoundedRect,
  getSkiaBackdropPaint,
  getSkiaFillPaint,
  getSkiaStrokePaint,
  isObjectContainedInBorder,
} from 'utils/helper/skiaUtils';
import {
  detectLargestRectangularObject,
  Point,
} from 'utils/helper/opencvUtils';
import { OcrKey } from 'utils/translation/i18next';

const backdropPaint = getSkiaBackdropPaint(OCR_BACKDROP_ALPHA);
const whitePaint = getSkiaStrokePaint('white');
const orangePaint = getSkiaStrokePaint('#FF6816');
const alertGreenPaint = getSkiaStrokePaint('#03824F', 8);
const shadowGreenPaint = getSkiaStrokePaint('#00FFB5', 10, 0.4);
const lightShadowGreenPaint = getSkiaStrokePaint('#00FFB5', 12, 0.2);

const objectPaint = getSkiaFillPaint('#FF6816', 0.4);
const objectBorderPaint = getSkiaFillPaint('#FF6816');
objectBorderPaint.setStrokeWidth(4);

const CAPTURE_ZONE_RADIUS = 20;
const TABLET_DOC_INDICATION_TAG_OFFSET_X = CAPTURE_ZONE_RADIUS;
const MOBILE_DOC_INDICATION_TAG_OFFSET_X = -60;

export type EdgeDetectionConfig = {
  captureZone: {
    show?: boolean;
    getWidth: (frame: DrawableFrame) => number;
    getHeight: (frame: DrawableFrame) => number;
  };
  docIndicationTag?: {
    show?: boolean;
  };
  objectDetection: {
    minAreaScale: number;
    maxAreaScale: number;
  };
};
export const useEdgeDetectionForOcr = (
  config: EdgeDetectionConfig,
  onDetected?: () => void,
  isDisabled?: boolean,
) => {
  const { t } = useTranslation('ocr');
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { resize } = useResizePlugin();

  const {
    captureZone: captureZoneConfig,
    docIndicationTag,
    objectDetection: { minAreaScale, maxAreaScale },
  } = config;

  // Object detect
  const sharedObjectPoints = useSharedValue<Point[]>([]);
  const isDocumentDetected = useRef(false);
  const sharedIsDocumentWithinBorder = useSharedValue(false);
  const resetDetectionStatus = useCallback(() => {
    isDocumentDetected.current = false;
    sharedIsDocumentWithinBorder.value = false;
    console.log('🚀🚀 Reset detection status');
  }, []);
  const onDocumentDetected = Worklets.createRunOnJS(async () => {
    if (isDocumentDetected.current) return;

    console.log('Document detected!');
    isDocumentDetected.current = true;
    onDetected?.();
  });

  const [helperMessage, setHelperMessage] = useState<string | undefined>(
    undefined,
  );
  const onShowHelperMessage = Worklets.createRunOnJS((message?: OcrKey) => {
    let msg = message ?? '';
    if (message) {
      msg = t(message);
    }
    setHelperMessage(msg);
  });

  const docIndicationTagSkImage = useImage(
    isTabletMode ? OcrDocIndicationTagTablet : OcrDocIndicationTagMobile,
  );
  const docIndicationTagImgSharedValue = useSharedValue<SkImage | null>(null);
  const docIndicationTagOffsetSharedValue = useSharedValue<{
    x: number;
    y: number;
  }>({ x: 0, y: 0 });
  useEffect(() => {
    if (docIndicationTag?.show === false) return;

    docIndicationTagImgSharedValue.value = docIndicationTagSkImage;

    if (docIndicationTagSkImage) {
      const imageWidth = docIndicationTagSkImage.width();
      const imageHeight = docIndicationTagSkImage.height();

      const offsetX = isTabletMode
        ? TABLET_DOC_INDICATION_TAG_OFFSET_X
        : -(imageWidth + MOBILE_DOC_INDICATION_TAG_OFFSET_X);
      const offsetY = -imageHeight / 2;

      docIndicationTagOffsetSharedValue.value = { x: offsetX, y: offsetY };
    }
  }, [
    docIndicationTag?.show,
    docIndicationTagImgSharedValue,
    docIndicationTagOffsetSharedValue,
    docIndicationTagSkImage,
    isTabletMode,
  ]);

  const frameProcessor = useSkiaFrameProcessor(
    frame => {
      'worklet';
      frame.render();

      const captureZoneWidth = captureZoneConfig.getWidth(frame);
      const captureZoneHeight = captureZoneConfig.getHeight(frame);
      const captureZoneArea = captureZoneWidth * captureZoneHeight;

      // Center the capture zone
      const captureZoneTopLeftX = (frame.width - captureZoneWidth) / 2;
      const captureZoneTopLeftY = (frame.height - captureZoneHeight) / 2;

      const captureZoneRoundedRect = getRoundedRect(
        captureZoneTopLeftX,
        captureZoneTopLeftY,
        captureZoneWidth,
        captureZoneHeight,
        CAPTURE_ZONE_RADIUS,
      );

      if (captureZoneConfig.show !== false) {
        const backdropRect = Skia.RRectXY(
          Skia.XYWHRect(0, 0, frame.width, frame.height),
          0,
          0,
        );
        frame.drawDRRect(backdropRect, captureZoneRoundedRect, backdropPaint);
      }

      const paths = getPathsForRoundedRect(
        captureZoneTopLeftX,
        captureZoneTopLeftY,
        captureZoneWidth,
        captureZoneHeight,
        CAPTURE_ZONE_RADIUS,
      );

      runAtTargetFps(2, () => {
        if (isDisabled) return;

        const ratio = 500 / frame.width;
        const resized = resize(frame, {
          scale: {
            width: frame.width * ratio,
            height: frame.height * ratio,
          },
          pixelFormat: 'rgb',
          dataType: 'uint8',
        });

        const { objectPoints, area } = detectLargestRectangularObject(
          frame.width,
          frame.height,
          resized,
          ratio,
          captureZoneWidth,
          captureZoneHeight,
          minAreaScale,
        );

        if (objectPoints.length === 0) {
          onShowHelperMessage();
          sharedObjectPoints.value = [];
          sharedIsDocumentWithinBorder.value = false;
          console.log('No object detected');
          return;
        }

        if (area > captureZoneArea * maxAreaScale) {
          sharedObjectPoints.value = [];
          sharedIsDocumentWithinBorder.value = false;
          onShowHelperMessage('ocr.edgeDetection.helperMsg.device.further');
          return;
        }

        const midAreaScale = minAreaScale + (maxAreaScale - minAreaScale) / 2;
        if (
          area >= captureZoneArea * minAreaScale &&
          area < captureZoneArea * midAreaScale
        ) {
          sharedObjectPoints.value = objectPoints;
          sharedIsDocumentWithinBorder.value = false;
          onShowHelperMessage('ocr.edgeDetection.helperMsg.device.closer');
          return;
        }

        sharedObjectPoints.value = objectPoints;
        onShowHelperMessage();
        const isDetected = isObjectContainedInBorder(
          sharedObjectPoints.value,
          captureZoneTopLeftX,
          captureZoneTopLeftY,
          captureZoneWidth,
          captureZoneHeight,
        );
        // if (isDetected) {
        sharedIsDocumentWithinBorder.value = isDetected;
        // }
      });

      if (captureZoneConfig.show !== false) {
        // CaptureZone border
        paths.forEach((path, index) => {
          const paint =
            index === 0 && docIndicationTag?.show !== false
              ? orangePaint
              : whitePaint;
          frame.drawPath(path, paint);
        });

        // Green border shown when captured
        // Multiple drawing to simulate glowing effects
        if (sharedIsDocumentWithinBorder.value) {
          const largerRoundedRect = getRoundedRect(
            captureZoneTopLeftX - 2,
            captureZoneTopLeftY - 2,
            captureZoneWidth + 4,
            captureZoneHeight + 4,
            CAPTURE_ZONE_RADIUS,
          );
          const largestRoundedRect = getRoundedRect(
            captureZoneTopLeftX - 4,
            captureZoneTopLeftY - 4,
            captureZoneWidth + 8,
            captureZoneHeight + 8,
            CAPTURE_ZONE_RADIUS,
          );
          frame.drawRRect(largestRoundedRect, lightShadowGreenPaint);
          frame.drawRRect(largerRoundedRect, shadowGreenPaint);
          frame.drawRRect(captureZoneRoundedRect, alertGreenPaint);
        }

        // Document indication tag
        if (
          !sharedIsDocumentWithinBorder.value &&
          docIndicationTagImgSharedValue.value
        ) {
          const image = docIndicationTagImgSharedValue.value;
          const { x: offsetX, y: offsetY } =
            docIndicationTagOffsetSharedValue.value;
          drawImageOnFrame(
            frame,
            image,
            captureZoneTopLeftX + offsetX,
            captureZoneTopLeftY + offsetY,
          );
        }
      }

      // Outline objects
      if (__DEV__ && sharedObjectPoints.value.length > 0) {
        drawObjectOnFrame(
          frame,
          sharedObjectPoints.value,
          objectPaint,
          objectBorderPaint,
        );
      }

      // Callback to notify document detection
      if (sharedIsDocumentWithinBorder.value) {
        onDocumentDetected();
      }
    },
    [config, isDisabled],
  );

  return {
    frameProcessor,
    helperMessage,
    resetDetectionStatus,
  };
};
