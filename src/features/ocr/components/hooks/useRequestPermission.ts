import { useCameraPermissions } from 'expo-camera';
import { useCallback } from 'react';

export type UseRequestPermissionProps = {
  onPermitted: () => void;
  onDenied: () => void;
};
export function useRequestPermission({
  onPermitted,
  onDenied,
}: UseRequestPermissionProps) {
  const [cameraPermissionResponse, requestCameraPermission] =
    useCameraPermissions();
  const handler = useCallback(async () => {
    if (cameraPermissionResponse?.granted) {
      onPermitted();
    } else if (
      !cameraPermissionResponse?.granted &&
      cameraPermissionResponse?.canAskAgain
    ) {
      const permission = await requestCameraPermission();
      if (permission.granted) {
        onPermitted();
      }
    } else {
      onDenied();
    }
  }, [
    cameraPermissionResponse?.granted,
    cameraPermissionResponse?.canAskAgain,
    requestCameraPermission,
    onPermitted,
    onDenied,
  ]);
  return handler;
}
