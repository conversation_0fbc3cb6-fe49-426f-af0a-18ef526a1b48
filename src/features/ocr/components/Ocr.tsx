import { useCallback, useEffect, useRef, useState } from 'react';
import { Icon, addErrorBottomToast, addErrorToast } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { ViewStyle, useWindowDimensions } from 'react-native';
import styled from '@emotion/native';
import {
  MOBILE_WIDTH_SCALE,
  MOBILE_HEIGHT_SCALE,
  TABLET_WIDTH_SCALE,
  TABLET_HEIGHT_SCALE,
} from 'features/ocr/components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { InstructionModal } from './InstructionModal';
import { OcrProps } from 'features/ocr/components/OcrModal';
import { useGetFeatureFlags } from 'hooks/useGetFeatureFlags';
import { Backdrop } from './Backdrop';
import { useRotateByMotion } from 'hooks/useRotateByMotion';
import { DeviceMotion } from 'expo-sensors';
import {
  BackButton,
  Container,
  ShutterButton,
  OrientationProps,
} from './Common';
import { CaptureReminder } from './CaptureReminder';
import { CameraView } from 'expo-camera';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';

const CaptureArea = styled.View(({ theme: { space, colors } }) => {
  const { width: screenWidth, height: screenHeight } = useWindowDimensions();
  const { top } = useSafeAreaInsets();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const widthScale = isTabletMode ? TABLET_WIDTH_SCALE : MOBILE_WIDTH_SCALE;
  const heightScale = isTabletMode ? TABLET_HEIGHT_SCALE : MOBILE_HEIGHT_SCALE;
  const actualHeight = isTabletMode ? screenHeight - top : screenHeight;
  const width = screenWidth * widthScale;
  const height = actualHeight * heightScale;

  return {
    position: 'absolute',
    left: '50%',
    top: '50%',
    width,
    height,
    transform: [{ translateX: -width / 2 }, { translateY: -height / 2 }],
    borderWidth: space[1],
    borderLeftColor: colors.palette.white,
    borderTopColor: isTabletMode ? colors.primary : colors.palette.white,
    borderBottomColor: colors.palette.white,
    borderRightColor: isTabletMode ? colors.palette.white : colors.primary,
    borderRadius: space[3],
    padding: space[3],
  };
});

const HelpButton = styled.TouchableOpacity<OrientationProps>(
  ({ theme: { space }, orientation }) => {
    const { isTabletMode } = useLayoutAdoptionCheck();
    const { bottom } = useSafeAreaInsets();
    if (isTabletMode) {
      return {
        position: 'absolute',
        bottom: space[19],
        right: space[7],
      };
    }
    const isLandscape = orientation === -90 || orientation === 90;

    let startPoint: ViewStyle = {};
    if (orientation === -90) {
      startPoint = {
        right: space[5],
      };
    } else {
      startPoint = {
        left: space[5],
      };
    }
    return {
      position: 'absolute',
      bottom: space[5] + bottom,
      transform: isLandscape ? [{ rotate: '90deg' }] : [],
      ...startPoint,
    };
  },
);

DeviceMotion.setUpdateInterval(500);

export function Ocr({ onShutterPress, onBackPress }: OcrProps) {
  const { t } = useTranslation('ocr');
  const { colors } = useTheme();
  const featureFlags = useGetFeatureFlags();
  const { orientation } = useRotateByMotion();
  const cameraRef = useRef<CameraView>(null);
  const [cameraWidth, setCameraWidth] = useState(0);
  const [cameraHeight, setCameraHeight] = useState(0);
  const [isCamReady, setIsCamReady] = useState(false);

  const [isInstructionVisible, setIsInstructionVisible] = useState(false);

  const [captureReminderHeight, setCaptureReminderHeight] = useState(0);
  const isBorderEnabled = featureFlags.data?.ocr_border.available ?? false;

  const onShutterButtonPress = useCallback(async () => {
    try {
      const result = await cameraRef.current?.takePictureAsync({
        quality: 1,
        base64: true,
      });
      if (!result || !result.base64) {
        throw new Error('Failed to take a picture');
      }
      onShutterPress({
        uri: result.uri,
        base64: result.base64,
        width: result.width,
        height: result.height,
      });
    } catch (e) {
      addErrorBottomToast([
        {
          message: 'Failed to take a picture, please try again',
        },
      ]);
    }
  }, [onShutterPress]);

  useEffect(() => {
    setIsCamReady(true);
  }, []);

  return (
    <Container>
      <StatusBar hidden />
      <CameraView
        ref={cameraRef}
        style={{ flex: 1 }}
        active={isCamReady}
        animateShutter
        onLayout={e => {
          setCameraWidth(e.nativeEvent.layout.width);
          setCameraHeight(e.nativeEvent.layout.height);
        }}
        onCameraReady={() => {
          console.log('Camera is ready');
        }}
        onMountError={e => {
          addErrorToast([
            {
              message: 'Failed to mount camera',
            },
          ]);
          console.error('Failed to mount camera', e);
        }}
      />
      {isBorderEnabled && (
        <>
          <Backdrop
            position="top"
            orientation={orientation}
            width={cameraWidth}
            height={cameraHeight}
          />
          <Backdrop
            position="bottom"
            orientation={orientation}
            width={cameraWidth}
            height={cameraHeight}
          />
          <Backdrop
            position="left"
            orientation={orientation}
            width={cameraWidth}
            height={cameraHeight}
          />
          <Backdrop
            position="right"
            orientation={orientation}
            width={cameraWidth}
            height={cameraHeight}
          />
          <CaptureArea />
          <CaptureReminder
            heightOffset={captureReminderHeight}
            onLayout={e => {
              setCaptureReminderHeight(e.nativeEvent.layout.height);
            }}>
            {t('ocr.captureReminder')}
          </CaptureReminder>
        </>
      )}
      <ShutterButton
        orientation={orientation}
        disabled={!cameraRef.current}
        onPress={onShutterButtonPress}
      />
      <HelpButton
        orientation={orientation}
        onPress={() => {
          setIsInstructionVisible(true);
        }}>
        <Icon.Tooltip size={30} fill={colors.palette.white} />
      </HelpButton>
      <BackButton orientation={orientation} onPress={onBackPress} />
      <InstructionModal
        isVisible={isInstructionVisible}
        handleClose={() => {
          setIsInstructionVisible(false);
        }}
      />
    </Container>
  );
}
