import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon } from 'cube-ui-components';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { TouchableOpacityProps } from 'react-native';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const BackButtonOuter = styled.TouchableOpacity(({ theme: { space } }) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { bottom: bottomSafeArea } = useSafeAreaInsets();

  const styles = {
    position: 'absolute',
  };

  if (!isTabletMode) {
    return {
      ...styles,
      bottom: space[4] + bottomSafeArea,
      right: space[5],
    };
  }

  return {
    ...styles,
    top: space[6],
    right: space[7],
  };
});

export const BackButton = (props: TouchableOpacityProps) => {
  const { colors } = useTheme();
  return (
    <BackButtonOuter {...props}>
      <Icon.Close size={30} fill={colors.palette.white} />
    </BackButtonOuter>
  );
};
