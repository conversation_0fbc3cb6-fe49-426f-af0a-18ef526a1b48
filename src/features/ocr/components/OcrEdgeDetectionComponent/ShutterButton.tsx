import styled from '@emotion/native';
import { TouchableOpacityProps } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { ShutterButtonInner } from 'features/ocr/components/Common';

const ShutterButtonOuter = styled.TouchableOpacity(
  ({ theme: { space, colors } }) => {
    const { isTabletMode } = useLayoutAdoptionCheck();
    const { bottom: bottomSafeArea } = useSafeAreaInsets();

    const styles = {
      position: 'absolute',
      borderRadius: 50,
      borderWidth: space[1],
      borderColor: colors.palette.white,
      justifyContent: 'center',
      alignItems: 'center',
    };

    if (!isTabletMode) {
      const size = space[14];

      return {
        ...styles,
        width: size,
        height: size,
        bottom: space[4] + bottomSafeArea,
        left: '50%',
        transform: [{ translateX: -size / 2 }],
      };
    }

    const size = space[17];

    return {
      ...styles,
      width: size,
      height: size,
      top: '50%',
      right: space[7],
      transform: [{ translateY: -size / 2 }],
    };
  },
);

export const ShutterButton = (props: TouchableOpacityProps) => (
  <ShutterButtonOuter {...props}>
    <ShutterButtonInner />
  </ShutterButtonOuter>
);
