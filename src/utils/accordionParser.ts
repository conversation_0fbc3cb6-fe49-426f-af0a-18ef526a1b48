import { CollapsibleItem } from 'features/aiBot/components/CollapsibleItems/CollapsibleItems';
import { DomUtils, parseDocument } from 'htmlparser2';

export function parseAccordionHtml(text: string): Array<CollapsibleItem> {
  const doc = parseDocument(text);
 const detailsElements = DomUtils.findAll(elem => elem.name === 'details', doc.children);
  const result = detailsElements.map(detail => {
    const summary = DomUtils.findOne(elem => elem.name === 'summary', detail.children);
    const title = summary ? DomUtils.textContent(summary) : '';
    const content = DomUtils.textContent(detail).replaceAll(title, '').trim();
    return { title, content, isCollapsed: true };
  });
  return result;
}

export function replaceAccordionContent(text: string, replacement: string) {
  return text.replace(/<div class="accordion">.*?<\/div>/s, `\n${replacement}\n`);
}