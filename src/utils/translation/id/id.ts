// --- TDC
import aiBot from 'features/aiBot/translation/my/aiBot.en';
import coverageDetails from 'features/coverageDetails/translation/id/coverageDetails.id';
import ecoach from 'features/ecoach/translation/my/ecoach.en';
import livenessCheck from 'features/livenessCheck/translations/id/livenessCheck.id';
import ocr from 'features/ocr/translation/ocr.en';
import voicePrint from 'features/voicePrint/translation/voicePrint.id';

import birthday from 'features/birthday/translation/id/birthday.id';
// --- still in my
import customerFactFind from 'features/customerFactFind/translations/my/customerFactFind.my';

// --- still in ib
import affiliates from 'features/affiliates/translation/ib/affiliates.ib';
import agentAssist from 'features/agentAssist/translation/ib/agentAssist.fib';
import customer from 'features/lead/translation/ib/customer.my';
import teamManagement from 'features/teamManagement/translation/ib/en';

// ******* After IDN-specific translations splitting

import pdfViewer from 'features/pdfViewer/translation/pdfViewer.id';
import eApp from 'features/eAppV2/id/translation/eApp.id';
import fna from 'features/fna/translation/id/fna.id';
import proposal from 'features/proposal/translation/id/id';

import agentProfile from 'features/agentProfile/translation/id/agentProfile.id';
import document from 'features/Document/translation/id/documents.id';
import eRecruit from 'features/eRecruit/translation/id/eRecruit.id';
import news from 'features/fwdNews/translation/id/news.id';
import home from 'features/home/<USER>/id/home.id';
import lead from 'features/lead/translation/id/lead.id';
import leadProfile from 'features/lead/translation/id/leadProfile.id';
import notification from 'features/notification/translation/id/notification.id';
import performance from 'features/performance/translation/id/performance.id';
import policy from 'features/policy/translation/id/policy.id';
import navigation from 'navigation/translation/id/navigation.id';

// Social Marketing translations
import socialMarketing from 'features/socialMarketing/translation/id/socialMarketing.id';

const id = {
  common: {
    'login.welcome':
      'Selamat datang! Masuk untuk memulai pengalaman kerja digital baru Anda.',
    'login.id': 'ID Agen',
    'login.password': 'Kata Sandi',
    'login.footer': 'FWD Cube versi {{appVersion}}',
    'login.error':
      'status: ID pengguna/kata sandi tidak valid. <NAME_EMAIL>',
    'login.login': 'Masuk',
    'login.error.username.password':
      'ID pengguna atau kata sandi tidak valid. Jika Anda lupa kata sandi, silakan atur ulang di Sales Connect.',
    'login.error.banca.channel':
      'Ups, Anda saat ini tidak diizinkan untuk masuk ke FWD Cube melalui ponsel. Silakan unduh dan instal FWD CUBE di tablet.',
    'login.error.connection':
      'Masalah koneksi. Silakan periksa koneksi Anda dan coba lagi sebentar lagi.',
    'login.error.dueTo': 'Tidak dapat masuk karena kesalahan: {{message}}',
    'login.error.onlyOneAgent':
      'Hanya 1 ID agen yang dapat digunakan untuk masuk di perangkat ini',
    'login.versionCheck.title': 'Pembaruan versi',
    'login.versionCheck.message': 'Silakan unduh versi terbaru.',
    'login.versionCheck.download': 'Unduh',
    'login.versionCheck.askMeLater': 'Tanya Saya Nanti',
    'login.toCube': 'Masuk ke FWD Cube',
    'login.forgetPassword': 'Lupa Kata Sandi? Atur ulang ',
    'login.forgetPasswordV2': 'Lupa Kata Sandi?',
    'login.resetPassword': 'Atur ulang ',
    'login.biometric.button': 'Masuk dengan {{bioMeticType}}',
    'login.biometric.touchID': 'Touch ID',
    'login.biometric.faceID': 'Face ID',
    'login.biometric.iris': 'Iris',
    'login.biometric.biometric': 'Biometrik',
    'login.biometric.prompt.question':
      'Apakah Anda ingin mengizinkan "FWD Cube" menggunakan {{bioMeticType}}?',
    'login.biometric.prompt.description':
      'Aktifkan {{bioMeticType}} untuk masuk lebih cepat. Anda dapat mengaktifkan atau menonaktifkan fitur ini kapan saja di Pengaturan.',
    'login.biometric.prompt.enable': 'Aktifkan',
    'login.biometric.prompt.cancel': 'Batalkan',
    'login.biometric.prompt.notNow': 'Nanti Saja',
    'login.biometric.prompt.fallbackLabel': 'Coba input manual',
    'login.biometric.error.failed': 'Login biometrik gagal',
    'login.biometric.error.noHardware':
      'Perangkat Anda tidak mendukung login biometrik',
    'login.biometric.error.notEnrolled':
      'Anda belum mendaftarkan data biometrik di perangkat Anda',
    'login.here': 'disini',
    'home.welcome.hi': 'Hai, {{name}}',
    'home.welcome.question': 'Apa yang ingin Anda lakukan?',
    'validation.required': 'Kolom wajib diisi',
    'navigation.tabScreen.MyLeads': 'Prospek Saya',
    'navigation.tabScreen.SalesActivity': 'Aktivitas Penjualan',
    'navigation.tabScreen.Overview': 'Ringkasan',
    'navigation.tabScreen.MyTasks': 'Tugas Saya',
    'todayTasks.title': 'Tugas Hari Ini',
    'form.invalidInput': 'Kolom tidak valid',
    'form.invalidInputFormat': 'Format tidak valid',
    'form.phoneNumberTooShort': 'Nomor telepon terlalu pendek',
    'form.phoneNumberTooLong': 'Nomor telepon terlalu panjang',
    'form.phoneNumberTooShortWithLength': 'Panjang minimum harus {{length}}',
    'form.phoneNumberTooLongWithLength': 'Panjang maksimum harus {{length}}',
    'form.nameTooLong': 'Tidak boleh melebihi 30 karakter',
    'form.nameTooLongWithLength': 'Tidak boleh melebihi {{length}} karakter',
    'form.taxNumberFormatError':
      'Kolom ini hanya menerima 2 huruf diikuti oleh 10–11 angka.',
    'form.inputNumberOnly': 'Harap masukkan angka saja',
    'form.yearOfPassingLimitCharacters': 'Harus tepat 4 karakter',
    'form.icNumberLimitCharacters': 'Harus tepat 16 karakter',
    'form.inputMustBeExactly': 'Must be exactly {{number}} characters',
    'form.inputMustBe': 'Must be {{number}} characters',
    'form.invalidFormat': 'Format tidak valid',
    'form.candidateAboveYearsOld': 'Kandidat harus berusia di atas 18 tahun',
    'form.candidateBelowYearsOld': 'Kandidat harus berusia di bawah 60 tahun',
    'form.mustBeAtLeast': 'Harus minimal {{minPhoneNumberlength}} karakter',
    'form.leaderALCmaxNameLength': 'Tidak boleh melebihi 50 karakter',
    'form.leaderCodeNotExist': 'Kode pemimpin tidak ditemukan',
    'form.invalidEmail': 'Email harus mengandung satu @ dan domain yang valid',
    'form.invalidEmailFormat': 'Format email tidak valid',
    'form.mustHaveTakafulTBEFamilyCertificate':
      'Kandidat harus memiliki sertifikat Takaful TBE Family',
    'form.invalidProfessionalQualiLength': 'Tidak boleh lebih dari 30 karakter',

    'form.invalidPhoneNumber': 'Nomor telepon tidak valid',
    'form.shouldNotExceedSomeNumber':
      'Input tidak boleh melebihi {{number}} karakter',
    'forcedLogout.title': 'Akun Anda TELAH DITANGGUHKAN',
    'forcedLogout.message':
      'Jika Anda memerlukan klarifikasi lebih lanjut, silakan kirim <NAME_EMAIL>.',
    imLookingFor: 'Saya sedang mencari...',
    noResultsFound: 'Tidak ada hasil ditemukan',
    loadingFail: 'Gagal mengambil data. Silakan kunjungi kembali nanti.',
    searchSuggestion:
      'Sesuaikan kata kunci untuk hasil yang lebih baik atau coba pencarian lain.',
    foundXResults: 'Kami menemukan {{count}} hasil',
    view: 'Lihat',
    done: 'Selesai',
    close: 'Tutup',
    more: 'Lainnya',
    add: 'Tambah',
    confirm: 'Konfirmasi',
    details: 'Rincian',
    select: 'Pilih',
    compare: 'Bandingkan',
    cancel: 'Batal',
    search: 'Cari',
    'search.leadName': 'Nama prospek',
    'search.companyName': 'Nama perusahaan',
    'search.mobile': 'Ponsel',
    'search.email': 'Email',
    searchResults: 'Hasil pencarian ({{count}})',
    years: 'tahun',
    year: 'tahun',
    save: 'Simpan',
    delete: 'Hapus',
    reset: 'Atur Ulang',
    exit: 'Keluar',
    ok: 'OK',
    rm: 'RM',
    continue: 'Lanjutkan',
    next: 'Berikutnya',
    withCurrency: 'IDR {{amount}}',
    currencySymbol: 'IDR',
    withYears: '{{year}} tahun',
    back: 'Kembali',
    'position.1': 'Pertama',
    'position.2': 'Kedua',
    'position.3': 'Ketiga',
    'position.4': 'Keempat',
    'position.5': 'Kelima',
    'position.6': 'Keenam',
    'position.7': 'Ketujuh',
    'position.8': 'Kedelapan',
    'position.9': 'Kesembilan',
    FWD: 'Direktur Kekayaan Finansial',
    FWE: 'Eksekutif Kekayaan Finansial',
    FWC: 'Konsultan Kekayaan Finansial',
    asOfDate: 'Per {{date}}',
    shortYearOld: 'thn',
    en: 'Inggris',
    my: 'Bahasa',
    error: 'Kesalahan',
    backendError:
      'Kesalahan saat menghubungkan ke sistem backend. Silakan coba lagi nanti atau hubungi dukungan.',
    download: 'Unduh',
    share: 'Bagikan',
    viewProfile: 'Lihat profil',
    'searchExistingLeadModal.existingLeadTitle':
      'Cari prospek FWD yang sudah ada',
    'searchExistingLeadModal.existingCompanyTitle':
      'Cari perusahaan yang sudah ada',
    'searchExistingLeadModal.entityLeadPlaceholder':
      'Nama perusahaan/ nomor telepon/ email',
    'searchExistingLeadModal.individualLeadPlaceholder':
      'Nama prospek/ nomor telepon/ email',
    'searchExistingLeadModal.emptyIndividual':
      'Tidak ada prospek yang tersedia. Silakan coba pencarian lain.',
    'searchExistingLeadModal.emptyEntity':
      'Tidak ada perusahaan yang tersedia. Silakan coba pencarian lain.',
    days: 'hari',
    date: 'Tanggal',
    yes: 'Ya',
    no: 'Tidak',
    'autoComplete.noData': 'Tidak ada opsi yang cocok',
    // Contact
    'contact.title': 'Kontak',
    'contact.message': 'FWD Cube',
    'contact.close': 'Tutup',
    'contact.call': 'Telepon',
    'contact.sms': 'SMS',
    'contact.email': 'Email',
    'contact.more': 'Lainnya',
    'contact.phone.notProvide': 'Nomor telepon dan email tidak tersedia',
    'contact.phone.notValid': 'Nomor telepon tidak valid',
    pdfDownload: 'Mengunduh dokumen...',
    pdfDownloadedSuccessfully: 'Dokumen berhasil diunduh',
    pdfFailToDownload: 'Gagal mengunduh',
    pdfFailToShare: 'Gagal membagikan file: {{msg}}',
    dateHint: 'DD/MM/YYYY',
    incompleteFields_other: '{{count}} kolom belum lengkap',
    incompleteFields_one: '{{count}} kolom belum lengkap',
    goToTheField: 'Klik untuk melengkapi',
    'ocr.upload.title': 'Unggah kartu identitas',
    'ocr.upload.title.mobile': 'Pindai kartu identitas',
    'ocr.upload.description':
      'untuk mengisi informasi di bawah ini secara otomatis.',
    'ocr.upload.description.mobile': 'misalnya KTP atau paspor',
    'ocr.upload': 'Unggah',
    'ocr.uploading': 'Mengunggah dokumen...',
    'ocr.verifying': 'Memverifikasi...',
    'ocr.scanID': 'Pindai kartu identitas',
    'ocr.scanID.example': 'misalnya KTP atau paspor',
    'ocr.scanID.info.title': 'Daftar ID yang diterima',
    'ocr.scanID.info.point.1': 'UMID (Unified Multi-Purpose ID)',
    'ocr.scanID.info.point.2': 'Paspor RP',
    'ocr.scanID.info.point.3': 'SIM (Surat Izin Mengemudi)',
    'ocr.scanID.info.point.4': 'ID PRC (Professional Regulation Commission)',
    'ocr.scanID.info.point.5': 'SSS (ID Sistem Jaminan Sosial)',
    'ocr.scanID.info.point.6': 'TIN (ID Direktorat Jenderal Pajak)',
    'ocr.instruction.takeA': 'Ambil ',
    'ocr.instruction.closeUp': 'foto close-up',
    'ocr.instruction.photo': ' dari kartu identitas Anda',
    'ocr.instruction.makeSure': 'Pastikan foto Anda:',
    'ocr.instruction.makeSure.clear': 'Jelas',
    'ocr.instruction.makeSure.clear.1': ' terlihat dan fokus',
    'ocr.instruction.makeSure.withoutFlash': 'Diambil',
    'ocr.instruction.makeSure.withoutFlash.1': ' tanpa flash',
    'ocr.picker.title': 'Unggah foto profil melalui',
    'ocr.re-upload': 'Unggah ulang',
    'ocr.verified': 'Terverifikasi',
    selectLanguage: 'Pilih Bahasa',
    IDR: 'IDR',
    USD: 'USD',
    'birthDay.sendCard': 'Send birthday card',
    new: 'New',
    'upload.invalidFileSize':
      'The file you’re uploading exceeds the maximum file size {{maxSize}} allowed.  Please retry uploading a file less in size.',
    'upload.invalidFileExtension':
      'The file type you’re uploading is invalid.  Only {{fileTypes}} are accepted.',
    'generalMsg.pleaseTryAgainLater': 'Please try again later',
    'role.FWD': 'FWD-Executive',
    'role.FWE': 'FWE',
    'role.FWC': 'FWC',
    'role.BD': 'BD-Executive',
    'role.BA': 'BA-Associate',
    'role.FWP': 'FWP-Planner',
    'role.FWO': 'FWO-Officer',
    'role.FWM': 'FWM-Manager',
  },
  smart: {
    'proposaldetails.totalpremium': 'Total premi/tahunan',
    'plan.sumAssured': 'Total uang pertanggungan',
    'proposaldetails.basepremium': 'Premi',
    'proposaldetails.basesumassured': 'Uang pertanggungan',
    'proposaldetails.paymentmode': 'Metode pembayaran',
    'proposaldetails.paymenterm': 'Jangka waktu pembayaran',
    'personalDetails.title': 'Data Pribadi',
    'personalDetails.scanIDCard': 'Pindai kartu identitas',
    'personalDetails.scanIDCard.hint': 'misalnya KTP atau paspor',
    'personalDetails.name': 'Nama',
    'personalDetails.customerType': 'Tipe pelanggan',
    'personalDetails.salutation': 'Sapaan/Gelar',
    'personalDetails.firstName': 'Nama depan',
    'personalDetails.middleName': 'Nama tengah (opsional)',
    'personalDetails.lastName': 'Nama belakang',
    'personalDetails.extensionName': 'Nama tambahan (opsional)',
    'personalDetails.dateOfBirth': 'Tanggal lahir',
    'personalDetails.occupation.question':
      'Apa yang paling menggambarkan pekerjaan Anda?',
    'personalDetails.smoking': 'Kebiasaan merokok',
    'personalDetails.reviewPersonalHealthQuestion':
      'Tinjau pertanyaan kesehatan pribadi',
    'personalDetails.gender': 'Jenis kelamin (opsional)',
    'rider.sumAssured': 'Uang pertanggungan (PHP)',
    'rider.regularTopup': 'Jumlah top-up (PHP)',
    'rider.coverage': 'Perlindungan hingga',
    'rider.planOption': 'Pilihan paket',
  },
  proposal,
  savedProposals: {
    title: 'Proposal yang Disimpan',
    totalSavedProposal: 'Total',
    filterBy: 'Filter berdasarkan',
    filtered: 'Difilter berdasarkan',
    proposalStage: 'Tahap proposal',
    clearAll: 'Atur Ulang',
    apply: 'Terapkan',
    'filter.FNA': 'FNA',
    'filter.CFF': 'CFF',
    'filter.QUICK_SI': 'Kutipan cepat',
    'filter.FULL_SI': 'SI',
    'filter.IN_APP': 'Aplikasi',
    'filter.INDIVIDUAL': 'Individu',
    'filter.ORGANISATION': 'Organisasi',
    'filter.APP_SUBMITTED': 'Dikirim',
    'filter.emptyRecord': 'Tidak ada data',
    'filter.COVERAGE': 'Perlindungan',
    'filter.REMOTE_SELLING_COMPLETED': 'Penjualan jarak jauh selesai',
    'filter.UNKNOWN': 'Tidak diketahui',
    'filter.REJECTED_BY_LEADER': 'Ditolak oleh leader',
    'filter.PENDING_FOR_LEADER': 'Menunggu persetujuan leader',
    'filter.APPROVED_BY_LEADER': 'Disetujui oleh leader',
    'filter.EXPIRED_AFTER_15_DAYS': 'Kedaluwarsa setelah 15 hari',
    fullTable: 'Tabel lengkap',
    sortByTime: 'Urutkan berdasarkan',
    newest: 'Terbaru',
    oldest: 'Terlama',
    showIn: 'Tampilkan dalam',
    inDays: '{{day}} hari',
    noResultsFound: 'Ups! Tidak ada proposal yang disimpan.',
    loadingProposalsMessage:
      'Mohon tunggu, kami sedang mengambil data dari {{day}} hari yang lalu',
    certificateOwner: 'Pemilik Polis',
    proposalName: 'Nama Proposal',
    insured: 'Tertanggung',
    insuredName: 'Nama Tertanggung',
    status: 'Status',
    productName: 'Nama Produk',
    product: 'Produk',
    premiumAmount: 'Jumlah Premi',
    modalPremium: 'Premi Modal',
    sumAssured: 'Uang Pertanggungan',
    proposalNo: 'Nomor Proposal',
    lastUpdate: 'Pembaruan Terakhir',
    lastUpdateDate: 'Tanggal Pembaruan',
    date: 'Tanggal',
    showDataIn: 'Tampilkan data dalam',
    lastDays: '{{day}} hari terakhir',
    proposalPlaceholder: 'Proposal',
    searchHint:
      'misalnya nama depan/belakang pelanggan, nama produk, atau nama proposal',
    expired: 'Kedaluwarsa',
    'paymentMode.EVERY_YEAR': 'tahunan',
    'paymentMode.EVERY_HALF_YEAR': 'setengah tahunan',
    'paymentMode.EVERY_QUARTER': 'triwulanan',
    'paymentMode.EVERY_MONTH': 'bulanan',
    'paymentMode.ONE_TIME': 'premi tunggal',
    'search.noResult': 'Tidak ada hasil ditemukan.',
    'search.adjustKeyword': 'Sesuaikan kata kunci untuk hasil yang lebih baik.',
    'search.result': 'Hasil pencarian',
    'search.proposal': 'Cari proposal',
    'search.searchBar.placeholder': 'Cari proposal yang disimpan',
    'search.description':
      'misalnya nama depan/belakang pelanggan, nama produk, atau nama proposal',
    'search.dataFrom.period': 'Menampilkan data dari 90 hari terakhir.',
    createNewProposal: 'Buat proposal baru',
  },
  product: {
    gotIt: 'Mengerti',
    invalidLicense: 'Agen tidak memiliki lisensi untuk menjual {{productName}}',
    selectOneProduct: 'Pilih satu produk',
    yourGoalSummary: 'Ringkasan tujuan Anda',
    recommended: 'Direkomendasikan',
    recommendedFor: 'Direkomendasikan untuk',
    details: 'Rincian',
    otherProducts: 'Produk lainnya',
    forYou: 'Untuk Anda',
    'reason.title': 'Alasan memilih produk yang tidak direkomendasikan:',
    'reason.otherReasonPlaceholder': 'Silakan sebutkan alasan lainnya',
    'reason.0': '',
    'reason.1': 'Klien memiliki polis FWD yang sudah ada dengan fitur serupa',
    'reason.2':
      'Klien memiliki polis dari perusahaan asuransi lain dengan fitur serupa',
    'reason.3': 'Pengguna memilih produk',
    'reason.4':
      'Produk yang direkomendasikan tidak sesuai dengan preferensi saya.',
    'reason.5': 'Produk yang direkomendasikan tidak memenuhi kebutuhan saya.',
    'reason.6': 'Saya memiliki polis yang sudah ada dengan fitur serupa.',
    'disclaimer.title': 'Pernyataan Pengakuan Nasabah',
    'disclaimer.haveRead':
      'Saya telah membaca dan memahami hal-hal berikut sebelum mengajukan aplikasi ini:',

    'disclaimer.point.1':
      'Penilaian FWD Insurance Berhad terhadap tingkat risiko investasi saya dan rekomendasi produk yang sesuai berdasarkan hasil tersebut;',
    'disclaimer.point.2':
      'Fitur dari rencana ini serta strategi dan tujuan investasi dari Dana Investasi yang saya pilih;',
    'disclaimer.point.3':
      'Manfaat yang dibayarkan dalam rencana ini terkait dengan kinerja Dana Investasi yang saya pilih, yang nilainya dapat naik dan turun. Kinerja masa lalu dana tidak menjamin kinerja di masa depan. Oleh karena itu, risiko investasi dalam rencana ini sepenuhnya menjadi tanggung jawab saya.',

    'disclaimer.point.4':
      'Saya mengakui bahwa FWD Insurance Berhad dan/atau agen/perantaranya dapat merekomendasikan strategi dana yang paling sesuai dengan kebutuhan keuangan saya, yang dapat saya gunakan sebagai panduan. Jika saya memilih strategi dana yang berbeda dari yang direkomendasikan, saya setuju untuk tidak menuntut FWD Insurance Berhad, prinsipal, perwakilan, dan penerusnya dalam bentuk apa pun, termasuk segala tanggung jawab, klaim, biaya peluang, dan/atau penyebab tindakan apa pun yang mungkin memengaruhi saya akibat pilihan ini.',

    'disclaimer.1':
      'Saya/Kami memahami bahwa semua informasi yang diberikan dalam formulir ini dimaksudkan untuk memungkinkan agen memberikan saran/rekomendasi produk yang sesuai kepada saya dan akan diperlakukan secara rahasia.\n\nSaya/Kami memahami dan menyetujui bahwa setiap informasi pribadi yang dikumpulkan atau dimiliki oleh FWD (baik yang terdapat dalam formulir ini maupun yang diperoleh dengan cara lain) dapat digunakan, diproses, diungkapkan, dan dibagikan oleh FWD Insurance Berhad kepada individu/organisasi yang terkait dengan FWD Insurance Berhad atau pihak ketiga terpilih (baik di dalam maupun di luar Malaysia, termasuk perusahaan reasuransi dan investigasi klaim serta asosiasi/federasi industri) untuk tujuan memproses formulir ini dan memberikan layanan lanjutan untuk produk dan layanan keuangan ini dan lainnya, serta untuk berkomunikasi dengan saya/kami untuk tujuan tersebut.\n\nSaya/Kami memahami bahwa saya/kami memiliki hak untuk meminta akses terhadap informasi pribadi saya yang dimiliki oleh FWD Insurance Berhad dan untuk meminta koreksi atas informasi pribadi yang tidak akurat atau membatasi pemrosesan informasi pribadi saya. Saya/Kami menyetujui dan dengan ini memberikan wewenang kepada FWD Insurance Berhad untuk mengenakan biaya atas permintaan akses atau koreksi data tersebut.',
    'disclaimer.1.short':
      'Saya/Kami memahami bahwa semua informasi yang diberikan dalam formulir ini dimaksudkan untuk memungkinkan agen memberikan saran/rekomendasi',
    'disclaimer.2':
      'Saya memilih untuk menggunakan produk yang berbeda dari yang direkomendasikan oleh FWD Life Insurance Corporation berdasarkan kebutuhan keuangan yang saya nyatakan. Saya setuju untuk membebaskan FWD Life Insurance Corporation, prinsipal, perwakilan, dan penerusnya dari segala bentuk tanggung jawab, klaim, biaya peluang, dan/atau penyebab tindakan apa pun yang mungkin memengaruhi saya akibat pilihan ini.\nSaya memahami bahwa evaluasi dan rekomendasi didasarkan pada informasi dan data yang saya berikan dan dirancang untuk membantu saya menilai kebutuhan keuangan saya. Saya menyadari bahwa kebutuhan keuangan saya dapat berubah seiring waktu tergantung pada situasi dan tujuan pribadi saya. Oleh karena itu, saran dan rekomendasi yang diberikan di sini bersifat umum dan hanya dimaksudkan sebagai referensi berdasarkan kondisi saya saat ini.',
    'disclaimer.2.short':
      'Saya memilih untuk menggunakan produk yang berbeda dari yang direkomendasikan oleh FWD Life Insurance Corporation berdasarkan',
    'disclaimer.3':
      'Saya/Kami memahami bahwa semua informasi yang diberikan dalam formulir ini dimaksudkan untuk memungkinkan agen memberikan saran/rekomendasi produk yang sesuai kepada saya dan akan diperlakukan secara rahasia.\n\nSaya/Kami memahami dan menyetujui bahwa setiap informasi pribadi yang dikumpulkan atau dimiliki oleh FWD (baik yang terdapat dalam formulir ini maupun yang diperoleh dengan cara lain) dapat digunakan, diproses, diungkapkan, dan dibagikan oleh FWD Insurance Berhad kepada individu/organisasi yang terkait dengan FWD Insurance Berhad atau pihak ketiga terpilih (baik di dalam maupun di luar Malaysia, termasuk perusahaan reasuransi dan investigasi klaim serta asosiasi/federasi industri) untuk tujuan memproses formulir ini dan memberikan layanan lanjutan untuk produk dan layanan keuangan ini dan lainnya, serta untuk berkomunikasi dengan saya/kami untuk tujuan tersebut.\n\nSaya/Kami memahami bahwa saya/kami memiliki hak untuk meminta akses terhadap informasi pribadi saya yang dimiliki oleh FWD Insurance Berhad dan untuk meminta koreksi atas informasi pribadi yang tidak akurat atau membatasi pemrosesan informasi pribadi saya. Saya/Kami menyetujui dan dengan ini memberikan wewenang kepada FWD Insurance Berhad untuk mengenakan biaya atas permintaan akses atau koreksi data tersebut.',
    'disclaimer.3.short':
      'Saya/Kami memahami bahwa semua informasi yang diberikan dalam formulir ini dimaksudkan untuk memungkinkan agen memberikan saran/rekomendasi',
    'disclaimer.close': 'Tutup',
    'disclaimer.more': '...selengkapnya',
    'disclaimer.accept':
      'Saya/Kami mengakui bahwa agen telah memberikan kepada saya/kami salinan formulir Customer Fact Find yang telah diisi atau telah menunjukkan isi formulir tersebut dalam bentuk fisik atau salinan digital sebelum penerbitan polis.',
    'disclaimer.accept2':
      'Saya/Kami mengakui bahwa agen telah menunjukkan atau memberikan alamat web dari Panduan Layanan kepada saya/kami.',
    'disclaimer.acceptedOn': 'Diterima pada',
    back: 'Kembali',
    continue: 'Lanjutkan',
    shareFNADoc: 'Bagikan dokumen FNA',
    previewFNA: 'Pratinjau CFF',
    previewCFF: 'Pratinjau CFF',
    viewCFF: 'Lihat CFF',
    done: 'Selesai',
    createSI: 'Buat Ilustrasi Penjualan',
    selectProduct: 'Silakan pilih produk',
    selectPackage: 'Silakan pilih paket',
    productBrochure: 'Brosur Produk',
    'start.sale.illustration': 'Mulai ilustrasi penjualan',
    basePlan: 'Mulai ilustrasi penjualan',
    'familySharePlan.title':
      'Apakah Anda membeli paket baru, atau bergabung dengan yang sudah ada?',
    'familySharePlan.plan.new': 'Plan baru',
    'familySharePlan.plan.existing': 'Plan yang sudah ada',
    'familySharePlan.nricLabel': 'Nomor NRIC pemilik polis',
    'familySharePlan.nricHint': 'YYMMDD-PB-###G',
    'familySharePlan.invalidNric': 'Nomor NRIC tidak valid',
    'familySharePlan.hasDeathClaim':
      'Pemilik polis ini telah mengajukan klaim kematian',
    'familySharePlan.noPlan':
      'Tidak ada plan yang terdaftar atas nama pemilik polis ini',
    'familySharePlan.choosePlan': 'Pilih plan yang sudah ada',
    'familySharePlan.primaryPolicyNumber': 'Nomor polis utama',
    'familySharePlan.insured': 'Tertanggung',
    'familySharePlan.availableSlots': 'Slot tersedia',
    'maxQuoteError.title': 'Buat Ilustrasi Penjualan Baru',
    'maxQuoteError.desc':
      'Anda telah mencapai batas {{attempt}} kali untuk memilih ulang produk dalam ilustrasi penjualan yang sama. Silakan buat ilustrasi penjualan baru untuk melanjutkan.',
    'maxQuoteError.desc.attempt_one': '{{count}} percobaan',
    'maxQuoteError.desc.attempt_other': '{{count}} percobaan',
    backToHome: 'Kembali ke beranda',
    startOver: 'Mulai ulang',
  },
  navigation,
  eApp,
  coverageDetails,
  teamManagement,
  policy,
  lead,
  document,
  customer,
  news,
  performance,
  fna,
  agentProfile,
  home,
  leadProfile,
  pdfViewer,
  customerFactFind,
  eRecruit,
  birthday,
  aiBot,
  ecoach,
  notification,
  affiliates,
  agentAssist,
  ocr,
  livenessCheck,
  socialMarketing,
  voicePrint,
  locale: {
    ph: 'Filipina',
    my: 'Malaysia',
    id: 'Indonesia',
    en: 'Inggris',
  },
  language: {
    my: {
      local: 'Bahasa Malaysia',
      en: 'Bahasa Malaysia',
    },
    id: {
      local: 'Bahasa Indonesia',
      en: 'Bahasa Malaysia',
    },
    en: {
      local: 'English',
      en: 'English',
    },
    ph: {
      local: 'Filipino',
      en: 'Filipino',
    },
  },
};
export default id;
