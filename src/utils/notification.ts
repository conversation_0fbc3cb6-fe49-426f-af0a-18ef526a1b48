import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';

import { Alert, Platform } from 'react-native';
import Constants from 'expo-constants';
import { SchedulableTriggerInputTypes } from 'expo-notifications';

export async function registerForPushNotificationsAsync() {
  let token;
  if (Device.isDevice) {
    const { isGranted } = await promptGetPermissionsAsync();
    if (!isGranted) return;

    const res = await Notifications.getExpoPushTokenAsync({
      projectId: Constants?.expoConfig?.extra?.eas.projectId,
    });

    if (res.data) token = res.data;
  } else {
    // alert('Must use physical device for Push Notifications');
  }

  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  return token;
}

export async function scheduleNotificationAsync({
  title,
  body,
  options,
  data = {},
}: {
  title: string;
  body: string;
  options: {
    mins?: number;
    repeats?: boolean;
  };
  data?: Record<string, unknown>;
}) {
  const { mins = 0, repeats = false } = options;

  const { isGranted } = await promptGetPermissionsAsync();
  if (isGranted) {
    const identifier: string = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
      },
      trigger: {
        type: SchedulableTriggerInputTypes.TIME_INTERVAL,
        seconds: (__DEV__ ? 1 : 60) * mins, // __DEV__ condition For testing
        repeats,
      },
    });
    return identifier;
  } else {
    alert('Push Notifications permission not granted');
  }
}

export async function cancelScheduledNotificationAsync(identifier: string) {
  try {
    await Notifications.cancelScheduledNotificationAsync(identifier);
    console.log('identifier:', identifier, 'canceled');
  } catch (e) {
    alert('Cannot cancel scheduled notification');
  }
}

export async function promptGetPermissionsAsync(onSuccess?: () => void) {
  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;
  if (existingStatus !== 'granted') {
    const { status } = await Notifications.requestPermissionsAsync({
      ios: {
        allowAlert: true,
        allowBadge: true,
        allowSound: true,
        allowAnnouncements: true,
      },
    });

    finalStatus = status;
  }
  finalStatus === 'granted' && onSuccess && onSuccess();

  return { isGranted: finalStatus === 'granted', status: finalStatus };
}
