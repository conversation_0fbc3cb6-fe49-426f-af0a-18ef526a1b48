import {
  INVALID_FORMAT,
  MAX_NAME_LENGHT,
} from 'features/coverageDetails/validation/common/constant';
import {
  NAME_HAS_ADJACENT_SPACES,
  NAME_HAS_HYPEN_NEXT_TO_BLANK,
  NAME_HAS_SPECIAL_CHARACTER_AFTER_COMMA,
  NAME_START_WITH_SPACE,
  STRING_HAS_COMMA_FOLLOWING_BY_NON_ALPHABET_OR_SPACE_REGEX,
} from 'features/coverageDetails/validation/common/name.validation';
import { t } from 'i18next';
import { GenderCodeUnion } from 'types';
import { LeadFormValues } from 'types/lead';
import { country } from 'utils/context';
import {
  formPhoneNumber,
  strictEmailRegex,
} from 'utils/validation/customValidation';
import { AnyObject, object, string, TestContext } from 'yup';

const REQUIRED_INPUT = t('validation.required');
const minPhoneNumberlengthMY = 9;
const maxPhoneNumberlengthMY = 13;

export const invalidInputMessage = () => t('form.invalidInput');

export const initialLeadData: LeadFormValues = {
  fullName: '',
  birthDate: '',
  mobilePhoneCountryCode: '',
  mobilePhoneNumber: '',
  genderCode: undefined,
  email: '',
  interestedCategories: '',
};

export const requiredAddLeadFieldsObj = {
  fullName:
    country === 'my' || country === 'ib'
      ? string()
          .required(REQUIRED_INPUT)
          .max(MAX_NAME_LENGHT, t(INVALID_FORMAT))
          .validateName(t(INVALID_FORMAT))
          .test({
            name: 'name-validation-enhancement',
            test(value, ctx) {
              return nameValidationWithTFunction(value, ctx);
            },
          })
      : string()
          .required(REQUIRED_INPUT)
          .validateLeadName(invalidInputMessage()),
  mobilePhoneCountryCode: string().required(REQUIRED_INPUT),
  mobilePhoneNumber:
    country == 'my'
      ? formPhoneNumber('mobilePhoneCountryCode')
          .max(
            13,
            t('form.phoneNumberTooLong', { length: maxPhoneNumberlengthMY }),
          )
          .min(
            9,
            t('form.phoneNumberTooShort', { length: minPhoneNumberlengthMY }),
          )
      : formPhoneNumber('mobilePhoneCountryCode'),
} as const;

// export type KeyOfRequiredAddLeadFieldsObj =
//   keyof typeof requiredAddLeadFieldsObj;

export const addNewLeadSchema = object({
  ...requiredAddLeadFieldsObj,
  birthDate: string().nullable(),
  genderCode: string<GenderCodeUnion>().nullable(),
  age: string().nullable(),
  email: string().test('email', 'Invalid email format', value =>
    !value ? true : strictEmailRegex.test(value),
  ),
  interestedCategories: string().nullable(),
});

function nameValidationWithTFunction(
  value: string,
  ctx: TestContext<AnyObject>,
) {
  if (value.startsWith(' '))
    return ctx.createError({ message: t(NAME_START_WITH_SPACE) });

  if (value.includes('  '))
    return ctx.createError({ message: t(NAME_HAS_ADJACENT_SPACES) });

  if (value.includes(' -') || value.includes('- '))
    return ctx.createError({ message: t(NAME_HAS_HYPEN_NEXT_TO_BLANK) });

  if (STRING_HAS_COMMA_FOLLOWING_BY_NON_ALPHABET_OR_SPACE_REGEX.test(value))
    return ctx.createError({
      message: t(NAME_HAS_SPECIAL_CHARACTER_AFTER_COMMA),
    });

  return true;
}
