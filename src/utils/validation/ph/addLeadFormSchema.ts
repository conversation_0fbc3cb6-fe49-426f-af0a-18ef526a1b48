import { AddLeadFormData, BuildCountry, GenderCodeUnion } from 'types';
import { object, string } from 'yup';
import { t } from 'i18next';
import {
  formPhoneNumber,
  strictEmailRegex,
} from 'utils/validation/customValidation';
import { country } from 'utils/context';
import { nameValidationWithTFunction } from '../ib/addNewLeadSchema';
import { MAX_NAME_LENGHT } from 'features/coverageDetails/validation/common/constant';
import { emailRegex as recruitmentEmailRegex } from 'features/eRecruit/id/validations/addNewCandidateSchema';

const PH_PHONE_NUMBER_LENGTH = 10;

// const REQUIRE_ERROR_NAMED = 'validation.required.named';
// const REQUIRED_INPUT = 'validation.input.required.field';
// const INVALID_INPUT = 'validation.input.invalid.field';
const REQUIRED_INPUT = 'Required field';
export const invalidInputMessage = () => t('common:form.invalidInputFormat');
const phInitialLeadFormData: AddLeadFormData = {
  firstName: '',
  lastName: '',
  mobilePhoneCountryCode: '',
  mobilePhoneNumber: '',
  genderCode: undefined,
  birthDate: '',
  email: '',
  interestedCategories: '',
};

const ibInitialLeadFormData: AddLeadFormData = {
  fullName: '',
  mobilePhoneCountryCode: '',
  mobilePhoneNumber: '',
  genderCode: undefined,
  birthDate: '',
  email: '',
  interestedCategories: '',
};

export const initialLeadFormData: AddLeadFormData =
  country === 'ib' ? ibInitialLeadFormData : phInitialLeadFormData;

const phRequiredAddLeadFieldsObj = {
  firstName: string()
    .required(REQUIRED_INPUT)
    .validateLeadName(invalidInputMessage()),
  lastName: string()
    .required(REQUIRED_INPUT)
    .validateLeadName(invalidInputMessage()),
  mobilePhoneCountryCode: string().required(REQUIRED_INPUT),
  mobilePhoneNumber: formPhoneNumber('mobilePhoneCountryCode')
    .max(
      PH_PHONE_NUMBER_LENGTH,
      t('form.phoneNumberTooLong', { length: PH_PHONE_NUMBER_LENGTH }),
    )
    .min(
      PH_PHONE_NUMBER_LENGTH,
      t('form.phoneNumberTooShort', { length: PH_PHONE_NUMBER_LENGTH }),
    ),
  // ! not using as const because t() to change language may be blocked by due to read-only
} as const;

const ibRequiredAddLeadFieldsObj = {
  fullName: string()
    .required(REQUIRED_INPUT)
    .validateLeadName(invalidInputMessage()),
  mobilePhoneCountryCode: string().required(REQUIRED_INPUT),
  mobilePhoneNumber: formPhoneNumber('mobilePhoneCountryCode'),
  // ! not using as const because t() to change language may be blocked by due to read-only
} as const;

const idRequiredAddLeadFieldsObj = {
  fullName: string()
    .required(REQUIRED_INPUT)
    .max(
      MAX_NAME_LENGHT,
      t('form.nameTooLongWithLength', { length: MAX_NAME_LENGHT }),
    )
    .validateLeadName(invalidInputMessage())
    .test({
      name: 'name-validation-enhancement',
      test(value, ctx) {
        return nameValidationWithTFunction(value, ctx);
      },
    }),
  mobilePhoneCountryCode: string().required(REQUIRED_INPUT),
  mobilePhoneNumber: formPhoneNumber('mobilePhoneCountryCode').required(
    REQUIRED_INPUT,
  ),
  // ! not using as const because t() to change language may be blocked by due to read-only
} as const;

const getRequiredAddLeadFieldsObj = (country: BuildCountry) => {
  switch (country) {
    case 'ib':
      return ibRequiredAddLeadFieldsObj;
    case 'id':
      return idRequiredAddLeadFieldsObj;
    case 'ph':
    default:
      return phRequiredAddLeadFieldsObj;
  }
};

export const requiredAddLeadFieldsObj = getRequiredAddLeadFieldsObj(country);

export type KeyOfRequiredAddLeadFieldsObj =
  keyof typeof requiredAddLeadFieldsObj;

export const addLeadValidationSchema = object({
  ...requiredAddLeadFieldsObj,
  genderCode: string<GenderCodeUnion>().nullable(),
  birthDate: string().nullable(),
  // productInterestedTypeCode: array().of(string()).nullable(),
  email: string().test('email', 'Invalid email format', value =>
    !value
      ? true
      : country === 'id'
      ? recruitmentEmailRegex.test(value)
      : strictEmailRegex.test(value),
  ),
  interestedCategories: string().nullable(),
});
