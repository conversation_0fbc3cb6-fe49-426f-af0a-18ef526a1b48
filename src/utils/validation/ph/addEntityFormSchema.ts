import { t } from 'i18next';
import { AddEntityFormData } from 'types/lead';
import { object, string } from 'yup';
import { formPhoneNumber } from 'utils/validation/customValidation';

const PH_PHONE_NUMBER_LENGTH = 10;

// const REQUIRE_ERROR_NAMED = 'validation.required.named';
// const REQUIRED_INPUT = 'validation.input.required.field';
// const INVALID_INPUT = 'validation.input.invalid.field';
const REQUIRED_INPUT = 'Required field';
export const INVALID_EMAIL_INPUT = 'The email doesn’t exist. Please try again.';
export const invalidInputMessage = () => t('form.invalidInput');

export const initialEntityFormData: AddEntityFormData = {
  companyName: '', // Entity Name
  occupationIndustryCode: '', // Industry / Nature of business
  email: '', // Primary contact details - Email
  mobilePhoneCountryCode: '', //  Primary contact details - country code
  mobilePhoneNumber: '', // Primary contact details - phone number
  homePhoneCountryCode: '', // Secondary contact details - country code
  homePhoneNumber: '', // Secondary contact details - phone number
  salutation: '',
  firstName: '',
  middleName: '',
  lastName: '',
  nameExtension: '',
  jobTitle: '',
  extra: {
    alts_blts_ref_num: '',
    service_branch: '',
    referrer_code: '',
    bank_customer_id: '',
  },
};

export const requiredAddEntityFieldsObj = {
  companyName: string()
    .required(REQUIRED_INPUT)
    .validateCompanyName(invalidInputMessage()),
  occupationIndustryCode: string().required(REQUIRED_INPUT),
  email: string().required(REQUIRED_INPUT),
  mobilePhoneCountryCode: string().required(REQUIRED_INPUT),
  mobilePhoneNumber: formPhoneNumber('mobilePhoneCountryCode')
    .max(
      PH_PHONE_NUMBER_LENGTH,
      t('form.phoneNumberTooLong', { length: PH_PHONE_NUMBER_LENGTH }),
    )
    .min(
      PH_PHONE_NUMBER_LENGTH,
      t('form.phoneNumberTooShort', { length: PH_PHONE_NUMBER_LENGTH }),
    ),
  homePhoneCountryCode: string().required(REQUIRED_INPUT),
  homePhoneNumber: formPhoneNumber('homePhoneCountryCode')
    .max(
      PH_PHONE_NUMBER_LENGTH,
      t('form.phoneNumberTooLong', { length: PH_PHONE_NUMBER_LENGTH }),
    )
    .min(
      PH_PHONE_NUMBER_LENGTH,
      t('form.phoneNumberTooShort', { length: PH_PHONE_NUMBER_LENGTH }),
    ),
  salutation: string().required(REQUIRED_INPUT),
  firstName: string()
    .required(REQUIRED_INPUT)
    .validateName(invalidInputMessage()),
  lastName: string()
    .required(REQUIRED_INPUT)
    .validateName(invalidInputMessage()),
  jobTitle: string()
    .required(REQUIRED_INPUT)
    .validateName(invalidInputMessage()),
} as const;

export type KeyOfRequiredAddEntityFieldsObj =
  keyof typeof requiredAddEntityFieldsObj;

export const addEntityValidationSchema = object({
  ...requiredAddEntityFieldsObj,
  middleName: string().nullable().validateName(invalidInputMessage()),
  nameExtension: string().nullable(),
});
