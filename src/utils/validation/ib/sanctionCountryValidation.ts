import { t } from 'i18next';
import { CountryRiskLevel, OptionList } from 'types/optionList';
import { AnyObject, TestContext } from 'yup';

const validateSanctionCountry = (
  value: string | undefined,
  ctx: TestContext<AnyObject>,
  field: 'COUNTRY' | 'NATIONALITY',
) => {
  const optionList = ctx.options.context?.optionList as OptionList<'ib'>;

  const riskLevel = (optionList?.[field]?.options ?? []).find(
    o => o?.value === value,
  )?.level;

  if (riskLevel === CountryRiskLevel.VERY_HIGH) {
    return ctx.createError({
      message: t('coverageDetails:validation.invalidApplication'),
    });
  }

  return true;
};

export default validateSanctionCountry;
