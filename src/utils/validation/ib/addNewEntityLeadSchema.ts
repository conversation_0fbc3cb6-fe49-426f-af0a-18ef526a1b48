// import { GenderCodeUnion } from 'types';
// import { object, string, TestContext, AnyObject } from 'yup';
// import { t } from 'i18next';
// import {
//   formPhoneNumber,
//   strictEmailRegex,
// } from 'utils/validation/customValidation';
// import { country } from 'utils/context';
// import {
//   MAX_NAME_LENGHT,
//   NAME_VALIDATE_REGEX,
//   INVALID_FORMAT,
// } from 'features/coverageDetails/validation/common/constant';
// import {
//   NAME_HAS_ADJACENT_SPACES,
//   NAME_HAS_HYPEN_NEXT_TO_BLANK,
//   NAME_HAS_SPECIAL_CHARACTER_AFTER_COMMA,
//   NAME_START_WITH_SPACE,
//   STRING_HAS_COMMA_FOLLOWING_BY_NON_ALPHABET_OR_SPACE_REGEX,
// } from 'features/coverageDetails/validation/common/name.validation';
// import { EntityLeadFormValues } from 'types/lead';

// const REQUIRED_INPUT = t('validation.required');
// export const invalidInputMessage = () => t('form.invalidInput');

// export const initialLeadData: EntityLeadFormValues = {
//   companyName: '',
//   firstName: '',
//   mobilePhoneCountryCode: '',
//   mobilePhoneNumber: '',
//   email: '',
//   natureOfBusiness: '',
// };

// const nameRule =
//   country === 'my'
//     ? string()
//         .required(REQUIRED_INPUT)
//         .max(MAX_NAME_LENGHT, t(INVALID_FORMAT))
//         .validateLeadName(t(INVALID_FORMAT))
//         .test({
//           name: 'name-validation-enhancement',
//           test(value, ctx) {
//             return nameValidationWithTFunction(value, ctx);
//           },
//         })
//     : string().required(REQUIRED_INPUT).validateLeadName(invalidInputMessage());

// export const requiredAddLeadFieldsObj = {
//   companyName: nameRule,
//   firstName: nameRule,
//   mobilePhoneCountryCode: string().required(REQUIRED_INPUT),
//   mobilePhoneNumber: formPhoneNumber('mobilePhoneCountryCode'),
// } as const;

// // export type KeyOfRequiredAddLeadFieldsObj =
// //   keyof typeof requiredAddLeadFieldsObj;

// export const addNewEntityLeadSchema = object({
//   ...requiredAddLeadFieldsObj,
//   email: string().test('email', 'Invalid email format', value =>
//     !value ? true : strictEmailRegex.test(value),
//   ),
//   natureOfBusiness: string().nullable(),
// });

// function nameValidationWithTFunction(
//   value: string,
//   ctx: TestContext<AnyObject>,
// ) {
//   if (value.startsWith(' '))
//     return ctx.createError({ message: t(NAME_START_WITH_SPACE) });

//   if (value.includes('  '))
//     return ctx.createError({ message: t(NAME_HAS_ADJACENT_SPACES) });

//   if (value.includes(' -') || value.includes('- '))
//     return ctx.createError({ message: t(NAME_HAS_HYPEN_NEXT_TO_BLANK) });

//   if (STRING_HAS_COMMA_FOLLOWING_BY_NON_ALPHABET_OR_SPACE_REGEX.test(value))
//     return ctx.createError({
//       message: t(NAME_HAS_SPECIAL_CHARACTER_AFTER_COMMA),
//     });

//   return true;
// }
