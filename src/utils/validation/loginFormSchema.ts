import { object, string } from 'yup';
import { t } from 'i18next';

const REQUIRE_ERROR = 'validation.required';

// defined as function for change language rerender of t function

// Deprecated as login button is disabled if no agentId/ password filled
export const loginFormSchema = () =>
  object().shape({
    agentId: string().required(t(REQUIRE_ERROR)),
    password: string().required(t(REQUIRE_ERROR)),
  });
