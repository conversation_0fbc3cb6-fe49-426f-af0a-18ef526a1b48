import { isAxiosError } from 'axios';
import { ResponseMsgListItem } from 'types';
import ComplianceError from 'utils/typescript/ComplianceError';

function extractMessagesFromAxiosError(error: unknown): ResponseMsgListItem[] {
  if (!isAxiosError(error)) {
    return [];
  }

  // Common places a message may appear
  const data = error.response?.data;

  return data?.messageList;
}

export async function withCompliance<T>(
  callback: () => Promise<T>,
): Promise<T> {
  try {
    return await callback();
  } catch (error: unknown) {
    // 1) Axios errors
    const messages = extractMessagesFromAxiosError(error);
    if (
      messages &&
      messages.some(msg => ComplianceError.isComplianceError(msg.code ?? ''))
    ) {
      throw new ComplianceError(
        messages
          .filter(msg => ComplianceError.isComplianceError(msg.code ?? ''))
          .map(msg => msg.content),
      );
    }

    // Otherwise rethrow original
    throw error;
  }
}
