import { decimalPlacesByCountry } from 'constants/decimalPlaces';
import { mask as rnmtMask } from 'react-native-mask-text';

export function formatCurrency(
  value: string | number = 0,
  precision: null | number = null,
) {
  'worklet';
  if (precision !== null && precision < 0) {
    throw new Error('Negative precision ' + precision);
  }
  if (value === null) {
    value = 0;
  }
  if (typeof value === 'string') {
    value = Number(value);
  }
  let numericStr = value.toString();
  if (precision !== null && !Number.isInteger(value)) {
    numericStr =
      value.toString().match(`^-?\\d+(?:\\.\\d{0,${precision}})?`)?.[0] ||
      value.toString();
  }
  const integerAndDecimal = numericStr.split('.').filter(Boolean);
  integerAndDecimal[0] = integerAndDecimal[0].replace(
    /(\d)(?=(\d\d\d)+(?!\d))/g,
    '$1,',
  );
  return integerAndDecimal.join('.');
}

export function formatCurrencyWithMask(
  val: number | undefined,
  decimalPlace = decimalPlacesByCountry,
) {
  if (decimalPlace < 0)
    throw new Error('Invalid decimalPlace: ' + decimalPlace);

  const formattedValue = String(val || 0).replace(/[^0-9.]/g, '');

  const [integer, decimal] = formattedValue.split('.');
  let decimalText = decimal ? '.' + decimal.slice(0, decimalPlace) : '';

  if (decimalPlace) {
    if (!decimal) {
      decimalText = '';
    }

    if (decimal && decimal.length <= decimalPlace) {
      decimalText = `.${decimal}${'0'.repeat(decimalPlace - decimal.length)}`;
    }
  }

  return (
    rnmtMask(integer, '', 'currency', {
      groupSeparator: ',',
    }) + (decimalPlace === 0 ? '' : decimalText)
  );
}
