import get from 'lodash/get';
import set from 'lodash/set';

export const extractByKeys = <T>(obj: T, paths: string[]) => {
  const res = {};

  for (const path of paths) {
    const value = get(obj, path);
    set(res, path, value);
  }

  return res;
};

export const cloneDeep = <T>(obj: T): T => JSON.parse(JSON.stringify(obj));

export const removeNullProperty = <T>(obj: T): T => {
  if (!obj) {
    return obj;
  }
  const cloneObj = cloneDeep(obj);
  Object.keys(cloneObj).forEach(k => {
    if (cloneObj[k as keyof T] === null) {
      delete cloneObj[k as keyof T];
    }
  });

  return cloneObj;
};

export function getTypedObjectValues<T extends object>(obj: T): T[keyof T][] {
  return Object.values(obj) as T[keyof T][];
}
