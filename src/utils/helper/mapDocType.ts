import { ID_COUNTRY } from "constants/optionList";
import { File } from "types/case";
import { Party } from "types/party";

export const mapDocTypeWithDocTitle = (
    documents: Record<string, {
        type: string;
        images: string[];
    }>,
    file: File,
    party: Party | undefined,
    t: (path: string) => string,
  ) => {
      if (!documents[file.docType]) {
        documents[file.docType] = {
          type: mapDocTitleByTypeAndParty(file.docType, party, t),
          images: [],
        };
      }
      documents[file.docType].images.push(file.fileName);
      return documents;
  };

  export const mapDocTitleByTypeAndParty = (
    docType: string,
    party: Party | undefined,
    t: (path: string) => string
  ) => {
    const { person } = party || {};
    let docTypeTitle = t(`eApp:documentUpload.${docType}`)
    // handle frontID title logic
    if (docType === 'frontID') {
      if (person?.nationality === ID_COUNTRY) {
        if ((person?.age || 0) >= 17) {
          docTypeTitle = t('eApp:documentUpload.frontID.ktp');
        } else if ((person?.age || 0) < 17) {
          docTypeTitle = t('eApp:documentUpload.frontID.birthCert');
        }
      } else {
        docTypeTitle = t('eApp:documentUpload.frontID.passport');
      }
    }
    return docTypeTitle;
  };