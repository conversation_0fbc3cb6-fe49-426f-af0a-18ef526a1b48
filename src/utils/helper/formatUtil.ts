import { format, isValid, parseISO, parse } from 'date-fns';
import { country } from 'utils/context';

export const formatSales = (sale: number) => {
  if (sale === null) return '';
  const IntegerAndDecimal = sale.toString().split('.');
  IntegerAndDecimal[0] = IntegerAndDecimal[0].replace(
    /\B(?=(\d{3})+(?!\d))/g,
    ',',
  );
  return IntegerAndDecimal.join('.');
};

export const formatPendingRequirementDate = (
  dateString: Date | undefined | null | string | number,
) => {
  if (!dateString || dateString === null) {
    return '';
  }

  const date = new Date(dateString);
  return date
    .toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    })
    .replace(',', '-')
    .replace(' ', '-');
};

export const capitalFirst = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const dateFormatUtil = (
  date: Date | string,
  formatVariant: 'dash' | 'slash' = 'slash',
): string => {
  const result = typeof date === 'object' ? (date as Date) : parseISO(date);
  if (isValid(result)) {
    switch (country) {
      case 'my':
      case 'ib':
      case 'id':
        if (formatVariant === 'dash') {
          return format(result, 'dd-MM-yyyy');
        }
        return format(result, 'dd/MM/yyyy');
      case 'ph':
      default:
        if (formatVariant === 'dash') {
          return format(result, 'MM-dd-yyyy');
        }
        return format(result, 'MM/dd/yyyy');
    }
  }

  return date?.toString();
};

export const dateTimeFormatUtil = (
  date: Date | string,
  formatVariant: 'dash' | 'slash' = 'slash',
): string => {
  const result = typeof date === 'object' ? (date as Date) : parseISO(date);
  return (
    dateFormatUtil(result, formatVariant) +
    ' ' +
    result.toLocaleTimeString('en-US', { hour12: false })
  );
};

export const dateFormatWithSlashUtil = (date: Date | string): string => {
  return dateFormatUtil(date, 'slash');
};

export const dateFormatWithDashUtil = (date: Date | string): string => {
  return dateFormatUtil(date, 'slash');
};

export const formatSignatureDate = (
  dateString: Date | undefined | null | string | number,
  withTime?: boolean,
) => {
  if (!dateString || dateString === null) {
    return '';
  }

  return format(
    new Date(dateString),
    withTime ? 'd MMM yyyy hh:mm:ss' : 'd MMM yyyy',
  );
};

export const formatENDate = (date: Date) => {
  if (!date) {
    return '';
  }

  return new Date(date)
    .toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    })
    .replace(/([a-zA-Z]+) (\d+), (\d+)/, '$2 $1 $3');
};
export const formatSeconds = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  const minutesStr = minutes.toString();
  const secondsStr =
    remainingSeconds < 10
      ? '0' + remainingSeconds
      : remainingSeconds.toString();

  return minutesStr + ' min ' + secondsStr + ' secs';
};
