import {
  addYears,
  differenceInDays,
  differenceInMonths,
  intervalToDuration,
  isAfter,
  isFuture,
} from 'date-fns';
import { country } from 'utils/context';
import { TimeUnit } from './dateUtil';

export const calculateAge = (dob: Date): number => {
  if (!dob || isNaN(dob.getTime())) return 0;
  if (isFuture(dob)) {
    return 0;
  }
  const age =
    intervalToDuration({
      start: dob.setHours(0, 0, 0),
      end: new Date(),
    }).years ?? 0;
  switch (country) {
    case 'my':
    case 'ib':
      if (isAfter(new Date(), addYears(new Date(dob), age))) {
        return age + 1;
      }
      return age;
    case 'ph':
    case 'id':
    default:
      return age;
  }
};

export const calculateAgeDiff = (dob: Date, unit?: TimeUnit): number => {
  switch (unit) {
    case 'year':
    case 'years':
      return calculateAge(dob);
    case 'month':
    case 'months':
      return differenceInMonths(new Date(), dob);
    case 'day':
    case 'days':
      return differenceInDays(new Date(), dob);
    default:
      return calculateAge(dob);
  }
};
