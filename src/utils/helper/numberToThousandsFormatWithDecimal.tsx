  //-- With all tailing decimials

export const numberToThousandsFormatWithDecimal = (
    x: number | null,
    toFixed?: number,
    customReturnString?: string,
  ) => {
    if (x == null) return customReturnString ? customReturnString : '';
    const IntegerAndDecimal = x
      .toString()
      .split('.');
    IntegerAndDecimal[0] = IntegerAndDecimal[0].replace(
      /\B(?=(\d{3})+(?!\d))/g,
      ',',
    );
    return IntegerAndDecimal.join('.');
  };
