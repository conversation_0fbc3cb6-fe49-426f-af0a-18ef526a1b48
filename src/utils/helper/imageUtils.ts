import * as ExpoImagePicker from 'expo-image-picker';
import { addErrorBottomToast } from 'cube-ui-components';
import {
  ImageResult,
  ImageManipulator,
  SaveFormat,
} from 'expo-image-manipulator';
import { <PERSON>uffer } from 'buffer';
import GATracking from './gaTracking';
import * as FileSystem from 'expo-file-system';

const getFileSize = (base64String: string) => {
  const binaryData = Buffer.from(base64String, 'base64');
  const fileSizeInBytes = binaryData.length;
  const fileSizeInMB = fileSizeInBytes / (1024 * 1024); // Convert bytes to megabytes

  return {
    Byte: fileSizeInBytes,
    MB: fileSizeInMB,
  };
};

export const compressImage = async (
  result: ExpoImagePicker.ImagePickerResult,
  maxSizeInMB: number,
  forceFirstCompression = false, // default to skip first compression given by do-while loop
): Promise<[ImageResult | null, number | null]> => {
  try {
    if (result.canceled) return [null, null];
    const maxSizeInByte = maxSizeInMB * 1024 * 1024;
    let compression = 1; // Start with full quality (no compression)

    let sizeUnderObservation: {
      Byte: number;
      MB: number;
    };
    let widthUnderObservation: number;
    let heightUnderObservation: number;
    let outputImage: ImageResult | null = null;

    const { height, width, uri, base64: rawBase64 } = result.assets[0];
    widthUnderObservation = width;
    heightUnderObservation = height;
    // Ensure base64 is always truthful
    let ensuredBase64 = rawBase64;
    if (!ensuredBase64 && uri) {
      try {
        ensuredBase64 = await FileSystem.readAsStringAsync(uri, {
          encoding: FileSystem.EncodingType.Base64,
        });
      } catch (error) {
        console.warn(
          `Failed to convert image at ${uri} to base64. Error: `,
          error,
          "-- when the input asset doesn't have a valid base64 representation",
        );
        console.warn(
          'you original input image asset has its base64 missed. Please check',
        );
      }
    }


    outputImage = {
      height,
      width,
      uri,
      base64: ensuredBase64 || undefined,
    };
    // make sure the image is in jpeg format
    const imageRef = await ImageManipulator.manipulate(uri).renderAsync();
    const manipulatedResult = await imageRef.saveAsync({
      format: SaveFormat.JPEG,
      base64: true,
    });

    sizeUnderObservation = manipulatedResult.base64
      ? getFileSize(manipulatedResult.base64)
      : { Byte: 0, MB: 0 };

    if (sizeUnderObservation.Byte <= maxSizeInByte && !forceFirstCompression) {
      return [manipulatedResult, sizeUnderObservation.MB];
    }

    do {
      const interval = compression > 0.5 ? 0.1 : 0.05;
      compression -= interval; // Decrease compression quality
      const manipulatedImageRef = await ImageManipulator.manipulate(uri)
        .resize({
          width: widthUnderObservation * compression,
          height: heightUnderObservation * compression,
        })
        .renderAsync();

      const manipulatedResult = await manipulatedImageRef.saveAsync({
        compress: compression,
        format: SaveFormat.JPEG,
        base64: true,
      });

      const {
        width: maniWidth,
        height: maniHeight,
        base64: maniBase64,
      } = manipulatedResult;

      if (!maniBase64) {
        addErrorBottomToast([
          { message: `Failed to process image` }, //FIXME: translation
        ]);
        GATracking.log(['---- compressImage ---- Failed to process image']);
        return [null, null];
      }

      sizeUnderObservation = getFileSize(maniBase64);
      widthUnderObservation = maniWidth;
      heightUnderObservation = maniHeight;
      outputImage = manipulatedResult;
    } while (sizeUnderObservation.Byte > maxSizeInByte && compression > 0.05);
    return [outputImage, sizeUnderObservation.MB];
  } catch (error) {
    GATracking.log(['---- compressImage ---- Failed to compress image', error]);
    return [null, null];
  }
};

export const getBase64Image = async (imageUri: string) => {
  if (!imageUri) return '';
  try {
    const response = await fetch(imageUri);
    const blob = await response.blob();
    const reader = new FileReader();
    return new Promise<string>((resolve, reject) => {
      reader.onloadend = () => {
        const base64data = reader.result as string;
        // console.log('Base64 Image:', base64data);
        resolve(base64data);
      };
      reader.onerror = error => {
        console.error('Error reading blob as base64:', error);
        reject('');
      };
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Error fetching image:', error);
    return '';
  }
};
