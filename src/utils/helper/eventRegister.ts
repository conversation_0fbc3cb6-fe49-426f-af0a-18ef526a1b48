import { AppEventArgsList } from 'types/event';

class EventRegister {
  static _Listeners: {
    count: number;
    refs: Record<string, { name: unknown; callback: (args: any) => void }>;
  } = {
    count: 0,
    refs: {},
  };

  static addEventListener<EventKey extends keyof AppEventArgsList>(
    eventName: EventKey,
    callback: (args: AppEventArgsList[EventKey]) => void,
  ) {
    EventRegister._Listeners.count++;
    const eventId = 'l' + EventRegister._Listeners.count;
    EventRegister._Listeners.refs[eventId] = {
      name: eventName,
      callback,
    };
    return eventId;
  }

  static removeEventListener(id: string) {
    return delete EventRegister._Listeners.refs[id];
  }

  static removeAllListeners() {
    let removeError = false;
    Object.keys(EventRegister._Listeners.refs).forEach(_id => {
      const removed = delete EventRegister._Listeners.refs[_id];
      removeError = !removeError ? !removed : removeError;
    });
    return !removeError;
  }

  static emit<EventKey extends keyof AppEventArgsList>(
    eventName: EventKey,
    data: AppEventArgsList[EventKey],
  ) {
    Object.keys(EventRegister._Listeners.refs).forEach(_id => {
      if (
        EventRegister._Listeners.refs[_id] &&
        eventName === EventRegister._Listeners.refs[_id].name
      )
        EventRegister._Listeners.refs[_id].callback(data);
    });
  }
}

export { EventRegister };
