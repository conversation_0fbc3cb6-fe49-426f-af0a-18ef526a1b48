/**
 * for displaying optional label/input by rows
 * @param array
 * @param size
 * @returns
 */
export function chunk<T>(array: Array<T>, size = 1) {
  if (!array.length) return [];

  const chunks = [];
  let i = 0;

  while (i < array.length) {
    chunks.push(array.slice(i, i + size));
    i += size;
  }

  if (chunks[chunks.length - 1].length < size) {
    const nullFilled = Array.from(
      new Array(size - chunks[chunks.length - 1].length),
    ).map(() => null);
    chunks[chunks.length - 1] = [...chunks[chunks.length - 1], ...nullFilled];
  }

  return chunks;
}
