import * as Crypto from 'expo-crypto';
import { format } from 'date-fns';
import {
  defaultAffinityReferenceInfo,
  defaultBancaReferenceInfo,
} from 'constants/defaultValues';
import { CHANNELS } from 'types/channel';
import useBoundStore from 'hooks/useBoundStore';

export type BltsRefInfo = {
  alts_blts_ref_num: string;
  referrer_code?: string;
  service_branch?: string | null;
};

export function generateBltsRefNum() {
  const timestamp = format(new Date(), 'yyMMddHHmmss');
  const randomChars = Crypto.randomUUID().substring(0, 4);
  return `SGN${randomChars}${timestamp}`;
}

export function generateBltsRefInfo(): BltsRefInfo {
  return {
    alts_blts_ref_num: generateBltsRefNum(),
    referrer_code: defaultBancaReferenceInfo?.referrerCode,
    service_branch: defaultBancaReferenceInfo?.serviceBranch,
  };
}

// --Jessica: change to use following: 4-digit deviceId is stored at Zustand
export const generateBltsRefNumStored = (
  deviceId: string | null,
  setDeviceId: (deviceId: string) => void,
): string => {
  const timestamp = format(new Date(), 'yyMMddHHmmss');
  const randomChars = Crypto.randomUUID().substring(0, 4);

  if (deviceId !== null) {
    return `SGN${deviceId}${timestamp}`;
  } else {
    setDeviceId(randomChars);
    return `SGN${randomChars}${timestamp}`;
  }
};

export function useGenerateBltsRefInfoStored(channel = 'BANCA'): BltsRefInfo {
  const deviceId: string | null = useBoundStore(store => store.deviceId);
  const setDeviceId = useBoundStore(store => store.appActions.setDeviceId);
  return {
    alts_blts_ref_num: generateBltsRefNumStored(deviceId, setDeviceId),
    referrer_code:
      channel === CHANNELS.BANCA
        ? defaultBancaReferenceInfo?.referrerCode
        : defaultAffinityReferenceInfo?.referrerCode,
    service_branch:
      channel === CHANNELS.BANCA
        ? defaultBancaReferenceInfo?.serviceBranch
        : '',
  };
}
