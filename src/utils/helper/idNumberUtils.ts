import { maskNricNumber } from 'components/IdNumberField';
import { isSameDay, isValid, parse } from 'date-fns';
import { mask } from 'react-native-mask-text';
import { Gender } from 'types/person';

export const newNRICtoDOB = (icNumber: string) => {
  const dob = icNumber.substring(0, 6);
  const year = dob.substring(0, 2);
  const month = dob.substring(2, 4);
  const day = dob.substring(4, 6);
  const fullYear =
    (parseInt(year) <= new Date().getFullYear() % 100 ? '20' : '19') + year; // Adjusting for the year format

  let dateOfBirth = new Date(`${fullYear}-${month}-${day}`);
  if (dateOfBirth > new Date(`20${year}-${month}-${day}`)) {
    dateOfBirth = new Date(`19${year}-${month}-${day}`);
  }
  return isNaN(dateOfBirth.getTime()) ? undefined : dateOfBirth;
};

export const convertNewNRICtoDOBAndGender = (
  icNumber: string,
): {
  dateOfBirth: Date | undefined;
  gender: string | undefined;
} => {
  if (!isValidNewNRIC(icNumber)) {
    return {
      dateOfBirth: undefined,
      gender: undefined,
    };
  }

  // Extract date of birth
  const dateOfBirth = newNRICtoDOB(icNumber.substring(0, 6));

  // Extract gender
  const genderDigit = icNumber.charAt(11);
  const gender = parseInt(genderDigit) % 2 === 0 ? Gender.FEMALE : Gender.MALE;

  return {
    dateOfBirth,
    gender,
  };
};

export const isValidNewNRIC = (icNumber: string | undefined) => {
  if (!icNumber) return true; // ignore empty value
  // Check if the IC number has the correct format
  const icRegex = /^\d{6}\d{2}\d{4}$/;
  if (!icRegex.test(icNumber)) {
    return false;
  }

  // Extract date of birth
  const dob = icNumber.substring(0, 6);
  const year = dob.substring(0, 2);
  const month = dob.substring(2, 4);
  const day = dob.substring(4, 6);

  // Validate the date components
  if (
    !checkValidDob(parseInt('20' + year), parseInt(month), parseInt(day)) &&
    !checkValidDob(parseInt('19' + year), parseInt(month), parseInt(day))
  ) {
    return false;
  }

  return true;
};

const checkValidDob = (year: number, month: number, day: number) => {
  const dob = parseDateStringToLocalDate(`${year}-${month}-${day}`);
  return isValid(dob) && dob.getTime() <= new Date().getTime();
};

export const validateNewNRIC = (
  nric: string,
  dob: Date | undefined,
  gender: Gender | undefined,
) => {
  // Extract date of birth
  const nricDob = nric.substring(0, 6);
  const year = nricDob.substring(0, 2);
  const month = nricDob.substring(2, 4);
  const day = nricDob.substring(4, 6);
  const parsedDateOfBirthLower = parseDateStringToLocalDate(
    `19${year}-${month}-${day}`,
  );
  const parsedDateOfBirthUpper = parseDateStringToLocalDate(
    `20${year}-${month}-${day}`,
  );

  // Extract gender
  const genderDigit = nric.charAt(11);
  const parsedGender =
    parseInt(genderDigit) % 2 === 0 ? Gender.FEMALE : Gender.MALE;
  if (
    !dob ||
    !parsedDateOfBirthLower ||
    !parsedDateOfBirthUpper ||
    !gender ||
    !parsedGender
  ) {
    return false;
  }
  return (
    (isSameDay(dob, parsedDateOfBirthUpper) ||
      isSameDay(dob, parsedDateOfBirthLower)) &&
    gender === parsedGender
  );
};

export const formatNewNricNumber = (idNumber: string) => {
  return mask(idNumber, maskNricNumber.pattern);
};

// parse DateString from yyyy-MM-dd to local date
const parseDateStringToLocalDate = (dateString: string) => {
  return parse(dateString, 'yyyy-MM-dd', new Date());
};
