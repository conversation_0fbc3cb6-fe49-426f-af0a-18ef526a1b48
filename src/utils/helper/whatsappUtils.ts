import { addErrorToast } from 'cube-ui-components';
import * as Linking from 'expo-linking';

export const whatsappHandler = async (mobilePhone?: string) => {
  const whatsAppUrl = mobilePhone
    ? `http://api.whatsapp.com/send?phone=${mobilePhone}`
    : ``;

  const isAvailable = await Linking.canOpenURL(whatsAppUrl);

  try {
    if (isAvailable) {
      await Linking.openURL(whatsAppUrl);
    } else {
      addErrorToast([
        {
          message: 'Phone number is not valid',
        },
      ]);
    }
  } catch (error) {
    addErrorToast([{ message: 'Phone number is not valid' }]);
  }
};
