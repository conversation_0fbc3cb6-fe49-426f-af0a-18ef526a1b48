import { GroupCode, ListProduct, Product, ProductGroup } from 'types/products';
import { country } from 'utils/context';

export const mapProducts = (products: Product[]) => {
  // Check if any product is missing the productGroup
  const allHaveValidProductGroup = products.every(product =>
    Boolean(product.productGroup),
  );
  // temporarily hard-coded for ID, need to remove later
  if (!allHaveValidProductGroup || country === 'id') {
    return products; // Return the original array if any product lacks a productGroup
  }

  // Using reduce to iterate over products and accumulate grouped data
  return products.reduce((prevValue: ListProduct[], currentValue: Product) => {
    const productGroups = currentValue.productGroup
      ? Array.isArray(currentValue.productGroup)
        ? currentValue.productGroup.map((group: ProductGroup) =>
            group.en.split(','),
          )
        : currentValue.productGroup.en.split(',')
      : [];
    // Iterating over each group in the array
    productGroups.flat().forEach(group => {
      // Finding index of the group in the accumulated result
      const index = prevValue.findIndex(item => item.title === group.trim());
      // If the group is found, add the current product to its data array
      if (index !== -1) {
        prevValue[index].data = [...prevValue[index].data, currentValue];
      } else {
        // If the group is not found, create a new group entry
        prevValue.push({
          title: group.trim() as GroupCode,
          data: [currentValue],
        });
      }
    });

    return prevValue;
  }, []);
};

export const mapProductsForRecommendation = (products: Product[]) => {
  // Using reduce to iterate over products and accumulate grouped data
  return products.reduce((prevValue: ListProduct[], currentValue: Product) => {
    const productGroups = currentValue.productGroup
      ? Array.isArray(currentValue.productGroup)
        ? currentValue.productGroup.map((group: ProductGroup) =>
            group.en.split(','),
          )
        : currentValue.productGroup.en.split(',')
      : [];
    // Iterating over each group in the array
    productGroups.flat().forEach(group => {
      // Finding index of the group in the accumulated result
      const index = prevValue.findIndex(item => item.title === group.trim());
      // If the group is found, add the current product to its data array
      if (index !== -1) {
        prevValue[index].data = [...prevValue[index].data, currentValue];
      } else {
        // If the group is not found, create a new group entry
        prevValue.push({
          title: group.trim() as GroupCode,
          data: [currentValue],
        });
      }
    });

    return prevValue;
  }, []);
};
