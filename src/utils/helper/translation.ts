import { t } from 'i18next';
import { TranslationField } from 'types';
import { i18n } from 'utils';

export function renderLabelByLanguage(labels?: TranslationField) {
  if (!labels) {
    return;
  }
  const result = labels[i18n.language as keyof TranslationField];
  if (!result) {
    return labels.en?.toString() || '';
  }
  return result.toString();
}
export const getMaxSizeInMbErrorMessage = (maxSizeInMB?: number) => {
  return t('upload.invalidFileSize', {
    maxSize: maxSizeInMB ? `(${maxSizeInMB} MB)` : '',
  });
};
