import {
  AgentAction,
  AuthSlice,
  AuthState,
  BioMetricInfo,
  CubeAuthToken,
  CubeIdToken,
  StoreSlice,
} from 'types';
import { ObjectUtil } from 'utils';
import { StateCreator } from 'zustand';
import { jwtDecode } from 'jwt-decode';
import { queryClient } from 'api/RootQueryClient';
import dayjs from 'dayjs';

export const createAuthSlice: StateCreator<
  StoreSlice,
  [],
  [],
  AuthSlice
> = set => ({
  auth: ObjectUtil.cloneDeep(initialState),
  authActions: {
    login: (accessToken: string, refreshToken: string, idToken?: string) =>
      set(state => {
        const { sub } = jwtDecode(accessToken) as CubeAuthToken;
        state.auth.authInfo.accessToken = accessToken;
        state.auth.authInfo.refreshToken = refreshToken;
        state.auth.authInfo.idToken = idToken;
        state.auth.agentCode = sub;

        if (idToken) {
          const { channel } = jwtDecode(idToken) as CubeIdToken;
          state.auth.channel = channel;
        }

        console.log(
          `🔑 Previous login agent code: ${state.auth.previousAgentCode} | 🔑 Current login agent code: ${state.auth.agentCode}`,
        );

        if (state.auth.previousAgentCode !== state.auth.agentCode) {
          console.log('🧹 ~ reset zustand persist data');
          state.leadSearch.recentSearchCustomerItem = [];
          state.leadSearch.recentSearchLeadItem = [];
          state.candidateSearch.recentSearchCandidateItem = [];
          // Reset ecoach data when user switches to prevent showing previous user's state
          state.ecoach = {
            isQuickfire: true,
            quickfireVideoUrl: null,
            quickfireCompletedHearts: 40,
            videoToAudioSpeed: 10,
            objectionHandlingProductConfig: undefined,
            historyTabIndex: 0,
            historyTabScrollX: 0,
          };
        }

        state.auth.previousAgentCode = state.auth.agentCode; // update previousAgentCode after login and reset zustand persist data

        state.auth.isLoggedInUntilAppKilled = true;

        return state;
      }),
    logout: () =>
      set(state => {
        state.auth.authInfo = {
          accessToken: null,
          refreshToken: null,
        };
        state.auth.agentCode = null;
        state.lead.isExpiredLeadChecked = false;
        state.lead.expiredLeadSkippedTime = null;
        state.home.agentReward.isShown = false;
        queryClient.clear();
        return state;
      }),
    saveBiometricInfo: (biometricInfo: BioMetricInfo) =>
      set(state => {
        state.auth.bioMetricInfo = biometricInfo;
        return state;
      }),
    enableBiometric: () =>
      set(state => {
        state.auth.bioMetricEnabled = true;
        return state;
      }),
    disableBiometric: () =>
      set(state => {
        state.auth.bioMetricEnabled = false;
        return state;
      }),
  },
});

const initialState: AuthState = {
  agentCode: null,
  previousAgentCode: null,
  authInfo: {
    accessToken: null,
    refreshToken: null,
  },
  agentInfo: {
    profilePic: '',
    personalDetails: {
      title: '',
      firstName: '',
      lastName: '',
      fullName: '',
      dateOfBirth: '',
    },
    contacts: {
      email: '',
      mobilePhone: '',
    },
    achievements: {
      isELITE: false,
      isMDRT: false,
    },
    licenses: [],
    companyTitle: '',
    agentStatus: {
      isAgentLicenseActive: null,
      isAgentLicenseExpired: null,
      canSellUnitlinkProduct: null,
    },
    trainingCourses: [],
  },
  bioMetricEnabled: null,
  bioMetricInfo: {
    enrolled: false,
    supported: [],
  },
  isLoggedInUntilAppKilled: false,
};
