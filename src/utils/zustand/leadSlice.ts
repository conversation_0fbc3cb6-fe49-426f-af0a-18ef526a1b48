import produce from 'immer';
import {
  LeadFormValues,
  LeadsFilters,
  LeadSlice,
  LeadState,
  StoreSlice,
} from 'types';
import { ObjectUtil } from 'utils';
import { countryModuleSellerConfig } from 'utils/config/module';
import { StateCreator } from 'zustand';

export const createLeadSlice: StateCreator<
  StoreSlice,
  [],
  [],
  LeadSlice
> = set => ({
  lead: ObjectUtil.cloneDeep(initialState),
  leadActions: {
    today: {
      toggleSort: () =>
        set(state => {
          state.lead.today.sortByNewest = !state.lead.today.sortByNewest;
          return state;
        }),
      updateFilteredLength: filteredLength =>
        set(state => {
          state.lead.today.filteredLength = filteredLength;
          return state;
        }),
      updateFilter: (leadFilters: LeadsFilters) =>
        set(state => {
          state.lead.today.filters = leadFilters;
          return state;
        }),
      setShowToolTip: (show: boolean) =>
        set(state => {
          state.lead.today.showToolTip = show;
          return state;
        }),
    },
    others: {
      toggleSort: () =>
        set(state => {
          state.lead.others.sortByNewest = !state.lead.others.sortByNewest;
          return state;
        }),
      updateFilteredLength: filteredLength =>
        set(state => {
          state.lead.others.filteredLength = filteredLength;
          return state;
        }),
      updateFilter: (leadFilters: LeadsFilters) =>
        set(state => {
          state.lead.others.filters = leadFilters;
          return state;
        }),
    },
    search: {
      updateFilter: (leadFilters: LeadsFilters) =>
        set(state => {
          state.lead.leadSearch.filters = leadFilters;
          return state;
        }),
    },
    updateSearchBarHeight: (height: number) =>
      set(state => {
        state.lead.searchBarHeight = height;
        return state;
      }),
    updatePrimaryBarHeight: (height: number) =>
      set(state => {
        state.lead.primaryBarHeight = height;
        return state;
      }),
    updateSecondaryBarHeight: (height: number) =>
      set(state => {
        state.lead.secondaryBarHeight = height;
        return state;
      }),
    updateTertiaryBarHeight: (height: number) =>
      set(state => {
        state.lead.tertiaryBarHeight = height;
        return state;
      }),
    resetFilters: () =>
      set(state => {
        state.lead.today.filters = initialState.today.filters;
        state.lead.others.filters = initialState.others.filters;
        state.lead.leadSearch.filters = initialState.leadSearch.filters;
        state.lead.today.sortByNewest = initialState.today.sortByNewest;
        state.lead.others.sortByNewest = initialState.others.sortByNewest;
        return state;
      }),
    resetTodayFilters: () =>
      set(state => {
        state.lead.today.filters = initialState.today.filters;
        state.lead.today.sortByNewest = initialState.today.sortByNewest;
        return state;
      }),
    activities: {
      toggleSort: () =>
        set(state => {
          state.lead.activities.sortByNewest =
            !state.lead.activities.sortByNewest;
          return state;
        }),
      resetSort: () =>
        set(state => {
          state.lead.activities.sortByNewest =
            initialState.activities.sortByNewest;
          return state;
        }),
    },
    addLeadForm: {
      saveDraft: (formData: LeadFormValues) =>
        set(state => {
          state.lead.draftAddLeadForm = {
            data: formData,
            savedAt: new Date().toISOString(),
          };
          return state;
        }),
      clearDraft: () =>
        set(state => {
          state.lead.draftAddLeadForm = null;
          return state;
        }),
    },
  },
  leadSearchActions: {
    setSearchModalShow: () =>
      set(state => {
        state.leadSearch.searchModalShow = !state.leadSearch.searchModalShow;
        return state;
      }),
    showHeaderSearch: () =>
      set(state => {
        state.leadSearch.isHeaderSearchShow = true;
        return state;
      }),
    hideHeaderSearch: () =>
      set(state => {
        state.leadSearch.isHeaderSearchShow = false;
        return state;
      }),
    updateRecentLeadSearch: item =>
      set(
        produce((state: StoreSlice) => {
          state.leadSearch.recentSearchLeadItem.unshift(item);
        }),
      ),
    updateRecentCustomerSearch: item =>
      set(
        produce((state: StoreSlice) => {
          state.leadSearch.recentSearchCustomerItem.unshift(item);
        }),
      ),
    trimRecentSearch: () =>
      set(
        produce((state: StoreSlice) => {
          state.leadSearch.recentSearchLeadItem =
            state.leadSearch.recentSearchLeadItem.slice(0, 3);
          state.leadSearch.recentSearchCustomerItem =
            state.leadSearch.recentSearchCustomerItem.slice(0, 3);
        }),
      ),
  },
  leadSearch: ObjectUtil.cloneDeep(initialState.leadSearch),
  leadProfileActions: {
    updateInquiringLeadId: (leadId: string) =>
      set(state => {
        state.lead.inquiringLeadId = leadId;
        return state;
      }),
    updateTopBarHeight: (height: number) =>
      set(state => {
        if (height > state.lead.profile.topBarHeight)
          state.lead.profile.topBarHeight = height;
        return state;
      }),
  },
  expiredLeadActions: {
    setCheckedExpiredLeads: () =>
      set(state => {
        state.lead.isExpiredLeadChecked = true;
        return state;
      }),

    setExpiredLeadSkippedTime: () =>
      set(state => {
        state.lead.expiredLeadSkippedTime = new Date().toISOString();
        return state;
      }),
  },
});

const initialState: LeadState = {
  today: {
    sortByNewest: countryModuleSellerConfig.lead?.today?.sortByNewest ?? false,
    filters: {
      type: {},
      source: {},
      campaignCode: {},
      status: {},
    },
    showToolTip: true,
  },
  others: {
    sortByNewest: true,
    filters: {
      type: {},
      source: {},
      campaignCode: {},
      status: {},
    },
  },
  searchBarHeight: 0,
  primaryBarHeight: 0,
  secondaryBarHeight: 0,
  tertiaryBarHeight: 0,
  leadSearch: {
    searchModalShow: false,
    isHeaderSearchShow: true,
    recentSearchLeadItem: [],
    recentSearchCustomerItem: [],
    filters: {
      type: {},
      source: {},
      campaignCode: {},
      status: {},
    },
  },
  profile: {
    topBarHeight: 0,
  },
  inquiringLeadId: '',
  isExpiredLeadChecked: false,
  expiredLeadSkippedTime: null,
  activities: {
    sortByNewest: true,
  },
  draftAddLeadForm: null,
};
