import { NewsSlice, NewsState, StoreSlice } from 'types';
import { StateCreator } from 'zustand';

export const createNewsSlice: StateCreator<
  StoreSlice,
  [],
  [],
  NewsSlice
> = set => ({
  news: { ...initialState },
  newsActions: {
    updateBookmarkedNewsIdMap: (id, markAs) =>
      set(state => {
        state.news.bookmarkedNewsIdMap[id] = markAs;
        return state;
      }),
  },
});

const initialState: NewsState = {
  bookmarkedNewsIdMap: {},
};
