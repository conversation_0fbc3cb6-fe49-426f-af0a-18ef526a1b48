import { CaseSlice, CaseState, StoreSlice } from 'types';
import { ObjectUtil } from 'utils';
import { StateCreator } from 'zustand';

export const createCaseSlice: StateCreator<
  StoreSlice,
  [],
  [],
  CaseSlice
> = set => ({
  case: ObjectUtil.cloneDeep(initialState),
  caseActions: {
    setActiveCase: (caseId: string) => {
      set(state => {
        state.case.caseId = caseId;
        return state;
      });
    },
    clearActiveCase: () => {
      set(state => {
        state.case = initialState;
        return state;
      });
    },
  },
});

const initialState: CaseState = {
  caseId: undefined,
};
