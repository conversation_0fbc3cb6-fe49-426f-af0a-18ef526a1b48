import { NBLeaderReviewTableContent } from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/NewBusinessLeaderReview';
import { PolicySlice, PolicyState, StoreSlice } from 'types';
import { ObjectUtil } from 'utils/helper';
import { StateCreator } from 'zustand';

export const createPolicySlice: StateCreator<
  StoreSlice,
  [],
  [],
  PolicySlice
> = set => ({
  policy: ObjectUtil.cloneDeep(initialState),
  policyActions: {
    updateReviewPolicyList: (policyList: NBLeaderReviewTableContent[]) =>
      set(state => {
        state.policy.reviewPolicyList = policyList;
        return state;
      }),
  },
});

const initialState: PolicyState = {
  reviewPolicyList: [],
};
