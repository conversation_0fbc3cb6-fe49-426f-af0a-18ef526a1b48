const siConfig = {
  coverageDetails: {
    nationality: {
      visible: false,
      optionListField: 'NATIONALITY',
    },
    residencyType: {
      visible: false,
    },
    phoneCode: {
      disabled: true,
    },
    occupation: {
      disabled: false,
    },
    residence: {
      visible: false,
    },
  },
  rider: {
    shouldDisplayFilter: true,
  },
  email: {
    sendEmailToProceed: {
      quickQuote: false,
      fullQuote: false,
    },
    isMailToEnabled: {
      quickQuote: false,
      fullQuote: false,
    },
  },
  rpq: {
    entity: {
      enabled: true,
    },
    individual: {
      enabled: true,
    },
  },
  flow: {
    entity: {
      quickQuoteScreenName: null,
      quickQuoteLabel: null,
      quickQuoteLabelWithFNA: null,
      enableFullQuoteAgreement: true,
      fullQuoteScreenName: 'EApp',
      fullQuoteLabel: 'proposal:startApplication',
    },
    individual: {
      quickQuoteScreenName: 'Fna',
      quickQuoteLabel: 'proposal:startFNA',
      quickQuoteLabelWithFNA: 'proposal:reviewFNA',
      enableFullQuoteAgreement: true,
      fullQuoteScreenName: 'EApp',
      fullQuoteLabel: 'proposal:startApplication',
    },
  },
  simulation: {
    hasWithdrawalAndTopup: true,
  },
  fund: {
    filterType: 'riskLevel', // property of fund option in template
    hasTopup: true,
    fillUnselectedFund: false,
    fundAllocationMultipleOfNumber: 5,
    topUpAllocationMultipleOfNumber: 5,
    fundAllocationMin: 10,
    topUpAllocationMin: 5,
  },
  pdf: {
    isPasswordProtected: false,
    isPreviewable: true,
    isRequiredToReadAll: {
      quickQuote: false,
      fullQuote: false,
    },
    supportedLanguages: ['en'],
    isAttachmentChangeable: false,
  },
  maxFund: null,
  summaryModalsEnabled: false, // todo: remove this when all regions have summary modals
  isProductReselectionEnabled: false,
  siForm: {
    currency: {
      visible: false,
    },
    loadingSubtitle: {
      visible: false,
    },
    resumeFromQuickQuote: false,
  },
};

export default siConfig;
