import { EAppConfig } from 'types/moduleConfig';

const eAppConfig: EAppConfig = {
  debugPolicyNo: false,
  debugUWMe: false,
  ocrInfoIcon: true,
  shouldCloseHQBeforeDecision: false,
  hasSignaturePlaceholder: true,
  disableFormAfterDecision: false,
  maintainSignaturePlaceOfSigning: false,
  documentUpload: {
    selectFromLibrary: true,
    attachFile: true,
  },
  shouldTrackGeoLocation: false,
  uwmeDismissible: true,
  checkSubmissionReadiness: false,
  verifyReplacementInfoInHealthQuestion: false,
};

export default eAppConfig;
