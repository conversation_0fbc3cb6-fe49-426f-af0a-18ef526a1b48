import { SavedProposalsConfig } from 'types/moduleConfig';

const proposalConfig: SavedProposalsConfig = {
  stages: {
    QUICK_SI: true,
    FNA: true,
    CFF: false,
    FULL_SI: true,
    IN_APP: true,
  },
  clientType: {
    INDIVIDUAL: false,
    ENTITY: false,
  },
  saveNewQuotation: {
    onCreateQuickSi: true,
    onCreateFullSi: true,
    fromSavedQuickSi: true,
    fromSavedFullSi: true,
  },
  createBenefitIllFromExpiredApplication: false,
};

export default proposalConfig;
