import {
  SocialMarketingPlatform,
  SocialMarketingPostType,
  SocialMarketingRatio,
  SocialMarketingVideoDuration,
} from 'features/socialMarketing/types';
import { SocialMarketingConfig } from 'types/moduleConfig';

const socialMarketingConfig: SocialMarketingConfig = {
  defaultPostingChannel: SocialMarketingPlatform.Facebook,
  postingChannels: Object.values(SocialMarketingPlatform),
  socialMediaChannels: {
    [SocialMarketingPostType.Image]: [
      {
        platform: SocialMarketingPlatform.Facebook,
        ratio: SocialMarketingRatio.FourToFive,
      },
      {
        platform: SocialMarketingPlatform.Instagram,
        ratio: SocialMarketingRatio.FourToFive,
      },
      {
        platform: SocialMarketingPlatform.Linkedin,
        ratio: SocialMarketingRatio.FourToFive,
      },
      {
        platform: SocialMarketingPlatform.TikTok,
        ratio: SocialMarketingRatio.NineToSixteen,
      },
    ],
    [SocialMarketingPostType.ShortVideo]: [
      {
        platform: SocialMarketingPlatform.Facebook,
        ratio: SocialMarketingRatio.NineToSixteen,
        durationInSeconds: SocialMarketingVideoDuration.FiveSeconds,
      },
      {
        platform: SocialMarketingPlatform.Instagram,
        ratio: SocialMarketingRatio.NineToSixteen,
        durationInSeconds: SocialMarketingVideoDuration.FiveSeconds,
      },
      {
        platform: SocialMarketingPlatform.Linkedin,
        ratio: SocialMarketingRatio.NineToSixteen,
        durationInSeconds: SocialMarketingVideoDuration.FiveSeconds,
      },
      {
        platform: SocialMarketingPlatform.TikTok,
        ratio: SocialMarketingRatio.NineToSixteen,
        durationInSeconds: SocialMarketingVideoDuration.FiveSeconds,
      },
    ],
  },
};

export default socialMarketingConfig;
