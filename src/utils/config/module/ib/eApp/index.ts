import { EAppConfig } from 'types/moduleConfig';

const eAppConfig: EAppConfig = {
  debugPolicyNo: false,
  debugUWMe: true,
  ocrInfoIcon: false,
  shouldCloseHQBeforeDecision: true,
  hasSignaturePlaceholder: false,
  disableFormAfterDecision: true,
  maintainSignaturePlaceOfSigning: true,
  documentUpload: {
    selectFromLibrary: true,
    attachFile: true,
  },
  shouldTrackGeoLocation: false,
  uwmeDismissible: true,
  checkSubmissionReadiness: false,
  verifyReplacementInfoInHealthQuestion: true,
};

export default eAppConfig;
