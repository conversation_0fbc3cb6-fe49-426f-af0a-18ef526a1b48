const siConfig = {
  coverageDetails: {
    nationality: {
      visible: true,
      optionListField: 'NATIONALITY',
    },
    residencyType: {
      visible: true,
    },
    phoneCode: {
      disabled: false,
    },
    occupation: {
      disabled: false, // indicate if the occupation can be disabled
    },
    residence: {
      visible: true,
    },
  },

  rider: {
    shouldDisplayFilter: false,
  },
  email: {
    sendEmailToProceed: {
      quickQuote: true,
      fullQuote: true,
    },
    isMailToEnabled: {
      quickQuote: true,
      fullQuote: true,
    },
  },
  rpq: {
    entity: {
      enabled: true,
    },
    individual: {
      enabled: true,
    },
  },
  flow: {
    entity: {
      quickQuoteScreenName: null,
      quickQuoteLabel: null,
      quickQuoteLabelWithFNA: null,
      enableFullQuoteAgreement: false,
      fullQuoteScreenName: 'EApp',
      fullQuoteLabel: 'proposal:startApplication',
    },
    individual: {
      quickQuoteScreenName: 'Fna',
      quickQuoteLabel: 'proposal:startFNA',
      quickQuoteLabelWithFNA: 'proposal:reviewFNA',
      enableFullQuoteAgreement: false,
      fullQuoteScreenName: 'EApp',
      fullQuoteLabel: 'proposal:startApplication',
    },
  },
  simulation: {
    hasWithdrawalAndTopup: false,
  },
  fund: {
    filterType: '',
    hasTopup: false,
    fillUnselectedFund: false,
    fundAllocationMultipleOfNumber: 5,
    topUpAllocationMultipleOfNumber: 5,
    fundAllocationMin: 5,
    topUpAllocationMin: 5,
  },
  pdf: {
    isPasswordProtected: true,
    isPreviewable: false,
    isRequiredToReadAll: {
      quickQuote: true,
      fullQuote: true,
    },
    supportedLanguages: ['en', 'my'],
    isAttachmentChangeable: true,
  },
  maxFund: 5,
  summaryModalsEnabled: true, // todo: remove this when all regions have summary modals
  isProductReselectionEnabled: true,
  siForm: {
    currency: {
      visible: false,
    },
    loadingSubtitle: {
      visible: false,
    },
    resumeFromQuickQuote: false,
  },
};

export default siConfig;
