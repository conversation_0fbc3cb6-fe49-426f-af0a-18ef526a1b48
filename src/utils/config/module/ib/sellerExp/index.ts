import { SellerExpModuleConfig } from 'types/moduleConfig';

const sellerConfig: SellerExpModuleConfig = {
  // * Not included in the first release
  isLoginActiveChecked: true,
  Home: true,
  Lead: true,
  Proposals: true,
  Policies: true,
  Performance: true,
  Document: false,
  Others: false,
  Customers: false,
  ReportGeneration: false,
  TeamManagement: true,
  SocialMarketing: true,
  ERecruit: true,
  Affiliate: false,
  myLMS: true,
  Merchandise: false,
  dashboard: {
    TrainerGuruCard: {
      isShown: true,
    },
    FWDNewsCard: {
      isShown: true,
    },
    PerformanceCard: {
      isShown: true,
      isMdrtShown: true,
    },
    BusinessOpportunityCard: {
      isShown: true,
    },
    MarketingCard: {
      isShown: true,
    },
  },
  tasks: {
    AgencyConfidentialReportSection: true,
    BirthdaySection: true,
    ContactLeadsSection: true,
    PaymentRemindersSection: true,
    PolicyIssuesSection: true,
    showCount: false,
  },
  home: {
    businessOppCard: {
      isReminderVisible: false,
      hasViewAll: true,
    },
  },
  lead: {
    leadActivityModal: true,
    policyModal: false,
    recommendedProducts: false,
  },
  policy: {
    showOutstandingItem: true,
  },
  performance: {
    tipYourPerformance: false,
    metricOne: 'APE',
    metricTwo: 'Case',
    TargetSetting: true,
    targetOneField: 'apeTarget',
    completionOneField: 'apeCompletion',
    submissionOneField: 'apeSubmission',
    submissionListField: 'apeSubmissionList',
    submissionItemSubField: 'apeSub',
    completionListField: 'apeCompletionList',
    completionItemSubField: 'ape',
    isPerformanceBSCShown: true,
    isRecognitionShown: true,
    isRankingShown: false,
  },
  isLoggedInUntilAppKilled: true,
  contactOptions: ['call', 'whatsapp', 'email'],
  showPromptLibrary: true,
  contactOptions: ['call', 'whatsapp', 'email'],
};
export default sellerConfig;
