import { ProfileConfig } from 'types/moduleConfig';

enum LANGUAGE {
  ENGLISH = 'english',
}

enum TONE_OF_VOICE {
  CASUAL = 'casual',
  INSPIRATIONAL = 'inspirational',
  PROFESSIONAL = 'professional',
  COMICAL = 'comical',
  PERSONAL = 'personal',
}

const profileConfig: ProfileConfig = {
  defaultLanguage: LANGUAGE.ENGLISH,
  defaultToneOfVoice: TONE_OF_VOICE.CASUAL,
  profileLanguages: Object.values(LANGUAGE),
  profileToneOfVoices: Object.values(TONE_OF_VOICE),
};

export default profileConfig;
