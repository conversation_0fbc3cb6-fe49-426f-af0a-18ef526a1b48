import sellerConfig from './sellerExp';
import fnaConfig from './fna';
import siConfig from './si';
import proposalConfig from './proposal';
import emailSharing from './emailSharing';
import eAppConfig from './eApp';
import ecoachConfig from './ecoach';
import profileConfig from './profile';
import socialMarketingConfig from './socialMarketing';

const my = {
  supportedDevices: {
    tablet: true,
    phone: true,
  },
  proposalConfig,
  sellerConfig,
  fnaConfig,
  siConfig,
  emailSharing,
  eAppConfig,
  ecoachConfig,
  ocrConfig: { validationOrder: ['blurry', 'blank'] },
  profileConfig,
  socialMarketingConfig,
};

export default my;
