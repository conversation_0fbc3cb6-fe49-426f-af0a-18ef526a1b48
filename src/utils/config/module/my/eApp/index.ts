import { EAppConfig } from 'types/moduleConfig';

const eAppConfig: EAppConfig = {
  debugPolicyNo: true,
  debugUWMe: true,
  ocrInfoIcon: false,
  shouldCloseHQBeforeDecision: true,
  hasSignaturePlaceholder: false,
  disableFormAfterDecision: false,
  maintainSignaturePlaceOfSigning: true,
  documentUpload: {
    selectFromLibrary: true,
    attachFile: true,
  },
  shouldTrackGeoLocation: false,
  uwmeDismissible: false,
  checkSubmissionReadiness: false,
  verifyReplacementInfoInHealthQuestion: true,
};

export default eAppConfig;
