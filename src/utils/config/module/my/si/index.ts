import { build } from 'utils/context';

const siConfig = {
  coverageDetails: {
    nationality: {
      visible: true,
      optionListField: 'NATIONALITY',
    },
    residencyType: {
      visible: false,
    },
    phoneCode: {
      disabled: true,
    },
    occupation: {
      disabled: true, // indicate if the occupation can be disabled
    },
    residence: {
      visible: false,
    },
  },

  rider: {
    shouldDisplayFilter: false,
  },
  email: {
    sendEmailToProceed: {
      quickQuote: false,
      fullQuote: true,
    },
    isMailToEnabled: {
      quickQuote: false,
      fullQuote: true,
    },
  },
  rpq: {
    entity: {
      enabled: false,
    },
    individual: {
      enabled: false,
    },
  },
  flow: {
    entity: {
      quickQuoteScreenName: null,
      quickQuoteLabel: null,
      quickQuoteLabelWithFNA: null,
      enableFullQuoteAgreement: false,
      fullQuoteScreenName: 'EApp',
      fullQuoteLabel: 'proposal:startApplication',
    },
    individual: {
      quickQuoteScreenName: 'Fna',
      quickQuoteLabel: 'proposal:startFNA',
      quickQuoteLabelWithFNA: 'proposal:reviewFNA',
      enableFullQuoteAgreement: false,
      fullQuoteScreenName: 'EApp',
      fullQuoteLabel: 'proposal:startApplication',
    },
  },
  simulation: {
    hasWithdrawalAndTopup: false,
  },
  fund: {
    filterType: 'fundType',
    hasTopup: false,
    fillUnselectedFund: true,
    fundAllocationMultipleOfNumber: 5,
    topUpAllocationMultipleOfNumber: 5,
    fundAllocationMin: 5,
    topUpAllocationMin: 5,
  },
  pdf: {
    isPasswordProtected: true,
    isPreviewable: false,
    isRequiredToReadAll: {
      quickQuote: false,
      fullQuote: true,
    },
    supportedLanguages: ['en', 'my'],
    isAttachmentChangeable: true,
  },
  maxFund: 5,
  summaryModalsEnabled: true, // todo: remove this when all regions have summary modals
  isProductReselectionEnabled: true,
  siForm: {
    currency: {
      visible: false,
    },
    loadingSubtitle: {
      visible: false,
    },
    resumeFromQuickQuote: true,
  },
};
export default siConfig;
