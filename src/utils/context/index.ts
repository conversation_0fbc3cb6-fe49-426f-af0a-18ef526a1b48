import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { BuildCountry, BuildType } from 'types';

export const country = Constants?.expoConfig?.extra?.country as BuildCountry;
export const build = Constants?.expoConfig?.extra?.build as BuildType;
export const appVersion = Constants.expoConfig?.version;
export const buildNumber =
  Platform.OS === 'ios'
    ? Constants.expoConfig?.ios?.buildNumber
    : Constants.expoConfig?.android?.versionCode;
export const baseUrl: string =
  Constants?.expoConfig?.extra?.urls?.baseUrl || '';
export const ocrUrl: string = Constants?.expoConfig?.extra?.urls?.ocrUrl || '';
export const irisUrl: string =
  Constants?.expoConfig?.extra?.urls?.irisUrl || '';
export const eIrisUrl: string =
  Constants?.expoConfig?.extra?.urls?.eIrisUrl ?? '--';
export const salesConnectUrlForgotPassword: string =
  Constants?.expoConfig?.extra?.urls?.salesConnectUrlForgotPassword ?? '--';
export const salesConnectUrlLogin: string =
  Constants?.expoConfig?.extra?.urls?.salesConnectUrlLogin ?? '--';
export const fwdPintrAppleStoreUrl: string =
  Constants?.expoConfig?.extra?.urls?.fwdPintrAppleStoreUrl ?? '--';
export const fwdPintrGooglePlayStoreUrl: string =
  Constants?.expoConfig?.extra?.urls?.fwdPintrGooglePlayStoreUrl ?? '--';
export const smartUrl: string =
  Constants?.expoConfig?.extra?.urls?.smartUrl || '';
export const contentStackKey: string =
  Constants?.expoConfig?.extra?.contentStackKey || '';
export const contentStackDeliveryToken: string =
  Constants?.expoConfig?.extra?.contentStackDeliveryToken || '';
export const contentStackEnvironment: string =
  Constants?.expoConfig?.extra?.contentStackEnvironment || '';
export const contentStackSettingEntryId: string =
  Constants?.expoConfig?.extra?.contentStackSettingEntryId || '';
export const smartToken: string =
  Constants?.expoConfig?.extra?.urls?.smartToken || '';
export const smartUserToken: string =
  Constants?.expoConfig?.extra?.urls?.smartUserToken || '';
export const smartUserAgent: string =
  Constants?.expoConfig?.extra?.urls?.smartUserAgent || '';
export const dragonPayToken: string =
  Constants?.expoConfig?.extra?.urls?.dragonPayToken || '';
export const dragonPaySecretKey: string =
  Constants?.expoConfig?.extra?.urls?.dragonPaySecretKey || '';
export const ocrToken: string =
  Constants?.expoConfig?.extra?.urls?.ocrToken || '';
export const getPolicyToken: string =
  Constants?.expoConfig?.extra?.urls?.getPolicyToken || '';
export const fullRemoteSellingEnabled: boolean =
  Constants?.expoConfig?.extra?.fullRemoteSellingEnabled === 'true';
export const enhancedRemoteSellingEnabled: boolean =
  Constants?.expoConfig?.extra?.enhancedRemoteSellingEnabled === 'true';
export const aiBotContentStackId: string =
  Constants?.expoConfig?.extra?.urls?.aiBotContentStackId || '';
export const aiBotContentStackApiKey: string =
  Constants?.expoConfig?.extra?.urls?.aiBotContentStackApiKey || '';
export const aiBotContentStackDeliveryToken: string =
  Constants?.expoConfig?.extra?.urls?.aiBotContentStackDeliveryToken || '';
export const aiBotContentStackEnvironment: string =
  Constants?.expoConfig?.extra?.urls?.aiBotContentStackEnvironment || '';
export const aiBotChatBotId: string =
  Constants?.expoConfig?.extra?.urls?.aiBotChatBotId || '';

export const ecoachBaseUrl: string =
  Constants?.expoConfig?.extra?.urls?.ecoachBaseUrl || '';
export const ecoachPublicConversationWebsocketUrl: string =
  Constants?.expoConfig?.extra?.urls?.ecoachPublicConversationWebsocketUrl ||
  '';
export const ecoachPublicAvatarWebsocketUrl: string =
  Constants?.expoConfig?.extra?.urls?.ecoachPublicAvatarWebsocketUrl || '';
export const ecoachPublicConversationApi: string =
  Constants?.expoConfig?.extra?.urls?.ecoachPublicConversationApi || '';
export const ecoachTurnServerUrl: string =
  Constants?.expoConfig?.extra?.urls?.ecoachTurnServerUrl || '';
export const ecoachTurnServerUser: string =
  Constants?.expoConfig?.extra?.urls?.ecoachTurnServerUser || '';
export const ecoachTurnServerPass: string =
  Constants?.expoConfig?.extra?.urls?.ecoachTurnServerPass || '';

export const surveyUrl: string =
  Constants?.expoConfig?.extra?.urls?.surveyUrl || '';

export const idpId = Constants?.expoConfig?.extra?.idpClientId || '';
export const idpSecret = Constants?.expoConfig?.extra?.idpClientSecret || '';
export const startClaimUrl: string =
  Constants?.expoConfig?.extra?.urls?.startClaimUrl || '';

export const agencyTermsAndConditionsUrl: string =
  Constants?.expoConfig?.extra?.urls?.agencyTermsAndConditionsUrl || '';
export const bancaTermsAndConditionsUrl: string =
  Constants?.expoConfig?.extra?.urls?.bancaTermsAndConditionsUrl || '';
export const facebookAppId: string =
  Constants?.expoConfig?.extra?.facebookAppId || '';

export const isTestEnvironment = build !== 'prd';

export const digitalStorefrontUrl: string =
  Constants?.expoConfig?.extra?.urls?.digitalStorefrontUrl || '';

export const facebookLoginUrl: string =
  Constants?.expoConfig?.extra?.urls?.facebookLoginUrl || '';

export const linkedInClientId: string =
  Constants?.expoConfig?.extra?.urls?.linkedInClientId || '';

export const linkedInAuthUrl: string =
  Constants?.expoConfig?.extra?.urls?.linkedInAuthUrl || '';

export const facebookUrl: string =
  Constants?.expoConfig?.extra?.urls?.facebookUrl || '';

export const instagramAppId: string =
  Constants?.expoConfig?.extra?.instagramAppId || '';

export const instagramLoginUrl: string =
  Constants?.expoConfig?.extra?.urls?.instagramLoginUrl || '';
