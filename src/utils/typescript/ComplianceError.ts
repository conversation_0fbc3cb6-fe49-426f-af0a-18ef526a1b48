export default class ComplianceError extends Error {
  code: string;
  messages: string[];
  private static readonly errorCode = 'COMPLIANCE_ERROR';

  constructor(messages: string[]) {
    super(ComplianceError.getFirstComplianceMessage(messages));

    Object.setPrototypeOf(this, ComplianceError.prototype);

    this.name = 'ComplianceError';
    this.code = ComplianceError.errorCode;
    this.messages = messages; // keep the whole array
  }

  static getFirstComplianceMessage(messages: string[]) {
    return messages[0];
  }

  static isComplianceError(code: string): boolean {
    return code === ComplianceError.errorCode;
  }
}
